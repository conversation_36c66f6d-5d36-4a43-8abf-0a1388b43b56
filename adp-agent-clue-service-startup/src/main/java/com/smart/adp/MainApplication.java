package com.smart.adp;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR> create on 2022/8/24 15:16.
 * (scanBasePackages = {"com.smart.adp"})
 */
@Slf4j
@EnableAsync
@SpringBootApplication(scanBasePackages = {"com.smart.adp"})
@MapperScan(basePackages = {"com.smart.adp.infrastructure.repository", "com.smart.adp.infrastructure.helper.smartBinLogMonitor"})
@EnableFeignClients({"com.smart.adp.infrastructure.feign"})
@EnableAspectJAutoProxy(exposeProxy = true)
public class MainApplication {

    public static void main(String[] args) {
        ApplicationContext context = SpringApplication.run(MainApplication.class, args);
        String serverPort = context.getEnvironment().getProperty("server.port");
        log.info("服务启动成功：[{}]!", serverPort);
    }
}
