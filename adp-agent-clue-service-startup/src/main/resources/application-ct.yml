feign:
  httpclient:
    enabled: true
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 10000 # 连接超时时间
        readTimeout: 30000   # 读超时时间
        loggerLevel: full # 日志级别
      mss-sms:
        url: xx
      mss-push:
        url: xx
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 20000

logging:
  level:
    org.springframework: info
    com.smart.cnec.mall.campaign.mapper: debug
    com.smart.cnec.mall.campaign.feign: debug
    com.smart.adp.infrastructure: debug
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    url: jdbc:mysql://*************:3306/adp_leads?useUnicode=true&allowPublicKeyRetrieval=true&characterEncoding=UTF-8&useSSL=false&autoReconnect=true&failOverReadOnly=false&useAffectedRows=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true
    username: appuser
    password: app@user!!
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 20
      max-wait: 10000
      validation-query: SELECT 1
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      # stat
      filters: stat
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: .js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: 123
  elasticsearch:
    rest:
      username: admin
      password: wM5kME6%dTx4
      uris: **************,**************
      port: 9200
      read-timeout: 60s
      connection-timeout: 30s
  data:
    elasticsearch:
      repositories:
        enabled: true
  pool:
    corePoolSize: 2
    maxPoolSize: 10
    queueCapacity: 100000

  redis:
    host: **************
    port: 6379
    timeout: 20000
    password: adb@smart2021
  rabbitmq:
    host: **************
    port: 5672
    virtual-host: /adp/public
    username: adp
    password: adp@smart2021
    connection-timeout: 5000
    publisher-confirm-type: correlated  # 开启发送确认
    publisher-returns: true             # 开启路由失败回调
    template:
      mandatory: true                   # 必须设置才能触发returns回调
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000
rest:
  connection:
    connectTimeout: 1000
    connectionRequestTimeout: 200
    socketTimeout: 1500
refer:
  url:
    adp:
      api: 'http://adp-java-xapi-api.adp-uat.svc:8080'
      orc: 'http://adp-java-orc-service.adp-uat.svc:8080'
    dws:
      api: 'http://adp-dws-data-service.adp-uat.svc:18082'
    cdp:
      api: 'http://cdp-api-up.smartchina.com.cn'

xxl.job:
  admin:
    addresses: http://tradein-api-uat.smart.cn/trade-in-job-admin
    accessToken: default_token
  executor:
    appname: adp-agent-clue
    address:
    ip:
    port: 9999
    logpath: /idea-project/smart-displace/smart-displace-job/jobhandler
    logretentiondays: 30

thread-pool:
  coreSizeMultiple: 8
  maxSizeMultiple: 16
  queueCapacity: 1000

init: false
mybatis-flex:
  configuration:
    map-underscore-to-camel-case: true  # 启用下划线到驼峰的自动转换
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl # 可选，开启SQL日志

system.meta.esIndex: uat

smartListening:
  enable: false # 启用监听
  structureListen:
    enable: true # 启用表结构监听
  dataListen:
    enable: false # 启用数据监听
  dbConfig: # 数据库配置
    ip: *************
    port: 3306
    username: appuser
    password: app@user!!
    include-databases: mp,orc,csc,adp_leads,interfacecenter,tradin