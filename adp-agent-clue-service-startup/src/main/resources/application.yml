server:
  port: 8080

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
  application:
    name: adp-agent-clue-service
  profiles:
    active: ct
  main:
    allow-bean-definition-overriding: true
    lazy-initialization: true
logging.level.org.springframework.boot.autoconfigure: error
logging.level.org.springframework.data.elasticsearch: debug
logging.level.org.elasticsearch: debug


swagger3:
  # 是否启用swagger，必须配置为true才会生效，线上环境不配置，默认就不会生效（非必填，默认false）
  enabled: true
  # 扫描的包路径（非必填，默认:false）
  basePackage: com.smart
  # swagger请求的basePath，（非必填，默认:/）
  basePath: /
  # 文档标题（非必填，默认:空字符串）
  title: 代理商线索模块
  # 文档描述（非必填，默认:空字符串）
  description: 这里是描述
  # 文档版本号（非必填，默认:空字符串）
  version: v1.0.0
  # 作者（非必填，默认:空字符串）
  author: andy.liu
