#SmartADPCache.enable=true
#SmartADPCache.env=uat
#feign.client.config.default.connectTimeout=10000
#feign.client.config.default.loggerLevel=full
#feign.client.config.default.readTimeout=30000
#feign.client.config.mss-push.url=xx
#feign.client.config.mss-sms.url=xx
#feign.httpclient.enabled=true
#feign.hystrix.enabled=true
#hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=20000
#logging.level.com.smart.cnec.mall.campaign.feign=debug
#logging.level.com.smart.cnec.mall.campaign.mapper=debug
#logging.level.org.springframework=debug
#rest.connection.connectTimeout=1000
#rest.connection.connectionRequestTimeout=200
#rest.connection.socketTimeout=1500
#spring.data.elasticsearch.repositories.enabled=true
#spring.datasource.driver-class-name=org.postgresql.Driver
#spring.datasource.hikari.maximum-pool-size=20
#spring.datasource.hikari.minimum-idle=5
#spring.datasource.password=Smart@2021
#spring.datasource.url=*************************************
#spring.datasource.username=adp_data_read
#spring.elasticsearch.rest.connection-timeout=30s
#spring.elasticsearch.rest.password=wM5kME6%dTx4
#spring.elasticsearch.rest.port=9200
#spring.elasticsearch.rest.read-timeout=60s
#spring.elasticsearch.rest.uris=**************,**************,*************
#spring.elasticsearch.rest.username=admin
#spring.pool.corePoolSize=2
#spring.pool.maxPoolSize=10
#spring.pool.queueCapacity=100000
#spring.redis.database=12
#spring.redis.host=**************
#spring.redis.password=mss@smart2021
#spring.redis.port=6379
#spring.redis.timeout=20000
