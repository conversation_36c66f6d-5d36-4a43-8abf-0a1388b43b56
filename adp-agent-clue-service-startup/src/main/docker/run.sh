cd /adp/
echo ${PROFILE}
#
## 设置环境变量
#export APP_ENV=${PROFILE}
#
#case $APP_ENV in
#  ct)
#    SKYWALKING_CONFIG_NAME="-DSW_AGENT_NAME=mss-mid-ct::adp-agent-clue-service"
#    ;;
#  prod)
#    SKYWALKING_CONFIG_NAME="-DSW_AGENT_NAME=mss-mid-prod::adp-agent-clue-service"
#    ;;
#  sit)
#    SKYWALKING_CONFIG_NAME="-DSW_AGENT_NAME=mss-mid-sit::adp-agent-clue-service"
#    ;;
#  uat)
#    SKYWALKING_CONFIG_NAME="-DSW_AGENT_NAME=mss-mid-uat::adp-agent-clue-service"
#    ;;
#  pp)
#    SKYWALKING_CONFIG_NAME="-DSW_AGENT_NAME=mss-mid-pp::adp-agent-clue-service"
#      ;;
#  *)
#    echo "Unknown environment: $APP_ENV"
#    exit 1
#    ;;
#esac
#echo $SKYWALKING_CONFIG_NAME
#
#APM_PREPROD=$SKYWALKING_CONFIG_NAME
#APM_PREPROD=$APM_PREPROD" -DSW_AGENT_COLLECTOR_BACKEND_SERVICES=skywalking-oap.skywalking.svc:11800"
#APM_PREPROD=$APM_PREPROD" -DSW_JDBC_TRACE_SQL_PARAMETERS=true"

echo "start java now ..."

echo "param1 : " $1

JAVA_OPTS="-Xms1G -XX:PermSize=256M -XX:MaxPermSize=512M -Dfile.encoding=utf-8"

java ${JAVA_OPTS} -jar $1

#echo "start java now ..."
#
#echo "param1 : " $1
#
#JAVA_OPTS="-Xms1G -XX:PermSize=256M -XX:MaxPermSize=512M -Dfile.encoding=utf-8"
#
#java ${JAVA_OPTS} -jar $1

