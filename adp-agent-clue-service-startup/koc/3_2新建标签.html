<!DOCTYPE html>
<html>
  <head>
    <title>3.2新建标签</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/3_2新建标签/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/3_2新建标签/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u853" class="ax_default box_2">
        <div id="u853_div" class=""></div>
        <div id="u853_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u854" class="ax_default shape">
        <div id="u854_div" class=""></div>
        <div id="u854_text" class="text ">
          <p><span style="text-decoration:none;">新建标签</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u855" class="ax_default line1">
        <img id="u855_img" class="img " src="images/3_2新建标签/u855.svg"/>
        <div id="u855_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u856" class="ax_default label">
        <div id="u856_div" class=""></div>
        <div id="u856_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u857" class="ax_default shape">
        <div id="u857_div" class=""></div>
        <div id="u857_text" class="text ">
          <p><span style="text-decoration:none;">保存</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u858" class="ax_default shape">
        <div id="u858_div" class=""></div>
        <div id="u858_text" class="text ">
          <p><span style="text-decoration:none;">关闭</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u859" class="ax_default text_field">
        <div id="u859_div" class=""></div>
        <input id="u859_input" type="text" value="" class="u859_input"/>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u860" class="ax_default text_field disabled">
        <div id="u860_div" class="disabled"></div>
        <input id="u860_input" type="text" value="" class="u860_input" disabled/>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u861" class="ax_default text_field disabled">
        <div id="u861_div" class="disabled"></div>
        <input id="u861_input" type="text" value="" class="u861_input" disabled/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u862" class="ax_default box_1">
        <div id="u862_div" class=""></div>
        <div id="u862_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u863" class="ax_default label">
        <div id="u863_div" class=""></div>
        <div id="u863_text" class="text ">
          <p><span style="text-decoration:none;">一级标签1</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u864" class="ax_default label">
        <div id="u864_div" class=""></div>
        <div id="u864_text" class="text ">
          <p><span style="text-decoration:none;">一级标签2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u865" class="ax_default label">
        <div id="u865_div" class=""></div>
        <div id="u865_text" class="text ">
          <p><span style="text-decoration:none;">一级标签3</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u866" class="ax_default label">
        <div id="u866_div" class=""></div>
        <div id="u866_text" class="text ">
          <p><span style="text-decoration:none;">一级标签：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u867" class="ax_default label">
        <div id="u867_div" class=""></div>
        <div id="u867_text" class="text ">
          <p><span style="text-decoration:none;">二级标签：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u868" class="ax_default label">
        <div id="u868_div" class=""></div>
        <div id="u868_text" class="text ">
          <p><span style="text-decoration:none;">三级标签：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u869" class="ax_default paragraph1">
        <div id="u869_div" class=""></div>
        <div id="u869_text" class="text ">
          <p><span style="text-decoration:none;">1、点击新建标签如上图所示，默认二级标签和三级标签不可用</span></p><p><span style="text-decoration:none;">2、点击一级标签则默认下拉框带出已有一级标签，支持模糊查找,未查找到则不展示下拉选项</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u870" class="ax_default _二级标题">
        <div id="u870_div" class=""></div>
        <div id="u870_text" class="text ">
          <p><span style="text-decoration:none;">PC端：标签管理-新建标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u871" class="ax_default _文本段落">
        <div id="u871_div" class=""></div>
        <div id="u871_text" class="text ">
          <p><span style="text-decoration:none;">保存需要判断标签是否存爱，已存在的判断是一二三级完全匹配认为是已存在</span></p><p><span style="text-decoration:none;">①如果标签体系，已经存在的，则不更新</span></p><p><span style="text-decoration:none;">②如果标签体系，不存在的，则直接新增</span></p><p><span style="text-decoration:none;"><br></span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
