<!DOCTYPE html>
<html>
  <head>
    <title>1.1查询和打标页面-查询后有单个</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/1_1查询和打标页面-查询后有单个/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/1_1查询和打标页面-查询后有单个/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (图像) -->
      <div id="u0" class="ax_default _图片_">
        <img id="u0_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u0.svg"/>
        <div id="u0_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u1" class="ax_default _图片_">
        <img id="u1_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u1.png"/>
        <div id="u1_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u2" class="ax_default _图片_">
        <img id="u2_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u2.png"/>
        <div id="u2_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3" class="ax_default label">
        <div id="u3_div" class=""></div>
        <div id="u3_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4" class="ax_default label">
        <div id="u4_div" class=""></div>
        <div id="u4_text" class="text ">
          <p><span style="text-decoration:none;">smartid:</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u5" class="ax_default text_field">
        <div id="u5_div" class=""></div>
        <input id="u5_input" type="text" value="" class="u5_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6" class="ax_default label">
        <div id="u6_div" class=""></div>
        <div id="u6_text" class="text ">
          <p><span style="text-decoration:none;">-暂无数据-</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u7" class="ax_default label">
        <div id="u7_div" class=""></div>
        <div id="u7_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u8" class="ax_default text_field">
        <div id="u8_div" class=""></div>
        <input id="u8_input" type="text" value="" class="u8_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u9" class="ax_default label">
        <div id="u9_div" class=""></div>
        <div id="u9_text" class="text ">
          <p><span style="text-decoration:none;">昵称：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u10" class="ax_default text_field">
        <div id="u10_div" class=""></div>
        <input id="u10_input" type="text" value="" class="u10_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u11" class="ax_default primary_button">
        <div id="u11_div" class=""></div>
        <div id="u11_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u12" class="ax_default _线段">
        <img id="u12_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u12.svg"/>
        <div id="u12_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u13" class="ax_default _图片_">
        <img id="u13_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u0.svg"/>
        <div id="u13_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u14" class="ax_default _图片_">
        <img id="u14_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u1.png"/>
        <div id="u14_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u15" class="ax_default _图片_">
        <img id="u15_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u2.png"/>
        <div id="u15_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u16" class="ax_default label">
        <div id="u16_div" class=""></div>
        <div id="u16_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u17" class="ax_default label">
        <div id="u17_div" class=""></div>
        <div id="u17_text" class="text ">
          <p><span style="text-decoration:none;">smartid:</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u18" class="ax_default text_field">
        <div id="u18_div" class=""></div>
        <input id="u18_input" type="text" value="" class="u18_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u19" class="ax_default label">
        <div id="u19_div" class=""></div>
        <div id="u19_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u20" class="ax_default text_field">
        <div id="u20_div" class=""></div>
        <input id="u20_input" type="text" value="" class="u20_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u21" class="ax_default label">
        <div id="u21_div" class=""></div>
        <div id="u21_text" class="text ">
          <p><span style="text-decoration:none;">昵称：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u22" class="ax_default text_field">
        <div id="u22_div" class=""></div>
        <input id="u22_input" type="text" value="小团团" class="u22_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u23" class="ax_default primary_button">
        <div id="u23_div" class=""></div>
        <div id="u23_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u24" class="ax_default _线段">
        <img id="u24_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u12.svg"/>
        <div id="u24_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u25" class="ax_default box_2">
        <div id="u25_div" class=""></div>
        <div id="u25_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u26" class="ax_default" data-left="658" data-top="401" data-width="325" data-height="1">

        <!-- Unnamed (组合) -->
        <div id="u27" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0">
        </div>

        <!-- Unnamed (直线 / 行距) -->
        <div id="u28" class="ax_default line">
          <img id="u28_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u28.svg"/>
          <div id="u28_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u29" class="ax_default box_2">
        <div id="u29_div" class=""></div>
        <div id="u29_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u30" class="ax_default label">
        <div id="u30_div" class=""></div>
        <div id="u30_text" class="text ">
          <p><span style="text-decoration:none;">用户昵称：小团团</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u31" class="ax_default label">
        <div id="u31_div" class=""></div>
        <div id="u31_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：3星级达人--lv2，城市--猎手....</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u32" class="ax_default box_2">
        <div id="u32_div" class=""></div>
        <div id="u32_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u33" class="ax_default label">
        <div id="u33_div" class=""></div>
        <div id="u33_text" class="text ">
          <p><span style="text-decoration:none;">已有标签：年轻</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u34" class="ax_default label">
        <div id="u34_div" class=""></div>
        <div id="u34_text" class="text ">
          <p><span style="text-decoration:none;">活力</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u35" class="ax_default label">
        <div id="u35_div" class=""></div>
        <div id="u35_text" class="text ">
          <p><span style="text-decoration:none;">3星期内有意愿购买</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u36" class="ax_default label">
        <div id="u36_div" class=""></div>
        <div id="u36_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u37" class="ax_default label">
        <div id="u37_div" class=""></div>
        <div id="u37_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u38" class="ax_default label">
        <div id="u38_div" class=""></div>
        <div id="u38_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u39" class="ax_default primary_button">
        <div id="u39_div" class=""></div>
        <div id="u39_text" class="text ">
          <p><span style="text-decoration:none;">添加标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u40" class="ax_default label">
        <div id="u40_div" class=""></div>
        <div id="u40_text" class="text ">
          <p><span style="text-decoration:none;">smartid：331365453422234452</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u41" class="ax_default _图片_">
        <img id="u41_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u0.svg"/>
        <div id="u41_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u42" class="ax_default _图片_">
        <img id="u42_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u1.png"/>
        <div id="u42_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u43" class="ax_default _图片_">
        <img id="u43_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u2.png"/>
        <div id="u43_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u44" class="ax_default label">
        <div id="u44_div" class=""></div>
        <div id="u44_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u45" class="ax_default label">
        <div id="u45_div" class=""></div>
        <div id="u45_text" class="text ">
          <p><span style="text-decoration:none;">smartid:</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u46" class="ax_default text_field">
        <div id="u46_div" class=""></div>
        <input id="u46_input" type="text" value="" class="u46_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u47" class="ax_default label">
        <div id="u47_div" class=""></div>
        <div id="u47_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u48" class="ax_default text_field">
        <div id="u48_div" class=""></div>
        <input id="u48_input" type="text" value="" class="u48_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u49" class="ax_default label">
        <div id="u49_div" class=""></div>
        <div id="u49_text" class="text ">
          <p><span style="text-decoration:none;">昵称：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u50" class="ax_default text_field">
        <div id="u50_div" class=""></div>
        <input id="u50_input" type="text" value="小团团" class="u50_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u51" class="ax_default primary_button">
        <div id="u51_div" class=""></div>
        <div id="u51_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u52" class="ax_default _线段">
        <img id="u52_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u12.svg"/>
        <div id="u52_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u53" class="ax_default box_2">
        <div id="u53_div" class=""></div>
        <div id="u53_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u54" class="ax_default" data-left="1964" data-top="401" data-width="325" data-height="1">

        <!-- Unnamed (组合) -->
        <div id="u55" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0">
        </div>

        <!-- Unnamed (直线 / 行距) -->
        <div id="u56" class="ax_default line">
          <img id="u56_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u28.svg"/>
          <div id="u56_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u57" class="ax_default box_2">
        <div id="u57_div" class=""></div>
        <div id="u57_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u58" class="ax_default label">
        <div id="u58_div" class=""></div>
        <div id="u58_text" class="text ">
          <p><span style="text-decoration:none;">用户昵称：小团团</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u59" class="ax_default label">
        <div id="u59_div" class=""></div>
        <div id="u59_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：3星级达人--lv2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u60" class="ax_default label">
        <div id="u60_div" class=""></div>
        <div id="u60_text" class="text ">
          <p><span style="text-decoration:none;">smartid：331365453422234452</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u61" class="ax_default shape">
        <div id="u61_div" class=""></div>
        <div id="u61_text" class="text ">
          <p><span style="text-decoration:none;">打标记录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u62" class="ax_default box_2">
        <div id="u62_div" class=""></div>
        <div id="u62_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u63" class="ax_default label">
        <div id="u63_div" class=""></div>
        <div id="u63_text" class="text ">
          <p><span style="text-decoration:none;">修改人手机号码：135****2246</span></p><p><span style="text-decoration:none;">操作动作：删除</span></p><p><span style="text-decoration:none;">删除内容：三级标签1</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u64" class="ax_default box_2">
        <div id="u64_div" class=""></div>
        <div id="u64_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u65" class="ax_default label">
        <div id="u65_div" class=""></div>
        <div id="u65_text" class="text ">
          <p><span style="text-decoration:none;">修改人手机号码：135****3246<br>操作内容：新增</span></p><p><span style="text-decoration:none;">新增内容：活力1</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u66" class="ax_default _文本段落">
        <div id="u66_div" class=""></div>
        <div id="u66_text" class="text ">
          <p><span style="text-decoration:none;">2025-06-22 17:00:00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u67" class="ax_default _文本段落">
        <div id="u67_div" class=""></div>
        <div id="u67_text" class="text ">
          <p><span style="text-decoration:none;">2025-06-22 16:00:00</span></p>
        </div>
      </div>

      <!-- Unnamed (连接符) -->
      <div id="u68" class="ax_default _连接">
        <img id="u68_seg0" class="img " src="images/1_1查询和打标页面-查询后有单个/u68_seg0.svg" alt="u68_seg0"/>
        <img id="u68_seg1" class="img " src="images/1_1查询和打标页面-查询后有单个/u68_seg1.svg" alt="u68_seg1"/>
        <div id="u68_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u69" class="ax_default label">
        <div id="u69_div" class=""></div>
        <div id="u69_text" class="text ">
          <p><span style="text-decoration:none;">更多</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u70" class="ax_default _二级标题">
        <div id="u70_div" class=""></div>
        <div id="u70_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u71" class="ax_default paragraph1">
        <div id="u71_div" class=""></div>
        <div id="u71_text" class="text ">
          <p><span style="text-decoration:none;">1、首次打开时默认展示如上图所示，页面显示“暂无数据”</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u72" class="ax_default paragraph1">
        <div id="u72_div" class=""></div>
        <div id="u72_text" class="text ">
          <p><span style="text-decoration:none;">1、当输入多个条件或者单个条件，查询结果中命中为1条时，则直接该查询结果，如上图。各查询条件之间是or的关系：查询条件为精确查询</span></p><p><span style="text-decoration:none;">2、如果查询结果大于1条，则需要展示查询列表（见1.2）</span></p><p><span style="text-decoration:none;">3、本页面默认展示tab页“标签信息”，排列如上图所示，可点击“打标信息”进行切换展示其他页面</span></p><p><span style="text-decoration:none;">4、达人类型：如果查过展示行，可以点击“更多“去查看更多信息（见1.4）</span></p><p><span style="text-decoration:none;">5、已有标签：可以直接对已有标签点击“删除”，点击“删除“需要进行二次弹窗确认。如果成功确认删除，则页面更新最新结果，通过”打标记录“中记录该操作信息</span></p><p><span style="text-decoration:none;">6、添加标签：可以对该用户进行添加标签操作（见1.3）</span></p><p><span style="text-decoration:none;">7、页面直接输入可添加备注，可以删除已添加的备注；删除需要二次弹窗确认</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u73" class="ax_default paragraph1">
        <div id="u73_div" class=""></div>
        <div id="u73_text" class="text ">
          <p><span style="text-decoration:none;">1、点击“打标记录“后页面如上图所示</span></p><p><span style="text-decoration:none;">2、本页面按照操作时间展示具体的操作记录</span></p><p><span style="text-decoration:none;">3、操作动作分为2个：删除、新增</span></p><p><span style="text-decoration:none;">4、内容需要展示操作人手机号码、操作的内容</span></p><p><span style="text-decoration:none;">5、按时间倒序，可滚动查看，分页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u74" class="ax_default box_2">
        <div id="u74_div" class=""></div>
        <div id="u74_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u75" class="ax_default label">
        <div id="u75_div" class=""></div>
        <div id="u75_text" class="text ">
          <p><span style="text-decoration:none;">已有标签：</span><span style="text-decoration:none;color:#7F7F7F;">暂无标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u76" class="ax_default primary_button">
        <div id="u76_div" class=""></div>
        <div id="u76_text" class="text ">
          <p><span style="text-decoration:none;">添加标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u77" class="ax_default box_2">
        <div id="u77_div" class=""></div>
        <div id="u77_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u78" class="ax_default label">
        <div id="u78_div" class=""></div>
        <div id="u78_text" class="text ">
          <p><span style="text-decoration:none;">备注：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u79" class="ax_default text_field">
        <div id="u79_div" class=""></div>
        <input id="u79_input" type="text" value="请输入备注" class="u79_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u80" class="ax_default primary_button">
        <div id="u80_div" class=""></div>
        <div id="u80_text" class="text ">
          <p><span style="text-decoration:none;">保存备注</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u81" class="ax_default label">
        <div id="u81_div" class=""></div>
        <div id="u81_text" class="text ">
          <p><span style="text-decoration:none;">暂无备注</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u82" class="ax_default box_2">
        <div id="u82_div" class=""></div>
        <div id="u82_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u83" class="ax_default label">
        <div id="u83_div" class=""></div>
        <div id="u83_text" class="text ">
          <p><span style="text-decoration:none;">用户昵称：小团团</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u84" class="ax_default label">
        <div id="u84_div" class=""></div>
        <div id="u84_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：</span><span style="text-decoration:none;color:#7F7F7F;">无</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u85" class="ax_default label">
        <div id="u85_div" class=""></div>
        <div id="u85_text" class="text ">
          <p><span style="text-decoration:none;">smartid：331365453422234452</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u86" class="ax_default shape">
        <div id="u86_div" class=""></div>
        <div id="u86_text" class="text ">
          <p><span style="text-decoration:none;">标签信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u87" class="ax_default shape">
        <div id="u87_div" class=""></div>
        <div id="u87_text" class="text ">
          <p><span style="text-decoration:none;">打标记录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u88" class="ax_default shape">
        <div id="u88_div" class=""></div>
        <div id="u88_text" class="text ">
          <p><span style="text-decoration:none;">备注</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u89" class="ax_default _图片_">
        <img id="u89_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u0.svg"/>
        <div id="u89_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u90" class="ax_default _图片_">
        <img id="u90_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u1.png"/>
        <div id="u90_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u91" class="ax_default _图片_">
        <img id="u91_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u2.png"/>
        <div id="u91_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u92" class="ax_default label">
        <div id="u92_div" class=""></div>
        <div id="u92_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u93" class="ax_default label">
        <div id="u93_div" class=""></div>
        <div id="u93_text" class="text ">
          <p><span style="text-decoration:none;">smartid:</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u94" class="ax_default text_field">
        <div id="u94_div" class=""></div>
        <input id="u94_input" type="text" value="" class="u94_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u95" class="ax_default label">
        <div id="u95_div" class=""></div>
        <div id="u95_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u96" class="ax_default text_field">
        <div id="u96_div" class=""></div>
        <input id="u96_input" type="text" value="" class="u96_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u97" class="ax_default label">
        <div id="u97_div" class=""></div>
        <div id="u97_text" class="text ">
          <p><span style="text-decoration:none;">昵称：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u98" class="ax_default text_field">
        <div id="u98_div" class=""></div>
        <input id="u98_input" type="text" value="小团团" class="u98_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u99" class="ax_default primary_button">
        <div id="u99_div" class=""></div>
        <div id="u99_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u100" class="ax_default _线段">
        <img id="u100_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u12.svg"/>
        <div id="u100_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u101" class="ax_default box_2">
        <div id="u101_div" class=""></div>
        <div id="u101_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u102" class="ax_default" data-left="1446" data-top="401" data-width="325" data-height="1">

        <!-- Unnamed (组合) -->
        <div id="u103" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0">
        </div>

        <!-- Unnamed (直线 / 行距) -->
        <div id="u104" class="ax_default line">
          <img id="u104_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u28.svg"/>
          <div id="u104_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u105" class="ax_default box_2">
        <div id="u105_div" class=""></div>
        <div id="u105_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u106" class="ax_default label">
        <div id="u106_div" class=""></div>
        <div id="u106_text" class="text ">
          <p><span style="text-decoration:none;">用户昵称：小团团</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u107" class="ax_default label">
        <div id="u107_div" class=""></div>
        <div id="u107_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：3星级达人--lv2，城市--猎手....</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u108" class="ax_default label">
        <div id="u108_div" class=""></div>
        <div id="u108_text" class="text ">
          <p><span style="text-decoration:none;">smartid：331365453422234452</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u109" class="ax_default shape">
        <div id="u109_div" class=""></div>
        <div id="u109_text" class="text ">
          <p><span style="text-decoration:none;">备注</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u110" class="ax_default shape">
        <div id="u110_div" class=""></div>
        <div id="u110_text" class="text ">
          <p><span style="text-decoration:none;">打标记录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u111" class="ax_default box_2">
        <div id="u111_div" class=""></div>
        <div id="u111_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u112" class="ax_default label">
        <div id="u112_div" class=""></div>
        <div id="u112_text" class="text ">
          <p><span style="text-decoration:none;">备注：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u113" class="ax_default text_field">
        <div id="u113_div" class=""></div>
        <input id="u113_input" type="text" value="请输入备注" class="u113_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u114" class="ax_default label">
        <div id="u114_div" class=""></div>
        <div id="u114_text" class="text ">
          <p><span style="text-decoration:none;">更多</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u115" class="ax_default primary_button">
        <div id="u115_div" class=""></div>
        <div id="u115_text" class="text ">
          <p><span style="text-decoration:none;">保存备注</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u116" class="ax_default label">
        <div id="u116_div" class=""></div>
        <div id="u116_text" class="text ">
          <p><span style="text-decoration:none;">我是备注1</span></p><p><span style="text-decoration:none;"><br></span></p><p><span style="text-decoration:none;">我还是备注</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u117" class="ax_default label">
        <div id="u117_div" class=""></div>
        <div id="u117_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u118" class="ax_default label">
        <div id="u118_div" class=""></div>
        <div id="u118_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u119" class="ax_default shape">
        <div id="u119_div" class=""></div>
        <div id="u119_text" class="text ">
          <p><span style="text-decoration:none;">标签信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u120" class="ax_default shape">
        <div id="u120_div" class=""></div>
        <div id="u120_text" class="text ">
          <p><span style="text-decoration:none;">打标记录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u121" class="ax_default shape">
        <div id="u121_div" class=""></div>
        <div id="u121_text" class="text ">
          <p><span style="text-decoration:none;">标签信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u122" class="ax_default shape">
        <div id="u122_div" class=""></div>
        <div id="u122_text" class="text ">
          <p><span style="text-decoration:none;">备注</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
