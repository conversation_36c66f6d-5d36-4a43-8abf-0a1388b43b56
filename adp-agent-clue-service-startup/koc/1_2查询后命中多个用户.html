<!DOCTYPE html>
<html>
  <head>
    <title>1.2查询后命中多个用户</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/1_2查询后命中多个用户/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/1_2查询后命中多个用户/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (图像) -->
      <div id="u123" class="ax_default _图片_">
        <img id="u123_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u0.svg"/>
        <div id="u123_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u124" class="ax_default _图片_">
        <img id="u124_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u1.png"/>
        <div id="u124_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u125" class="ax_default _图片_">
        <img id="u125_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u2.png"/>
        <div id="u125_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u126" class="ax_default label">
        <div id="u126_div" class=""></div>
        <div id="u126_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u127" class="ax_default label">
        <div id="u127_div" class=""></div>
        <div id="u127_text" class="text ">
          <p><span style="text-decoration:none;">smartid:</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u128" class="ax_default text_field">
        <div id="u128_div" class=""></div>
        <input id="u128_input" type="text" value="" class="u128_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u129" class="ax_default label">
        <div id="u129_div" class=""></div>
        <div id="u129_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u130" class="ax_default text_field">
        <div id="u130_div" class=""></div>
        <input id="u130_input" type="text" value="" class="u130_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u131" class="ax_default label">
        <div id="u131_div" class=""></div>
        <div id="u131_text" class="text ">
          <p><span style="text-decoration:none;">昵称：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u132" class="ax_default text_field">
        <div id="u132_div" class=""></div>
        <input id="u132_input" type="text" value="小团团" class="u132_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u133" class="ax_default primary_button">
        <div id="u133_div" class=""></div>
        <div id="u133_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u134" class="ax_default _线段">
        <img id="u134_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u12.svg"/>
        <div id="u134_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u135" class="ax_default box_2">
        <div id="u135_div" class=""></div>
        <div id="u135_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u136" class="ax_default label">
        <div id="u136_div" class=""></div>
        <div id="u136_text" class="text ">
          <p><span style="text-decoration:none;">用户昵称：小团团</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u137" class="ax_default label">
        <div id="u137_div" class=""></div>
        <div id="u137_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：3星级达人--lv2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u138" class="ax_default label">
        <div id="u138_div" class=""></div>
        <div id="u138_text" class="text ">
          <p><span style="text-decoration:none;">smartid：331365453422234452</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u139" class="ax_default box_2">
        <div id="u139_div" class=""></div>
        <div id="u139_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u140" class="ax_default label">
        <div id="u140_div" class=""></div>
        <div id="u140_text" class="text ">
          <p><span style="text-decoration:none;">用户昵称：小团团</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u141" class="ax_default label">
        <div id="u141_div" class=""></div>
        <div id="u141_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：-</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u142" class="ax_default label">
        <div id="u142_div" class=""></div>
        <div id="u142_text" class="text ">
          <p><span style="text-decoration:none;">smartid：331365453422234452</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u143" class="ax_default icon">
        <img id="u143_img" class="img " src="images/1_2查询后命中多个用户/u143.svg"/>
        <div id="u143_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u144" class="ax_default icon">
        <img id="u144_img" class="img " src="images/1_2查询后命中多个用户/u143.svg"/>
        <div id="u144_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u145" class="ax_default _图片_">
        <img id="u145_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u0.svg"/>
        <div id="u145_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u146" class="ax_default _图片_">
        <img id="u146_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u1.png"/>
        <div id="u146_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u147" class="ax_default _图片_">
        <img id="u147_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u2.png"/>
        <div id="u147_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u148" class="ax_default label">
        <div id="u148_div" class=""></div>
        <div id="u148_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u149" class="ax_default label">
        <div id="u149_div" class=""></div>
        <div id="u149_text" class="text ">
          <p><span style="text-decoration:none;">smartid:</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u150" class="ax_default text_field">
        <div id="u150_div" class=""></div>
        <input id="u150_input" type="text" value="" class="u150_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u151" class="ax_default label">
        <div id="u151_div" class=""></div>
        <div id="u151_text" class="text ">
          <p><span style="text-decoration:none;">-暂无数据-</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u152" class="ax_default label">
        <div id="u152_div" class=""></div>
        <div id="u152_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u153" class="ax_default text_field">
        <div id="u153_div" class=""></div>
        <input id="u153_input" type="text" value="" class="u153_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u154" class="ax_default label">
        <div id="u154_div" class=""></div>
        <div id="u154_text" class="text ">
          <p><span style="text-decoration:none;">昵称：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u155" class="ax_default text_field">
        <div id="u155_div" class=""></div>
        <input id="u155_input" type="text" value="" class="u155_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u156" class="ax_default primary_button">
        <div id="u156_div" class=""></div>
        <div id="u156_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u157" class="ax_default _线段">
        <img id="u157_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u12.svg"/>
        <div id="u157_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接符) -->
      <div id="u158" class="ax_default _连接">
        <img id="u158_seg0" class="img " src="images/1_2查询后命中多个用户/u158_seg0.svg" alt="u158_seg0"/>
        <img id="u158_seg1" class="img " src="images/1_2查询后命中多个用户/u158_seg1.svg" alt="u158_seg1"/>
        <img id="u158_seg2" class="img " src="images/1_2查询后命中多个用户/u158_seg2.svg" alt="u158_seg2"/>
        <div id="u158_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接符) -->
      <div id="u159" class="ax_default _连接">
        <img id="u159_seg0" class="img " src="images/1_2查询后命中多个用户/u159_seg0.svg" alt="u159_seg0"/>
        <img id="u159_seg1" class="img " src="images/1_2查询后命中多个用户/u159_seg1.svg" alt="u159_seg1"/>
        <img id="u159_seg2" class="img " src="images/1_2查询后命中多个用户/u159_seg2.svg" alt="u159_seg2"/>
        <div id="u159_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u160" class="ax_default paragraph1">
        <div id="u160_div" class=""></div>
        <div id="u160_text" class="text ">
          <p><span style="font-family:'FOR smart Next';font-weight:400;text-decoration:none;">1、输入条件后，如果有多个命中条件的，则展示用户列表,</span><span style="font-family:'Helvetica Regular', 'Helvetica';font-weight:400;text-decoration:none;">各查询条件之间是or的关系</span></p><p><span style="font-family:'FOR smart Next';font-weight:400;text-decoration:none;">2、可以滚动下滑（数据分页展示）查看更多数据</span></p><p><span style="font-family:'FOR smart Next';font-weight:400;text-decoration:none;">3、点击某个用户后的箭头，则打开用户详情页，如右图</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u161" class="ax_default _二级标题">
        <div id="u161_div" class=""></div>
        <div id="u161_text" class="text ">
          <p><span style="text-decoration:none;">app端：查询后命中多个用户</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u162" class="ax_default _图片_">
        <img id="u162_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u0.svg"/>
        <div id="u162_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u163" class="ax_default _图片_">
        <img id="u163_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u1.png"/>
        <div id="u163_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u164" class="ax_default _图片_">
        <img id="u164_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u2.png"/>
        <div id="u164_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u165" class="ax_default label">
        <div id="u165_div" class=""></div>
        <div id="u165_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u166" class="ax_default label">
        <div id="u166_div" class=""></div>
        <div id="u166_text" class="text ">
          <p><span style="text-decoration:none;">smartid:</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u167" class="ax_default text_field">
        <div id="u167_div" class=""></div>
        <input id="u167_input" type="text" value="" class="u167_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u168" class="ax_default label">
        <div id="u168_div" class=""></div>
        <div id="u168_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u169" class="ax_default text_field">
        <div id="u169_div" class=""></div>
        <input id="u169_input" type="text" value="" class="u169_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u170" class="ax_default label">
        <div id="u170_div" class=""></div>
        <div id="u170_text" class="text ">
          <p><span style="text-decoration:none;">昵称：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u171" class="ax_default text_field">
        <div id="u171_div" class=""></div>
        <input id="u171_input" type="text" value="小团团" class="u171_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u172" class="ax_default primary_button">
        <div id="u172_div" class=""></div>
        <div id="u172_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u173" class="ax_default _线段">
        <img id="u173_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u12.svg"/>
        <div id="u173_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u174" class="ax_default box_2">
        <div id="u174_div" class=""></div>
        <div id="u174_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u175" class="ax_default" data-left="1115" data-top="452" data-width="325" data-height="1">

        <!-- Unnamed (组合) -->
        <div id="u176" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0">
        </div>

        <!-- Unnamed (直线 / 行距) -->
        <div id="u177" class="ax_default line">
          <img id="u177_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u28.svg"/>
          <div id="u177_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u178" class="ax_default box_2">
        <div id="u178_div" class=""></div>
        <div id="u178_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u179" class="ax_default label">
        <div id="u179_div" class=""></div>
        <div id="u179_text" class="text ">
          <p><span style="text-decoration:none;">用户昵称：小团团</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u180" class="ax_default label">
        <div id="u180_div" class=""></div>
        <div id="u180_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：3星级达人--lv2，城市--猎手....</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u181" class="ax_default box_2">
        <div id="u181_div" class=""></div>
        <div id="u181_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u182" class="ax_default label">
        <div id="u182_div" class=""></div>
        <div id="u182_text" class="text ">
          <p><span style="text-decoration:none;">已有标签：年轻</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u183" class="ax_default label">
        <div id="u183_div" class=""></div>
        <div id="u183_text" class="text ">
          <p><span style="text-decoration:none;">活力</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u184" class="ax_default label">
        <div id="u184_div" class=""></div>
        <div id="u184_text" class="text ">
          <p><span style="text-decoration:none;">3星期内有意愿购买</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u185" class="ax_default label">
        <div id="u185_div" class=""></div>
        <div id="u185_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u186" class="ax_default label">
        <div id="u186_div" class=""></div>
        <div id="u186_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u187" class="ax_default label">
        <div id="u187_div" class=""></div>
        <div id="u187_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u188" class="ax_default primary_button">
        <div id="u188_div" class=""></div>
        <div id="u188_text" class="text ">
          <p><span style="text-decoration:none;">添加标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u189" class="ax_default label">
        <div id="u189_div" class=""></div>
        <div id="u189_text" class="text ">
          <p><span style="text-decoration:none;">smartid：331365453422234452</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u190" class="ax_default shape">
        <div id="u190_div" class=""></div>
        <div id="u190_text" class="text ">
          <p><span style="text-decoration:none;">标签信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u191" class="ax_default shape">
        <div id="u191_div" class=""></div>
        <div id="u191_text" class="text ">
          <p><span style="text-decoration:none;">打标记录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u192" class="ax_default label">
        <div id="u192_div" class=""></div>
        <div id="u192_text" class="text ">
          <p><span style="text-decoration:none;">更多</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u193" class="ax_default shape">
        <div id="u193_div" class=""></div>
        <div id="u193_text" class="text ">
          <p><span style="text-decoration:none;">备注</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
