<!DOCTYPE html>
<html>
  <head>
    <title>1.4达人类型有多个时</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/1_4达人类型有多个时/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/1_4达人类型有多个时/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (图像) -->
      <div id="u360" class="ax_default _图片_">
        <img id="u360_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u0.svg"/>
        <div id="u360_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u361" class="ax_default _图片_">
        <img id="u361_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u1.png"/>
        <div id="u361_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u362" class="ax_default _图片_">
        <img id="u362_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u2.png"/>
        <div id="u362_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u363" class="ax_default label">
        <div id="u363_div" class=""></div>
        <div id="u363_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u364" class="ax_default label">
        <div id="u364_div" class=""></div>
        <div id="u364_text" class="text ">
          <p><span style="text-decoration:none;">smartid:</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u365" class="ax_default text_field">
        <div id="u365_div" class=""></div>
        <input id="u365_input" type="text" value="" class="u365_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u366" class="ax_default label">
        <div id="u366_div" class=""></div>
        <div id="u366_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u367" class="ax_default text_field">
        <div id="u367_div" class=""></div>
        <input id="u367_input" type="text" value="" class="u367_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u368" class="ax_default label">
        <div id="u368_div" class=""></div>
        <div id="u368_text" class="text ">
          <p><span style="text-decoration:none;">昵称：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u369" class="ax_default text_field">
        <div id="u369_div" class=""></div>
        <input id="u369_input" type="text" value="小团团" class="u369_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u370" class="ax_default primary_button">
        <div id="u370_div" class=""></div>
        <div id="u370_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u371" class="ax_default _线段">
        <img id="u371_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u12.svg"/>
        <div id="u371_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u372" class="ax_default box_2">
        <div id="u372_div" class=""></div>
        <div id="u372_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u373" class="ax_default" data-left="683" data-top="495" data-width="325" data-height="1">

        <!-- Unnamed (组合) -->
        <div id="u374" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0">
        </div>

        <!-- Unnamed (直线 / 行距) -->
        <div id="u375" class="ax_default line">
          <img id="u375_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u28.svg"/>
          <div id="u375_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u376" class="ax_default shape">
        <div id="u376_div" class=""></div>
        <div id="u376_text" class="text ">
          <p><span style="text-decoration:none;">基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u377" class="ax_default box_2">
        <div id="u377_div" class=""></div>
        <div id="u377_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u378" class="ax_default label">
        <div id="u378_div" class=""></div>
        <div id="u378_text" class="text ">
          <p><span style="text-decoration:none;">活力</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u379" class="ax_default label">
        <div id="u379_div" class=""></div>
        <div id="u379_text" class="text ">
          <p><span style="text-decoration:none;">3星期内有意愿购买</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u380" class="ax_default label">
        <div id="u380_div" class=""></div>
        <div id="u380_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u381" class="ax_default label">
        <div id="u381_div" class=""></div>
        <div id="u381_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u382" class="ax_default primary_button">
        <div id="u382_div" class=""></div>
        <div id="u382_text" class="text ">
          <p><span style="text-decoration:none;">添加标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u383" class="ax_default shape">
        <div id="u383_div" class=""></div>
        <div id="u383_text" class="text ">
          <p><span style="text-decoration:none;">标签信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u384" class="ax_default shape">
        <div id="u384_div" class=""></div>
        <div id="u384_text" class="text ">
          <p><span style="text-decoration:none;">打标记录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u385" class="ax_default box_2">
        <div id="u385_div" class=""></div>
        <div id="u385_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u386" class="ax_default shape">
        <div id="u386_div" class=""></div>
        <div id="u386_text" class="text ">
          <p><span style="text-decoration:none;">达人类型</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u387" class="ax_default _线段">
        <img id="u387_img" class="img " src="images/1_4达人类型有多个时/u387.svg"/>
        <div id="u387_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u388" class="ax_default label">
        <div id="u388_div" class=""></div>
        <div id="u388_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u389" class="ax_default shape">
        <div id="u389_div" class=""></div>
        <div id="u389_text" class="text ">
          <p><span style="text-decoration:none;">关闭</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u390" class="ax_default _图片_">
        <img id="u390_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u0.svg"/>
        <div id="u390_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u391" class="ax_default _图片_">
        <img id="u391_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u1.png"/>
        <div id="u391_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u392" class="ax_default _图片_">
        <img id="u392_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u2.png"/>
        <div id="u392_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u393" class="ax_default label">
        <div id="u393_div" class=""></div>
        <div id="u393_text" class="text ">
          <p><span style="text-decoration:none;">查询和打标</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u394" class="ax_default label">
        <div id="u394_div" class=""></div>
        <div id="u394_text" class="text ">
          <p><span style="text-decoration:none;">smartid:</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u395" class="ax_default text_field">
        <div id="u395_div" class=""></div>
        <input id="u395_input" type="text" value="" class="u395_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u396" class="ax_default label">
        <div id="u396_div" class=""></div>
        <div id="u396_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u397" class="ax_default text_field">
        <div id="u397_div" class=""></div>
        <input id="u397_input" type="text" value="" class="u397_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u398" class="ax_default label">
        <div id="u398_div" class=""></div>
        <div id="u398_text" class="text ">
          <p><span style="text-decoration:none;">昵称：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u399" class="ax_default text_field">
        <div id="u399_div" class=""></div>
        <input id="u399_input" type="text" value="小团团" class="u399_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u400" class="ax_default primary_button">
        <div id="u400_div" class=""></div>
        <div id="u400_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u401" class="ax_default _线段">
        <img id="u401_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u12.svg"/>
        <div id="u401_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u402" class="ax_default box_2">
        <div id="u402_div" class=""></div>
        <div id="u402_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u403" class="ax_default" data-left="157" data-top="495" data-width="325" data-height="1">

        <!-- Unnamed (组合) -->
        <div id="u404" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0">
        </div>

        <!-- Unnamed (直线 / 行距) -->
        <div id="u405" class="ax_default line">
          <img id="u405_img" class="img " src="images/1_1查询和打标页面-查询后有单个/u28.svg"/>
          <div id="u405_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u406" class="ax_default shape">
        <div id="u406_div" class=""></div>
        <div id="u406_text" class="text ">
          <p><span style="text-decoration:none;">基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u407" class="ax_default box_2">
        <div id="u407_div" class=""></div>
        <div id="u407_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u408" class="ax_default label">
        <div id="u408_div" class=""></div>
        <div id="u408_text" class="text ">
          <p><span style="text-decoration:none;">用户昵称：小团团</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u409" class="ax_default label">
        <div id="u409_div" class=""></div>
        <div id="u409_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：3星级达人--lv2，城市--猎手....</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u410" class="ax_default box_2">
        <div id="u410_div" class=""></div>
        <div id="u410_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u411" class="ax_default label">
        <div id="u411_div" class=""></div>
        <div id="u411_text" class="text ">
          <p><span style="text-decoration:none;">已有标签：年轻</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u412" class="ax_default label">
        <div id="u412_div" class=""></div>
        <div id="u412_text" class="text ">
          <p><span style="text-decoration:none;">活力</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u413" class="ax_default label">
        <div id="u413_div" class=""></div>
        <div id="u413_text" class="text ">
          <p><span style="text-decoration:none;">3星期内有意愿购买</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u414" class="ax_default label">
        <div id="u414_div" class=""></div>
        <div id="u414_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u415" class="ax_default label">
        <div id="u415_div" class=""></div>
        <div id="u415_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u416" class="ax_default label">
        <div id="u416_div" class=""></div>
        <div id="u416_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u417" class="ax_default primary_button">
        <div id="u417_div" class=""></div>
        <div id="u417_text" class="text ">
          <p><span style="text-decoration:none;">添加标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u418" class="ax_default label">
        <div id="u418_div" class=""></div>
        <div id="u418_text" class="text ">
          <p><span style="text-decoration:none;">smartid：331365453422234452</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u419" class="ax_default shape">
        <div id="u419_div" class=""></div>
        <div id="u419_text" class="text ">
          <p><span style="text-decoration:none;">标签信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u420" class="ax_default shape">
        <div id="u420_div" class=""></div>
        <div id="u420_text" class="text ">
          <p><span style="text-decoration:none;">打标记录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u421" class="ax_default label">
        <div id="u421_div" class=""></div>
        <div id="u421_text" class="text ">
          <p><span style="text-decoration:none;">更多</span></p>
        </div>
      </div>

      <!-- Unnamed (连接符) -->
      <div id="u422" class="ax_default _连接">
        <img id="u422_seg0" class="img " src="images/1_4达人类型有多个时/u422_seg0.svg" alt="u422_seg0"/>
        <img id="u422_seg1" class="img " src="images/1_4达人类型有多个时/u422_seg1.svg" alt="u422_seg1"/>
        <img id="u422_seg2" class="img " src="images/1_4达人类型有多个时/u422_seg2.svg" alt="u422_seg2"/>
        <img id="u422_seg3" class="img " src="images/1_4达人类型有多个时/u422_seg3.svg" alt="u422_seg3"/>
        <div id="u422_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u423" class="ax_default label">
        <div id="u423_div" class=""></div>
        <div id="u423_text" class="text ">
          <p><span style="text-decoration:none;">3星级达人--lv2</span></p><p><span style="text-decoration:none;"><br></span></p><p><span style="text-decoration:none;">城市--猎手</span></p><p><span style="text-decoration:none;"><br></span></p><p><span style="text-decoration:none;">城市-小资队长</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u424" class="ax_default box_2">
        <div id="u424_div" class=""></div>
        <div id="u424_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u425" class="ax_default paragraph1">
        <div id="u425_div" class=""></div>
        <div id="u425_text" class="text ">
          <p><span style="text-decoration:none;">1、点击“更多”则展示如上图所示</span></p><p><span style="text-decoration:none;">2、支持上下滑动展示所有数据</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u426" class="ax_default _二级标题">
        <div id="u426_div" class=""></div>
        <div id="u426_text" class="text ">
          <p><span style="text-decoration:none;">app端：达人类型有多个时</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
