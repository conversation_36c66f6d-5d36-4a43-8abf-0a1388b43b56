<!DOCTYPE html>
<html>
  <head>
    <title>3.1批量导入标签</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/3_1批量导入标签/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/3_1批量导入标签/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (表格) -->
      <div id="u829" class="ax_default">

        <!-- Unnamed (表格单元) -->
        <div id="u830" class="ax_default">
          <img id="u830_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u830_text" class="text ">
            <p><span style="text-decoration:none;">一级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u831" class="ax_default">
          <img id="u831_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u831_text" class="text ">
            <p><span style="text-decoration:none;">二级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u832" class="ax_default">
          <img id="u832_img" class="img " src="images/3_1批量导入标签/u832.png"/>
          <div id="u832_text" class="text ">
            <p><span style="text-decoration:none;">三级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u833" class="ax_default">
          <img id="u833_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u833_text" class="text ">
            <p><span style="text-decoration:none;">一级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u834" class="ax_default">
          <img id="u834_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u834_text" class="text ">
            <p><span style="text-decoration:none;">二级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u835" class="ax_default">
          <img id="u835_img" class="img " src="images/3_1批量导入标签/u835.png"/>
          <div id="u835_text" class="text ">
            <p><span style="text-decoration:none;">三级标签1</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u836" class="ax_default label">
        <div id="u836_div" class=""></div>
        <div id="u836_text" class="text ">
          <p><span style="text-decoration:none;">导入模板表头和示例：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u837" class="ax_default paragraph1">
        <div id="u837_div" class=""></div>
        <div id="u837_text" class="text ">
          <p><span style="text-decoration:none;">1、点击按钮“批量导入”弹窗如上图</span></p><p><span style="text-decoration:none;">2、点击“导入模板下载“则直接将模板文件进行下载</span></p><p><span style="text-decoration:none;">3、上传文件后点击“导入”，则展示导入进度（如右图）</span></p><p><span style="text-decoration:none;">4、导入原则：已存在的判断是一二三级完全匹配认为是已存在</span></p><p><span style="text-decoration:none;">①如果标签体系，已经存在的，则不更新</span></p><p><span style="text-decoration:none;">②如果标签体系，不存在的，则直接新增</span></p><p><span style="text-decoration:none;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u838" class="ax_default _二级标题">
        <div id="u838_div" class=""></div>
        <div id="u838_text" class="text ">
          <p><span style="text-decoration:none;">PC端：标签管理-批量导入</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u839" class="ax_default _图片_">
        <img id="u839_img" class="img " src="images/2_1批量设置/u524.png"/>
        <div id="u839_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u840" class="ax_default box_2">
        <div id="u840_div" class=""></div>
        <div id="u840_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u841" class="ax_default box_2">
        <div id="u841_div" class=""></div>
        <div id="u841_text" class="text ">
          <p><span style="text-decoration:none;">3条导入失败</span></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u842" class="ax_default">

        <!-- Unnamed (表格单元) -->
        <div id="u843" class="ax_default">
          <img id="u843_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u843_text" class="text ">
            <p><span style="text-decoration:none;">一级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u844" class="ax_default">
          <img id="u844_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u844_text" class="text ">
            <p><span style="text-decoration:none;">二级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u845" class="ax_default">
          <img id="u845_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u845_text" class="text ">
            <p><span style="text-decoration:none;">三级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u846" class="ax_default">
          <img id="u846_img" class="img " src="images/3_1批量导入标签/u832.png"/>
          <div id="u846_text" class="text ">
            <p><span style="text-decoration:none;">错误信息</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u847" class="ax_default">
          <img id="u847_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u847_text" class="text ">
            <p><span style="text-decoration:none;">一级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u848" class="ax_default">
          <img id="u848_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u848_text" class="text ">
            <p><span style="text-decoration:none;">二级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u849" class="ax_default">
          <img id="u849_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u849_text" class="text ">
            <p><span style="text-decoration:none;">三级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u850" class="ax_default">
          <img id="u850_img" class="img " src="images/3_1批量导入标签/u835.png"/>
          <div id="u850_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u851" class="ax_default paragraph1">
        <div id="u851_div" class=""></div>
        <div id="u851_text" class="text ">
          <p><span style="text-decoration:none;">1、下载的文件中仅将错误的行数据全部展示出来，如下图</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u852" class="ax_default paragraph1">
        <div id="u852_div" class=""></div>
        <div id="u852_text" class="text ">
          <p><span style="text-decoration:none;">1、导入时，如果有错误的，则错误数据不导入。并且弹窗展示如下内容</span></p><p><span style="text-decoration:none;">2、判断规则：</span></p><p><span style="text-decoration:none;">①一级标签、二级标签、三级标签：必须都有值，否则算异常错误，已存在不认为是错误，直接跳过即可</span></p><p><span style="text-decoration:none;"><br></span></p><p><span style="text-decoration:none;"><br></span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
