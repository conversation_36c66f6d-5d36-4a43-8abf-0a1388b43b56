<!DOCTYPE html>
<html>
  <head>
    <title>2.5查看备注</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/2_5查看备注/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/2_5查看备注/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u714" class="ax_default shape">
        <div id="u714_div" class=""></div>
        <div id="u714_text" class="text ">
          <p><span style="text-decoration:none;">查看备注</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u715" class="ax_default line1">
        <img id="u715_img" class="img " src="images/2_5查看备注/u715.svg"/>
        <div id="u715_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u716" class="ax_default label">
        <div id="u716_div" class=""></div>
        <div id="u716_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u717" class="ax_default label">
        <div id="u717_div" class=""></div>
        <div id="u717_text" class="text ">
          <p><span style="text-decoration:none;">·&nbsp; 这是备注1</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u718" class="ax_default label">
        <div id="u718_div" class=""></div>
        <div id="u718_text" class="text ">
          <p><span style="text-decoration:none;">·&nbsp; 这是备注2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u719" class="ax_default label">
        <div id="u719_div" class=""></div>
        <div id="u719_text" class="text ">
          <p><span style="text-decoration:none;">·&nbsp; 这是备注3</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
