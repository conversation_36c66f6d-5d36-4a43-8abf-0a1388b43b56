<!DOCTYPE html>
<html>
  <head>
    <title>2.3编辑达人类型</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/2_3编辑达人类型/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/2_3编辑达人类型/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u611" class="ax_default box_2">
        <div id="u611_div" class=""></div>
        <div id="u611_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u612" class="ax_default shape">
        <div id="u612_div" class=""></div>
        <div id="u612_text" class="text ">
          <p><span style="text-decoration:none;">编辑</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u613" class="ax_default line1">
        <img id="u613_img" class="img " src="images/2_2编辑用户标签/u597.svg"/>
        <div id="u613_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u614" class="ax_default label">
        <div id="u614_div" class=""></div>
        <div id="u614_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u615" class="ax_default box_2">
        <div id="u615_div" class=""></div>
        <div id="u615_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u616" class="ax_default label">
        <div id="u616_div" class=""></div>
        <div id="u616_text" class="text ">
          <p><span style="text-decoration:none;">已有标签：年轻</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u617" class="ax_default label">
        <div id="u617_div" class=""></div>
        <div id="u617_text" class="text ">
          <p><span style="text-decoration:none;">活力</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u618" class="ax_default label">
        <div id="u618_div" class=""></div>
        <div id="u618_text" class="text ">
          <p><span style="text-decoration:none;">3星期内有意愿购买</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u619" class="ax_default shape">
        <div id="u619_div" class=""></div>
        <div id="u619_text" class="text ">
          <p><span style="text-decoration:none;">取消</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u620" class="ax_default box_2">
        <div id="u620_div" class=""></div>
        <div id="u620_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u621" class="ax_default label">
        <div id="u621_div" class=""></div>
        <div id="u621_text" class="text ">
          <p><span style="text-decoration:none;">smartid：23444234356</span></p><p><span style="text-decoration:none;"><br></span></p><p><span style="text-decoration:none;">用户昵称：小团团1</span></p><p><span style="text-decoration:none;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u622" class="ax_default box_2">
        <div id="u622_div" class=""></div>
        <div id="u622_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u623" class="ax_default label">
        <div id="u623_div" class=""></div>
        <div id="u623_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：3星级达人--lv2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u624" class="ax_default primary_button">
        <div id="u624_div" class=""></div>
        <div id="u624_text" class="text ">
          <p><span style="text-decoration:none;">保存</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u625" class="ax_default label">
        <div id="u625_div" class=""></div>
        <div id="u625_text" class="text ">
          <p><span style="text-decoration:none;">修改</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u626" class="ax_default label">
        <div id="u626_div" class=""></div>
        <div id="u626_text" class="text ">
          <p><span style="text-decoration:none;">修改</span></p>
        </div>
      </div>

      <!-- Unnamed (连接符) -->
      <div id="u627" class="ax_default _连接">
        <img id="u627_seg0" class="img " src="images/2_3编辑达人类型/u627_seg0.svg" alt="u627_seg0"/>
        <img id="u627_seg1" class="img " src="images/2_3编辑达人类型/u627_seg1.svg" alt="u627_seg1"/>
        <img id="u627_seg2" class="img " src="images/2_3编辑达人类型/u627_seg2.svg" alt="u627_seg2"/>
        <img id="u627_seg3" class="img " src="images/2_3编辑达人类型/u627_seg3.svg" alt="u627_seg3"/>
        <div id="u627_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u628" class="ax_default paragraph1">
        <div id="u628_div" class=""></div>
        <div id="u628_text" class="text ">
          <p><span style="text-decoration:none;">1、点击“修改”达人类型如右图<br>2、取消：则取消本次操作</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u629" class="ax_default paragraph1">
        <div id="u629_div" class=""></div>
        <div id="u629_text" class="text ">
          <p><span style="text-decoration:none;">1、展示达人类型的配置数据</span></p><p><span style="text-decoration:none;">2、筛选条件：支持三个条件的模糊查找</span></p><p><span style="text-decoration:none;">3、添加：点击“添加”后，则该达人类型被选中到“已选中”栏，并且按钮变为红色的“取消”</span></p><p><span style="text-decoration:none;">4、取消：</span></p><p><span style="text-decoration:none;">①点击“取消”，则该达人类型从“已选中”栏移除，并且按钮变为“添加”</span></p><p><span style="text-decoration:none;">②可以点击已选中栏的“×”按钮直接取消</span></p><p><span style="text-decoration:none;">5、添加达人类型，则本次修改回到右侧页面</span></p><p><span style="text-decoration:none;">6、取消：则取消本次操作</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u630" class="ax_default _二级标题">
        <div id="u630_div" class=""></div>
        <div id="u630_text" class="text ">
          <p><span style="text-decoration:none;">PC端：用户信息管理-编辑达人类型</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u631" class="ax_default _形状">
        <div id="u631_div" class=""></div>
        <div id="u631_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u632" class="ax_default _形状">
        <div id="u632_div" class=""></div>
        <div id="u632_text" class="text ">
          <p><span style="text-decoration:none;">达人类型</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u633" class="ax_default _形状">
        <div id="u633_div" class=""></div>
        <div id="u633_text" class="text ">
          <p><span style="text-decoration:none;">备注</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u634" class="ax_default _形状">
        <div id="u634_div" class=""></div>
        <div id="u634_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u635" class="ax_default _形状">
        <div id="u635_div" class=""></div>
        <div id="u635_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u636" class="ax_default droplist">
        <div id="u636_div" class=""></div>
        <select id="u636_input" class="u636_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u637" class="ax_default box_2">
        <div id="u637_div" class=""></div>
        <div id="u637_text" class="text ">
          <p><span style="text-decoration:none;">达人类型</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u638" class="ax_default droplist">
        <div id="u638_div" class=""></div>
        <select id="u638_input" class="u638_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u639" class="ax_default box_2">
        <div id="u639_div" class=""></div>
        <div id="u639_text" class="text ">
          <p><span style="text-decoration:none;">类型内容</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u640" class="ax_default _形状">
        <div id="u640_div" class=""></div>
        <div id="u640_text" class="text ">
          <p><span style="text-decoration:none;">类型内容</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u641" class="ax_default _形状">
        <div id="u641_div" class=""></div>
        <div id="u641_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u642" class="ax_default droplist">
        <div id="u642_div" class=""></div>
        <select id="u642_input" class="u642_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u643" class="ax_default box_2">
        <div id="u643_div" class=""></div>
        <div id="u643_text" class="text ">
          <p><span style="text-decoration:none;">备注</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u644" class="ax_default droplist">
        <div id="u644_div" class=""></div>
        <select id="u644_input" class="u644_input">
          <option class="u644_input_option" value="已添加/未添加">已添加/未添加</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u645" class="ax_default box_2">
        <div id="u645_div" class=""></div>
        <div id="u645_text" class="text ">
          <p><span style="text-decoration:none;">状态</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u646" class="ax_default _形状">
        <div id="u646_div" class=""></div>
        <div id="u646_text" class="text ">
          <p><span style="text-decoration:none;">筛选</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u647" class="ax_default _形状">
        <div id="u647_div" class=""></div>
        <div id="u647_text" class="text ">
          <p><span style="text-decoration:none;">重置</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u648" class="ax_default _形状">
        <div id="u648_div" class=""></div>
        <div id="u648_text" class="text ">
          <p><span style="text-decoration:none;">创建时间</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u649" class="ax_default _形状">
        <div id="u649_div" class=""></div>
        <div id="u649_text" class="text ">
          <p><span style="text-decoration:none;">2025-09-09 23:00:00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u650" class="ax_default _形状">
        <div id="u650_div" class=""></div>
        <div id="u650_text" class="text ">
          <p><span style="text-decoration:none;">2025-09-09 23:00:00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u651" class="ax_default _形状">
        <div id="u651_div" class=""></div>
        <div id="u651_text" class="text ">
          <p><span style="text-decoration:none;">2025-09-09 23:00:00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u652" class="ax_default _形状">
        <div id="u652_div" class=""></div>
        <div id="u652_text" class="text ">
          <p><span style="text-decoration:none;">2025-09-09 23:00:00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u653" class="ax_default _形状">
        <div id="u653_div" class=""></div>
        <div id="u653_text" class="text ">
          <p><span style="text-decoration:none;">2025-09-09 23:00:00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u654" class="ax_default _形状">
        <div id="u654_div" class=""></div>
        <div id="u654_text" class="text ">
          <p><span style="text-decoration:none;">操作</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u655" class="ax_default _形状">
        <div id="u655_div" class=""></div>
        <div id="u655_text" class="text ">
          <p><span style="text-decoration:none;">添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u656" class="ax_default _形状">
        <div id="u656_div" class=""></div>
        <div id="u656_text" class="text ">
          <p><span style="text-decoration:none;">添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u657" class="ax_default _形状">
        <div id="u657_div" class=""></div>
        <div id="u657_text" class="text ">
          <p><span style="text-decoration:none;">添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u658" class="ax_default _形状">
        <div id="u658_div" class=""></div>
        <div id="u658_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u659" class="ax_default _形状">
        <div id="u659_div" class=""></div>
        <div id="u659_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u660" class="ax_default _形状">
        <div id="u660_div" class=""></div>
        <div id="u660_text" class="text ">
          <p><span style="text-decoration:none;">&lt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u661" class="ax_default _形状">
        <div id="u661_div" class=""></div>
        <div id="u661_text" class="text ">
          <p><span style="text-decoration:none;">1</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u662" class="ax_default _形状">
        <div id="u662_div" class=""></div>
        <div id="u662_text" class="text ">
          <p><span style="text-decoration:none;">2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u663" class="ax_default _形状">
        <div id="u663_div" class=""></div>
        <div id="u663_text" class="text ">
          <p><span style="text-decoration:none;">20</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u664" class="ax_default _形状">
        <div id="u664_div" class=""></div>
        <div id="u664_text" class="text ">
          <p><span style="text-decoration:none;">&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u665" class="ax_default _形状">
        <div id="u665_div" class=""></div>
        <div id="u665_text" class="text ">
          <p><span style="text-decoration:none;">10条/页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u666" class="ax_default _文本段落">
        <div id="u666_div" class=""></div>
        <div id="u666_text" class="text ">
          <p><span style="text-decoration:none;">...</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u667" class="ax_default _形状">
        <div id="u667_div" class=""></div>
        <div id="u667_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u668" class="ax_default _形状">
        <div id="u668_div" class=""></div>
        <div id="u668_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u669" class="ax_default _形状">
        <div id="u669_div" class=""></div>
        <div id="u669_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u670" class="ax_default _形状">
        <div id="u670_div" class=""></div>
        <div id="u670_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u671" class="ax_default _形状">
        <div id="u671_div" class=""></div>
        <div id="u671_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u672" class="ax_default _形状">
        <div id="u672_div" class=""></div>
        <div id="u672_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u673" class="ax_default _形状">
        <div id="u673_div" class=""></div>
        <div id="u673_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u674" class="ax_default _形状">
        <div id="u674_div" class=""></div>
        <div id="u674_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u675" class="ax_default _形状">
        <div id="u675_div" class=""></div>
        <div id="u675_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u676" class="ax_default _形状">
        <div id="u676_div" class=""></div>
        <div id="u676_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u677" class="ax_default _形状">
        <div id="u677_div" class=""></div>
        <div id="u677_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u678" class="ax_default _形状">
        <div id="u678_div" class=""></div>
        <div id="u678_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u679" class="ax_default _形状">
        <div id="u679_div" class=""></div>
        <div id="u679_text" class="text ">
          <p><span style="text-decoration:none;">状态</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u680" class="ax_default _形状">
        <div id="u680_div" class=""></div>
        <div id="u680_text" class="text ">
          <p><span style="text-decoration:none;">未添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u681" class="ax_default _形状">
        <div id="u681_div" class=""></div>
        <div id="u681_text" class="text ">
          <p><span style="text-decoration:none;">未添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u682" class="ax_default _形状">
        <div id="u682_div" class=""></div>
        <div id="u682_text" class="text ">
          <p><span style="text-decoration:none;">未添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u683" class="ax_default _形状">
        <div id="u683_div" class=""></div>
        <div id="u683_text" class="text ">
          <p><span style="text-decoration:none;">已添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u684" class="ax_default _形状">
        <div id="u684_div" class=""></div>
        <div id="u684_text" class="text ">
          <p><span style="text-decoration:none;">已添加</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
