<!DOCTYPE html>
<html>
  <head>
    <title>2.4打标记录</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/2_4打标记录/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/2_4打标记录/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u685" class="ax_default box_2">
        <div id="u685_div" class=""></div>
        <div id="u685_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u686" class="ax_default shape">
        <div id="u686_div" class=""></div>
        <div id="u686_text" class="text ">
          <p><span style="text-decoration:none;">打标记录</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u687" class="ax_default line1">
        <img id="u687_img" class="img " src="images/2_4打标记录/u687.svg"/>
        <div id="u687_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u688" class="ax_default label">
        <div id="u688_div" class=""></div>
        <div id="u688_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u689" class="ax_default" data-left="619" data-top="588" data-width="273" data-height="32">

        <!-- Unnamed (矩形) -->
        <div id="u690" class="ax_default shape">
          <div id="u690_div" class=""></div>
          <div id="u690_text" class="text ">
            <p><span style="text-decoration:none;">1</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u691" class="ax_default shape">
          <div id="u691_div" class=""></div>
          <div id="u691_text" class="text ">
            <p><span style="text-decoration:none;">2</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u692" class="ax_default shape">
          <div id="u692_div" class=""></div>
          <div id="u692_text" class="text ">
            <p><span style="text-decoration:none;">3</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u693" class="ax_default shape">
          <div id="u693_div" class=""></div>
          <div id="u693_text" class="text ">
            <p><span style="text-decoration:none;">5</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u694" class="ax_default" data-left="619" data-top="588" data-width="32" data-height="32">

          <!-- Unnamed (矩形) -->
          <div id="u695" class="ax_default shape">
            <div id="u695_div" class=""></div>
            <div id="u695_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (图像) -->
          <div id="u696" class="ax_default image">
            <img id="u696_img" class="img " src="images/2_0用户信息展示/u493.png"/>
            <div id="u696_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u697" class="ax_default" data-left="860" data-top="588" data-width="32" data-height="32">

          <!-- Unnamed (矩形) -->
          <div id="u698" class="ax_default shape">
            <div id="u698_div" class=""></div>
            <div id="u698_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (图像) -->
          <div id="u699" class="ax_default image">
            <img id="u699_img" class="img " src="images/2_4打标记录/u699.svg"/>
            <div id="u699_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u700" class="ax_default label">
          <div id="u700_div" class=""></div>
          <div id="u700_text" class="text ">
            <p><span style="text-decoration:none;">...</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u701" class="ax_default label">
        <div id="u701_div" class=""></div>
        <div id="u701_text" class="text ">
          <p><span style="text-decoration:none;">共 15 条，每页 5 条</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u702" class="ax_default box_2">
        <div id="u702_div" class=""></div>
        <div id="u702_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u703" class="ax_default label">
        <div id="u703_div" class=""></div>
        <div id="u703_text" class="text ">
          <p><span style="text-decoration:none;">用户昵称：小团团</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u704" class="ax_default label">
        <div id="u704_div" class=""></div>
        <div id="u704_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：3星级达人--lv2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u705" class="ax_default label">
        <div id="u705_div" class=""></div>
        <div id="u705_text" class="text ">
          <p><span style="text-decoration:none;">smartid：331365453422234452</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u706" class="ax_default box_2">
        <div id="u706_div" class=""></div>
        <div id="u706_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u707" class="ax_default label">
        <div id="u707_div" class=""></div>
        <div id="u707_text" class="text ">
          <p><span style="text-decoration:none;">修改人手机号码：135****2246</span></p><p><span style="text-decoration:none;">操作动作：删除</span></p><p><span style="text-decoration:none;">删除内容：三级标签1</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u708" class="ax_default box_2">
        <div id="u708_div" class=""></div>
        <div id="u708_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u709" class="ax_default label">
        <div id="u709_div" class=""></div>
        <div id="u709_text" class="text ">
          <p><span style="text-decoration:none;">修改人手机号码：135****3246<br>操作内容：新增</span></p><p><span style="text-decoration:none;">新增内容：活力1</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u710" class="ax_default _文本段落">
        <div id="u710_div" class=""></div>
        <div id="u710_text" class="text ">
          <p><span style="text-decoration:none;">2025-06-22 17:00:00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u711" class="ax_default _文本段落">
        <div id="u711_div" class=""></div>
        <div id="u711_text" class="text ">
          <p><span style="text-decoration:none;">2025-06-22 16:00:00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u712" class="ax_default paragraph1">
        <div id="u712_div" class=""></div>
        <div id="u712_text" class="text ">
          <p><span style="text-decoration:none;">1、点击“打标记录“后页面如上图所示</span></p><p><span style="text-decoration:none;">2、本页面按照操作时间展示具体的操作记录</span></p><p><span style="text-decoration:none;">3、操作动作分为2个：删除、新增</span></p><p><span style="text-decoration:none;">4、内容需要展示操作人手机号码、操作的内容</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u713" class="ax_default _二级标题">
        <div id="u713_div" class=""></div>
        <div id="u713_text" class="text ">
          <p><span style="text-decoration:none;">PC端：用户信息管理-打标记录</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
