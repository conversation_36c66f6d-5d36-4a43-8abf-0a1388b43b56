<!DOCTYPE html>
<html>
  <head>
    <title>4.3编辑</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/4_3编辑/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/4_3编辑/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1023" class="ax_default box_2">
        <div id="u1023_div" class=""></div>
        <div id="u1023_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1024" class="ax_default shape">
        <div id="u1024_div" class=""></div>
        <div id="u1024_text" class="text ">
          <p><span style="text-decoration:none;">编辑达人类型</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u1025" class="ax_default line1">
        <img id="u1025_img" class="img " src="images/3_2新建标签/u855.svg"/>
        <div id="u1025_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1026" class="ax_default label">
        <div id="u1026_div" class=""></div>
        <div id="u1026_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1027" class="ax_default shape">
        <div id="u1027_div" class=""></div>
        <div id="u1027_text" class="text ">
          <p><span style="text-decoration:none;">保存</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1028" class="ax_default shape">
        <div id="u1028_div" class=""></div>
        <div id="u1028_text" class="text ">
          <p><span style="text-decoration:none;">关闭</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1029" class="ax_default label">
        <div id="u1029_div" class=""></div>
        <div id="u1029_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u1030" class="ax_default text_field">
        <div id="u1030_div" class=""></div>
        <input id="u1030_input" type="text" value="达人类型1" class="u1030_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1031" class="ax_default label">
        <div id="u1031_div" class=""></div>
        <div id="u1031_text" class="text ">
          <p><span style="text-decoration:none;">类型内容：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u1032" class="ax_default text_field">
        <div id="u1032_div" class=""></div>
        <input id="u1032_input" type="text" value="类型内容1" class="u1032_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1033" class="ax_default label">
        <div id="u1033_div" class=""></div>
        <div id="u1033_text" class="text ">
          <p><span style="text-decoration:none;">备注：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u1034" class="ax_default text_field">
        <div id="u1034_div" class=""></div>
        <input id="u1034_input" type="text" value="备注1" class="u1034_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1035" class="ax_default paragraph1">
        <div id="u1035_div" class=""></div>
        <div id="u1035_text" class="text ">
          <p><span style="text-decoration:none;">1、点击编辑按钮则将已有内容带出，可以直接修改</span></p><p><span style="text-decoration:none;">2、修改后点击保存需要校验：</span></p><p><span style="text-decoration:none;">①已使用达人类型不能修改。并且提醒：该类型正在使用中，暂不能修改</span></p><p><span style="text-decoration:none;">②当类型内容被使用时，默认对应的达人类型均被使用，也要被纳入不能修改的判断条件中。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1036" class="ax_default _二级标题">
        <div id="u1036_div" class=""></div>
        <div id="u1036_text" class="text ">
          <p><span style="text-decoration:none;">PC端：达人类型管理-编辑</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
