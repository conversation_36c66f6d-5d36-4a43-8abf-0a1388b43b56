<!DOCTYPE html>
<html>
  <head>
    <title>3.3编辑标签</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/3_3编辑标签/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/3_3编辑标签/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u872" class="ax_default box_2">
        <div id="u872_div" class=""></div>
        <div id="u872_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u873" class="ax_default shape">
        <div id="u873_div" class=""></div>
        <div id="u873_text" class="text ">
          <p><span style="text-decoration:none;">编辑标签</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u874" class="ax_default line1">
        <img id="u874_img" class="img " src="images/3_2新建标签/u855.svg"/>
        <div id="u874_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u875" class="ax_default label">
        <div id="u875_div" class=""></div>
        <div id="u875_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u876" class="ax_default shape">
        <div id="u876_div" class=""></div>
        <div id="u876_text" class="text ">
          <p><span style="text-decoration:none;">保存</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u877" class="ax_default shape">
        <div id="u877_div" class=""></div>
        <div id="u877_text" class="text ">
          <p><span style="text-decoration:none;">关闭</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u878" class="ax_default label">
        <div id="u878_div" class=""></div>
        <div id="u878_text" class="text ">
          <p><span style="text-decoration:none;">一级标签：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u879" class="ax_default text_field">
        <div id="u879_div" class=""></div>
        <input id="u879_input" type="text" value="一级标签1" class="u879_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u880" class="ax_default label">
        <div id="u880_div" class=""></div>
        <div id="u880_text" class="text ">
          <p><span style="text-decoration:none;">二级标签：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u881" class="ax_default text_field">
        <div id="u881_div" class=""></div>
        <input id="u881_input" type="text" value="二级标签1" class="u881_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u882" class="ax_default label">
        <div id="u882_div" class=""></div>
        <div id="u882_text" class="text ">
          <p><span style="text-decoration:none;">三级标签：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u883" class="ax_default text_field">
        <div id="u883_div" class=""></div>
        <input id="u883_input" type="text" value="三级标签1" class="u883_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u884" class="ax_default paragraph1">
        <div id="u884_div" class=""></div>
        <div id="u884_text" class="text ">
          <p><span style="text-decoration:none;">1、点击编辑按钮则将已有内容带出，可以直接修改</span></p><p><span style="text-decoration:none;">2、修改后点击保存需要校验：</span></p><p><span style="text-decoration:none;">①已使用标签不能修改。并且提醒：该标签正在使用中，暂不能修改</span></p><p><span style="text-decoration:none;">②当三级标签被使用时，默认对应的1级标签、2级标签均被使用，也要被纳入不能修改的判断条件中。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u885" class="ax_default _二级标题">
        <div id="u885_div" class=""></div>
        <div id="u885_text" class="text ">
          <p><span style="text-decoration:none;">PC端：标签管理-编辑标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u886" class="ax_default _文本段落">
        <div id="u886_div" class=""></div>
        <div id="u886_text" class="text ">
          <p><span style="text-decoration:none;">保存需要判断标签是否存爱，已存在的判断是一二三级完全匹配认为是已存在</span></p><p><span style="text-decoration:none;">①如果标签体系，已经存在的，则不更新</span></p><p><span style="text-decoration:none;">②如果标签体系，不存在的，则直接新增</span></p><p><span style="text-decoration:none;"><br></span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
