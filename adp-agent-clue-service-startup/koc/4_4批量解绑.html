<!DOCTYPE html>
<html>
  <head>
    <title>4.4批量解绑</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/4_4批量解绑/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/4_4批量解绑/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1037" class="ax_default box_2">
        <div id="u1037_div" class=""></div>
        <div id="u1037_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1038" class="ax_default shape">
        <div id="u1038_div" class=""></div>
        <div id="u1038_text" class="text ">
          <p><span style="text-decoration:none;">批量解绑用户和达人类型绑定关系</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u1039" class="ax_default line1">
        <img id="u1039_img" class="img " src="images/3_2新建标签/u855.svg"/>
        <div id="u1039_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1040" class="ax_default label">
        <div id="u1040_div" class=""></div>
        <div id="u1040_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1041" class="ax_default shape">
        <div id="u1041_div" class=""></div>
        <div id="u1041_text" class="text ">
          <p><span style="text-decoration:none;">批量解绑</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1042" class="ax_default shape">
        <div id="u1042_div" class=""></div>
        <div id="u1042_text" class="text ">
          <p><span style="text-decoration:none;">关闭</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1043" class="ax_default label">
        <div id="u1043_div" class=""></div>
        <div id="u1043_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1044" class="ax_default label">
        <div id="u1044_div" class=""></div>
        <div id="u1044_text" class="text ">
          <p><span style="text-decoration:none;">类型内容：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1045" class="ax_default paragraph1">
        <div id="u1045_div" class=""></div>
        <div id="u1045_text" class="text ">
          <p><span style="text-decoration:none;">1、点击批量解绑弹窗如上图，默认类型内容不可修改</span></p><p><span style="text-decoration:none;">2、达人类型：下拉框单选，类型内容根据类型默认带出</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u1046" class="ax_default droplist">
        <div id="u1046_div" class=""></div>
        <select id="u1046_input" class="u1046_input">
          <option class="u1046_input_option" value="请选择">请选择</option>
          <option class="u1046_input_option" value="创作达人">创作达人</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1047" class="ax_default box_2">
        <div id="u1047_div" class=""></div>
        <div id="u1047_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1048" class="ax_default shape">
        <div id="u1048_div" class=""></div>
        <div id="u1048_text" class="text ">
          <p><span style="text-decoration:none;">批量解绑用户和达人类型绑定关系</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u1049" class="ax_default line1">
        <img id="u1049_img" class="img " src="images/3_2新建标签/u855.svg"/>
        <div id="u1049_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1050" class="ax_default label">
        <div id="u1050_div" class=""></div>
        <div id="u1050_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1051" class="ax_default label">
        <div id="u1051_div" class=""></div>
        <div id="u1051_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1052" class="ax_default label">
        <div id="u1052_div" class=""></div>
        <div id="u1052_text" class="text ">
          <p><span style="text-decoration:none;">类型内容：</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u1053" class="ax_default droplist">
        <div id="u1053_div" class=""></div>
        <select id="u1053_input" class="u1053_input">
          <option class="u1053_input_option" value="请选择">请选择</option>
          <option class="u1053_input_option" selected value="创作达人">创作达人</option>
        </select>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u1054" class="ax_default droplist">
        <div id="u1054_div" class=""></div>
        <select id="u1054_input" class="u1054_input">
          <option class="u1054_input_option" selected value="请选择">请选择</option>
          <option class="u1054_input_option" value="灵感萌新">灵感萌新</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1055" class="ax_default shape">
        <div id="u1055_div" class=""></div>
        <div id="u1055_text" class="text ">
          <p><span style="text-decoration:none;">查询已绑定数量</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1056" class="ax_default shape">
        <div id="u1056_div" class=""></div>
        <div id="u1056_text" class="text ">
          <p><span style="text-decoration:none;">批量解绑</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1057" class="ax_default shape">
        <div id="u1057_div" class=""></div>
        <div id="u1057_text" class="text ">
          <p><span style="text-decoration:none;">关闭</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1058" class="ax_default shape">
        <div id="u1058_div" class=""></div>
        <div id="u1058_text" class="text ">
          <p><span style="text-decoration:none;">查询已绑定数量</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1059" class="ax_default box_2">
        <div id="u1059_div" class=""></div>
        <div id="u1059_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1060" class="ax_default shape">
        <div id="u1060_div" class=""></div>
        <div id="u1060_text" class="text ">
          <p><span style="text-decoration:none;">批量解绑用户和达人类型绑定关系</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u1061" class="ax_default line1">
        <img id="u1061_img" class="img " src="images/3_2新建标签/u855.svg"/>
        <div id="u1061_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1062" class="ax_default label">
        <div id="u1062_div" class=""></div>
        <div id="u1062_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1063" class="ax_default shape">
        <div id="u1063_div" class=""></div>
        <div id="u1063_text" class="text ">
          <p><span style="text-decoration:none;">批量解绑</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1064" class="ax_default shape">
        <div id="u1064_div" class=""></div>
        <div id="u1064_text" class="text ">
          <p><span style="text-decoration:none;">关闭</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1065" class="ax_default label">
        <div id="u1065_div" class=""></div>
        <div id="u1065_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1066" class="ax_default label">
        <div id="u1066_div" class=""></div>
        <div id="u1066_text" class="text ">
          <p><span style="text-decoration:none;">类型内容：</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u1067" class="ax_default droplist">
        <div id="u1067_div" class=""></div>
        <select id="u1067_input" class="u1067_input">
          <option class="u1067_input_option" value="请选择">请选择</option>
          <option class="u1067_input_option" selected value="创作达人">创作达人</option>
        </select>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u1068" class="ax_default droplist">
        <div id="u1068_div" class=""></div>
        <select id="u1068_input" class="u1068_input">
          <option class="u1068_input_option" value="请选择">请选择</option>
          <option class="u1068_input_option" selected value="灵感萌新">灵感萌新</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1069" class="ax_default shape">
        <div id="u1069_div" class=""></div>
        <div id="u1069_text" class="text ">
          <p><span style="text-decoration:none;">查询已绑定数量</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1070" class="ax_default label">
        <div id="u1070_div" class=""></div>
        <div id="u1070_text" class="text ">
          <p><span style="text-decoration:none;">已绑定数量：</span><span style="text-decoration:none;color:rgba(217, 0, 27, 0.996078431372549);">500人</span></p>
        </div>
      </div>

      <!-- Unnamed (连接符) -->
      <div id="u1071" class="ax_default _连接">
        <img id="u1071_seg0" class="img " src="images/4_4批量解绑/u1071_seg0.svg" alt="u1071_seg0"/>
        <img id="u1071_seg1" class="img " src="images/4_4批量解绑/u1071_seg1.svg" alt="u1071_seg1"/>
        <img id="u1071_seg2" class="img " src="images/4_4批量解绑/u1071_seg2.svg" alt="u1071_seg2"/>
        <img id="u1071_seg3" class="img " src="images/4_4批量解绑/u1071_seg3.svg" alt="u1071_seg3"/>
        <img id="u1071_seg4" class="img " src="images/4_4批量解绑/u1071_seg4.svg" alt="u1071_seg4"/>
        <div id="u1071_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接符) -->
      <div id="u1072" class="ax_default _连接">
        <img id="u1072_seg0" class="img " src="images/4_4批量解绑/u1072_seg0.svg" alt="u1072_seg0"/>
        <img id="u1072_seg1" class="img " src="images/4_4批量解绑/u1072_seg1.svg" alt="u1072_seg1"/>
        <img id="u1072_seg2" class="img " src="images/4_4批量解绑/u1072_seg2.svg" alt="u1072_seg2"/>
        <div id="u1072_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1073" class="ax_default paragraph1">
        <div id="u1073_div" class=""></div>
        <div id="u1073_text" class="text ">
          <p><span style="text-decoration:none;">1、批量解绑/查询已绑定数量：点击时达人类型和类型必须有值。否则需要提醒“***没有值，请补充”</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1074" class="ax_default paragraph1">
        <div id="u1074_div" class=""></div>
        <div id="u1074_text" class="text ">
          <p><span style="text-decoration:none;">1、查询已绑定数量：符合条件查询后页面如上图，展示”已绑定数量“</span></p><p><span style="text-decoration:none;">2、批量解绑：符合条件后，需要进行二次弹窗确认</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1075" class="ax_default _二级标题">
        <div id="u1075_div" class=""></div>
        <div id="u1075_text" class="text ">
          <p><span style="text-decoration:none;">PC端：达人类型管理-批量解绑</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1076" class="ax_default box_2">
        <div id="u1076_div" class=""></div>
        <div id="u1076_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
