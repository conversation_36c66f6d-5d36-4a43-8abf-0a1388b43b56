<!DOCTYPE html>
<html>
  <head>
    <title>2.0用户信息展示</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/2_0用户信息展示/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/2_0用户信息展示/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u427" class="ax_default box_2">
        <div id="u427_div" class=""></div>
        <div id="u427_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u428" class="ax_default box_1">
        <div id="u428_div" class=""></div>
        <div id="u428_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u429" class="ax_default box_1">
        <div id="u429_div" class=""></div>
        <div id="u429_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u430" class="ax_default _图片_">
        <img id="u430_img" class="img " src="images/2_0用户信息展示/u430.png"/>
        <div id="u430_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u431" class="ax_default _图片_">
        <img id="u431_img" class="img " src="images/2_0用户信息展示/u431.png"/>
        <div id="u431_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u432" class="ax_default _图片_">
        <img id="u432_img" class="img " src="images/2_0用户信息展示/u432.png"/>
        <div id="u432_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u433" class="ax_default box_1">
        <div id="u433_div" class=""></div>
        <div id="u433_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u434" class="ax_default box_1">
        <div id="u434_div" class=""></div>
        <div id="u434_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u435" class="ax_default box_1">
        <div id="u435_div" class=""></div>
        <div id="u435_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u436" class="ax_default label">
        <div id="u436_div" class=""></div>
        <div id="u436_text" class="text ">
          <p><span style="text-decoration:none;">打标管理</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u437" class="ax_default label">
        <div id="u437_div" class=""></div>
        <div id="u437_text" class="text ">
          <p><span style="text-decoration:none;">标签管理</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u438" class="ax_default icon">
        <img id="u438_img" class="img " src="images/2_0用户信息展示/u438.svg"/>
        <div id="u438_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u439" class="ax_default box_2">
        <div id="u439_div" class=""></div>
        <div id="u439_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u440" class="ax_default label">
        <div id="u440_div" class=""></div>
        <div id="u440_text" class="text ">
          <p><span style="text-decoration:none;">用户信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u441" class="ax_default label">
        <div id="u441_div" class=""></div>
        <div id="u441_text" class="text ">
          <p><span style="text-decoration:none;">用户信息管理</span></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u442" class="ax_default">

        <!-- Unnamed (表格单元) -->
        <div id="u443" class="ax_default">
          <img id="u443_img" class="img " src="images/2_0用户信息展示/u443.png"/>
          <div id="u443_text" class="text ">
            <p><span style="text-decoration:none;">序号</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u444" class="ax_default">
          <img id="u444_img" class="img " src="images/2_0用户信息展示/u444.png"/>
          <div id="u444_text" class="text ">
            <p><span style="text-decoration:none;">smartid</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u445" class="ax_default">
          <img id="u445_img" class="img " src="images/2_0用户信息展示/u445.png"/>
          <div id="u445_text" class="text ">
            <p><span style="text-decoration:none;">用户昵称</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u446" class="ax_default">
          <img id="u446_img" class="img " src="images/2_0用户信息展示/u446.png"/>
          <div id="u446_text" class="text ">
            <p><span style="text-decoration:none;">用户手机号码</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u447" class="ax_default">
          <img id="u447_img" class="img " src="images/2_0用户信息展示/u447.png"/>
          <div id="u447_text" class="text ">
            <p><span style="text-decoration:none;">达人类型</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u448" class="ax_default">
          <img id="u448_img" class="img " src="images/2_0用户信息展示/u448.png"/>
          <div id="u448_text" class="text ">
            <p><span style="text-decoration:none;">用户标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u449" class="ax_default">
          <img id="u449_img" class="img " src="images/2_0用户信息展示/u449.png"/>
          <div id="u449_text" class="text ">
            <p><span style="text-decoration:none;">备注</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u450" class="ax_default">
          <img id="u450_img" class="img " src="images/2_0用户信息展示/u450.png"/>
          <div id="u450_text" class="text ">
            <p><span style="text-decoration:none;">标签最近一次修改时间</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u451" class="ax_default">
          <img id="u451_img" class="img " src="images/2_0用户信息展示/u451.png"/>
          <div id="u451_text" class="text ">
            <p><span style="text-decoration:none;">操作</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u452" class="ax_default">
          <img id="u452_img" class="img " src="images/2_0用户信息展示/u452.png"/>
          <div id="u452_text" class="text ">
            <p><span style="text-decoration:none;">1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u453" class="ax_default">
          <img id="u453_img" class="img " src="images/2_0用户信息展示/u453.png"/>
          <div id="u453_text" class="text ">
            <p><span style="text-decoration:none;">3737</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u454" class="ax_default">
          <img id="u454_img" class="img " src="images/2_0用户信息展示/u454.png"/>
          <div id="u454_text" class="text ">
            <p><span style="text-decoration:none;">小团团1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u455" class="ax_default">
          <img id="u455_img" class="img " src="images/2_0用户信息展示/u455.png"/>
          <div id="u455_text" class="text ">
            <p><span style="text-decoration:none;">156****2222</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u456" class="ax_default">
          <img id="u456_img" class="img " src="images/2_0用户信息展示/u456.png"/>
          <div id="u456_text" class="text ">
            <p style="font-size:13px;"><span style="text-decoration:none;">A--lv2，</span><span style="font-size:14px;text-decoration:none;">3星级达人--lv2</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u457" class="ax_default">
          <img id="u457_img" class="img " src="images/2_0用户信息展示/u457.png"/>
          <div id="u457_text" class="text ">
            <p><span style="text-decoration:none;">标签3，标签4</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u458" class="ax_default">
          <img id="u458_img" class="img " src="images/2_0用户信息展示/u458.png"/>
          <div id="u458_text" class="text ">
            <p><span style="text-decoration:none;">查看</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u459" class="ax_default">
          <img id="u459_img" class="img " src="images/2_0用户信息展示/u459.png"/>
          <div id="u459_text" class="text ">
            <p><span style="text-decoration:none;">2025.06.22 17:00:00</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u460" class="ax_default">
          <img id="u460_img" class="img " src="images/2_0用户信息展示/u460.png"/>
          <div id="u460_text" class="text ">
            <p><span style="text-decoration:none;">编辑&nbsp;&nbsp; 打标记录</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u461" class="ax_default">
          <img id="u461_img" class="img " src="images/2_0用户信息展示/u461.png"/>
          <div id="u461_text" class="text ">
            <p><span style="text-decoration:none;">2</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u462" class="ax_default">
          <img id="u462_img" class="img " src="images/2_0用户信息展示/u462.png"/>
          <div id="u462_text" class="text ">
            <p><span style="text-decoration:none;">3737</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u463" class="ax_default">
          <img id="u463_img" class="img " src="images/2_0用户信息展示/u463.png"/>
          <div id="u463_text" class="text ">
            <p><span style="text-decoration:none;">小团团2</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u464" class="ax_default">
          <img id="u464_img" class="img " src="images/2_0用户信息展示/u464.png"/>
          <div id="u464_text" class="text ">
            <p><span style="text-decoration:none;">156****5221</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u465" class="ax_default">
          <img id="u465_img" class="img " src="images/2_0用户信息展示/u465.png"/>
          <div id="u465_text" class="text ">
            <p><span style="text-decoration:none;">3星级达人--lv2...</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u466" class="ax_default">
          <img id="u466_img" class="img " src="images/2_0用户信息展示/u466.png"/>
          <div id="u466_text" class="text ">
            <p><span style="text-decoration:none;">标签3，标签4...</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u467" class="ax_default">
          <img id="u467_img" class="img " src="images/2_0用户信息展示/u467.png"/>
          <div id="u467_text" class="text ">
            <p><span style="text-decoration:none;">查看</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u468" class="ax_default">
          <img id="u468_img" class="img " src="images/2_0用户信息展示/u468.png"/>
          <div id="u468_text" class="text ">
            <p><span style="text-decoration:none;">2025.06.22 17:00:00</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u469" class="ax_default">
          <img id="u469_img" class="img " src="images/2_0用户信息展示/u469.png"/>
          <div id="u469_text" class="text ">
            <p><span style="text-decoration:none;">编辑&nbsp;&nbsp; 打标记录</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u470" class="ax_default label">
        <div id="u470_div" class=""></div>
        <div id="u470_text" class="text ">
          <p><span style="text-decoration:none;">smartid</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u471" class="ax_default text_field">
        <div id="u471_div" class=""></div>
        <input id="u471_input" type="text" value="" class="u471_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u472" class="ax_default label">
        <div id="u472_div" class=""></div>
        <div id="u472_text" class="text ">
          <p><span style="text-decoration:none;">手机号码：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u473" class="ax_default text_field">
        <div id="u473_div" class=""></div>
        <input id="u473_input" type="text" value="" class="u473_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u474" class="ax_default label">
        <div id="u474_div" class=""></div>
        <div id="u474_text" class="text ">
          <p><span style="text-decoration:none;">标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u475" class="ax_default button">
        <div id="u475_div" class=""></div>
        <div id="u475_text" class="text ">
          <p><span style="text-decoration:none;">重置</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u476" class="ax_default primary_button">
        <div id="u476_div" class=""></div>
        <div id="u476_text" class="text ">
          <p><span style="text-decoration:none;">查询</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u477" class="ax_default" data-left="702" data-top="567" data-width="599" data-height="28">

        <!-- Unnamed (矩形) -->
        <div id="u478" class="ax_default shape">
          <div id="u478_div" class=""></div>
          <div id="u478_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u479" class="ax_default shape">
          <div id="u479_div" class=""></div>
          <div id="u479_text" class="text ">
            <p><span style="text-decoration:none;">1</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u480" class="ax_default shape">
          <div id="u480_div" class=""></div>
          <div id="u480_text" class="text ">
            <p><span style="text-decoration:none;">2</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u481" class="ax_default shape">
          <div id="u481_div" class=""></div>
          <div id="u481_text" class="text ">
            <p><span style="text-decoration:none;">3</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u482" class="ax_default shape">
          <div id="u482_div" class=""></div>
          <div id="u482_text" class="text ">
            <p><span style="text-decoration:none;">4</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u483" class="ax_default shape">
          <div id="u483_div" class=""></div>
          <div id="u483_text" class="text ">
            <p><span style="text-decoration:none;">5</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u484" class="ax_default shape">
          <div id="u484_div" class=""></div>
          <div id="u484_text" class="text ">
            <p><span style="text-decoration:none;">9</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u485" class="ax_default shape">
          <div id="u485_div" class=""></div>
          <div id="u485_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u486" class="ax_default paragraph1">
          <div id="u486_div" class=""></div>
          <div id="u486_text" class="text ">
            <p><span style="text-decoration:none;">跳至</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u487" class="ax_default shape">
          <div id="u487_div" class=""></div>
          <div id="u487_text" class="text ">
            <p><span style="text-decoration:none;">5</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u488" class="ax_default paragraph1">
          <div id="u488_div" class=""></div>
          <div id="u488_text" class="text ">
            <p><span style="text-decoration:none;">页</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u489" class="ax_default shape">
          <div id="u489_div" class=""></div>
          <div id="u489_text" class="text ">
            <p><span style="text-decoration:none;">8</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u490" class="ax_default shape">
          <div id="u490_div" class=""></div>
          <div id="u490_text" class="text ">
            <p><span style="text-decoration:none;">7</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u491" class="ax_default shape">
          <div id="u491_div" class=""></div>
          <div id="u491_text" class="text ">
            <p><span style="text-decoration:none;">6</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u492" class="ax_default shape">
          <div id="u492_div" class=""></div>
          <div id="u492_text" class="text ">
            <p><span style="font-family:'Arial Normal', 'Arial';text-decoration:none;">10</span><span style="font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';text-decoration:none;">条/页</span></p>
          </div>
        </div>

        <!-- Unnamed (图像) -->
        <div id="u493" class="ax_default image">
          <img id="u493_img" class="img " src="images/2_0用户信息展示/u493.png"/>
          <div id="u493_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图像) -->
        <div id="u494" class="ax_default image">
          <img id="u494_img" class="img " src="images/2_0用户信息展示/u493.png"/>
          <div id="u494_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图像) -->
        <div id="u495" class="ax_default image">
          <img id="u495_img" class="img " src="images/2_0用户信息展示/u493.png"/>
          <div id="u495_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u496" class="ax_default primary_button">
        <div id="u496_div" class=""></div>
        <div id="u496_text" class="text ">
          <p><span style="text-decoration:none;">导出</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u497" class="ax_default primary_button">
        <div id="u497_div" class=""></div>
        <div id="u497_text" class="text ">
          <p><span style="text-decoration:none;">批量设置</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u498" class="ax_default label">
        <div id="u498_div" class=""></div>
        <div id="u498_text" class="text ">
          <p><span style="text-decoration:none;">昵称</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u499" class="ax_default text_field">
        <div id="u499_div" class=""></div>
        <input id="u499_input" type="text" value="" class="u499_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u500" class="ax_default label">
        <div id="u500_div" class=""></div>
        <div id="u500_text" class="text ">
          <p><span style="text-decoration:none;">达人类型管理</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u501" class="ax_default paragraph1">
        <div id="u501_div" class=""></div>
        <div id="u501_text" class="text ">
          <p><span style="text-decoration:none;">1、筛选条件标签为下拉选项，支持模糊搜索，筛选的是3级标签</span></p><p><span style="text-decoration:none;">2、筛选条件：smartid，手机号，昵称个条件均为文本输入，均为精确查找</span></p><p><span style="text-decoration:none;">3、列表页：</span></p><p><span style="text-decoration:none;">①达人类型/用户标签：展示不下时，鼠标悬浮到对应的内容时，将浮窗展示全部内容</span></p><p><span style="text-decoration:none;">②用户手机号码：脱敏展示，需要点击取消脱敏<br>4、按钮：查看、编辑、打标记录点击后弹窗展示</span></p><p><span style="text-decoration:none;">5、导出：可以将查询结果全部导出，即所见即所得。导出时用户手机号码中间4位脱敏展示，备注也需要导出</span></p><p><span style="text-decoration:none;"><br></span></p><p><span style="text-decoration:none;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u502" class="ax_default _二级标题">
        <div id="u502_div" class=""></div>
        <div id="u502_text" class="text ">
          <p><span style="text-decoration:none;">PC端：用户信息管理</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u503" class="ax_default droplist">
        <div id="u503_div" class=""></div>
        <select id="u503_input" class="u503_input">
        </select>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
