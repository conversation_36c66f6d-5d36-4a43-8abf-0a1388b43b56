1. 需求背景
   需求目标：构建一个用户打标管理系统，支持企微端用户打标操作和PC端标签管理维护，实现用户标签的精准管理和达人类型的分类管理。

性能需求：
用户查询响应时间 < 500ms（万级用户数据）
批量打标操作支持2000用户/次
批量导入文件限制10MB
标签统计实时计算


3. 整体技术方案设计
   3.1 架构设计
   本节先从全局角度说明整体技术方案要点，包含实现的思路与方法，可按需补充架构图或流程图或时序图或调用链路图等

数据流向设计
用户数据: UC系统为主数据源，ADP不存储用户基础信息 （问题：过度依赖uc，是否考虑adp接uc用户信息）
标签数据: ADP系统独立管理，三级标签结构
打标关系: 仅存储在ADP系统
达人类型: ADP系统管理，类型内容唯一


3.2 接口设计
模块划分
系统按照业务功能划分为四个核心模块：
查询打标模块 - 用户查询和打标操作
用户信息维护模块 - 用户标签和达人类型维护
标签管理模块 - 标签的增删改查和层级管理
达人类型管理模块 - 达人类型的维护和用户关联


3.3 存储设计


标签信息表 (t_sac_tag_info)
CREATE TABLE `t_sac_tag_info` (
`TAG_ID` varchar(36) NOT NULL COMMENT '标签ID',
`TAG_NAME` varchar(30) NOT NULL COMMENT '标签名称',
`PARENT_TAG_ID` varchar(36) DEFAULT NULL COMMENT '父标签ID',
`TAG_LEVEL` tinyint NOT NULL COMMENT '标签层级：1,2,3',
`FULL_PATH` varchar(500) NOT NULL COMMENT '完整路径',
`TAG_STATUS` tinyint NOT NULL DEFAULT 1 COMMENT '标签状态：1-上架，0-下架',
`SORT_ORDER` int DEFAULT 0 COMMENT '同级排序',
`CREATOR` varchar(50) NOT NULL COMMENT '创建人ID',
`CREATED_NAME` varchar(50) NOT NULL COMMENT '创建人',
`CREATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建日期',
`MODIFIER` varchar(50) NOT NULL COMMENT '修改人ID',
`MODIFY_NAME` varchar(50) NOT NULL COMMENT '修改人',
`LAST_UPDATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后更新日期',
PRIMARY KEY (`TAG_ID`),
UNIQUE KEY `UK_TAG_NAME_PARENT` (`TAG_NAME`,`PARENT_TAG_ID`),
KEY `IDX_PARENT_TAG` (`PARENT_TAG_ID`),
KEY `IDX_TAG_STATUS` (`TAG_STATUS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='标签信息表';
达人类型表 (t_sac_tag_info)
CREATE TABLE `t_sac_expert_type` (
`TYPE_ID` varchar(36) NOT NULL COMMENT '达人类型ID',
`TYPE_NAME` varchar(30) NOT NULL COMMENT '达人类型名称',
`TYPE_CONTENT` varchar(100) NOT NULL COMMENT '达人类型内容',
`CREATOR` varchar(50) NOT NULL COMMENT '创建人ID',
`CREATED_NAME` varchar(50) NOT NULL COMMENT '创建人',
`CREATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建日期',
`MODIFIER` varchar(50) NOT NULL COMMENT '修改人ID',
`MODIFY_NAME` varchar(50) NOT NULL COMMENT '修改人',
`LAST_UPDATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后更新日期',
PRIMARY KEY (`TYPE_ID`) USING BTREE,
UNIQUE KEY `UK_TYPE_CONTENT` (`TYPE_CONTENT`) USING BTREE,
KEY `IDX1_T_ADP_EXPERT_TYPE` (`TYPE_NAME`) USING BTREE,
KEY `IDX2_T_ADP_EXPERT_TYPE` (`TYPE_NAME`,`TYPE_CONTENT`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='达人类型表';
标签操作日志表 (t_sac_tag_operation_log)
CREATE TABLE `t_sac_tag_operation_log` (
`LOG_ID` varchar(36) NOT NULL COMMENT '日志ID',
`OPERATION_TYPE` tinyint NOT NULL COMMENT '操作类型',
`TARGET_ID` varchar(36) DEFAULT NULL COMMENT '操作目标ID（标签ID/类型ID/备注ID）',
`SMART_ID` varchar(36) DEFAULT NULL COMMENT '用户smartId',
`BATCH_ID` varchar(36) DEFAULT NULL COMMENT '批量操作ID',
`OPERATOR` varchar(50) NOT NULL COMMENT '操作人',
`OPERATION_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '操作时间',
PRIMARY KEY (`LOG_ID`),
KEY `IDX_OPERATION_TYPE` (`OPERATION_TYPE`),
KEY `IDX_TARGET_ID` (`TARGET_ID`),
KEY `IDX_SMART_ID` (`SMART_ID`),
KEY `IDX_OPERATOR` (`OPERATOR`),
KEY `IDX_OPERATION_DATE` (`OPERATION_DATE`),
KEY `IDX_BATCH_ID` (`BATCH_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='标签操作日志表';
操作类型设计 1-创建标签,2-编辑标签,3-删除标签,4-下架标签,5-上架标签,6-添加达人类型,7-编辑达人类型,8-删除达人类型,9-添加标签到用户,10-从用户移除标签,11-添加达人类别到用户,12-从用户移除达人类别,13-添加备注,14-移除备注,15-编辑备注

标签关系设计


用户标签关系表 (t_sac_user_tag_rel)
CREATE TABLE `t_sac_user_tag_rel` (
`REL_ID` varchar(36) NOT NULL COMMENT '关系ID',
`SMART_ID` varchar(36) NOT NULL COMMENT '用户smartId',
`PHONE` varchar(20) NOT NULL COMMENT '用户手机号',
`NICK_NAME` varchar(50) NOT NULL COMMENT '用户昵称',
`REL_TYPE` tinyint NOT NULL COMMENT '关系类型：1-标签 2-达人类型 3-备注',
`REF_ID` varchar(36) DEFAULT NULL COMMENT '引用ID（标签ID/类型ID）',
`REMARK_CONTENT` varchar(200) DEFAULT NULL COMMENT '备注内容（仅当REL_TYPE=3时有效）',
`CREATOR` varchar(50) NOT NULL COMMENT '创建人ID',
`CREATED_NAME` varchar(50) NOT NULL COMMENT '创建人',
`CREATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建日期',
`MODIFIER` varchar(50) NOT NULL COMMENT '修改人ID',
`MODIFY_NAME` varchar(50) NOT NULL COMMENT '修改人',
`LAST_UPDATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后更新日期',
PRIMARY KEY (`REL_ID`),
UNIQUE KEY `UK_SMART_REF_TYPE` (`SMART_ID`, `REF_ID`, `REL_TYPE`),
KEY `IDX_SMART_ID` (`SMART_ID`),
KEY `IDX_REL_TYPE` (`REL_TYPE`),
KEY `IDX_CREATED_DATE` (`CREATED_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户标签关系表（标签、达人类型、备注）';

3.4 配置设计
功能开关、系统设置等配置信息
business:
batch:
max-users: 2000              # 批量操作最大用户数
max-file-size: 10MB          # 文件上传大小限制
timeout: 300s                # 批量操作超时时间

refer:
url:
uc:
api: 'http://ec-usercenter-client-service-svc.ec-sit.svc:31009'

3.5 性能设计

标签树策略
标签树数据结构选择：使用扁平结构
{"tags":{"id":"tag001","n":"客户分类","lvl":1,"pid":null,"ord":1},{"id":"tag002","n":"价值等级","lvl":2,"pid":"tag001","ord":1},{"id":"tag003","n":"价值","lvl":3,"pid":"tag002","ord":1}}
扁平结构优势：
数据传输量小，网络开销低，适合企微端
前端处理灵活，可根据需要构建树形或列表展示
支持快速搜索、过滤、排序
缓存维护简单，只需维护一种数据结构
权限控制通过level和parentTagId字段实现，与结构无关


缓存策略（仅基础数据）
数据类型 Key格式 存储结构 TTL 示例值
标签列表 tags:all String(JSON) 2小时 [{"id":"t1","name":"VIP"}]
达人类型配置 experts:types String(JSON) 2小时 [{"id":"e1","name":"美妆达人"}]