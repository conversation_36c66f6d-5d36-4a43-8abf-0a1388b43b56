<!DOCTYPE html>
<html>
  <head>
    <title>4.1批量导入</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/4_1批量导入/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/4_1批量导入/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (表格) -->
      <div id="u980" class="ax_default">

        <!-- Unnamed (表格单元) -->
        <div id="u981" class="ax_default">
          <img id="u981_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u981_text" class="text ">
            <p><span style="text-decoration:none;">达人类型</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u982" class="ax_default">
          <img id="u982_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u982_text" class="text ">
            <p><span style="text-decoration:none;">类型内容</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u983" class="ax_default">
          <img id="u983_img" class="img " src="images/3_1批量导入标签/u832.png"/>
          <div id="u983_text" class="text ">
            <p><span style="text-decoration:none;">备注</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u984" class="ax_default">
          <img id="u984_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u984_text" class="text ">
            <p><span style="text-decoration:none;">达人类型1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u985" class="ax_default">
          <img id="u985_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u985_text" class="text ">
            <p><span style="text-decoration:none;">类型内容2</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u986" class="ax_default">
          <img id="u986_img" class="img " src="images/3_1批量导入标签/u835.png"/>
          <div id="u986_text" class="text ">
            <p><span style="text-decoration:none;">备注1</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u987" class="ax_default">

        <!-- Unnamed (表格单元) -->
        <div id="u988" class="ax_default">
          <img id="u988_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u988_text" class="text ">
            <p><span style="text-decoration:none;">一级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u989" class="ax_default">
          <img id="u989_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u989_text" class="text ">
            <p><span style="text-decoration:none;">二级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u990" class="ax_default">
          <img id="u990_img" class="img " src="images/3_1批量导入标签/u830.png"/>
          <div id="u990_text" class="text ">
            <p><span style="text-decoration:none;">三级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u991" class="ax_default">
          <img id="u991_img" class="img " src="images/3_1批量导入标签/u832.png"/>
          <div id="u991_text" class="text ">
            <p><span style="text-decoration:none;">错误内容</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u992" class="ax_default">
          <img id="u992_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u992_text" class="text ">
            <p><span style="text-decoration:none;">一级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u993" class="ax_default">
          <img id="u993_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u993_text" class="text ">
            <p><span style="text-decoration:none;">二级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u994" class="ax_default">
          <img id="u994_img" class="img " src="images/3_1批量导入标签/u833.png"/>
          <div id="u994_text" class="text ">
            <p><span style="text-decoration:none;">三级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u995" class="ax_default">
          <img id="u995_img" class="img " src="images/3_1批量导入标签/u835.png"/>
          <div id="u995_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u996" class="ax_default paragraph1">
        <div id="u996_div" class=""></div>
        <div id="u996_text" class="text ">
          <p><span style="text-decoration:none;">1、下载的文件中仅将错误的行数据全部展示出来，如下图</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u997" class="ax_default label">
        <div id="u997_div" class=""></div>
        <div id="u997_text" class="text ">
          <p><span style="text-decoration:none;">导入模板表头和示例：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u998" class="ax_default paragraph1">
        <div id="u998_div" class=""></div>
        <div id="u998_text" class="text ">
          <p><span style="text-decoration:none;">1、点击按钮“批量导入”弹窗如上图</span></p><p><span style="text-decoration:none;">2、点击“导入模板下载“则直接将模板文件进行下载</span></p><p><span style="text-decoration:none;">3、上传文件后点击“导入”，则展示导入进度（如右图）</span></p><p><span style="text-decoration:none;">4、导入原则：达人类型精确匹配才算已存在</span></p><p><span style="text-decoration:none;">①如果达人类型，已经存在的，则不更新</span></p><p><span style="text-decoration:none;">②如果达人体系，不存在的，则直接新增</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u999" class="ax_default paragraph1">
        <div id="u999_div" class=""></div>
        <div id="u999_text" class="text ">
          <p><span style="text-decoration:none;">1、导入时，如果有错误的，则错误数据不导入。并且弹窗展示如下内容</span></p><p><span style="text-decoration:none;">2、判断规则：</span></p><p><span style="text-decoration:none;">①达人类型、类型内容：必须都有值，否则算异常错误，已存在不认为是错误，直接跳过即可</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1000" class="ax_default _二级标题">
        <div id="u1000_div" class=""></div>
        <div id="u1000_text" class="text ">
          <p><span style="text-decoration:none;">PC端：达人类型管理-批量导入</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u1001" class="ax_default _图片_">
        <img id="u1001_img" class="img " src="images/2_1批量设置/u524.png"/>
        <div id="u1001_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1002" class="ax_default box_2">
        <div id="u1002_div" class=""></div>
        <div id="u1002_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1003" class="ax_default box_2">
        <div id="u1003_div" class=""></div>
        <div id="u1003_text" class="text ">
          <p><span style="text-decoration:none;">3条导入失败</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
