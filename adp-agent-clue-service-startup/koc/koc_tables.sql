-- KOC打标系统数据库表结构（优化版 - 使用自增ID）
-- 创建时间：2025-08-04
-- 优化说明：使用自增ID提升性能，减少存储空间，提高查询效率

-- 1. 标签信息表
CREATE TABLE `t_sac_tag_info` (
    `tag_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `tag_name` varchar(30) NOT NULL COMMENT '标签名称',
    `parent_tag_id` bigint unsigned DEFAULT NULL COMMENT '父标签ID',
    `tag_level` tinyint NOT NULL COMMENT '标签层级：1,2,3',
    `full_path` varchar(500) NOT NULL COMMENT '完整路径',
    `tag_status` tinyint NOT NULL DEFAULT 1 COMMENT '标签状态：1-上架，0-下架',
    `sort_order` int DEFAULT 0 COMMENT '同级排序',
    `creator` varchar(50) NOT NULL COMMENT '创建人ID',
    `created_name` varchar(50) NOT NULL COMMENT '创建人',
    `created_date` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建日期',
    `modifier` varchar(50) NOT NULL COMMENT '修改人ID',
    `modify_name` varchar(50) NOT NULL COMMENT '修改人',
    `last_updated_date` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后更新日期',
    PRIMARY KEY (`tag_id`),
    UNIQUE KEY `uk_tag_id` (`tag_id`),
    UNIQUE KEY `uk_tag_name_parent` (`tag_name`,`parent_tag_id`),
    KEY `idx_parent_id` (`parent_tag_id`),
    KEY `idx_parent_tag_id` (`parent_tag_id`),
    KEY `idx_tag_status` (`tag_status`),
    KEY `idx_tag_level` (`tag_level`),
    KEY `idx_sort_order` (`sort_order`),
    CONSTRAINT `fk_tag_parent` FOREIGN KEY (`parent_id`) REFERENCES `t_sac_tag_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='标签信息表';

-- 2. 达人类型表
CREATE TABLE `t_sac_expert_type` (
    `type_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '达人类型ID',
    `type_name` varchar(30) NOT NULL COMMENT '达人类型名称',
    `type_content` varchar(100) NOT NULL COMMENT '达人类型内容',
    `creator` varchar(50) NOT NULL COMMENT '创建人ID',
    `created_name` varchar(50) NOT NULL COMMENT '创建人',
    `created_date` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建日期',
    `modifier` varchar(50) NOT NULL COMMENT '修改人ID',
    `modify_name` varchar(50) NOT NULL COMMENT '修改人',
    `last_updated_date` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后更新日期',
    PRIMARY KEY (`type_id`),
    UNIQUE KEY `uk_type_id` (`type_id`),
    UNIQUE KEY `uk_type_content` (`type_content`),
    KEY `idx_type_name` (`type_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='达人类型表';

-- 3. 用户标签关系表（标签、达人类型、备注）
CREATE TABLE `t_sac_user_tag_rel` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `smart_id` varchar(36) NOT NULL COMMENT '用户smartId',
    `phone` varchar(20) NOT NULL COMMENT '用户手机号',
    `nick_name` varchar(50) NOT NULL COMMENT '用户昵称',
    `rel_type` tinyint NOT NULL COMMENT '关系类型：1-标签 2-达人类型 3-备注',
    `ref_id` bigint unsigned DEFAULT NULL COMMENT '引用主键ID（标签主键ID/类型主键ID）',
    `remark_content` varchar(200) DEFAULT NULL COMMENT '备注内容（仅当REL_TYPE=3时有效）',
    `creator` varchar(50) NOT NULL COMMENT '创建人ID',
    `created_name` varchar(50) NOT NULL COMMENT '创建人',
    `created_date` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建日期',
    `modifier` varchar(50) NOT NULL COMMENT '修改人ID',
    `modify_name` varchar(50) NOT NULL COMMENT '修改人',
    `last_updated_date` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后更新日期',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_rel_id` (`rel_id`),
    UNIQUE KEY `uk_smart_ref_type` (`smart_id`, `ref_id`, `rel_type`),
    KEY `idx_smart_id` (`smart_id`),
    KEY `idx_phone` (`phone`),
    KEY `idx_rel_type` (`rel_type`),
    KEY `idx_ref_id` (`ref_id`),
    KEY `idx_ref_business_id` (`ref_business_id`),
    KEY `idx_created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户标签关系表（标签、达人类型、备注）';

-- 4. 标签操作日志表
CREATE TABLE `t_sac_tag_operation_log` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `log_id` varchar(32) NOT NULL COMMENT '日志业务ID',
    `operation_type` tinyint NOT NULL COMMENT '操作类型',
    `target_id` bigint unsigned DEFAULT NULL COMMENT '操作目标主键ID（标签主键ID/类型主键ID/关系主键ID）',
    `target_business_id` varchar(32) DEFAULT NULL COMMENT '操作目标业务ID（标签业务ID/类型业务ID/关系业务ID）',
    `smart_id` varchar(36) DEFAULT NULL COMMENT '用户smartId',
    `batch_id` varchar(32) DEFAULT NULL COMMENT '批量操作ID',
    `operator` varchar(50) NOT NULL COMMENT '操作人',
    `operation_date` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '操作时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_log_id` (`log_id`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_target_id` (`target_id`),
    KEY `idx_target_business_id` (`target_business_id`),
    KEY `idx_smart_id` (`smart_id`),
    KEY `idx_operator` (`operator`),
    KEY `idx_operation_date` (`operation_date`),
    KEY `idx_batch_id` (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='标签操作日志表';

-- 操作类型枚举说明：
-- 1-创建标签,2-编辑标签,3-删除标签,4-下架标签,5-上架标签,
-- 6-添加达人类型,7-编辑达人类型,8-删除达人类型,
-- 9-添加标签到用户,10-从用户移除标签,11-添加达人类别到用户,12-从用户移除达人类别,
-- 13-添加备注,14-移除备注,15-编辑备注

-- 初始化基础数据
INSERT INTO `t_sac_tag_info` (`tag_id`, `tag_name`, `parent_tag_id`, `tag_level`, `full_path`, `tag_status`, `sort_order`, `creator`, `created_name`, `modifier`, `modify_name`) VALUES
('tag001', '客户分类', NULL, 1, '客户分类', 1, 1, 'system', '系统', 'system', '系统'),
('tag002', '价值等级', 'tag001', 2, '客户分类/价值等级', 1, 1, 'system', '系统', 'system', '系统'),
('tag003', '高价值', 'tag002', 3, '客户分类/价值等级/高价值', 1, 1, 'system', '系统', 'system', '系统'),
('tag004', '中价值', 'tag002', 3, '客户分类/价值等级/中价值', 1, 2, 'system', '系统', 'system', '系统'),
('tag005', '低价值', 'tag002', 3, '客户分类/价值等级/低价值', 1, 3, 'system', '系统', 'system', '系统');

-- 更新父子关系的主键ID（需要在插入后执行）
UPDATE `t_sac_tag_info` t1
JOIN `t_sac_tag_info` t2 ON t1.parent_tag_id = t2.tag_id
SET t1.parent_id = t2.id
WHERE t1.parent_tag_id IS NOT NULL;

INSERT INTO `t_sac_expert_type` (`type_id`, `type_name`, `type_content`, `creator`, `created_name`, `modifier`, `modify_name`) VALUES
('expert001', '美妆达人', '美妆博主、化妆师等', 'system', '系统', 'system', '系统'),
('expert002', '时尚达人', '时尚博主、穿搭达人等', 'system', '系统', 'system', '系统'),
('expert003', '汽车达人', '汽车评测、汽车博主等', 'system', '系统', 'system', '系统');
