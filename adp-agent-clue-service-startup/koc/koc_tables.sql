-- KOC打标系统数据库表结构
-- 创建时间：2025-08-04

-- 1. 标签信息表
CREATE TABLE `t_sac_tag_info` (
    `TAG_ID` varchar(36) NOT NULL COMMENT '标签ID',
    `TAG_NAME` varchar(30) NOT NULL COMMENT '标签名称',
    `PARENT_TAG_ID` varchar(36) DEFAULT NULL COMMENT '父标签ID',
    `TAG_LEVEL` tinyint NOT NULL COMMENT '标签层级：1,2,3',
    `FULL_PATH` varchar(500) NOT NULL COMMENT '完整路径',
    `TAG_STATUS` tinyint NOT NULL DEFAULT 1 COMMENT '标签状态：1-上架，0-下架',
    `SORT_ORDER` int DEFAULT 0 COMMENT '同级排序',
    `CREATOR` varchar(50) NOT NULL COMMENT '创建人ID',
    `CREATED_NAME` varchar(50) NOT NULL COMMENT '创建人',
    `CREATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建日期',
    `MODIFIER` varchar(50) NOT NULL COMMENT '修改人ID',
    `MODIFY_NAME` varchar(50) NOT NULL COMMENT '修改人',
    `LAST_UPDATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后更新日期',
    PRIMARY KEY (`TAG_ID`),
    UNIQUE KEY `UK_TAG_NAME_PARENT` (`TAG_NAME`,`PARENT_TAG_ID`),
    KEY `IDX_PARENT_TAG` (`PARENT_TAG_ID`),
    KEY `IDX_TAG_STATUS` (`TAG_STATUS`),
    KEY `IDX_TAG_LEVEL` (`TAG_LEVEL`),
    KEY `IDX_SORT_ORDER` (`SORT_ORDER`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='标签信息表';

-- 2. 达人类型表
CREATE TABLE `t_sac_expert_type` (
    `TYPE_ID` varchar(36) NOT NULL COMMENT '达人类型ID',
    `TYPE_NAME` varchar(30) NOT NULL COMMENT '达人类型名称',
    `TYPE_CONTENT` varchar(100) NOT NULL COMMENT '达人类型内容',
    `CREATOR` varchar(50) NOT NULL COMMENT '创建人ID',
    `CREATED_NAME` varchar(50) NOT NULL COMMENT '创建人',
    `CREATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建日期',
    `MODIFIER` varchar(50) NOT NULL COMMENT '修改人ID',
    `MODIFY_NAME` varchar(50) NOT NULL COMMENT '修改人',
    `LAST_UPDATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后更新日期',
    PRIMARY KEY (`TYPE_ID`) USING BTREE,
    UNIQUE KEY `UK_TYPE_CONTENT` (`TYPE_CONTENT`) USING BTREE,
    KEY `IDX1_T_ADP_EXPERT_TYPE` (`TYPE_NAME`) USING BTREE,
    KEY `IDX2_T_ADP_EXPERT_TYPE` (`TYPE_NAME`,`TYPE_CONTENT`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='达人类型表';

-- 3. 用户标签关系表（标签、达人类型、备注）
CREATE TABLE `t_sac_user_tag_rel` (
    `REL_ID` varchar(36) NOT NULL COMMENT '关系ID',
    `SMART_ID` varchar(36) NOT NULL COMMENT '用户smartId',
    `PHONE` varchar(20) NOT NULL COMMENT '用户手机号',
    `NICK_NAME` varchar(50) NOT NULL COMMENT '用户昵称',
    `REL_TYPE` tinyint NOT NULL COMMENT '关系类型：1-标签 2-达人类型 3-备注',
    `REF_ID` varchar(36) DEFAULT NULL COMMENT '引用ID（标签ID/类型ID）',
    `REMARK_CONTENT` varchar(200) DEFAULT NULL COMMENT '备注内容（仅当REL_TYPE=3时有效）',
    `CREATOR` varchar(50) NOT NULL COMMENT '创建人ID',
    `CREATED_NAME` varchar(50) NOT NULL COMMENT '创建人',
    `CREATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建日期',
    `MODIFIER` varchar(50) NOT NULL COMMENT '修改人ID',
    `MODIFY_NAME` varchar(50) NOT NULL COMMENT '修改人',
    `LAST_UPDATED_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后更新日期',
    PRIMARY KEY (`REL_ID`),
    UNIQUE KEY `UK_SMART_REF_TYPE` (`SMART_ID`, `REF_ID`, `REL_TYPE`),
    KEY `IDX_SMART_ID` (`SMART_ID`),
    KEY `IDX_PHONE` (`PHONE`),
    KEY `IDX_REL_TYPE` (`REL_TYPE`),
    KEY `IDX_CREATED_DATE` (`CREATED_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户标签关系表（标签、达人类型、备注）';

-- 4. 标签操作日志表
CREATE TABLE `t_sac_tag_operation_log` (
    `LOG_ID` varchar(36) NOT NULL COMMENT '日志ID',
    `OPERATION_TYPE` tinyint NOT NULL COMMENT '操作类型',
    `TARGET_ID` varchar(36) DEFAULT NULL COMMENT '操作目标ID（标签ID/类型ID/备注ID）',
    `SMART_ID` varchar(36) DEFAULT NULL COMMENT '用户smartId',
    `BATCH_ID` varchar(36) DEFAULT NULL COMMENT '批量操作ID',
    `OPERATOR` varchar(50) NOT NULL COMMENT '操作人',
    `OPERATION_DATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '操作时间',
    PRIMARY KEY (`LOG_ID`),
    KEY `IDX_OPERATION_TYPE` (`OPERATION_TYPE`),
    KEY `IDX_TARGET_ID` (`TARGET_ID`),
    KEY `IDX_SMART_ID` (`SMART_ID`),
    KEY `IDX_OPERATOR` (`OPERATOR`),
    KEY `IDX_OPERATION_DATE` (`OPERATION_DATE`),
    KEY `IDX_BATCH_ID` (`BATCH_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='标签操作日志表';

-- 操作类型枚举说明：
-- 1-创建标签,2-编辑标签,3-删除标签,4-下架标签,5-上架标签,
-- 6-添加达人类型,7-编辑达人类型,8-删除达人类型,
-- 9-添加标签到用户,10-从用户移除标签,11-添加达人类别到用户,12-从用户移除达人类别,
-- 13-添加备注,14-移除备注,15-编辑备注

-- 初始化基础数据
INSERT INTO `t_sac_tag_info` (`TAG_ID`, `TAG_NAME`, `PARENT_TAG_ID`, `TAG_LEVEL`, `FULL_PATH`, `TAG_STATUS`, `SORT_ORDER`, `CREATOR`, `CREATED_NAME`, `MODIFIER`, `MODIFY_NAME`) VALUES
('tag001', '客户分类', NULL, 1, '客户分类', 1, 1, 'system', '系统', 'system', '系统'),
('tag002', '价值等级', 'tag001', 2, '客户分类/价值等级', 1, 1, 'system', '系统', 'system', '系统'),
('tag003', '高价值', 'tag002', 3, '客户分类/价值等级/高价值', 1, 1, 'system', '系统', 'system', '系统'),
('tag004', '中价值', 'tag002', 3, '客户分类/价值等级/中价值', 1, 2, 'system', '系统', 'system', '系统'),
('tag005', '低价值', 'tag002', 3, '客户分类/价值等级/低价值', 1, 3, 'system', '系统', 'system', '系统');

INSERT INTO `t_sac_expert_type` (`TYPE_ID`, `TYPE_NAME`, `TYPE_CONTENT`, `CREATOR`, `CREATED_NAME`, `MODIFIER`, `MODIFY_NAME`) VALUES
('expert001', '美妆达人', '美妆博主、化妆师等', 'system', '系统', 'system', '系统'),
('expert002', '时尚达人', '时尚博主、穿搭达人等', 'system', '系统', 'system', '系统'),
('expert003', '汽车达人', '汽车评测、汽车博主等', 'system', '系统', 'system', '系统');
