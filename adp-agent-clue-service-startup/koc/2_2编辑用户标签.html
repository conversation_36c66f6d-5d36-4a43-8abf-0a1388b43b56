<!DOCTYPE html>
<html>
  <head>
    <title>2.2编辑用户标签</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/2_2编辑用户标签/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/2_2编辑用户标签/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u545" class="ax_default paragraph1">
        <div id="u545_div" class=""></div>
        <div id="u545_text" class="text ">
          <p><span style="text-decoration:none;">1、点击编辑时如上图所示。用户可以修改标签</span></p><p><span style="text-decoration:none;">2、删除标签：点击后页面数据删除，并且点击“保存”时该动作生效</span></p><p><span style="text-decoration:none;">3、点击修改如右图</span></p><p><span style="text-decoration:none;">4、取消：则取消本次操作</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u546" class="ax_default _二级标题">
        <div id="u546_div" class=""></div>
        <div id="u546_text" class="text ">
          <p><span style="text-decoration:none;">PC端：用户信息管理-编辑用户标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u547" class="ax_default _形状">
        <div id="u547_div" class=""></div>
        <div id="u547_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u548" class="ax_default _形状">
        <div id="u548_div" class=""></div>
        <div id="u548_text" class="text ">
          <p><span style="text-decoration:none;">一级标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u549" class="ax_default _形状">
        <div id="u549_div" class=""></div>
        <div id="u549_text" class="text ">
          <p><span style="text-decoration:none;">三级标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u550" class="ax_default _形状">
        <div id="u550_div" class=""></div>
        <div id="u550_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u551" class="ax_default _形状">
        <div id="u551_div" class=""></div>
        <div id="u551_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u552" class="ax_default droplist">
        <div id="u552_div" class=""></div>
        <select id="u552_input" class="u552_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u553" class="ax_default box_2">
        <div id="u553_div" class=""></div>
        <div id="u553_text" class="text ">
          <p><span style="text-decoration:none;">一级标签</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u554" class="ax_default droplist">
        <div id="u554_div" class=""></div>
        <select id="u554_input" class="u554_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u555" class="ax_default box_2">
        <div id="u555_div" class=""></div>
        <div id="u555_text" class="text ">
          <p><span style="text-decoration:none;">二级标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u556" class="ax_default _形状">
        <div id="u556_div" class=""></div>
        <div id="u556_text" class="text ">
          <p><span style="text-decoration:none;">二级标签</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u557" class="ax_default _形状">
        <div id="u557_div" class=""></div>
        <div id="u557_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u558" class="ax_default droplist">
        <div id="u558_div" class=""></div>
        <select id="u558_input" class="u558_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u559" class="ax_default box_2">
        <div id="u559_div" class=""></div>
        <div id="u559_text" class="text ">
          <p><span style="text-decoration:none;">三级标签</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表框) -->
      <div id="u560" class="ax_default droplist">
        <div id="u560_div" class=""></div>
        <select id="u560_input" class="u560_input">
          <option class="u560_input_option" value="已添加/未添加">已添加/未添加</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u561" class="ax_default box_2">
        <div id="u561_div" class=""></div>
        <div id="u561_text" class="text ">
          <p><span style="text-decoration:none;">状态</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u562" class="ax_default _形状">
        <div id="u562_div" class=""></div>
        <div id="u562_text" class="text ">
          <p><span style="text-decoration:none;">筛选</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u563" class="ax_default _形状">
        <div id="u563_div" class=""></div>
        <div id="u563_text" class="text ">
          <p><span style="text-decoration:none;">重置</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u564" class="ax_default _形状">
        <div id="u564_div" class=""></div>
        <div id="u564_text" class="text ">
          <p><span style="text-decoration:none;">状态</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u565" class="ax_default _形状">
        <div id="u565_div" class=""></div>
        <div id="u565_text" class="text ">
          <p><span style="text-decoration:none;">未添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u566" class="ax_default _形状">
        <div id="u566_div" class=""></div>
        <div id="u566_text" class="text ">
          <p><span style="text-decoration:none;">未添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u567" class="ax_default _形状">
        <div id="u567_div" class=""></div>
        <div id="u567_text" class="text ">
          <p><span style="text-decoration:none;">未添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u568" class="ax_default _形状">
        <div id="u568_div" class=""></div>
        <div id="u568_text" class="text ">
          <p><span style="text-decoration:none;">已添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u569" class="ax_default _形状">
        <div id="u569_div" class=""></div>
        <div id="u569_text" class="text ">
          <p><span style="text-decoration:none;">已添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u570" class="ax_default _形状">
        <div id="u570_div" class=""></div>
        <div id="u570_text" class="text ">
          <p><span style="text-decoration:none;">操作</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u571" class="ax_default _形状">
        <div id="u571_div" class=""></div>
        <div id="u571_text" class="text ">
          <p><span style="text-decoration:none;">添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u572" class="ax_default _形状">
        <div id="u572_div" class=""></div>
        <div id="u572_text" class="text ">
          <p><span style="text-decoration:none;">添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u573" class="ax_default _形状">
        <div id="u573_div" class=""></div>
        <div id="u573_text" class="text ">
          <p><span style="text-decoration:none;">添加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u574" class="ax_default _形状">
        <div id="u574_div" class=""></div>
        <div id="u574_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u575" class="ax_default _形状">
        <div id="u575_div" class=""></div>
        <div id="u575_text" class="text ">
          <p><span style="text-decoration:none;">删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u576" class="ax_default _形状">
        <div id="u576_div" class=""></div>
        <div id="u576_text" class="text ">
          <p><span style="text-decoration:none;">&lt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u577" class="ax_default _形状">
        <div id="u577_div" class=""></div>
        <div id="u577_text" class="text ">
          <p><span style="text-decoration:none;">1</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u578" class="ax_default _形状">
        <div id="u578_div" class=""></div>
        <div id="u578_text" class="text ">
          <p><span style="text-decoration:none;">2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u579" class="ax_default _形状">
        <div id="u579_div" class=""></div>
        <div id="u579_text" class="text ">
          <p><span style="text-decoration:none;">20</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u580" class="ax_default _形状">
        <div id="u580_div" class=""></div>
        <div id="u580_text" class="text ">
          <p><span style="text-decoration:none;">&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u581" class="ax_default _形状">
        <div id="u581_div" class=""></div>
        <div id="u581_text" class="text ">
          <p><span style="text-decoration:none;">10条/页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u582" class="ax_default _文本段落">
        <div id="u582_div" class=""></div>
        <div id="u582_text" class="text ">
          <p><span style="text-decoration:none;">...</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u583" class="ax_default _形状">
        <div id="u583_div" class=""></div>
        <div id="u583_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u584" class="ax_default _形状">
        <div id="u584_div" class=""></div>
        <div id="u584_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u585" class="ax_default _形状">
        <div id="u585_div" class=""></div>
        <div id="u585_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u586" class="ax_default _形状">
        <div id="u586_div" class=""></div>
        <div id="u586_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u587" class="ax_default _形状">
        <div id="u587_div" class=""></div>
        <div id="u587_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u588" class="ax_default _形状">
        <div id="u588_div" class=""></div>
        <div id="u588_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u589" class="ax_default _形状">
        <div id="u589_div" class=""></div>
        <div id="u589_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u590" class="ax_default _形状">
        <div id="u590_div" class=""></div>
        <div id="u590_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u591" class="ax_default _形状">
        <div id="u591_div" class=""></div>
        <div id="u591_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u592" class="ax_default _形状">
        <div id="u592_div" class=""></div>
        <div id="u592_text" class="text ">
          <p><span style="text-decoration:none;">老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u593" class="ax_default _形状">
        <div id="u593_div" class=""></div>
        <div id="u593_text" class="text ">
          <p><span style="text-decoration:none;">中学语文老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u594" class="ax_default _形状">
        <div id="u594_div" class=""></div>
        <div id="u594_text" class="text ">
          <p><span style="text-decoration:none;">中学老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u595" class="ax_default box_2">
        <div id="u595_div" class=""></div>
        <div id="u595_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u596" class="ax_default shape">
        <div id="u596_div" class=""></div>
        <div id="u596_text" class="text ">
          <p><span style="text-decoration:none;">编辑</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u597" class="ax_default line1">
        <img id="u597_img" class="img " src="images/2_2编辑用户标签/u597.svg"/>
        <div id="u597_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u598" class="ax_default label">
        <div id="u598_div" class=""></div>
        <div id="u598_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u599" class="ax_default box_2">
        <div id="u599_div" class=""></div>
        <div id="u599_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u600" class="ax_default label">
        <div id="u600_div" class=""></div>
        <div id="u600_text" class="text ">
          <p><span style="text-decoration:none;">已有标签：年轻</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u601" class="ax_default label">
        <div id="u601_div" class=""></div>
        <div id="u601_text" class="text ">
          <p><span style="text-decoration:none;">活力</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u602" class="ax_default label">
        <div id="u602_div" class=""></div>
        <div id="u602_text" class="text ">
          <p><span style="text-decoration:none;">3星期内有意愿购买</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u603" class="ax_default shape">
        <div id="u603_div" class=""></div>
        <div id="u603_text" class="text ">
          <p><span style="text-decoration:none;">取消</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u604" class="ax_default box_2">
        <div id="u604_div" class=""></div>
        <div id="u604_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u605" class="ax_default label">
        <div id="u605_div" class=""></div>
        <div id="u605_text" class="text ">
          <p><span style="text-decoration:none;">smartid：23444234356</span></p><p><span style="text-decoration:none;"><br></span></p><p><span style="text-decoration:none;">用户昵称：小团团1</span></p><p><span style="text-decoration:none;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u606" class="ax_default box_2">
        <div id="u606_div" class=""></div>
        <div id="u606_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u607" class="ax_default label">
        <div id="u607_div" class=""></div>
        <div id="u607_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：3星级达人--lv2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u608" class="ax_default primary_button">
        <div id="u608_div" class=""></div>
        <div id="u608_text" class="text ">
          <p><span style="text-decoration:none;">保存</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u609" class="ax_default label">
        <div id="u609_div" class=""></div>
        <div id="u609_text" class="text ">
          <p><span style="text-decoration:none;">修改</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u610" class="ax_default label">
        <div id="u610_div" class=""></div>
        <div id="u610_text" class="text ">
          <p><span style="text-decoration:none;">修改</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
