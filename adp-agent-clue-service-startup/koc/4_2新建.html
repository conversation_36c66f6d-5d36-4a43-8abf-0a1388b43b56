<!DOCTYPE html>
<html>
  <head>
    <title>4.2新建</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/4_2新建/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/4_2新建/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1004" class="ax_default box_2">
        <div id="u1004_div" class=""></div>
        <div id="u1004_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1005" class="ax_default shape">
        <div id="u1005_div" class=""></div>
        <div id="u1005_text" class="text ">
          <p><span style="text-decoration:none;">新建达人类型</span></p>
        </div>
      </div>

      <!-- Unnamed (直线 / 行距) -->
      <div id="u1006" class="ax_default line1">
        <img id="u1006_img" class="img " src="images/3_2新建标签/u855.svg"/>
        <div id="u1006_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1007" class="ax_default label">
        <div id="u1007_div" class=""></div>
        <div id="u1007_text" class="text ">
          <p><span style="text-decoration:none;">×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1008" class="ax_default shape">
        <div id="u1008_div" class=""></div>
        <div id="u1008_text" class="text ">
          <p><span style="text-decoration:none;">保存</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1009" class="ax_default shape">
        <div id="u1009_div" class=""></div>
        <div id="u1009_text" class="text ">
          <p><span style="text-decoration:none;">关闭</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1010" class="ax_default label">
        <div id="u1010_div" class=""></div>
        <div id="u1010_text" class="text ">
          <p><span style="text-decoration:none;">达人类型：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u1011" class="ax_default text_field">
        <div id="u1011_div" class=""></div>
        <input id="u1011_input" type="text" value="" class="u1011_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1012" class="ax_default label">
        <div id="u1012_div" class=""></div>
        <div id="u1012_text" class="text ">
          <p><span style="text-decoration:none;">类型内容：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1013" class="ax_default label">
        <div id="u1013_div" class=""></div>
        <div id="u1013_text" class="text ">
          <p><span style="text-decoration:none;">备注：</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u1014" class="ax_default text_field disabled">
        <div id="u1014_div" class="disabled"></div>
        <input id="u1014_input" type="text" value="" class="u1014_input" disabled/>
      </div>

      <!-- Unnamed (文本框(单行)) -->
      <div id="u1015" class="ax_default text_field disabled">
        <div id="u1015_div" class="disabled"></div>
        <input id="u1015_input" type="text" value="" class="u1015_input" disabled/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1016" class="ax_default box_1">
        <div id="u1016_div" class=""></div>
        <div id="u1016_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1017" class="ax_default label">
        <div id="u1017_div" class=""></div>
        <div id="u1017_text" class="text ">
          <p><span style="text-decoration:none;">达人类型1</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1018" class="ax_default label">
        <div id="u1018_div" class=""></div>
        <div id="u1018_text" class="text ">
          <p><span style="text-decoration:none;">达人类型2</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1019" class="ax_default label">
        <div id="u1019_div" class=""></div>
        <div id="u1019_text" class="text ">
          <p><span style="text-decoration:none;">达人类型3</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1020" class="ax_default paragraph1">
        <div id="u1020_div" class=""></div>
        <div id="u1020_text" class="text ">
          <p><span style="text-decoration:none;">1、点击新建达人类型如上图所示，默认类型内容和备注不可用</span></p><p><span style="text-decoration:none;">2、点击达人类型则默认下拉框带出已有达人类型，支持模糊查找</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1021" class="ax_default _二级标题">
        <div id="u1021_div" class=""></div>
        <div id="u1021_text" class="text ">
          <p><span style="text-decoration:none;">PC端：达人类型管理-新建</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1022" class="ax_default _文本段落">
        <div id="u1022_div" class=""></div>
        <div id="u1022_text" class="text ">
          <p><span style="text-decoration:none;">保存需要判断重复，达人类型精确匹配才算已存在</span></p><p><span style="text-decoration:none;">①如果达人体系，已经存在的，则不更新</span></p><p><span style="text-decoration:none;">②如果达人体系，不存在的，则直接新增</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
