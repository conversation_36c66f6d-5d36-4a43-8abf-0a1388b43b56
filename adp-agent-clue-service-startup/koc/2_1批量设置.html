<!DOCTYPE html>
<html>
  <head>
    <title>2.1批量设置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/2_1批量设置/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/2_1批量设置/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (表格) -->
      <div id="u504" class="ax_default">

        <!-- Unnamed (表格单元) -->
        <div id="u505" class="ax_default">
          <img id="u505_img" class="img " src="images/2_1批量设置/u505.png"/>
          <div id="u505_text" class="text ">
            <p><span style="text-decoration:none;">smartid</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u506" class="ax_default">
          <img id="u506_img" class="img " src="images/2_1批量设置/u505.png"/>
          <div id="u506_text" class="text ">
            <p><span style="text-decoration:none;">一级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u507" class="ax_default">
          <img id="u507_img" class="img " src="images/2_1批量设置/u505.png"/>
          <div id="u507_text" class="text ">
            <p><span style="text-decoration:none;">二级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u508" class="ax_default">
          <img id="u508_img" class="img " src="images/2_1批量设置/u505.png"/>
          <div id="u508_text" class="text ">
            <p><span style="text-decoration:none;">三级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u509" class="ax_default">
          <img id="u509_img" class="img " src="images/2_1批量设置/u505.png"/>
          <div id="u509_text" class="text ">
            <p><span style="text-decoration:none;">达人类型</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u510" class="ax_default">
          <img id="u510_img" class="img " src="images/2_1批量设置/u505.png"/>
          <div id="u510_text" class="text ">
            <p><span style="text-decoration:none;">类型内容</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u511" class="ax_default">
          <img id="u511_img" class="img " src="images/2_1批量设置/u511.png"/>
          <div id="u511_text" class="text ">
            <p><span style="text-decoration:none;">备注</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u512" class="ax_default">
          <img id="u512_img" class="img " src="images/2_1批量设置/u512.png"/>
          <div id="u512_text" class="text ">
            <p><span style="text-decoration:none;">25563</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u513" class="ax_default">
          <img id="u513_img" class="img " src="images/2_1批量设置/u512.png"/>
          <div id="u513_text" class="text ">
            <p><span style="text-decoration:none;">一级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u514" class="ax_default">
          <img id="u514_img" class="img " src="images/2_1批量设置/u512.png"/>
          <div id="u514_text" class="text ">
            <p><span style="text-decoration:none;">二级标签2</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u515" class="ax_default">
          <img id="u515_img" class="img " src="images/2_1批量设置/u512.png"/>
          <div id="u515_text" class="text ">
            <p><span style="text-decoration:none;">三级标签3</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u516" class="ax_default">
          <img id="u516_img" class="img " src="images/2_1批量设置/u512.png"/>
          <div id="u516_text" class="text ">
            <p><span style="text-decoration:none;">达人类型1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u517" class="ax_default">
          <img id="u517_img" class="img " src="images/2_1批量设置/u512.png"/>
          <div id="u517_text" class="text ">
            <p><span style="text-decoration:none;">类型内容1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u518" class="ax_default">
          <img id="u518_img" class="img " src="images/2_1批量设置/u518.png"/>
          <div id="u518_text" class="text ">
            <p><span style="text-decoration:none;">备注1</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u519" class="ax_default paragraph1">
        <div id="u519_div" class=""></div>
        <div id="u519_text" class="text ">
          <p><span style="text-decoration:none;">1、导入时，如果有错误的，则正确的正常导入，错误数据均不导入。并且弹窗展示如下内容</span></p><p><span style="text-decoration:none;">2、判断规则：</span></p><p><span style="text-decoration:none;">①smartid必须有值</span></p><p><span style="text-decoration:none;">②一级标签、二级标签、三级标签：要么都有值，要么都无值，否则算异常错误</span></p><p><span style="text-decoration:none;">③达人类型、类型内容：要么都有值，要么都无值，否则算异常错误</span></p><p><span style="text-decoration:none;">④标签系统和达人体系的值必须和“标签管理”和“达人类型管理“中一致，否则也算错误</span></p><p><span style="text-decoration:none;">已存在不认为是错误，直接跳过即可</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u520" class="ax_default paragraph1">
        <div id="u520_div" class=""></div>
        <div id="u520_text" class="text ">
          <p><span style="text-decoration:none;">1、smartid，一级标签、二级标签、三级标签必须有值，且与标签管理中的匹配</span></p><p><span style="text-decoration:none;">2、达人类型、类型内容，必须有值，，且与达人类型管理中的匹配</span></p><p><span style="text-decoration:none;">3、2个体系校验互不影响</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u521" class="ax_default label">
        <div id="u521_div" class=""></div>
        <div id="u521_text" class="text ">
          <p><span style="text-decoration:none;">导入模板的表头和示例：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u522" class="ax_default paragraph1">
        <div id="u522_div" class=""></div>
        <div id="u522_text" class="text ">
          <p><span style="text-decoration:none;">导入原则：以smartid为基准,做唯一性校验</span></p><p><span style="text-decoration:none;">①如果标签体系或者达人体系，该用户已经存在的，则不更新</span></p><p><span style="text-decoration:none;">②如果标签体系或者达人体系，该用户不存在的，则直接新增</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u523" class="ax_default _二级标题">
        <div id="u523_div" class=""></div>
        <div id="u523_text" class="text ">
          <p><span style="text-decoration:none;">PC端：用户信息管理-批量设置</span></p>
        </div>
      </div>

      <!-- Unnamed (图像) -->
      <div id="u524" class="ax_default _图片_">
        <img id="u524_img" class="img " src="images/2_1批量设置/u524.png"/>
        <div id="u524_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u525" class="ax_default">

        <!-- Unnamed (表格单元) -->
        <div id="u526" class="ax_default">
          <img id="u526_img" class="img " src="images/2_1批量设置/u526.png"/>
          <div id="u526_text" class="text ">
            <p><span style="text-decoration:none;">smartid</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u527" class="ax_default">
          <img id="u527_img" class="img " src="images/2_1批量设置/u526.png"/>
          <div id="u527_text" class="text ">
            <p><span style="text-decoration:none;">一级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u528" class="ax_default">
          <img id="u528_img" class="img " src="images/2_1批量设置/u526.png"/>
          <div id="u528_text" class="text ">
            <p><span style="text-decoration:none;">二级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u529" class="ax_default">
          <img id="u529_img" class="img " src="images/2_1批量设置/u526.png"/>
          <div id="u529_text" class="text ">
            <p><span style="text-decoration:none;">三级标签</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u530" class="ax_default">
          <img id="u530_img" class="img " src="images/2_1批量设置/u526.png"/>
          <div id="u530_text" class="text ">
            <p><span style="text-decoration:none;">达人类型</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u531" class="ax_default">
          <img id="u531_img" class="img " src="images/2_1批量设置/u526.png"/>
          <div id="u531_text" class="text ">
            <p><span style="text-decoration:none;">类型内容</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u532" class="ax_default">
          <img id="u532_img" class="img " src="images/2_1批量设置/u526.png"/>
          <div id="u532_text" class="text ">
            <p><span style="text-decoration:none;">备注</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u533" class="ax_default">
          <img id="u533_img" class="img " src="images/2_1批量设置/u533.png"/>
          <div id="u533_text" class="text ">
            <p><span style="text-decoration:none;">错误信息</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u534" class="ax_default">
          <img id="u534_img" class="img " src="images/2_1批量设置/u534.png"/>
          <div id="u534_text" class="text ">
            <p><span style="text-decoration:none;">25563</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u535" class="ax_default">
          <img id="u535_img" class="img " src="images/2_1批量设置/u534.png"/>
          <div id="u535_text" class="text ">
            <p><span style="text-decoration:none;">一级标签1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u536" class="ax_default">
          <img id="u536_img" class="img " src="images/2_1批量设置/u534.png"/>
          <div id="u536_text" class="text ">
            <p><span style="text-decoration:none;">二级标签2</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u537" class="ax_default">
          <img id="u537_img" class="img " src="images/2_1批量设置/u534.png"/>
          <div id="u537_text" class="text ">
            <p><span style="text-decoration:none;">三级标签3</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u538" class="ax_default">
          <img id="u538_img" class="img " src="images/2_1批量设置/u534.png"/>
          <div id="u538_text" class="text ">
            <p><span style="text-decoration:none;">达人类型1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u539" class="ax_default">
          <img id="u539_img" class="img " src="images/2_1批量设置/u534.png"/>
          <div id="u539_text" class="text ">
            <p><span style="text-decoration:none;">类型内容1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u540" class="ax_default">
          <img id="u540_img" class="img " src="images/2_1批量设置/u534.png"/>
          <div id="u540_text" class="text ">
            <p><span style="text-decoration:none;">备注1</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u541" class="ax_default">
          <img id="u541_img" class="img " src="images/2_1批量设置/u541.png"/>
          <div id="u541_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u542" class="ax_default paragraph1">
        <div id="u542_div" class=""></div>
        <div id="u542_text" class="text ">
          <p><span style="text-decoration:none;">1、下载的文件中仅将错误的行数据全部展示出来，如下图</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u543" class="ax_default box_2">
        <div id="u543_div" class=""></div>
        <div id="u543_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u544" class="ax_default box_2">
        <div id="u544_div" class=""></div>
        <div id="u544_text" class="text ">
          <p><span style="text-decoration:none;">3条导入失败</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
