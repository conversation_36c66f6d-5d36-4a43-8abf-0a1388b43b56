package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/16
 */
@Getter
@AllArgsConstructor
public enum AuditStatusEnum {

    /**
     * 待审核
     */
    WAIT_FOR_AUDIT("0"),

    /**
     * 审核通过
     */
    AUDIT_PASS("1"),

    /**
     * 审核拒绝
     */
    AUDIT_REJECT("2"),

    ;

    private final String code;
}
