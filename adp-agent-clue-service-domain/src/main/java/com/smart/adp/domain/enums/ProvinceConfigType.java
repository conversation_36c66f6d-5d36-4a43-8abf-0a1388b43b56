package com.smart.adp.domain.enums;

public enum ProvinceConfigType {

        ORIGINAL_TO_SPECIAL("1", "城市编码映射特殊省份编码"),
        SPECIAL_TO_ORIGINAL("2", "特殊省份编码映射原省份编码");

        String type;
        String desc;

        ProvinceConfigType(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }