package com.smart.adp.domain.strategy.modifyClue;

import com.smart.adp.domain.bo.clue.ClueDlrModifyBO;
import com.smart.adp.domain.service.clue.IClueDlrService;
import com.smart.adp.domain.service.clue.ISacReviewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 线索特别关注策略
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Component
@Slf4j
public class ClueSpecialModifyStrategy implements ClueModifyStrategy {

    @Autowired
    private ISacReviewService sacReviewService;
    @Autowired
    private IClueDlrService clueDlrService;

    @Override
    public Boolean modify(ClueDlrModifyBO param) {
        log.info("更新线索特别关注: {}", param.getValue());
        Boolean modifyResultReview, modifyResultClue;
        // 特别关注需要更新线索表的column11和回访表的column12字段
        modifyResultReview = sacReviewService.modifyReview(param.buildSpecialReview());
        modifyResultClue = clueDlrService.modifySpecial(param.buildSpecialClue());
        // 这里要更新缓存
        return modifyResultReview && modifyResultClue;
    }
}