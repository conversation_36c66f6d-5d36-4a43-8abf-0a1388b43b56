package com.smart.adp.domain.valueObject.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.paginate.Page;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 线索的试驾录音的摘要内容 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Table(value = "t_sac_test_record_abstract", schema = "mp")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SacTestRecordAbstractVO extends Page {

    @Id(keyType = KeyType.Auto)
    private Long id;

    @Column(value = "CUST_ID")
    private String custId;

    @Column(value = "TEST_DRIVE_SHEET_ID")
    private String testDriveSheetId;

    @Column(value = "EMP_ID")
    private String empId;

    @Column(value = "EMP_NAME")
    private String empName;

    @Column(value = "ABSTRACT_CONTENT")
    private String abstractContent;

    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;
}
