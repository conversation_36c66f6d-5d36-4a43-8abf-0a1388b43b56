package com.smart.adp.domain.valueObject.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/9 13:26
 * @description 城市信息对象
 **/
@Table(value = "t_usc_mdm_org_city", schema = "mp")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class CityInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column("CITY_ID")
    private String cityId;

    @Column("PROVINCE_ID")
    private String provinceId;

    @Column("CITY_CODE")
    private String cityCode;

    @Column("CITY_NAME")
    private String cityName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;
}
