package com.smart.adp.domain.service.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import com.smart.adp.domain.bo.clue.SacReviewHisBO;

import java.util.List;

/**
 * @Description: 回访domain服务接口
 * @Author: rik.ren
 * @Date: 2025/3/5 20:32
 **/
public interface ISacReviewService {
    /**
     * 获取潜客详情对象
     *
     * @param param
     * @param needColumn
     * @return
     */
    SacReviewBO getReviewInfo(SacReviewBO param, QueryColumn... needColumn);

    /**
     * 获取潜客详情对象
     *
     * @param param
     * @param needColumn
     * @return
     */
    SacReviewHisBO getReviewHisInfo(SacReviewHisBO param, QueryColumn... needColumn);

    /**
     * 批量获取潜客详情集合
     *
     * @param param
     * @param needColumn
     * @return
     */
    List<SacReviewBO> queryReviewInfo(SacReviewBO param, QueryColumn... needColumn);


    /**
     * 更新回访表的用户意向车型
     *
     * @param param
     * @return
     */
    Boolean modifyReviewIntendedVehicleModel(SacReviewBO param);

    /**
     * 更新回访表
     *
     * @param param
     * @return
     */
    Boolean modifyReview(SacReviewBO param);
}
