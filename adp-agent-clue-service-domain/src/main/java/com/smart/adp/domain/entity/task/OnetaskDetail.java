package com.smart.adp.domain.entity.task;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@Table(value = "t_sac_onetask_detail", schema = "csc")
public class OnetaskDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务详情ID
     */
    @Id("DETAIL_ID")
    private String detailId;

    /**
     * 任务ID
     */
    @Column("TASK_ID")
    private String taskId;

    /**
     * 完成人员ID
     */
    @Column("TASK_PERSON_ID")
    private String taskPersonId;

    /**
     * 完成人员编码
     */
    @Column("TASK_PERSON_CODE")
    private String taskPersonCode;

    /**
     * 完成人员名称
     */
    @Column("TASK_PERSON_NAME")
    private String taskPersonName;

    /**
     * 完成人员专营店编码
     */
    @Column("TASK_PERSON_DLR_CODE")
    private String taskPersonDlrCode;

    /**
     * 完成人员专营店名称
     */
    @Column("TASK_PERSON_DLR_NAME")
    private String taskPersonDlrName;

    /**
     * 大区编码
     */
    @Column("AREA_CODE")
    private String areaCode;

    /**
     * 大区名称
     */
    @Column("AREA_NAME")
    private String areaName;

    /**
     * 客户ID
     */
    @Column("CUST_ID")
    private String custId;

    /**
     * 客户名称
     */
    @Column("CUST_NAME")
    private String custName;

    /**
     * 线索单号
     */
    @Column("SERVER_ORDER")
    private String serverOrder;

    /**
     * 回访ID
     */
    @Column("REVIEW_ID")
    private String reviewId;

    /**
     * 状态编码
     */
    @Column("STATE_CODE")
    private String stateCode;

    /**
     * 状态名称
     */
    @Column("STATE_NAME")
    private String stateName;

    /**
     * 备注
     */
    @Column("REMARK")
    private String remark;

    /**
     * 完成时间
     */
    @Column("BUSS_TIME")
    private LocalDateTime bussTime;

    /**
     * 任务表有效开始时间
     */
    @Column("BUSS_START_TIME")
    private LocalDateTime bussStartTime;

    /**
     * 任务表有效结束时间
     */
    @Column("BUSS_END_TIME")
    private LocalDateTime bussEndTime;

    /**
     * 附件路径
     */
    @Column("FILE_PATH")
    private String filePath;

    /**
     * 发送类型
     */
    @Column("SEND_TYPE")
    private String sendType;

    /**
     * 扩展信息
     */
    @Column("EXTEND_JSON")
    private String extendJson;

    /**
     * 扩展字段1
     */
    @Column("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column("COLUMN5")
    private String column5;

    /**
     * 时间戳
     */
    @Column("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @Column("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @Column("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @Column("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;
}