package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 试驾类型
 * @Author: rik.ren
 * @Date: 2025/3/16 14:55
 **/
@Getter
@AllArgsConstructor
public enum TestDriveTypeEnum {
    /**
     * 用户事件
     */
    TEST_RIDE(0, "试乘"),
    /**
     * 虚拟外呼录音
     */
    TEST_DRIVE(1, "试驾"),
    /**
     * 跟进记录
     */
    DEEP_TEST(2, "深度试驾"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static TestDriveTypeEnum getByCode(Integer code) {
        return Arrays.stream(TestDriveTypeEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
