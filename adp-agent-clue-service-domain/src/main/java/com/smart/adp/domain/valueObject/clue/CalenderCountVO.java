package com.smart.adp.domain.valueObject.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "日历计数 VO")
public class CalenderCountVO {

    /**
     * 日期
     */
    @Schema(description = "日期")
    private LocalDateTime date;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer count;
}
