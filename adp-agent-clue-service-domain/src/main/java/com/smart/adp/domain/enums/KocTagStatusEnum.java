package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: KOC标签状态枚举
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Getter
@AllArgsConstructor
public enum KocTagStatusEnum {

    /**
     * 下架
     */
    DISABLED(0, "下架"),

    /**
     * 上架
     */
    ENABLED(1, "上架");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static KocTagStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (KocTagStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     */
    public static String getDescByCode(Integer code) {
        KocTagStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "";
    }

    /**
     * 判断是否为有效的状态
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为启用状态
     */
    public static boolean isEnabled(Integer code) {
        return ENABLED.getCode().equals(code);
    }

    /**
     * 判断是否为禁用状态
     */
    public static boolean isDisabled(Integer code) {
        return DISABLED.getCode().equals(code);
    }
}
