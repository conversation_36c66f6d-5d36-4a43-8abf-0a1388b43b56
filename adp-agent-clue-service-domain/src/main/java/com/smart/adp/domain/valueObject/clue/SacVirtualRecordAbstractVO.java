package com.smart.adp.domain.valueObject.clue;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 线索的虚拟外呼的摘要内容 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Table(value = "t_sac_virtual_record_abstract", schema = "mp")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SacVirtualRecordAbstractVO {

    @Id(keyType = KeyType.Auto)
    private Long id;

    @Column(value = "CUST_ID")
    private String custId;

    @Column(value = "RECORD_ID")
    private String recordId;

    @Column(value = "EMP_ID")
    private String empId;

    @Column(value = "EMP_NAME")
    private String empName;

    @Column(value = "ABSTRACT_CONTENT")
    private String abstractContent;

    @Column(value = "CREATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createdDate;
}
