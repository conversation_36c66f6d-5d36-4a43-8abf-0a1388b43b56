package com.smart.adp.domain.strategy.modifyClue;

import com.smart.adp.domain.bo.clue.ClueDlrModifyBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description: 更新线索来源
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Component
@Slf4j
public class SourceOfCluesModifyStrategy implements ClueModifyStrategy {
    @Override
    public Boolean modify(ClueDlrModifyBO param) {
        log.info("更新线索来源: {}", param.getValue());
        return true;
    }
}