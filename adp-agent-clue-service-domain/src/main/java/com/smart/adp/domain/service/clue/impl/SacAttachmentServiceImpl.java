package com.smart.adp.domain.service.clue.impl;

import com.smart.adp.domain.bo.clue.SacAttachmentBO;
import com.smart.adp.domain.gateway.clue.SacAttachmentGateway;
import com.smart.adp.domain.service.clue.ISacAttachmentService;
import com.smart.adp.domain.valueObject.clue.SacAttachmentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 线索附件service实现
 * @Author: rik.ren
 * @Date: 2025/3/21 17:44
 **/
@Service
public class SacAttachmentServiceImpl implements ISacAttachmentService {

    @Autowired
    private SacAttachmentGateway attachmentGateway;

    /**
     * 根据对象条件查询附件
     *
     * @param param
     * @return
     */
    @Override
    public List<SacAttachmentVO> queryAttachment(SacAttachmentBO param) {
        List<SacAttachmentVO> voResult = attachmentGateway.queryAttachment(param, null);
        return voResult;
    }
}
