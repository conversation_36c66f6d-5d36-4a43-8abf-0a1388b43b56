package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import com.smart.adp.domain.bo.clue.SacReviewHisBO;
import com.smart.adp.domain.entity.clue.SacReview;
import com.smart.adp.domain.entity.clue.SacReviewHis;

import java.util.List;

/**
 * @Description: 回访gateway接口
 * @Author: rik.ren
 * @Date: 2025/3/5 20:43
 **/
public interface ReviewGateway {
    /**
     * 根据id查询
     *
     * @param id
     * @param columns
     * @return
     */
    SacReview findById(String id, QueryColumn... columns);

    /**
     * 获取回访任务集合
     *
     * @param reviewIds
     * @param columns
     * @return
     */
    List<SacReview> findByIds(List<String> reviewIds, QueryColumn... columns);

    /**
     * 根据实体条件查询集合
     *
     * @param param
     * @param needColumns
     * @return
     */
    List<SacReview> findListByCondition(SacReview param, QueryColumn... needColumns);

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns
     * @return
     */
    SacReviewBO findByCondition(SacReview param, QueryColumn... needColumns);

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns
     * @return
     */
    SacReviewHisBO findByCondition(SacReviewHis param, QueryColumn... needColumns);

    /**
     * 手机号查询你回访记录表
     *
     * @param phone
     * @return
     */
    SacReview findOneByPhone(String phone);

    /**
     * 更新review，param中有值得属性字段都会更新，不想更新的就设为空
     *
     * @param param
     * @return
     */
    Boolean modifyReview(SacReview param);

    Boolean modifyReviewInfo(SacReview param);

    Boolean saveReview(SacReview param);

    Boolean updateReviewAssignBatch(List<SacReview> sacReviews);
}
