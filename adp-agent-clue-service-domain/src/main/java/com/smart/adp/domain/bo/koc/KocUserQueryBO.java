package com.smart.adp.domain.bo.koc;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: KOC用户查询BO
 * @Author: system
 * @Date: 2025/8/4
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KocUserQueryBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询关键字（手机号/昵称/smartId）
     */
    private String keyword;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * smartId
     */
    private String smartId;

    /**
     * 标签ID列表（用于筛选）
     */
    private List<String> tagIds;

    /**
     * 达人类型ID列表（用于筛选）
     */
    private List<String> expertTypeIds;

    /**
     * 是否有备注
     */
    private Boolean hasRemark;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;
}
