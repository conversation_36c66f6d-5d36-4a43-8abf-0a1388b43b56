package com.smart.adp.domain.service.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;

import java.util.List;

/**
 * @Description: 用户分组service接口
 * @Author: rik.ren
 * @Date: 2025/3/9 15:58
 **/
public interface ISacUserGroupService {

    /**
     * 批量获取用户分组详情集合
     *
     * @param param
     * @param needColumn
     * @return
     */
    List<SacUserGroupEntity> queryEntity(SacUserGroupEntity param, QueryColumn... needColumn);
}
