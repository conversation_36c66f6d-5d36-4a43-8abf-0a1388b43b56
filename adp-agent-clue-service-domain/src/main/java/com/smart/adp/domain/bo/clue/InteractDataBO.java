package com.smart.adp.domain.bo.clue;

import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.enums.EventTypeEnum;
import com.smart.adp.domain.enums.InteractDataSearchTypeEnum;
import com.smart.adp.domain.enums.InteractDataTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description: 查询互动数据入参
 * @Author: rik.ren
 * @Date: 2025/3/13 13:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "查询互动数据入参")
public class InteractDataBO extends DomainPage<Object> {

    /**
     * 线索 ID
     */
    private String custId;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 创建开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 创建结束时间
     */
    private LocalDateTime endTime;

    /**
     * 搜索关键字
     */
    private String searchContent;


    /**
     * 数据类型
     *
     * @see InteractDataTypeEnum
     */
    private String dataType;

    /**
     * 数据类型
     *
     * @see EventTypeEnum
     */
    private String eventType;

    /**
     * 场景码，对应字典数据中的ADP_CLUE_001
     */
    private String senceCode;

    /**
     * 查询类型，0默认列表，1主动搜索
     * 默认搜索就是进入详情自带的搜索
     * 主动搜索就是用户选择条件搜索
     *
     * @see InteractDataSearchTypeEnum
     */
    private Integer searchType = 0;

}
