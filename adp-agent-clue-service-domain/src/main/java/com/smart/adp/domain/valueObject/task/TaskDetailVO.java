package com.smart.adp.domain.valueObject.task;

import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.domain.entity.task.OnetaskDetail;
import com.smart.adp.domain.enums.TaskDetailStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/6
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class TaskDetailVO {

    /**
     * 详情 ID
     */
    @Schema(description = "详情 ID")
    private String detailId;

    /**
     * 客户 ID
     */
    @Schema(description = "客户 ID")
    private String custId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String custName;

    /**
     * 任务明细状态
     * @see TaskDetailStatusEnum
     */
    @Schema(description = "任务明细状态")
    private String stateCode;

    /**
     * 回访 ID
     */
    @Schema(description = "回访 ID")
    private String reviewId;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 线索等级
     */
    @Schema(description = "线索等级")
    private String level;

    /**
     * po -> vo
     *
     * @param detail po
     * @return vo
     */
    public static TaskDetailVO fromPO(OnetaskDetail detail) {
        if (Objects.isNull(detail)) {
            return null;
        }

        TaskDetailVO vo = new TaskDetailVO();
        vo.setDetailId(detail.getDetailId());
        vo.setCustId(detail.getCustId());
        vo.setCustName(detail.getCustName());
        vo.setStateCode(detail.getStateCode());
        vo.setReviewId(detail.getReviewId());

        String phone = Optional.of(detail)
                                .map(OnetaskDetail::getExtendJson)
                                .map(JSONObject::parseObject)
                                .map(json -> json.getString("phone"))
                                .orElse(null);
        vo.setPhone(phone);

        return vo;
    }
}
