package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.base.AgentEmployeeBO;
import com.smart.adp.domain.entity.base.AgentEmployee;

import java.util.List;

/**
 * 代理商员工信息实现
 */
public interface AgentEmployeeGateway {

    AgentEmployee findOne(String userId, List<String> userStatusList);

    AgentEmployee findEmployee(String empCode);

    /**
     * 根据实体条件查询员工信息
     *
     * @param entityParam
     * @param needColumns
     * @return
     */
    List<AgentEmployee> findEmployee(AgentEmployee entityParam, AgentEmployeeBO boParam, QueryColumn... needColumns);

    /**
     * 查询门店的在职员工
     *
     * @param entityParam
     * @param needColumns
     * @return
     */
    List<AgentEmployee> findEmployeeByDlrCode(AgentEmployee entityParam, QueryColumn... needColumns);

    /**
     * 查询用户信息，带门店的
     *
     * @param entityParam
     * @param boParam
     * @param needColumns
     * @return
     */
    List<AgentEmployeeBO> findEmployeeDlr(AgentEmployee entityParam, AgentEmployeeBO boParam, QueryColumn... needColumns);
}
