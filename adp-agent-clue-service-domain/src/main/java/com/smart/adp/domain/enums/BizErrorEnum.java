package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务异常枚举
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023-03-03 14:00
 */
@Getter
@AllArgsConstructor
public enum BizErrorEnum {

    /**
     * 卡券管理领域
     */

    /**
     * 卡券手动发放领域
     */
    MANUAL_DISTRIBUTE_COUPON_NOT_EXIST("MANUAL_DISTRIBUTE_COUPON_NOT_EXIST", "手动发放卡券不存在"),
    MANUAL_DISTRIBUTE_COUPON_STATUS_INVALID("MANUAL_DISTRIBUTE_COUPON_STATUS_INVALID", "手动发放卡券无效"),
    MANUAL_DISTRIBUTE_NOT_EXIST("MANUAL_DISTRIBUTE_NOT_EXIST", "卡券手动发放记录不存在"),
    MANUAL_DISTRIBUTE_AUDIT_STATUS_EXIST("MANUAL_DISTRIBUTE_AUDIT_STATUS_EXIST", "卡券手动发放记录状态已存在"),
    MANUAL_DISTRIBUTE_AUDIT_FAILED("MANUAL_DISTRIBUTE_AUDIT_FAILED", "卡券手动发放审核失败"),
    MANUAL_DISTRIBUTE_USER_EXPORT_FAILED("MANUAL_DISTRIBUTE_USER_EXPORT_FAILED", "手动发放用户导出异常"),

    /**
     * 卡券明细领域
     */

    ;

    private final String code;

    private final String message;
}
