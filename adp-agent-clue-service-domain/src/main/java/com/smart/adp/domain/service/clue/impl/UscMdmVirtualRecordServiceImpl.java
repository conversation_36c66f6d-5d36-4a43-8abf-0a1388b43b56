package com.smart.adp.domain.service.clue.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.base.AgentEmployeeBO;
import com.smart.adp.domain.bo.clue.UscMdmVirtualRecordBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.clue.UscMdmVirtualRecordEntity;
import com.smart.adp.domain.gateway.clue.UscMdmVirtualRecordGateway;
import com.smart.adp.domain.service.base.IAgentEmployeeService;
import com.smart.adp.domain.service.clue.IUscMdmVirtualRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.smart.adp.domain.entity.base.table.AgentDlrInfoTableDef.AGENT_DLR_INFO;
import static com.smart.adp.domain.entity.base.table.AgentEmployeeTableDef.AGENT_EMPLOYEE;
import static com.smart.adp.domain.entity.clue.table.UscMdmVirtualRecordEntityTableDef.USC_MDM_VIRTUAL_RECORD_ENTITY;

/**
 * 虚拟外呼通话记录表 服务层实现。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Service
public class UscMdmVirtualRecordServiceImpl implements IUscMdmVirtualRecordService {

    @Autowired
    private UscMdmVirtualRecordGateway virtualRecordGateway;
    @Autowired
    private IAgentEmployeeService agentEmployeeService;

    /**
     * 更新虚拟外呼摘要
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyVirtualAbstractContent(UscMdmVirtualRecordBO param) {
        UscMdmVirtualRecordEntity entity =
                UscMdmVirtualRecordEntity.builder().recordId(param.getRecordId()).abstractContent(param.getAbstractContent()).lastUpdatedDate(LocalDateTime.now()).build();
        return virtualRecordGateway.updateVirtualRecord(entity);
    }

    /**
     * 查询虚拟外呼内容
     *
     * @param param
     * @return
     */
    @Override
    public DomainPage<UscMdmVirtualRecordBO> queryClueVirtualRecord(UscMdmVirtualRecordBO param) {

        // 1. 查询虚拟外呼内容
        UscMdmVirtualRecordEntity entity = UscMdmVirtualRecordEntity.builder().custId(param.getCustId()).build();
        QueryColumn[] needColumn = {USC_MDM_VIRTUAL_RECORD_ENTITY.CUST_ID, USC_MDM_VIRTUAL_RECORD_ENTITY.ABSTRACT_CONTENT,
                USC_MDM_VIRTUAL_RECORD_ENTITY.CALL_START_TIME, USC_MDM_VIRTUAL_RECORD_ENTITY.CALL_END_TIME,
                USC_MDM_VIRTUAL_RECORD_ENTITY.STATUS, USC_MDM_VIRTUAL_RECORD_ENTITY.CALL_TYPE,
                USC_MDM_VIRTUAL_RECORD_ENTITY.CALL_TIME, USC_MDM_VIRTUAL_RECORD_ENTITY.RECORD_ID,
                USC_MDM_VIRTUAL_RECORD_ENTITY.EMP_CODE};
        DomainPage<UscMdmVirtualRecordEntity> pageVirtualRecord = virtualRecordGateway.queryVirtualRecord(entity, param, null,
                needColumn);
        if (ObjectUtil.isEmpty(pageVirtualRecord) || CollectionUtil.isEmpty(pageVirtualRecord.getRecords())) {
            return null;
        }

        // 2. 批量查询员工信息
        AgentEmployeeBO employeeBo = new AgentEmployeeBO();
        List<String> listUserId =
                pageVirtualRecord.getRecords().stream().map(UscMdmVirtualRecordEntity::getEmpCode).distinct().collect(Collectors.toList());
        employeeBo.setUserIdList(listUserId);
        needColumn = new QueryColumn[]{AGENT_EMPLOYEE.EMP_NAME, AGENT_EMPLOYEE.USER_ID, AGENT_EMPLOYEE.EMP_CODE,
                AGENT_EMPLOYEE.DLR_CODE,
                AGENT_DLR_INFO.DLR_SHORT_NAME, AGENT_DLR_INFO.DLR_FULL_NAME, AGENT_DLR_INFO.CITY_ID, AGENT_DLR_INFO.DLR_TYPE,
                AGENT_DLR_INFO.COUNTY_ID, AGENT_DLR_INFO.PROVINCE_ID};
        List<AgentEmployeeBO> empResult = agentEmployeeService.findEmpDlrInfo(employeeBo, needColumn);

        // 3. 实体转换
        DomainPage<UscMdmVirtualRecordBO> result =
                new DomainPage<>(UscMdmVirtualRecordBO.conventBO(pageVirtualRecord.getRecords(), empResult),
                        pageVirtualRecord.getPageNumber(),
                        pageVirtualRecord.getPageSize(), pageVirtualRecord.getTotalCount());
        return result;
    }
}