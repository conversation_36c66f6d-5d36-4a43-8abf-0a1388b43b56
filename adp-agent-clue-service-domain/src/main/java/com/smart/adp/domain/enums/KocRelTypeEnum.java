package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: KOC关系类型枚举
 * @Author: system
 * @Date: 2025/8/4
 **/
@Getter
@AllArgsConstructor
public enum KocRelTypeEnum {

    /**
     * 标签
     */
    TAG(1, "标签"),

    /**
     * 达人类型
     */
    EXPERT_TYPE(2, "达人类型"),

    /**
     * 备注
     */
    REMARK(3, "备注");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static KocRelTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (KocRelTypeEnum relType : values()) {
            if (relType.getCode().equals(code)) {
                return relType;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     */
    public static String getDescByCode(Integer code) {
        KocRelTypeEnum relType = getByCode(code);
        return relType != null ? relType.getDesc() : "";
    }

    /**
     * 判断是否为有效的关系类型
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
