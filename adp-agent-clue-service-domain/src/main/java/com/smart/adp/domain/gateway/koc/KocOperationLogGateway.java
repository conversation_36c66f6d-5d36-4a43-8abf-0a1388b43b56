package com.smart.adp.domain.gateway.koc;

import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.koc.SacTagOperationLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: KOC操作日志网关接口
 * @Author: system
 * @Date: 2025/8/4
 **/
public interface KocOperationLogGateway {

    /**
     * 根据ID查询操作日志
     *
     * @param logId 日志ID
     * @return 操作日志
     */
    SacTagOperationLog findById(String logId);

    /**
     * 根据条件查询操作日志列表
     *
     * @param condition 查询条件
     * @return 操作日志列表
     */
    List<SacTagOperationLog> findByCondition(SacTagOperationLog condition);

    /**
     * 分页查询操作日志
     *
     * @param condition 查询条件
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    DomainPage<SacTagOperationLog> findPage(SacTagOperationLog condition, Integer pageNum, Integer pageSize);

    /**
     * 根据用户smartId查询操作日志
     *
     * @param smartId 用户smartId
     * @return 操作日志列表
     */
    List<SacTagOperationLog> findBySmartId(String smartId);

    /**
     * 根据操作人查询操作日志
     *
     * @param operator 操作人
     * @return 操作日志列表
     */
    List<SacTagOperationLog> findByOperator(String operator);

    /**
     * 根据操作类型查询操作日志
     *
     * @param operationType 操作类型
     * @return 操作日志列表
     */
    List<SacTagOperationLog> findByOperationType(Integer operationType);

    /**
     * 根据目标ID查询操作日志
     *
     * @param targetId 目标ID
     * @return 操作日志列表
     */
    List<SacTagOperationLog> findByTargetId(String targetId);

    /**
     * 根据批量操作ID查询操作日志
     *
     * @param batchId 批量操作ID
     * @return 操作日志列表
     */
    List<SacTagOperationLog> findByBatchId(String batchId);

    /**
     * 根据时间范围查询操作日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作日志列表
     */
    List<SacTagOperationLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 保存操作日志
     *
     * @param operationLog 操作日志
     * @return 是否成功
     */
    Boolean save(SacTagOperationLog operationLog);

    /**
     * 批量保存操作日志
     *
     * @param operationLogs 操作日志列表
     * @return 是否成功
     */
    Boolean batchSave(List<SacTagOperationLog> operationLogs);

    /**
     * 删除操作日志
     *
     * @param logId 日志ID
     * @return 是否成功
     */
    Boolean deleteById(String logId);

    /**
     * 根据时间删除过期日志
     *
     * @param expireTime 过期时间
     * @return 删除数量
     */
    Integer deleteExpiredLogs(LocalDateTime expireTime);

    /**
     * 统计操作次数
     *
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    Integer countOperations(Integer operationType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计用户操作次数
     *
     * @param smartId 用户smartId
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    Integer countUserOperations(String smartId, LocalDateTime startTime, LocalDateTime endTime);
}
