package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacClueMsgRecordBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.gateway.clue.ClueMsgRecordGateway;
import com.smart.adp.domain.service.clue.ISacClueMsgRecordService;
import com.smart.adp.domain.valueObject.clue.SacClueMsgRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static com.smart.adp.domain.valueObject.clue.table.SacClueMsgRecordVOTableDef.SAC_CLUE_MSG_RECORD_VO;

/**
 * @Description: 用户事件服务实现
 * @Author: rik.ren
 * @Date: 2025/3/14 15:46
 **/
@Service
public class SacClueMsgRecordServiceImpl implements ISacClueMsgRecordService {
    @Autowired
    private ClueMsgRecordGateway msgRecordGateway;
    // 定义静态常量，JVM加载类时初始化
    private static final QueryColumn[] DEFAULT_QUERY_COLUMNS = {
            SAC_CLUE_MSG_RECORD_VO.MESSAGE_ID,
            SAC_CLUE_MSG_RECORD_VO.IS_READ,
            SAC_CLUE_MSG_RECORD_VO.DLR_CODE,
            SAC_CLUE_MSG_RECORD_VO.PHONE,
            SAC_CLUE_MSG_RECORD_VO.MESSAGE_TYPE,
            SAC_CLUE_MSG_RECORD_VO.BUSI_KEYVALUE,
            SAC_CLUE_MSG_RECORD_VO.RECEIVE_EMP_ID,
            SAC_CLUE_MSG_RECORD_VO.MESSAGE_CONTENT,
            SAC_CLUE_MSG_RECORD_VO.RELATION_BILL_ID,
            SAC_CLUE_MSG_RECORD_VO.EXTEND_JSON,
            SAC_CLUE_MSG_RECORD_VO.CREATED_DATE,
            SAC_CLUE_MSG_RECORD_VO.LAST_UPDATED_DATE
    };

    @Override
    public List<SacClueMsgRecordVO> findListByCondition(SacClueMsgRecordBO boParam, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = DEFAULT_QUERY_COLUMNS;
        }
        return msgRecordGateway.findListByCondition(boParam, needColumns);
    }

    @Override
    public DomainPage<SacClueMsgRecordVO> findPageByCondition(SacClueMsgRecordBO boParam, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = DEFAULT_QUERY_COLUMNS;
        }
        return msgRecordGateway.findPageByCondition(boParam, needColumns);
    }

    @Override
    public Long findCountByCondition(SacClueMsgRecordBO boParam) {
        return msgRecordGateway.findCountByCondition(boParam);
    }

    @Override
    public Boolean markEmpMsgRead(SacClueMsgRecordBO boParam) {
        return msgRecordGateway.modifyMsgRecord(boParam);
    }
}
