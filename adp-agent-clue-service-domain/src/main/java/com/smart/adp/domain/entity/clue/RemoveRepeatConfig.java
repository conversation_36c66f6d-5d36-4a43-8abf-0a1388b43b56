package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/6 22:15
 * @description
 **/
@Table(value = "t_sac_remove_repeat_config", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class RemoveRepeatConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column("CONFIG_ID")
    private String configId;

    @Column("CHECK_PHONE")
    private String checkPhone;

    @Column("CHECK_TIME")
    private String checkTime;

    @Column("CHECK_TIME_HORIZON")
    private String checkTimeHorizon;

    @Column("CUSTOM")
    private String custom;

    @Column("OEM_ID")
    private String oemId;

    @Column("GROUP_ID")
    private String groupId;

    @Column("CREATOR")
    private String creator;

    @Column("CREATED_NAME")
    private String createName;

    @Column("CREATED_DATE")
    private LocalDateTime createDate;

    @Column("MODIFIER")
    private String modifier;

    @Column("MODIFY_NAME")
    private String modifierName;

    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    @Column("IS_ENABLE")
    private String isEnable;

    @Column("ORG_CODE")
    private String orgCode;

    @Column("ORG_NAME")
    private String orgName;

    @Column("UPDATE_CONTROL_ID")
    private String updateControlId;

}
