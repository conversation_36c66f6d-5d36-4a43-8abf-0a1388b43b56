package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.clue.SacClueInfoDlrBO;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlrLog;
import com.smart.adp.domain.enums.ClueQueryTypeEnum;
import com.smart.adp.domain.qry.ClueDlrQry;
import com.smart.adp.domain.valueObject.clue.CalenderCountVO;
import com.smart.adp.domain.valueObject.clue.ClueActiveInfoVO;
import com.smart.adp.domain.valueObject.clue.ClueDlrListVO;
import com.smart.adp.domain.valueObject.clue.ClueDlrStatisticsVO;

import java.time.LocalDateTime;
import java.util.List;


public interface ClueDlrGateway {

    SacClueInfoDlr findLastOne(String statusCode, QueryColumn... columns);

    /**
     * 获取非战败线索
     *
     * @param phone
     * @return
     */
    SacClueInfoDlr findClueByPhone(String phone);

    long countByQry(ClueDlrQry qry);

    List<ClueDlrListVO> list(ClueDlrQry qry);

    List<ClueDlrListVO> defeatList(ClueDlrQry qry);

    Page<ClueDlrListVO> page(ClueDlrQry qry, Page<ClueDlrListVO> page);

    Page<ClueDlrListVO> defeatPage(ClueDlrQry qry, Page<ClueDlrListVO> page);

    SacClueInfoDlr findById(String id, QueryColumn... columns);

    SacClueInfoDlr findByCondition(SacClueInfoDlr param, QueryColumn... columns);

    SacClueInfoDlr findByCondition(SacClueInfoDlrBO param, QueryColumn... columns);

    int saveClueInfo(SacClueInfoDlr clueInfoDlr);

    int updateClueInfo(SacClueInfoDlr clueInfoDlr);

    Boolean updateClueInfoBatch(List<SacClueInfoDlr> clueInfoDlrList);

    Boolean updateClueInfoDlrBatch(List<SacAllClueInfoDlr> sacAllClueInfoDlrList);

    int saveClueLog(SacClueInfoDlrLog sacClueInfoDlrLog);

    /**
     * 保存战败线索进入历史表
     *
     * @param sacAllClueInfoDlr
     * @return
     */
    int saveHistoryClueInfo(SacAllClueInfoDlr sacAllClueInfoDlr);

    List<CalenderCountVO> overdueCount(LocalDateTime startTime, LocalDateTime endTime, UserBusiEntity userInfo);

    List<SacAllClueInfoDlr> findDefeatedClueByPhone(String phone, String statusCode);

    SacAllClueInfoDlr findDefeatedClueByReviewId(String reviewId);

    List<SacAllClueInfoDlr> findDefeatedClueByReviewIds(QueryWrapper wrapper);

    SacClueInfoDlr findActiveClueByReviewId(String reviewId);

    List<SacClueInfoDlr> findActiveClueByReviewIds(QueryWrapper wrapper);

    int delDefeatedClueByPhone(String phone);

    ClueDlrStatisticsVO statisticsByType(ClueQueryTypeEnum type, UserBusiEntity userInfo);

    /**
     * 更新adp_leads库线索信息
     *
     * @param param
     * @return
     */
    Boolean modifyClueInfo(SacClueInfoDlr param);

    /**
     * 更新csc库线索信息
     *
     * @param param
     * @return
     */
    Boolean modifyAllClueInfo(SacAllClueInfoDlr param);

    /**
     * 更新线索信息
     *
     * @param param
     * @return
     */
    Boolean modifyClue(SacClueInfoDlr param);

    /**
     * 更新线索信息
     *
     * @param param
     * @return
     */
    Boolean modifyAllClue(SacAllClueInfoDlr param);

    /**
     * 根据手机号集合查询线索活跃信息
     *
     * @param listPhone
     * @return
     */
    List<ClueActiveInfoVO> queryClueActiveInfo(List<String> listPhone);

    List<SacClueInfoDlr> listByWrapper(QueryWrapper wrapper);

    long countByWrapper(QueryWrapper wrapper);

    List<SacClueInfoDlr> listAllByWrapper(QueryWrapper wrapper);

    /**
     * 全量表 count by wrapper
     *
     * @param wrapper -
     * @return long
     */
    long countAllByWrapper(QueryWrapper wrapper);

    /**
     * 战败激活数量
     *
     * @param user 当前用户
     * @return long
     */
    long activatesDefeatedNum(UserBusiEntity user);

    /**
     * 划转审核数量
     *
     * @param user 当前用户
     * @return long
     */
    long transferAuditNum(UserBusiEntity user);

    /**
     * 战败审核数量
     *
     * @param user 当前用户
     * @return long
     */
    long defeatedAuditNum(UserBusiEntity user);

    /**
     * 跟进任务数量
     *
     * @param user 当前用户
     * @return long
     */
    long reviewTaskNum(UserBusiEntity user);

    /**
     * 查询是否存在数据
     *
     * @param qry -
     * @return boolean
     */
    boolean existByQry(ClueDlrQry qry);
}
