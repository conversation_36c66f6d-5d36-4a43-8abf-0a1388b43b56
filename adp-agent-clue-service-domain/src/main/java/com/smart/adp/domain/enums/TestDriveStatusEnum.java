package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */
@Getter
@AllArgsConstructor
public enum TestDriveStatusEnum {

    CANCELLED("-1", "已取消"),
    NOT_STARTED("0", "未开始"),
    IN_PROGRESS("1", "试乘试驾中"),
    COMPLETED("2", "已结束"),
    EXPIRED("3", "已过期"),

    ;

    private final String code;
    private final String desc;
}
