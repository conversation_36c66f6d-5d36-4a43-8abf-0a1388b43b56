package com.smart.adp.domain.service.base.impl;

import com.smart.adp.domain.gateway.base.CarTypeGateway;
import com.smart.adp.domain.service.base.ICareTypeService;

import com.smart.adp.domain.valueObject.base.SmallCarTypeInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/3/18 16:04
 * @description
 **/
@Service
public class ICareTypeServiceImpl implements ICareTypeService {

    @Autowired
    private CarTypeGateway carTypeGateway;

    @Override
    public List<SmallCarTypeInfo> queryCarTypeList() {
        return carTypeGateway.queryCarTypeList();
    }
}
