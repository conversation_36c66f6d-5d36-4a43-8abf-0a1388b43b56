package com.smart.adp.domain.entity.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.bo.base.AgentEmployeeBO;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import static com.smart.adp.domain.entity.base.table.AgentEmployeeTableDef.AGENT_EMPLOYEE;

/**
 * <AUTHOR>
 * date 2025/3/5 10:57
 * @description 员工信息对象
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_usc_mdm_org_employee", schema = "mp")
public class AgentEmployee implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    @Column("EMP_ID")
    private String empId;

    /**
     * 职员编码
     */
    @Column("EMP_CODE")
    private String empCode;

    /**
     * 职员姓名
     */
    @Column("EMP_NAME")
    private String empName;

    /**
     * 用户ID
     */
    @Column("USER_ID")
    private String userId;

    /**
     * 用户名称
     */
    @Column("USER_NAME")
    private String userName;

    /**
     * 手机号
     */
    @Column("MOBILE")
    private String mobile;

    /**
     * 专营店编码
     */
    @Column("DLR_CODE")
    private String dlrCode;

    /**
     * 用户状态
     * '1',"在职"
     */
    @Column("USER_STATUS")
    private String userStatus;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 头像
     */
    @Column(value = "HEAD_PORTRAIT")
    private String headPortrait;

    @Column(value = "STATION_ID")
    private String stationId;

    @Column(value = "GENDER_CODE")
    private String genderCode;

    public AgentEmployee conditions(QueryWrapper wrapper, AgentEmployeeBO boParam) {
        wrapper.and(AGENT_EMPLOYEE.EMP_ID.eq(getEmpId(), StringUtil::hasText))
                .and(AGENT_EMPLOYEE.EMP_CODE.eq(getEmpCode(), StringUtil::hasText))
                .and(AGENT_EMPLOYEE.MOBILE.eq(getMobile(), StringUtil::hasText))
                .and(AGENT_EMPLOYEE.USER_ID.eq(getUserId(), StringUtil::hasText))
                .and(AGENT_EMPLOYEE.DLR_CODE.eq(getDlrCode(), StringUtil::hasText));
        if (ObjectUtil.isNotEmpty(boParam)) {
            wrapper.and(AGENT_EMPLOYEE.USER_ID.in(boParam.getUserIdList(), CollectionUtil::isNotEmpty));
            wrapper.and(AGENT_EMPLOYEE.EMP_ID.in(boParam.getEmpIdList(), CollectionUtil::isNotEmpty));
            wrapper.and(AGENT_EMPLOYEE.EMP_CODE.in(boParam.getEmpCodeList(), CollectionUtil::isNotEmpty));
        }
        return this;
    }
}
