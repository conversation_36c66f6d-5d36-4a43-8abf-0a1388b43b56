package com.smart.adp.domain.entity.message;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/4/18 20:52
 * @description 发送企微提醒消息体
 **/
@Data
public class SendWecomMessage implements Serializable {

    /**
     * 指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）
     */
    private String toUser;
    /**
     * 发送企微消息消息类型枚举,文本取 text
     */
    private String msgType;
    /**
     * 文本消息类型，消息内容
     */
    private MessageContent msgContent;
}
