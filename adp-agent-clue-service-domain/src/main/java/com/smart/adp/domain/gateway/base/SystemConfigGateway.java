package com.smart.adp.domain.gateway.base;

import com.smart.adp.domain.entity.base.SystemConfig;
import com.smart.adp.domain.valueObject.base.SystemConfigVO;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/3/6 20:40
 * @description
 **/
public interface SystemConfigGateway {
    List<SystemConfigVO> findSystemConfigInfo(String configCode, String orgCode, String isEnable);

    List<SystemConfig> findSysTemConfigByCode(String configCode);
}
