package com.smart.adp.domain.bo.koc;

import com.smart.adp.domain.entity.koc.SacTagInfo;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: KOC标签BO
 * @Author: system
 * @Date: 2025/8/4
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KocTagBO extends SacTagInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子标签列表
     */
    private List<KocTagBO> children;

    /**
     * 是否有子标签
     */
    private Boolean hasChildren;

    /**
     * 标签使用次数
     */
    private Integer useCount;

    /**
     * 是否选中（用于前端展示）
     */
    private Boolean selected;

    /**
     * 标签层级名称
     */
    private String levelName;

    /**
     * 父标签名称
     */
    private String parentTagName;

    /**
     * 构建标签树节点
     */
    public static KocTagBO buildFromEntity(SacTagInfo entity) {
        if (entity == null) {
            return null;
        }
        
        return KocTagBO.builder()
                .tagId(entity.getTagId())
                .tagName(entity.getTagName())
                .parentTagId(entity.getParentTagId())
                .tagLevel(entity.getTagLevel())
                .fullPath(entity.getFullPath())
                .tagStatus(entity.getTagStatus())
                .sortOrder(entity.getSortOrder())
                .creator(entity.getCreator())
                .createdName(entity.getCreatedName())
                .createdDate(entity.getCreatedDate())
                .modifier(entity.getModifier())
                .modifyName(entity.getModifyName())
                .lastUpdatedDate(entity.getLastUpdatedDate())
                .hasChildren(false)
                .selected(false)
                .build();
    }

    /**
     * 获取层级名称
     */
    public String getLevelName() {
        if (tagLevel == null) {
            return "";
        }
        switch (tagLevel) {
            case 1:
                return "一级标签";
            case 2:
                return "二级标签";
            case 3:
                return "三级标签";
            default:
                return "未知层级";
        }
    }
}
