package com.smart.adp.domain.strategy.modifyClue;

import com.smart.adp.domain.bo.clue.ClueDlrModifyBO;
import com.smart.adp.domain.service.clue.ISacUserGroupDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 更新用户标签
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Component
@Slf4j
public class UserLabelModifyStrategy implements ClueModifyStrategy {
    @Autowired
    private ISacUserGroupDetailService userGroupDetailService;

    @Override
    public Boolean modify(ClueDlrModifyBO param) {
        log.info("更新用户标签: {}", param.getValue());
        // 这个是更新t_sac_user_group_detail表
        Boolean modifyResult = userGroupDetailService.modifyUserGroupDetail(param.buildUserGroupDetailModifyParam());
        // 清除缓存
        return modifyResult;
    }
}