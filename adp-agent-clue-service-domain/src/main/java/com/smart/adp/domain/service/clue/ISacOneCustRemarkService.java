package com.smart.adp.domain.service.clue;

import com.smart.adp.domain.bo.clue.SacOneCustRemarkBO;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;

import java.util.List;

/**
 * @Description: 客户扩展信息接口
 * @Author: rik.ren
 * @Date: 2025/3/12 17:36
 **/
public interface ISacOneCustRemarkService {
    /**
     * 编辑扩展信息
     *
     * @param param
     * @return
     */
    Boolean modifyCustRemark(SacOneCustRemark param);

    /**
     * 查询一个remark
     *
     * @param param
     * @return
     */
    SacOneCustRemark queryRemark(SacOneCustRemark param);

    /**
     * 批量查询remark
     *
     * @param param
     * @return
     */
    List<SacOneCustRemark> queryRemark(SacOneCustRemarkBO param);

    /**
     * 插入remark
     *
     * @param param
     * @return
     */
    Boolean insertRemark(SacOneCustRemark param);
}
