package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nonnull;
import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/9
 */
@Getter
@AllArgsConstructor
public enum CustEventEnum {

    /**
     * 下发
     */
    CREATE(0, "createEventHandler"),

    /**
     * 跟进
     */
    REVIEW(1, "reviewEventHandler"),

    /**
     * 试驾执行
     */
    DRIVE(2, "driveEventHandler"),

    /**
     * 下定
     */
    ORDER(3, "orderEventHandler"),

    /**
     * 退订
     */
    RETURN(4, "returnEventHandler"),

    /**
     * 交付
     */
    DELIVERY(5, "deliveryEventHandler"),

    /**
     * 战败 - 不体现在阶段
     */
    DEFEAT(6, "defeatEventHandler"),

    ;

    private final int code;
    private final String handlerName;

    @Nonnull
    public static CustEventEnum getByCode(Integer code) {
        return Arrays.stream(CustEventEnum.values())
                     .filter(e -> Objects.equals(e.getCode(), code))
                     .findAny()
                     .orElseThrow(() -> new IllegalArgumentException("cust event code illegal: " + code));
    }
}
