package com.smart.adp.domain.valueObject.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/16
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "线索提示 VO")
public class ClueTipsVO {

    @Schema(description = "是否存在待跟进")
    private boolean waitForReview;

    @Schema(description = "是否存在未跟进")
    private boolean noReview;

    @Schema(description = "是否存在逾期")
    private boolean overdue;
}
