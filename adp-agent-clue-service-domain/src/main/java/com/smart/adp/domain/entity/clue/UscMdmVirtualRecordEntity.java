package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.bo.clue.UscMdmVirtualRecordBO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Objects;

import static com.smart.adp.domain.entity.clue.table.UscMdmVirtualRecordEntityTableDef.USC_MDM_VIRTUAL_RECORD_ENTITY;

/**
 * 虚拟外呼通话记录表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_usc_mdm_virtual_record", schema = "mp")
public class UscMdmVirtualRecordEntity {

    /**
     * 通话记录ID
     */
    @Id
    private String recordId;

    /**
     * 虚拟号码
     */
    @Column(value = "VIRTUAL_MOBILE")
    private String virtualMobile;

    /**
     * 员工号码
     */
    @Column(value = "BIND_MOBILE")
    private String bindMobile;

    /**
     * 员工USERID
     */
    @Column(value = "EMP_CODE")
    private String empCode;

    /**
     * 客户号码
     */
    @Column(value = "CONSUMER_MOBILE")
    private String consumerMobile;

    /**
     * 客户ID
     */
    @Column(value = "CUST_ID")
    private String custId;

    /**
     * 呼叫状态 (0 成功 1 忙 2 无应答 3 客户提前挂机 9 呼叫失败
     * 11 主叫号码与中间号没有关联)
     */
    @Column(value = "STATUS")
    private String status;

    /**
     * 呼叫类型 0主叫 1被叫
     */
    @Column(value = "CALL_TYPE")
    private String callType;

    /**
     * 接听开始时间
     */
    @Column(value = "CALL_START_TIME")
    private LocalDateTime callStartTime;

    /**
     * 接听结束时间
     */
    @Column(value = "CALL_END_TIME")
    private LocalDateTime callEndTime;

    /**
     * 录音地址
     */
    @Column(value = "CALL_RECORD_URL")
    private String callRecordUrl;

    /**
     * 通话时间
     */
    @Column(value = "CALL_TIME")
    private String callTime;

    /**
     * 质检ID
     */
    @Column(value = "CALL_ID")
    private String callId;

    /**
     * 创建人
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 最后一次更新时间
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE", isLogicDelete = true)
    private String isEnable;

    /**
     * 并发控制字段
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 虚拟外呼摘要内容
     */
    @Column(value = "ABSTRACT_CONTENT")
    private String abstractContent;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public UscMdmVirtualRecordEntity conditions(QueryWrapper wrapper, UscMdmVirtualRecordBO boParam) {
        wrapper.and(USC_MDM_VIRTUAL_RECORD_ENTITY.RECORD_ID.eq(getRecordId(), StringUtil::hasText))
                .and(USC_MDM_VIRTUAL_RECORD_ENTITY.CUST_ID.eq(getCustId(), StringUtil::hasText))
                .and(USC_MDM_VIRTUAL_RECORD_ENTITY.CONSUMER_MOBILE.eq(getConsumerMobile(), StringUtil::hasText))
                .and(USC_MDM_VIRTUAL_RECORD_ENTITY.CALL_START_TIME.ge(boParam.getDateBegin(), Objects::nonNull))
                .and(USC_MDM_VIRTUAL_RECORD_ENTITY.CALL_START_TIME.le(boParam.getDateEnd(), Objects::nonNull));
        return this;
    }

}
