package com.smart.adp.domain.common.context;
/*
import java.lang.reflect.Method;

import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
//import org.apache.dubbo.common.Constants;
//import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.common.utils.ReflectUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.filter.ExceptionFilter;
import org.apache.dubbo.rpc.service.GenericService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Activate(group = {CommonConstants.PROVIDER })
public class BusicenExceptionFilter extends ExceptionFilter {

	public BusicenExceptionFilter() {
		this(LoggerFactory.getLogger(BusicenExceptionFilter.class));
		
		super.listener = new BusicenExceptionListener();
	}

	public BusicenExceptionFilter(Logger logger) {
		super.listener = new BusicenExceptionListener();
	}

//	@Override
//	public Result onResponse(Result result, Invoker<?> invoker, Invocation invocation) {
//		if (result.hasException() && GenericService.class != invoker.getInterface()) {
//			try {
//				Throwable exception = result.getException();
//
//				if (exception instanceof BusicenException) {
//					return result;
//				}
//
//				// directly throw if it's checked exception
//				if (!(exception instanceof RuntimeException) && (exception instanceof Exception)) {
//					return result;
//				}
//				// directly throw if the exception appears in the signature
//				try {
//					Method method = invoker.getInterface().getMethod(invocation.getMethodName(),
//							invocation.getParameterTypes());
//					Class<?>[] exceptionClassses = method.getExceptionTypes();
//					for (Class<?> exceptionClass : exceptionClassses) {
//						if (exception.getClass().equals(exceptionClass)) {
//							return result;
//						}
//					}
//				} catch (NoSuchMethodException e) {
//					return result;
//				}
//
//				// for the exception not found in method's signature, print ERROR message in
//				// server's log.
//				logger.error(
//						"Got unchecked and undeclared exception which called by "
//								+ RpcContext.getContext().getRemoteHost() + ". service: "
//								+ invoker.getInterface().getName() + ", method: " + invocation.getMethodName()
//								+ ", exception: " + exception.getClass().getName() + ": " + exception.getMessage(),
//						exception);
//
//				// directly throw if exception class and interface class are in the same jar
//				// file.
//				String serviceFile = ReflectUtils.getCodeBase(invoker.getInterface());
//				String exceptionFile = ReflectUtils.getCodeBase(exception.getClass());
//				if (serviceFile == null || exceptionFile == null || serviceFile.equals(exceptionFile)) {
//					return result;
//				}
//				// directly throw if it's JDK exception
//				String className = exception.getClass().getName();
//				if (className.startsWith("java.") || className.startsWith("javax.")) {
//					return result;
//				}
//				// directly throw if it's dubbo exception
//				if (exception instanceof RpcException) {
//					return result;
//				}
//
//				// otherwise, wrap with RuntimeException and throw back to the client
//				return new RpcResult(new RuntimeException(StringUtils.toString(exception)));
//			} catch (Throwable e) {
//				logger.warn("Fail to ExceptionFilter when called by " + RpcContext.getContext().getRemoteHost()
//						+ ". service: " + invoker.getInterface().getName() + ", method: " + invocation.getMethodName()
//						+ ", exception: " + e.getClass().getName() + ": " + e.getMessage(), e);
//				return result;
//			}
//		}
//		return result;
//	}
	
	static class BusicenExceptionListener implements Listener {

        private Logger logger = LoggerFactory.getLogger(BusicenExceptionListener.class);

        @Override
        public void onResponse(Result appResponse, Invoker<?> invoker, Invocation invocation) {
            if (appResponse.hasException() && GenericService.class != invoker.getInterface()) {
                try {
                    Throwable exception = appResponse.getException();
                	if (exception instanceof BusicenException) {
    					return ;
    				}
                    // directly throw if it's checked exception
                    if (!(exception instanceof RuntimeException) && (exception instanceof Exception)) {
                        return;
                    }
                    // directly throw if the exception appears in the signature
                    try {
                        Method method = invoker.getInterface().getMethod(invocation.getMethodName(), invocation.getParameterTypes());
                        Class<?>[] exceptionClassses = method.getExceptionTypes();
                        for (Class<?> exceptionClass : exceptionClassses) {
                            if (exception.getClass().equals(exceptionClass)) {
                                return;
                            }
                        }
                    } catch (NoSuchMethodException e) {
                        return;
                    }

                    // for the exception not found in method's signature, print ERROR message in server's log.
                    logger.error("Got unchecked and undeclared exception which called by " + RpcContext.getContext().getRemoteHost() + ". service: " + invoker.getInterface().getName() + ", method: " + invocation.getMethodName() + ", exception: " + exception.getClass().getName() + ": " + exception.getMessage(), exception);

                    // directly throw if exception class and interface class are in the same jar file.
                    String serviceFile = ReflectUtils.getCodeBase(invoker.getInterface());
                    String exceptionFile = ReflectUtils.getCodeBase(exception.getClass());
                    if (serviceFile == null || exceptionFile == null || serviceFile.equals(exceptionFile)) {
                        return;
                    }
                    // directly throw if it's JDK exception
                    String className = exception.getClass().getName();
                    if (className.startsWith("java.") || className.startsWith("javax.")) {
                        return;
                    }
                    // directly throw if it's dubbo exception
                    if (exception instanceof RpcException) {
                        return;
                    }

                    // otherwise, wrap with RuntimeException and throw back to the client
                    appResponse.setException(new RuntimeException(StringUtils.toString(exception)));
                    return;
                } catch (Throwable e) {
                    logger.warn("Fail to ExceptionFilter when called by " + RpcContext.getContext().getRemoteHost() + ". service: " + invoker.getInterface().getName() + ", method: " + invocation.getMethodName() + ", exception: " + e.getClass().getName() + ": " + e.getMessage(), e);
                    return;
                }
            }
        }

        @Override
        public void onError(Throwable e, Invoker<?> invoker, Invocation invocation) {
            logger.error("Got unchecked and undeclared exception which called by " + RpcContext.getContext().getRemoteHost() + ". service: " + invoker.getInterface().getName() + ", method: " + invocation.getMethodName() + ", exception: " + e.getClass().getName() + ": " + e.getMessage(), e);
        }

        // For test purpose
        public void setLogger(Logger logger) {
            this.logger = logger;
        }
    }
	
}*/
