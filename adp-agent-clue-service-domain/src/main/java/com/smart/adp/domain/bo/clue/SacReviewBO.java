package com.smart.adp.domain.bo.clue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.entity.clue.SacReview;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 跟进BO
 * @Author: rik.ren
 * @Date: 2025/3/17 11:24
 **/
@Data
@ToString
public class SacReviewBO extends SacReview {

    /**
     * 客户描述
     */
    private String remark;

    /**
     * 用户阶段
     */
    private String userStage;

    /**
     * 上一个用户阶段
     */
    private String previousUserStage;

    /**
     * 线索等级(H/A/B/C/D/E)
     */
    private String clueLevel;

    /**
     * 意向车型
     */
    private String intentVehicleCode;

    /**
     * 竞品车型
     */
    private String competitiveVehicleCode;

    /**
     * 所在地
     */
    private String location;

    /**
     * 线索来源
     */
    private String clueSource;

    /**
     * 用户阶段-了解的时间
     */
    private LocalDateTime knowDate;

    /**
     * 用户阶段-到店的时间
     */
    private LocalDateTime toStoreDate;

    /**
     * 用户阶段-试驾的时间
     */
    private LocalDateTime testDriveDate;

    /**
     * 用户阶段-下定的时间
     */
    private LocalDateTime placeOrderDate;

    /**
     * 战败标签
     *
     * @see com.smart.adp.domain.enums.DefeatFlagEnum
     */
    private Integer defeatFlag = 0;

    public static SacReviewBO conventBO(SacReview entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        SacReviewBO sacReviewBO = new SacReviewBO();
        sacReviewBO.setReviewId(entity.getReviewId());
        sacReviewBO.setOrgCode(entity.getOrgCode());
        sacReviewBO.setOrgName(entity.getOrgName());
        sacReviewBO.setBillType(entity.getBillType());
        sacReviewBO.setBillTypeName(entity.getBillTypeName());
        sacReviewBO.setBusinessType(entity.getBusinessType());
        sacReviewBO.setBusinessTypeName(entity.getBusinessTypeName());
        sacReviewBO.setInfoChanMCode(entity.getInfoChanMCode());
        sacReviewBO.setInfoChanMName(entity.getInfoChanMName());
        sacReviewBO.setInfoChanDCode(entity.getInfoChanDCode());
        sacReviewBO.setInfoChanDName(entity.getInfoChanDName());
        sacReviewBO.setInfoChanDdCode(entity.getInfoChanDdCode());
        sacReviewBO.setInfoChanDdName(entity.getInfoChanDdName());
        sacReviewBO.setChannelCode(entity.getChannelCode());
        sacReviewBO.setChannelName(entity.getChannelName());
        sacReviewBO.setBillCode(entity.getBillCode());
        sacReviewBO.setPlanReviewTime(entity.getPlanReviewTime());
        sacReviewBO.setReviewTime(entity.getReviewTime());
        sacReviewBO.setLastReviewTime(entity.getLastReviewTime());
        sacReviewBO.setOverReviewTime(entity.getOverReviewTime());
        sacReviewBO.setPlanComeTime(entity.getPlanComeTime());
        sacReviewBO.setFactComeTime(entity.getFactComeTime());
        sacReviewBO.setIsCome(entity.getIsCome());
        sacReviewBO.setAssignStatus(entity.getAssignStatus());
        sacReviewBO.setAssignStatusName(entity.getAssignStatusName());
        sacReviewBO.setAssignTime(entity.getAssignTime());
        sacReviewBO.setAssignPersonId(entity.getAssignPersonId());
        sacReviewBO.setAssignPersonName(entity.getAssignPersonName());
        sacReviewBO.setReviewPersonId(entity.getReviewPersonId());
        sacReviewBO.setReviewPersonName(entity.getReviewPersonName());
        sacReviewBO.setReviewDesc(entity.getReviewDesc());
        sacReviewBO.setReviewStatus(entity.getReviewStatus());
        sacReviewBO.setReviewStatusName(entity.getReviewStatusName());
        sacReviewBO.setCustId(entity.getCustId());
        sacReviewBO.setCustName(entity.getCustName());
        sacReviewBO.setPhone(entity.getPhone());
        sacReviewBO.setGender(entity.getGender());
        sacReviewBO.setGenderName(entity.getGenderName());
        sacReviewBO.setTouchStatus(entity.getTouchStatus());
        sacReviewBO.setTouchStatusName(entity.getTouchStatusName());
        sacReviewBO.setErrorReasonCode(entity.getErrorReasonCode());
        sacReviewBO.setErrorReasonName(entity.getErrorReasonName());
        sacReviewBO.setNodeCode(entity.getNodeCode());
        sacReviewBO.setNodeName(entity.getNodeName());
        sacReviewBO.setSendDlrCode(entity.getSendDlrCode());
        sacReviewBO.setSendDlrShortName(entity.getSendDlrShortName());
        sacReviewBO.setSendTime(entity.getSendTime());
        sacReviewBO.setIntenLevelCode(entity.getIntenLevelCode());
        sacReviewBO.setIntenLevelName(entity.getIntenLevelName());
        sacReviewBO.setIntenBrandCode(entity.getIntenBrandCode());
        sacReviewBO.setIntenBrandName(entity.getIntenBrandName());
        sacReviewBO.setIntenSeriesCode(entity.getIntenSeriesCode());
        sacReviewBO.setIntenSeriesName(entity.getIntenSeriesName());
        sacReviewBO.setIntenCarTypeCode(entity.getIntenCarTypeCode());
        sacReviewBO.setIntenCarTypeName(entity.getIntenCarTypeName());
        sacReviewBO.setColumn1(entity.getColumn1());
        sacReviewBO.setColumn2(entity.getColumn2());
        sacReviewBO.setColumn3(entity.getColumn3());
        sacReviewBO.setColumn4(entity.getColumn4());
        sacReviewBO.setColumn5(entity.getColumn5());
        sacReviewBO.setColumn6(entity.getColumn6());
        sacReviewBO.setColumn7(entity.getColumn7());
        sacReviewBO.setColumn8(entity.getColumn8());
        sacReviewBO.setColumn9(entity.getColumn9());
        sacReviewBO.setColumn10(entity.getColumn10());
        sacReviewBO.setColumn11(entity.getColumn11());
        sacReviewBO.setColumn12(entity.getColumn12());
        sacReviewBO.setColumn13(entity.getColumn13());
        sacReviewBO.setColumn14(entity.getColumn14());
        sacReviewBO.setColumn15(entity.getColumn15());
        sacReviewBO.setColumn16(entity.getColumn16());
        sacReviewBO.setColumn17(entity.getColumn17());
        sacReviewBO.setColumn18(entity.getColumn18());
        sacReviewBO.setColumn19(entity.getColumn19());
        sacReviewBO.setColumn20(entity.getColumn20());
        sacReviewBO.setBigColumn1(entity.getBigColumn1());
        sacReviewBO.setBigColumn2(entity.getBigColumn2());
        sacReviewBO.setBigColumn3(entity.getBigColumn3());
        sacReviewBO.setBigColumn4(entity.getBigColumn4());
        sacReviewBO.setBigColumn5(entity.getBigColumn5());
        sacReviewBO.setExtendsJson(entity.getExtendsJson());
        sacReviewBO.setOemId(entity.getOemId());
        sacReviewBO.setGroupId(entity.getGroupId());
        sacReviewBO.setCreator(entity.getCreator());
        sacReviewBO.setCreatedName(entity.getCreatedName());
        sacReviewBO.setCreatedDate(entity.getCreatedDate());
        sacReviewBO.setModifier(entity.getModifier());
        sacReviewBO.setModifyName(entity.getModifyName());
        sacReviewBO.setLastUpdatedDate(entity.getLastUpdatedDate());
        sacReviewBO.setIsEnable(entity.getIsEnable());
        sacReviewBO.setUpdateControlId(entity.getUpdateControlId());
        sacReviewBO.setProvinceCode(entity.getProvinceCode());
        sacReviewBO.setProvinceName(entity.getProvinceName());
        sacReviewBO.setCityCode(entity.getCityCode());
        sacReviewBO.setCityName(entity.getCityName());
        sacReviewBO.setCountyCode(entity.getCountyCode());
        sacReviewBO.setCountyName(entity.getCountyName());
        sacReviewBO.setCityFirmCode(entity.getCityFirmCode());
        sacReviewBO.setCityFirmName(entity.getCityFirmName());
        sacReviewBO.setManageLabelCode(entity.getManageLabelCode());
        sacReviewBO.setManageLabelName(entity.getManageLabelName());
        sacReviewBO.setFirstReasonCode(entity.getFirstReasonCode());
        sacReviewBO.setFirstReasonName(entity.getFirstReasonName());
        return sacReviewBO;

    }

    public static List<SacReviewBO> conventBO(List<SacReview> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        return entityList.stream()
                .map(entity -> {
                    SacReviewBO bo = conventBO(entity);
                    return bo;
                })
                .collect(Collectors.toList());
    }

    public SacReviewHisBO convent(SacReviewBO bo) {
        SacReviewHisBO sacReviewHisBO = new SacReviewHisBO();
        sacReviewHisBO.setRemark(bo.getRemark());
        sacReviewHisBO.setUserStage(bo.getUserStage());
        sacReviewHisBO.setPreviousUserStage(bo.getPreviousUserStage());
        sacReviewHisBO.setClueLevel(bo.getClueLevel());
        sacReviewHisBO.setIntentVehicleCode(bo.getIntentVehicleCode());
        sacReviewHisBO.setCompetitiveVehicleCode(bo.getCompetitiveVehicleCode());
        sacReviewHisBO.setLocation(bo.getLocation());
        sacReviewHisBO.setClueSource(bo.getClueSource());
        sacReviewHisBO.setKnowDate(bo.getKnowDate());
        sacReviewHisBO.setToStoreDate(bo.getToStoreDate());
        sacReviewHisBO.setTestDriveDate(bo.getTestDriveDate());
        sacReviewHisBO.setPlaceOrderDate(bo.getPlaceOrderDate());
        sacReviewHisBO.setDefeatFlag(bo.getDefeatFlag());
        sacReviewHisBO.setReviewId(bo.getReviewId());
        sacReviewHisBO.setOrgCode(bo.getOrgCode());
        sacReviewHisBO.setOrgName(bo.getOrgName());
        sacReviewHisBO.setBillType(bo.getBillType());
        sacReviewHisBO.setBillTypeName(bo.getBillTypeName());
        sacReviewHisBO.setBusinessType(bo.getBusinessType());
        sacReviewHisBO.setBusinessTypeName(bo.getBusinessTypeName());
        sacReviewHisBO.setInfoChanMCode(bo.getInfoChanMCode());
        sacReviewHisBO.setInfoChanMName(bo.getInfoChanMName());
        sacReviewHisBO.setInfoChanDCode(bo.getInfoChanDCode());
        sacReviewHisBO.setInfoChanDName(bo.getInfoChanDName());
        sacReviewHisBO.setInfoChanDdCode(bo.getInfoChanDdCode());
        sacReviewHisBO.setInfoChanDdName(bo.getInfoChanDdName());
        sacReviewHisBO.setChannelCode(bo.getChannelCode());
        sacReviewHisBO.setChannelName(bo.getChannelName());
        sacReviewHisBO.setBillCode(bo.getBillCode());
        sacReviewHisBO.setPlanReviewTime(bo.getPlanReviewTime());
        sacReviewHisBO.setReviewTime(bo.getReviewTime());
        sacReviewHisBO.setLastReviewTime(bo.getLastReviewTime());
        sacReviewHisBO.setOverReviewTime(bo.getOverReviewTime());
        sacReviewHisBO.setPlanComeTime(bo.getPlanComeTime());
        sacReviewHisBO.setFactComeTime(bo.getFactComeTime());
        sacReviewHisBO.setIsCome(bo.getIsCome());
        sacReviewHisBO.setAssignStatus(bo.getAssignStatus());
        sacReviewHisBO.setAssignStatusName(bo.getAssignStatusName());
        sacReviewHisBO.setAssignTime(bo.getAssignTime());
        sacReviewHisBO.setAssignPersonId(bo.getAssignPersonId());
        sacReviewHisBO.setAssignPersonName(bo.getAssignPersonName());
        sacReviewHisBO.setReviewPersonId(bo.getReviewPersonId());
        sacReviewHisBO.setReviewPersonName(bo.getReviewPersonName());
        sacReviewHisBO.setReviewDesc(bo.getReviewDesc());
        sacReviewHisBO.setReviewStatus(bo.getReviewStatus());
        sacReviewHisBO.setReviewStatusName(bo.getReviewStatusName());
        sacReviewHisBO.setCustId(bo.getCustId());
        sacReviewHisBO.setCustName(bo.getCustName());
        sacReviewHisBO.setPhone(bo.getPhone());
        sacReviewHisBO.setGender(bo.getGender());
        sacReviewHisBO.setGenderName(bo.getGenderName());
        sacReviewHisBO.setTouchStatus(bo.getTouchStatus());
        sacReviewHisBO.setTouchStatusName(bo.getTouchStatusName());
        sacReviewHisBO.setErrorReasonCode(bo.getErrorReasonCode());
        sacReviewHisBO.setErrorReasonName(bo.getErrorReasonName());
        sacReviewHisBO.setNodeCode(bo.getNodeCode());
        sacReviewHisBO.setNodeName(bo.getNodeName());
        sacReviewHisBO.setSendDlrCode(bo.getSendDlrCode());
        sacReviewHisBO.setSendDlrShortName(bo.getSendDlrShortName());
        sacReviewHisBO.setSendTime(bo.getSendTime());
        sacReviewHisBO.setIntenLevelCode(bo.getIntenLevelCode());
        sacReviewHisBO.setIntenLevelName(bo.getIntenLevelName());
        sacReviewHisBO.setIntenBrandCode(bo.getIntenBrandCode());
        sacReviewHisBO.setIntenBrandName(bo.getIntenBrandName());
        sacReviewHisBO.setIntenSeriesCode(bo.getIntenSeriesCode());
        sacReviewHisBO.setIntenSeriesName(bo.getIntenSeriesName());
        sacReviewHisBO.setIntenCarTypeCode(bo.getIntenCarTypeCode());
        sacReviewHisBO.setIntenCarTypeName(bo.getIntenCarTypeName());
        sacReviewHisBO.setColumn1(bo.getColumn1());
        sacReviewHisBO.setColumn2(bo.getColumn2());
        sacReviewHisBO.setColumn3(bo.getColumn3());
        sacReviewHisBO.setColumn4(bo.getColumn4());
        sacReviewHisBO.setColumn5(bo.getColumn5());
        sacReviewHisBO.setColumn6(bo.getColumn6());
        sacReviewHisBO.setColumn7(bo.getColumn7());
        sacReviewHisBO.setColumn8(bo.getColumn8());
        sacReviewHisBO.setColumn9(bo.getColumn9());
        sacReviewHisBO.setColumn10(bo.getColumn10());
        sacReviewHisBO.setColumn11(bo.getColumn11());
        sacReviewHisBO.setColumn12(bo.getColumn12());
        sacReviewHisBO.setColumn13(bo.getColumn13());
        sacReviewHisBO.setColumn14(bo.getColumn14());
        sacReviewHisBO.setColumn15(bo.getColumn15());
        sacReviewHisBO.setColumn16(bo.getColumn16());
        sacReviewHisBO.setColumn17(bo.getColumn17());
        sacReviewHisBO.setColumn18(bo.getColumn18());
        sacReviewHisBO.setColumn19(bo.getColumn19());
        sacReviewHisBO.setColumn20(bo.getColumn20());
        sacReviewHisBO.setBigColumn1(bo.getBigColumn1());
        sacReviewHisBO.setBigColumn2(bo.getBigColumn2());
        sacReviewHisBO.setBigColumn3(bo.getBigColumn3());
        sacReviewHisBO.setBigColumn4(bo.getBigColumn4());
        sacReviewHisBO.setBigColumn5(bo.getBigColumn5());
        sacReviewHisBO.setExtendsJson(bo.getExtendsJson());
        sacReviewHisBO.setOemId(bo.getOemId());
        sacReviewHisBO.setGroupId(bo.getGroupId());
        sacReviewHisBO.setCreator(bo.getCreator());
        sacReviewHisBO.setCreatedName(bo.getCreatedName());
        sacReviewHisBO.setCreatedDate(bo.getCreatedDate());
        sacReviewHisBO.setModifier(bo.getModifier());
        sacReviewHisBO.setModifyName(bo.getModifyName());
        sacReviewHisBO.setLastUpdatedDate(bo.getLastUpdatedDate());
        sacReviewHisBO.setIsEnable(bo.getIsEnable());
        sacReviewHisBO.setUpdateControlId(bo.getUpdateControlId());
        sacReviewHisBO.setProvinceCode(bo.getProvinceCode());
        sacReviewHisBO.setProvinceName(bo.getProvinceName());
        sacReviewHisBO.setCityCode(bo.getCityCode());
        sacReviewHisBO.setCityName(bo.getCityName());
        sacReviewHisBO.setCountyCode(bo.getCountyCode());
        sacReviewHisBO.setCountyName(bo.getCountyName());
        sacReviewHisBO.setCityFirmCode(bo.getCityFirmCode());
        sacReviewHisBO.setCityFirmName(bo.getCityFirmName());
        sacReviewHisBO.setManageLabelCode(bo.getManageLabelCode());
        sacReviewHisBO.setManageLabelName(bo.getManageLabelName());
        sacReviewHisBO.setFirstReasonCode(bo.getFirstReasonCode());
        sacReviewHisBO.setFirstReasonName(bo.getFirstReasonName());
        return sacReviewHisBO;

    }
}
