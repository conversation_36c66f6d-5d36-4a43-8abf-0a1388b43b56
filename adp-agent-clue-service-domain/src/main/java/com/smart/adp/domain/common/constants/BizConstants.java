package com.smart.adp.domain.common.constants;

import com.mybatisflex.core.util.CollectionUtil;

import java.util.List;
import java.util.Set;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/27
 */
public interface BizConstants {

    Set<String> ARRIVE_REVIEW_TYPE_CODE_SET = CollectionUtil.newHashSet("2", "16");

    String DEFEAT_SENCE_CODE = "8";

    List<String> FIX_RESUME_SENCE_CODE_LIST = CollectionUtil.newArrayList("2", "8", "16");

    List<String> FIX_ORDER_STATE_LIST = CollectionUtil.newArrayList("220", "800");

    String ORDER_STATE_ORDER = "220";

    String ORDER_STATE_DELIVERY = "800";

    String ORDER_STATE_RETURN = "221";

    String ARRIVE_CHANNEL_CODE = "agent_nature";

    List<String> OTHER_EVENT_LIST = CollectionUtil.newArrayList("mini_program_open",
            "mini_program_resign_active",
            "open_mobile_app",
            "mobile_app_resign_active",
            "c_aiche_wishlist",
            "c_fin_my",
            "c_aiche_home",
            "c_basic_config",
            "c_fin_exterior",
            "c_fin_interior",
            "c_fin_package");

    List<String> CRUX_EVENT_LIST = CollectionUtil.newArrayList("c_media_source",
            "c_mp_leads",
            "c_finish_config",
            "c_save_config",
            "c_config_save",
            "c_wishlist",
            "c_finance",
            "c_use_finance",
            "c_deposit_pay",
            "c_order_pay",
            "c_interest_follow_leads",
            "c_appoint_car",
            "c_followup_task_start");

    String CLUE_SEARCH_RESUME_INNER_HIT_NAME = "resumes";

    long LONG_DEFAULT_VALUE = 0L;

    String CLUE_HOT_CODE = "Hot";

    String CLUE_WARM_CODE = "Warm";

    String CLUE_COLD_CODE = "Cold";

    String TRANSFER_DLR_APPLY_TYPE_CODE = "2";

    Function<String, String> clueTagRedisKey = id -> "CLUE:TAG:" + id;

    String NEGATIVE_PREFIX = "~";

    String ES_CLUE_RESUME_DESC_PATH = "custResumeList.content";

    Integer ES_INNER_HIT_SIZE = 20;

}
