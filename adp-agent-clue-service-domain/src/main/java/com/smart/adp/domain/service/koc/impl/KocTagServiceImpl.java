package com.smart.adp.domain.service.koc.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.smart.adp.domain.bo.koc.KocTagBO;
import com.smart.adp.domain.entity.koc.SacTagInfo;
import com.smart.adp.domain.enums.KocTagStatusEnum;
import com.smart.adp.domain.gateway.koc.KocTagGateway;
import com.smart.adp.domain.service.koc.IKocTagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: KOC标签领域服务实现
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Slf4j
@Service
public class KocTagServiceImpl implements IKocTagService {

    @Autowired
    private KocTagGateway kocTagGateway;

    @Override
    public SacTagInfo getTagById(String tagId) {
        if (StrUtil.isBlank(tagId)) {
            return null;
        }
        return kocTagGateway.findById(tagId);
    }

    @Override
    public List<KocTagBO> getTagTree() {
        return kocTagGateway.findTagTree();
    }

    @Override
    public List<KocTagBO> getChildTags(String parentTagId) {
        List<SacTagInfo> tagInfos = kocTagGateway.findByParentId(parentTagId);
        if (CollectionUtil.isEmpty(tagInfos)) {
            return new ArrayList<>();
        }
        
        return tagInfos.stream()
                .map(KocTagBO::buildFromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<KocTagBO> getTagsByLevel(Integer tagLevel) {
        if (tagLevel == null || tagLevel < 1 || tagLevel > 3) {
            return new ArrayList<>();
        }
        
        List<SacTagInfo> tagInfos = kocTagGateway.findByLevel(tagLevel);
        if (CollectionUtil.isEmpty(tagInfos)) {
            return new ArrayList<>();
        }
        
        return tagInfos.stream()
                .map(KocTagBO::buildFromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<KocTagBO> searchTags(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new ArrayList<>();
        }
        
        List<SacTagInfo> tagInfos = kocTagGateway.findByNameLike(keyword);
        if (CollectionUtil.isEmpty(tagInfos)) {
            return new ArrayList<>();
        }
        
        return tagInfos.stream()
                .map(KocTagBO::buildFromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<KocTagBO> getAllEnabledTags() {
        List<SacTagInfo> tagInfos = kocTagGateway.findAllEnabled();
        if (CollectionUtil.isEmpty(tagInfos)) {
            return new ArrayList<>();
        }
        
        return tagInfos.stream()
                .map(KocTagBO::buildFromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Boolean createTag(KocTagBO tagBO) {
        if (ObjectUtil.isEmpty(tagBO) || StrUtil.isBlank(tagBO.getTagName())) {
            log.warn("创建标签失败：标签信息为空");
            return false;
        }

        // 验证标签名称是否可用
        if (!validateTagName(tagBO.getTagName(), tagBO.getParentTagId(), null)) {
            log.warn("创建标签失败：标签名称已存在, tagName={}, parentTagId={}", 
                    tagBO.getTagName(), tagBO.getParentTagId());
            return false;
        }

        // 验证标签层级
        if (!validateTagLevel(tagBO.getParentTagId(), tagBO.getTagLevel())) {
            log.warn("创建标签失败：标签层级不正确, parentTagId={}, tagLevel={}", 
                    tagBO.getParentTagId(), tagBO.getTagLevel());
            return false;
        }

        // 构建完整路径
        String fullPath = buildTagFullPath(tagBO.getTagName(), tagBO.getParentTagId());
        tagBO.setFullPath(fullPath);

        // 设置默认状态为启用
        if (tagBO.getTagStatus() == null) {
            tagBO.setTagStatus(KocTagStatusEnum.ENABLED.getCode());
        }

        return kocTagGateway.save(tagBO);
    }

    @Override
    public Boolean updateTag(KocTagBO tagBO) {
        if (ObjectUtil.isEmpty(tagBO) || StrUtil.isBlank(tagBO.getTagId())) {
            log.warn("更新标签失败：标签信息为空");
            return false;
        }

        // 验证标签是否存在
        SacTagInfo existingTag = getTagById(tagBO.getTagId());
        if (ObjectUtil.isEmpty(existingTag)) {
            log.warn("更新标签失败：标签不存在, tagId={}", tagBO.getTagId());
            return false;
        }

        // 验证标签名称是否可用（排除自己）
        if (!validateTagName(tagBO.getTagName(), tagBO.getParentTagId(), tagBO.getTagId())) {
            log.warn("更新标签失败：标签名称已存在, tagName={}, parentTagId={}", 
                    tagBO.getTagName(), tagBO.getParentTagId());
            return false;
        }

        // 重新构建完整路径
        String fullPath = buildTagFullPath(tagBO.getTagName(), tagBO.getParentTagId());
        tagBO.setFullPath(fullPath);

        return kocTagGateway.update(tagBO);
    }

    @Override
    public Boolean deleteTag(String tagId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(tagId)) {
            log.warn("删除标签失败：标签ID为空");
            return false;
        }

        // 验证标签是否可以删除
        if (!validateTagDeletable(tagId)) {
            log.warn("删除标签失败：标签正在使用中或有子标签, tagId={}", tagId);
            return false;
        }

        return kocTagGateway.deleteById(tagId);
    }

    @Override
    public Boolean batchDeleteTags(List<String> tagIds, String operatorId, String operatorName) {
        if (CollectionUtil.isEmpty(tagIds)) {
            log.warn("批量删除标签失败：标签ID列表为空");
            return false;
        }

        // 验证所有标签是否可以删除
        for (String tagId : tagIds) {
            if (!validateTagDeletable(tagId)) {
                log.warn("批量删除标签失败：标签正在使用中或有子标签, tagId={}", tagId);
                return false;
            }
        }

        return kocTagGateway.batchDelete(tagIds);
    }

    @Override
    public Boolean enableTag(String tagId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(tagId)) {
            log.warn("启用标签失败：标签ID为空");
            return false;
        }

        return kocTagGateway.updateStatus(tagId, KocTagStatusEnum.ENABLED.getCode());
    }

    @Override
    public Boolean disableTag(String tagId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(tagId)) {
            log.warn("禁用标签失败：标签ID为空");
            return false;
        }

        return kocTagGateway.updateStatus(tagId, KocTagStatusEnum.DISABLED.getCode());
    }

    @Override
    public Boolean validateTagName(String tagName, String parentTagId, String excludeTagId) {
        if (StrUtil.isBlank(tagName)) {
            return false;
        }
        return !kocTagGateway.existsByName(tagName, parentTagId, excludeTagId);
    }

    @Override
    public Boolean validateTagDeletable(String tagId) {
        if (StrUtil.isBlank(tagId)) {
            return false;
        }

        // 检查是否有子标签
        List<SacTagInfo> childTags = kocTagGateway.findByParentId(tagId);
        if (CollectionUtil.isNotEmpty(childTags)) {
            return false;
        }

        // 检查是否被用户使用
        Integer usageCount = kocTagGateway.countUsage(tagId);
        return usageCount == null || usageCount == 0;
    }

    @Override
    public String buildTagFullPath(String tagName, String parentTagId) {
        if (StrUtil.isBlank(tagName)) {
            return "";
        }

        if (StrUtil.isBlank(parentTagId)) {
            return tagName;
        }

        SacTagInfo parentTag = getTagById(parentTagId);
        if (ObjectUtil.isEmpty(parentTag)) {
            return tagName;
        }

        return parentTag.getFullPath() + "/" + tagName;
    }

    @Override
    public List<KocTagBO> getTagsWithUsageCount(List<String> tagIds) {
        if (CollectionUtil.isEmpty(tagIds)) {
            return new ArrayList<>();
        }

        return kocTagGateway.batchCountUsage(tagIds);
    }

    @Override
    public Boolean validateTagLevel(String parentTagId, Integer targetLevel) {
        if (targetLevel == null || targetLevel < 1 || targetLevel > 3) {
            return false;
        }

        // 如果是一级标签，父标签ID应该为空
        if (targetLevel == 1) {
            return StrUtil.isBlank(parentTagId);
        }

        // 如果不是一级标签，必须有父标签
        if (StrUtil.isBlank(parentTagId)) {
            return false;
        }

        SacTagInfo parentTag = getTagById(parentTagId);
        if (ObjectUtil.isEmpty(parentTag)) {
            return false;
        }

        // 验证层级关系：子标签层级 = 父标签层级 + 1
        return targetLevel.equals(parentTag.getTagLevel() + 1);
    }
}
