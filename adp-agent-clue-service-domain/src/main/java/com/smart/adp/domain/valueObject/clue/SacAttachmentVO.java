package com.smart.adp.domain.valueObject.clue;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.bo.clue.SacAttachmentBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.smart.adp.domain.valueObject.clue.table.SacAttachmentVOTableDef.SAC_ATTACHMENT_VO;

/**
 * 附件表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "t_sac_attachment", schema = "csc")
public class SacAttachmentVO {

    /**
     * 附件ID
     */
    @Id
    private String attachmentId;

    /**
     * 业务单ID
     */
    @Column(value = "BILL_ID")
    private String billId;

    /**
     * 业务单类型
     */
    @Column(value = "BILL_TYPE")
    private String billType;

    /**
     * 附件名称
     */
    @Column(value = "FILE_NAME")
    private String fileName;

    /**
     * 附件扩展名
     */
    @Column(value = "FILE_EXTENSION")
    private String fileExtension;

    /**
     * 附件路径
     */
    @Column(value = "FILE_PATH")
    private String filePath;

    /**
     * 扩展字段1
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private java.util.Date createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private java.util.Date lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacAttachmentVO conditions(QueryWrapper wrapper, SacAttachmentBO attachmentBO) {
        wrapper.and(SAC_ATTACHMENT_VO.ATTACHMENT_ID.eq(getAttachmentId(), StringUtil::hasText))
                .and(SAC_ATTACHMENT_VO.ATTACHMENT_ID.in(attachmentBO.getAttachmentIdList(), CollectionUtil::isNotEmpty))
                .and(SAC_ATTACHMENT_VO.BILL_ID.eq(getBillId(), StringUtil::hasText))
                .and(SAC_ATTACHMENT_VO.BILL_TYPE.eq(getBillType(), StringUtil::hasText));
        return this;
    }
}
