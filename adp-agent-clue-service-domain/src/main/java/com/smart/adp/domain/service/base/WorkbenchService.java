package com.smart.adp.domain.service.base;

import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.enums.StatisticsTypeEnum;
import com.smart.adp.domain.valueObject.base.StatisticsVO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
public interface WorkbenchService {

    /**
     * 执行统计
     *
     * @param type 统计类型
     * @param user 当前用户
     * @return com.smart.adp.domain.valueObject.base.StatisticsVO
     */
    StatisticsVO doStatistics(StatisticsTypeEnum type, UserBusiEntity user);
}
