package com.smart.adp.domain.valueObject.base;

import com.smart.adp.domain.enums.StatisticsTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/11
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "统计 VO")
public class StatisticsVO {

    /**
     * 统计类型
     */
    @Schema(description = "统计类型")
    private StatisticsTypeEnum type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long num;
}
