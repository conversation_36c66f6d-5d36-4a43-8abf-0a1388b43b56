package com.smart.adp.domain.enums;

/**
 * 线索状态枚举
 * 目前adp的线索状态只用到了
 * 1 2 7 10
 */
public enum ClueStatusEnum {
    AWAITING_ALLOCATION("1", "待分配"),

    AWAITING_FOLLOW_UP("2", "待回访"),

    DUPLICATE_LEADS("5", "重复留资"),

    DEFEATED_APPLICATION("7", "战败申请"),

    OUT_OF_CONTROL_APPLICATION("8", "失控申请"),

    INVALID_APPLICATION("9", "无效申请"),

    DEFEATED("10", "战败"),

    OUT_OF_CONTROL("11", "失控"),

    INVALID_LEAD("12", "无效线索"),

    CLOSED("13", "关闭"),

    SCHEDULED_VISIT("14", "预约到店");

    private final String code;

    private final String desc;

    ClueStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举项
     *
     * @param code
     * @return
     */
    public static ClueStatusEnum getByCode(String code) {
        for (ClueStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No ClueStatus with code: " + code);
    }

    /**
     * 根据描述获取枚举
     * @param desc
     * @return
     */
    public static ClueStatusEnum fromDesc(String desc) {
        for (ClueStatusEnum status : values()) {
            if (status.getDesc().equals(desc)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No ClueStatus with desc: " + desc);
    }

    /**
     * 根据 code 判断是否是战败线索
     * （包括战败和战败申请）
     *
     * @param code 状态码
     * @return 是否是战败线索
     */
    public static boolean isDefeatedClue(String code) {
        return DEFEATED_APPLICATION.getCode().equals(code) || DEFEATED.getCode().equals(code);
    }

    /**
     * 根据 code 判断是否是非战败线索
     * （与战败无关的状态）
     *
     * @param code 状态码
     * @return 是否是非战败线索
     */
    public static boolean isNonDefeatedClue(String code) {
        return !isDefeatedClue(code);
    }

    /**
     * 根据code判断是否是战败申请状态
     *
     * @param code 状态码
     * @return 是否是战败申请状态
     */
    public static boolean isDefeatedApplication(String code) {
        return DEFEATED_APPLICATION.getCode().equals(code);
    }

}
