package com.smart.adp.domain.entity.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/3/12 15:00
 * @description 代理商线索来源配置
 **/
@Table(value = "agent_clue_source", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class AgentClueSource implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Id("id")
    private Long id;


    /**
     * 竞品车型名称
     */
    @Column("code")
    private String code;


    /**
     * 竞品车型名称
     */
    @Column("clue_source_name")
    private String clueSourceName;

    /**
     * 竞品车型类型
     */
    @Column("clue_source_type")
    private String clueSourceType;

}
