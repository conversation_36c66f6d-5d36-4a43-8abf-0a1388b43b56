package com.smart.adp.domain.valueObject.clue;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.bo.clue.SacOnecustInfoEventBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Objects;

import static com.smart.adp.domain.valueObject.clue.table.SacOnecustInfoEventVOTableDef.SAC_ONECUST_INFO_EVENT_VO;


/**
 * 客户事件表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(value = "t_sac_onecust_info_event", schema = "csc")
public class SacOnecustInfoEventVO {

    /**
     * 用户事件ID
     */
    @Id
    private String infoEventId;

    /**
     * 事件编码
     */
    @Column(value = "EVENT_CODE")
    private String eventCode;

    /**
     * 事件名称
     */
    @Column(value = "EVENT_NAME")
    private String eventName;

    /**
     * 手机号
     */
    @Column(value = "MOBILE")
    private String mobile;

    /**
     * 时间发生事件
     */
    @Column(value = "EVENT_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime eventTime;

    /**
     * smartId
     */
    @Column(value = "SMART_ID")
    private String smartId;

    /**
     * 创建人
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @Column(value = "CREATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdDate;

    /**
     * 修改人
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 最后一次更新时间
     */
    @Column(value = "LAST_UPDATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacOnecustInfoEventVO conditions(QueryWrapper wrapper, SacOnecustInfoEventBO resumeBO) {
        wrapper.and(SAC_ONECUST_INFO_EVENT_VO.EVENT_CODE.eq(getEventCode(), StringUtil::hasText))
                .and(SAC_ONECUST_INFO_EVENT_VO.EVENT_NAME.eq(getEventName(), StringUtil::hasText))
                .and(SAC_ONECUST_INFO_EVENT_VO.MOBILE.eq(getMobile(), StringUtil::hasText))
                .and(SAC_ONECUST_INFO_EVENT_VO.SMART_ID.eq(getSmartId(), StringUtil::hasText))
                .and(SAC_ONECUST_INFO_EVENT_VO.INFO_EVENT_ID.eq(getInfoEventId(), StringUtil::hasText))
                .and(SAC_ONECUST_INFO_EVENT_VO.EVENT_TIME.ge(resumeBO.getEventTimeBegin(), Objects::nonNull))
                .and(SAC_ONECUST_INFO_EVENT_VO.EVENT_TIME.le(resumeBO.getEventTimeEnd(), Objects::nonNull))
                .and(SAC_ONECUST_INFO_EVENT_VO.EVENT_CODE.in(resumeBO.getListEventCode(), CollUtil::isNotEmpty));
        return this;
    }
}
