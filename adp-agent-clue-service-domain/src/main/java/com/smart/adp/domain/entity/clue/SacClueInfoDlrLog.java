package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/10 22:52
 * @description
 **/
@Table(value = "t_sac_clue_info_dlr_log", schema = "csc")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
public class SacClueInfoDlrLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id("ID")
    private String id;

    @Column("PHONE")
    private String phone;

    @Column("SYSTEM_PARAM")
    private String systemParam;

    @Column("SYSTEM_SOURCE")
    private String systemSource;

    @Column("SYSTEM_RECORD")
    private String systemRecord;

    @Column("OEM_ID")
    private String oemId;

    @Column("GROUP_ID")
    private String groupId;

    @Column("OEM_CODE")
    private String oemCode;

    @Column("GROUP_CODE")
    private String groupCode;

    @Column("IS_ENABLE")
    private String isEnable;

    @Column("CREATOR")
    private String creator;

    @Column("CREATED_NAME")
    private String createdName;

    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    @Column("MODIFIER")
    private String modifier;

    @Column("MODIFY_NAME")
    private String modifierName;

    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    @Column("SDP_USER_ID")
    private String sdpUserId;

    @Column("SDP_ORG_ID")
    private String sdpOrgId;

    @Column("UPDATE_CONTROL_ID")
    private String updateControlId;

}
