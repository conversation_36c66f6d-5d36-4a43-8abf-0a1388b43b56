package com.smart.adp.domain.valueObject.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/3/6 20:48
 * @description
 **/
@Table(value = "t_sac_system_config_value", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class SystemConfigValue implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column("CONFIG_VALUE_ID")
    private String configValueId;

    @Column("CONFIG_ID")
    private String configId;

    @Column("ORG_CODE")
    private String orgCode;

    @Column("ORG_NAME")
    private String orgName;

    @Column("VALUE_CODE")
    private String valueCode;

    @Column("VALUE_NAME")
    private String valueName;

    @Column("IS_ENABLE")
    private String isEnable;
}
