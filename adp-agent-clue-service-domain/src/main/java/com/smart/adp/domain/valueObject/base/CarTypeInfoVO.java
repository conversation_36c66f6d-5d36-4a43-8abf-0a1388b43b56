package com.smart.adp.domain.valueObject.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * date 2025/3/18 15:39
 * @description
 **/
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "车型配置信息 VO")
public class CarTypeInfoVO {

    /**
     * 车型小类编码
     */
    @Schema(description = "车型小类编码，如HC11,HX11,HY11")
    private String carTypeCode;

    /**
     * 车型code
     */
    @Schema(description = "车型小类中文名称，如HC11,HX11,HY11")
    private String carTypeCN;

    /**
     * 车型小类编码对应描述
     */
    @Schema(description = "车型小类编码对应描述，如#1,#3,#5")
    private String carTypeDesc;

}
