package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.clue.CustEventFlowBO;
import com.smart.adp.domain.entity.clue.CustEventFlow;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
public interface CustEventFlowGateway {
    /**
     * 根据客户id查询旅程事件集合
     *
     * @param custId
     * @return
     */
    List<CustEventFlow> queryListEventByCustId(String custId);

    /**
     * 根据客户id查询旅程事件
     *
     * @param custId
     * @return
     */
    CustEventFlow queryEventByCustId(String custId);

    /**
     * 根据客户id查询旅程集合
     *
     * @param listCustId
     * @return
     */
    List<CustEventFlow> queryEventByCustId(List<String> listCustId);

    /**
     * 根据客户id查询旅程集合
     *
     * @param boParam
     * @return
     */
    List<CustEventFlow> queryEventByCustId(CustEventFlowBO boParam);

    /**
     * 根据客户id查询旅程集合，只查询每个custId的最新的一条数据
     *
     * @param boParam
     * @return
     */
    List<CustEventFlow> queryEventByCustIdLast(CustEventFlowBO boParam);

    /**
     * 是否存在
     *
     * @param custId 客户 id
     * @param type   事件类型
     * @return boolean
     */
    boolean exists(String custId, Integer type);

    int insertSelective(CustEventFlow flow);

    int insertBatchSelective(List<CustEventFlow> flows);

    CustEventFlow selectOne(QueryWrapper wrapper);
}
