package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.bo.clue.CustEventFlowBO;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import com.smart.adp.domain.gateway.clue.CustEventFlowGateway;
import com.smart.adp.domain.service.clue.ICustEventFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description: 线索用户旅程service实现
 * @Author: rik.ren
 * @Date: 2025/3/17 1:29
 **/
@Service
public class CustEventFlowServiceImpl implements ICustEventFlowService {
    @Autowired
    private CustEventFlowGateway custEventFlowGateway;

    /**
     * 根据客户id查询旅程事件
     *
     * @param boParam
     * @return
     */
    @Override
    public List<CustEventFlowBO> queryEventByCondition(CustEventFlowBO boParam) {
        if (ObjectUtil.isEmpty(boParam)) {
            return Collections.emptyList();
        }
        List<CustEventFlow> listEntity = custEventFlowGateway.queryEventByCustId(boParam);
        return CustEventFlowBO.conventBO(listEntity);
    }

    /**
     * 根据客户id查询旅程事件
     *
     * @param boParam
     * @return
     */
    @Override
    public List<CustEventFlowBO> queryEventByCustIdLast(CustEventFlowBO boParam) {
        if (ObjectUtil.isEmpty(boParam)) {
            return Collections.emptyList();
        }
        List<CustEventFlow> listEntity = custEventFlowGateway.queryEventByCustIdLast(boParam);
        return CustEventFlowBO.conventBO(listEntity);
    }
}
