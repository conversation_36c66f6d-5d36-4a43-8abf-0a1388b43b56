package com.smart.adp.domain.service.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacUserGroupDetailBO;
import com.smart.adp.domain.entity.clue.SacUserGroupDetailEntity;

import java.util.List;

/**
 * @Description: 用户分组详情service接口
 * @Author: rik.ren
 * @Date: 2025/3/9 15:58
 **/
public interface ISacUserGroupDetailService {
    /**
     * 获取用户分组详情对象
     *
     * @param param
     * @param needColumn
     * @return
     */
    SacUserGroupDetailEntity getEntity(SacUserGroupDetailEntity param, QueryColumn... needColumn);

    /**
     * 根据条件查询线索所在的分组
     * 这个查询的是在指定专家下面的所在的分组
     *
     * @param param
     * @param needColumn
     * @return
     */
    List<SacUserGroupDetailBO> queryGroupDetail(SacUserGroupDetailBO param, QueryColumn... needColumn);

    /**
     * 批量获取用户分组详情集合
     *
     * @param param
     * @param needColumn
     * @return
     */
    List<SacUserGroupDetailEntity> queryEntity(SacUserGroupDetailEntity param, QueryColumn... needColumn);

    /**
     * 更新用户分组
     *
     * @param param
     * @return
     */
    Boolean modifyUserGroupDetail(SacUserGroupDetailEntity param);
}
