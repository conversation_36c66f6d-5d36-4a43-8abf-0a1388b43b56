package com.smart.adp.domain.gateway.koc;

import com.smart.adp.domain.bo.koc.KocTagBO;
import com.smart.adp.domain.entity.koc.SacTagInfo;

import java.util.List;

/**
 * @Description: KOC标签网关接口
 * @Author: system
 * @Date: 2025/8/4
 **/
public interface KocTagGateway {

    /**
     * 根据ID查询标签信息
     *
     * @param tagId 标签ID
     * @return 标签信息
     */
    SacTagInfo findById(String tagId);

    /**
     * 根据条件查询标签列表
     *
     * @param condition 查询条件
     * @return 标签列表
     */
    List<SacTagInfo> findByCondition(SacTagInfo condition);

    /**
     * 查询所有启用的标签
     *
     * @return 标签列表
     */
    List<SacTagInfo> findAllEnabled();

    /**
     * 根据父标签ID查询子标签
     *
     * @param parentTagId 父标签ID
     * @return 子标签列表
     */
    List<SacTagInfo> findByParentId(String parentTagId);

    /**
     * 根据标签层级查询标签
     *
     * @param tagLevel 标签层级
     * @return 标签列表
     */
    List<SacTagInfo> findByLevel(Integer tagLevel);

    /**
     * 查询标签树结构
     *
     * @return 标签树
     */
    List<KocTagBO> findTagTree();

    /**
     * 根据标签名称模糊查询
     *
     * @param tagName 标签名称
     * @return 标签列表
     */
    List<SacTagInfo> findByNameLike(String tagName);

    /**
     * 保存标签信息
     *
     * @param tagInfo 标签信息
     * @return 是否成功
     */
    Boolean save(SacTagInfo tagInfo);

    /**
     * 更新标签信息
     *
     * @param tagInfo 标签信息
     * @return 是否成功
     */
    Boolean update(SacTagInfo tagInfo);

    /**
     * 删除标签信息
     *
     * @param tagId 标签ID
     * @return 是否成功
     */
    Boolean deleteById(String tagId);

    /**
     * 批量删除标签
     *
     * @param tagIds 标签ID列表
     * @return 是否成功
     */
    Boolean batchDelete(List<String> tagIds);

    /**
     * 更新标签状态
     *
     * @param tagId 标签ID
     * @param status 状态
     * @return 是否成功
     */
    Boolean updateStatus(String tagId, Integer status);

    /**
     * 检查标签名称是否存在
     *
     * @param tagName 标签名称
     * @param parentTagId 父标签ID
     * @param excludeTagId 排除的标签ID（用于编辑时排除自己）
     * @return 是否存在
     */
    Boolean existsByName(String tagName, String parentTagId, String excludeTagId);

    /**
     * 统计标签使用次数
     *
     * @param tagId 标签ID
     * @return 使用次数
     */
    Integer countUsage(String tagId);

    /**
     * 批量统计标签使用次数
     *
     * @param tagIds 标签ID列表
     * @return 使用次数映射
     */
    List<KocTagBO> batchCountUsage(List<String> tagIds);
}
