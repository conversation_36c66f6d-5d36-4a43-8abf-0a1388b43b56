package com.smart.adp.domain.service.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;

import java.util.List;

/**
 * @Description: 潜客domain服务接口
 * @Author: rik.ren
 * @Date: 2025/3/5 20:32
 **/
public interface IOneCustInfoService {
    /**
     * 获取潜客详情对象
     *
     * @param param
     * @param needColumn
     * @return
     */
    SacOnecustInfoEntity getEntity(SacOnecustInfoEntity param, QueryColumn... needColumn);

    /**
     * 批量获取潜客详情集合
     *
     * @param param
     * @param needColumn
     * @return
     */
    List<SacOnecustInfoEntity> queryEntity(SacOnecustInfoEntity param, QueryColumn... needColumn);

    /**
     * 更新潜客表
     *
     * @param param
     * @return
     */
    Boolean modifyCustomerDesc(SacOnecustInfoEntity param);

    /**
     * 更新竞品车型
     *
     * @param param
     * @return
     */
    Boolean modifyCompetitorModels(SacOnecustInfoEntity param);
}
