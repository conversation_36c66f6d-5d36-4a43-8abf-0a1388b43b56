package com.smart.adp.domain.strategy.modifyClue;

import com.smart.adp.domain.bo.clue.ClueDlrModifyBO;
import com.smart.adp.domain.service.clue.IClueDlrService;
import com.smart.adp.domain.service.clue.ISacOneCustRemarkService;
import com.smart.adp.domain.service.clue.ISacReviewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 更新意向车型
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Component
@Slf4j
public class IntendedVehicleModelModifyStrategy extends ClueModifyStrategySuper implements ClueModifyStrategy {

    @Autowired
    private IClueDlrService clueDlrService;
    @Autowired
    private ISacReviewService reviewService;
    @Autowired
    private ISacOneCustRemarkService oneCustRemarkService;

    @Override
    public Boolean modify(ClueDlrModifyBO param) {
        log.info("更新意向车型:{}", param.getValue());
        Boolean clueResult = clueDlrService.modifyClueIntendedVehicleModel(param.buildClueIntenCarTypeCodeParam());
        Boolean reviewResult = reviewService.modifyReview(param.buildReviewIntenCarTypeCodeParam());
        Boolean remarkResult;
        // 先判断数据是否存在
        if (!this.isExistRemark(param.getCustId())) {
            // 如果数据不存在，插入数据
            remarkResult = this.inserRemark(param.buildClueRemarkIntenCarTypeCodeParam());
        } else {
            remarkResult = oneCustRemarkService.modifyCustRemark(param.buildClueRemarkIntenCarTypeCodeParam());
        }
        return clueResult && reviewResult && remarkResult;
    }
}