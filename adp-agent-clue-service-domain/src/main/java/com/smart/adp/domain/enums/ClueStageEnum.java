package com.smart.adp.domain.enums;

import com.mybatisflex.core.util.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nonnull;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Getter
@AllArgsConstructor
public enum ClueStageEnum {

    /**
     * 了解
     */
    KNOW(1),

    /**
     * 到店
     */
    ARRIVE(2),

    /**
     * 试驾
     */
    DRIVE(3),

    /**
     * 下定
     */
    ORDER(4),

    /**
     * 交车
     */
    DELIVERY(5),

    /**
     * 战败
     */
    DEFEAT(6),

    ;

    private final int code;

    @Nonnull
    public static ClueStageEnum getByCode(Integer code) {
        return Arrays.stream(ClueStageEnum.values())
                     .filter(e -> Objects.equals(e.getCode(), code))
                     .findAny()
                     .orElseThrow(() -> new IllegalArgumentException("clue stage code illegal: " + code));
    }

    public static List<ClueStageEnum> getByCodes(List<Integer> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return Collections.emptyList();
        }

        return codes.stream()
                    .map(ClueStageEnum::getByCode)
                    .collect(Collectors.toList());
    }

    /**
     * get code str
     *
     * @return code str
     */
    public String getCodeStr() {
        return String.valueOf(getCode());
    }
}
