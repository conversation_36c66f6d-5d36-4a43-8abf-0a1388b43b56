package com.smart.adp.domain.bo.drive;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.smart.adp.domain.entity.drive.SacTestDriveSheetEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 试乘试驾单表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@NoArgsConstructor
@EqualsAndHashCode
public class SacTestDriveSheetBO extends SacTestDriveSheetEntity {

    /**
     * 分页属性
     */
    private Page<SacTestDriveSheetEntity> pageObj;

    /**
     * 开始时间
     */
    private LocalDateTime dateBegin;
    /**
     * 结束时间
     */
    private LocalDateTime dateEnd;

    public SacTestDriveSheetBO(Page<SacTestDriveSheetEntity> pageObj) {
        this.pageObj = pageObj;
    }

    public static SacTestDriveSheetBO conventBO(SacTestDriveSheetEntity entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        SacTestDriveSheetBO sacTestDriveSheetBO = new SacTestDriveSheetBO();
        sacTestDriveSheetBO.setTestDriveSheetId(entity.getTestDriveSheetId());
        sacTestDriveSheetBO.setTestDriveOrderNo(entity.getTestDriveOrderNo());
        sacTestDriveSheetBO.setAppointmentId(entity.getAppointmentId());
        sacTestDriveSheetBO.setTestStatus(entity.getTestStatus());
        sacTestDriveSheetBO.setRecordId(entity.getRecordId());
        sacTestDriveSheetBO.setDlrCode(entity.getDlrCode());
        sacTestDriveSheetBO.setDlrName(entity.getDlrName());
        sacTestDriveSheetBO.setSalesConsultantName(entity.getSalesConsultantName());
        sacTestDriveSheetBO.setSalesConsultantId(entity.getSalesConsultantId());
        sacTestDriveSheetBO.setIntenLevelCode(entity.getIntenLevelCode());
        sacTestDriveSheetBO.setIntenLevelName(entity.getIntenLevelName());
        sacTestDriveSheetBO.setDriverCustomerRelation(entity.getDriverCustomerRelation());
        sacTestDriveSheetBO.setDriverName(entity.getDriverName());
        sacTestDriveSheetBO.setDriverPhone(entity.getDriverPhone());
        sacTestDriveSheetBO.setDrivingLicenceType(entity.getDrivingLicenceType());
        sacTestDriveSheetBO.setDrivingLicenceNumber(entity.getDrivingLicenceNumber());
        sacTestDriveSheetBO.setAddress(entity.getAddress());
        sacTestDriveSheetBO.setDrivingLicencePhoto(entity.getDrivingLicencePhoto());
        sacTestDriveSheetBO.setTestRoadHaul(entity.getTestRoadHaul());
        sacTestDriveSheetBO.setTestStartRoadHaul(entity.getTestStartRoadHaul());
        sacTestDriveSheetBO.setTestEndRoadHaul(entity.getTestEndRoadHaul());
        sacTestDriveSheetBO.setDlrClueOrderNo(entity.getDlrClueOrderNo());
        sacTestDriveSheetBO.setCustomerName(entity.getCustomerName());
        sacTestDriveSheetBO.setCustomerId(entity.getCustomerId());
        sacTestDriveSheetBO.setCustomerPhone(entity.getCustomerPhone());
        sacTestDriveSheetBO.setCustomerSex(entity.getCustomerSex());
        sacTestDriveSheetBO.setSmallCarTypeCode(entity.getSmallCarTypeCode());
        sacTestDriveSheetBO.setSmallCarTypeName(entity.getSmallCarTypeName());
        sacTestDriveSheetBO.setPlateNumber(entity.getPlateNumber());
        sacTestDriveSheetBO.setCarVin(entity.getCarVin());
        sacTestDriveSheetBO.setTestType(entity.getTestType());
        sacTestDriveSheetBO.setAppointmentChannel(entity.getAppointmentChannel());
        sacTestDriveSheetBO.setStartTime(entity.getStartTime());
        sacTestDriveSheetBO.setEndTime(entity.getEndTime());
        sacTestDriveSheetBO.setTestDriveAgreement(entity.getTestDriveAgreement());
        sacTestDriveSheetBO.setCustomerIdNumberAgreement(entity.getCustomerIdNumberAgreement());
        sacTestDriveSheetBO.setCustomerSignatureAgreement(entity.getCustomerSignatureAgreement());
        sacTestDriveSheetBO.setDeposit(entity.getDeposit());
        sacTestDriveSheetBO.setOldTestDriveSheetId(entity.getOldTestDriveSheetId());
        sacTestDriveSheetBO.setReceiver(entity.getReceiver());
        sacTestDriveSheetBO.setReceiverCode(entity.getReceiverCode());
        sacTestDriveSheetBO.setReceiverTime(entity.getReceiverTime());
        sacTestDriveSheetBO.setOemId(entity.getOemId());
        sacTestDriveSheetBO.setGroupId(entity.getGroupId());
        sacTestDriveSheetBO.setCreator(entity.getCreator());
        sacTestDriveSheetBO.setCreatedName(entity.getCreatedName());
        sacTestDriveSheetBO.setCreatedDate(entity.getCreatedDate());
        sacTestDriveSheetBO.setModifier(entity.getModifier());
        sacTestDriveSheetBO.setModifyName(entity.getModifyName());
        sacTestDriveSheetBO.setLastUpdatedDate(entity.getLastUpdatedDate());
        sacTestDriveSheetBO.setUpdateControlId(entity.getUpdateControlId());
        sacTestDriveSheetBO.setIsEnable(entity.getIsEnable());
        sacTestDriveSheetBO.setColumn1(entity.getColumn1());
        sacTestDriveSheetBO.setColumn2(entity.getColumn2());
        sacTestDriveSheetBO.setColumn3(entity.getColumn3());
        sacTestDriveSheetBO.setColumn4(entity.getColumn4());
        sacTestDriveSheetBO.setColumn5(entity.getColumn5());
        sacTestDriveSheetBO.setColumn6(entity.getColumn6());
        sacTestDriveSheetBO.setColumn7(entity.getColumn7());
        sacTestDriveSheetBO.setColumn8(entity.getColumn8());
        sacTestDriveSheetBO.setColumn9(entity.getColumn9());
        sacTestDriveSheetBO.setColumn10(entity.getColumn10());
        sacTestDriveSheetBO.setIsCanChange(entity.getIsCanChange());
        sacTestDriveSheetBO.setOtherAgreement(entity.getOtherAgreement());
        sacTestDriveSheetBO.setImportFlag(entity.getImportFlag());
        sacTestDriveSheetBO.setRepeatFlag(entity.getRepeatFlag());
        sacTestDriveSheetBO.setEvaluateFlag(entity.getEvaluateFlag());
        sacTestDriveSheetBO.setTestDriveMethod(entity.getTestDriveMethod());
        return sacTestDriveSheetBO;
    }

    public static List<SacTestDriveSheetBO> conventBO(List<SacTestDriveSheetEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        return entityList.stream()
                .map(entity -> {
                    return conventBO(entity);
                })
                .collect(Collectors.toList());
    }

}
