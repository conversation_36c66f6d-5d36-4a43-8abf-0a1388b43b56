package com.smart.adp.domain.bo.clue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.entity.clue.SacUserGroupDetailEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import static com.smart.adp.domain.entity.clue.table.SacUserGroupDetailEntityTableDef.SAC_USER_GROUP_DETAIL_ENTITY;


/**
 * @Description: 用户分组详情
 * @Author: rik.ren
 * @Date: 2025/3/9 15:48
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SacUserGroupDetailBO extends SacUserGroupDetailEntity {

    /**
     * 分组ID
     */
    private List<String> listUserGroupId;
    /**
     * 用户分组名称
     */
    private String userGroupName;

    public static List<SacUserGroupDetailBO> conventFromList(List<SacUserGroupDetailEntity> listParam) {
        if (CollectionUtil.isEmpty(listParam)) {
            return null;
        }
        List<SacUserGroupDetailBO> listResult = new ArrayList<>();
        for (SacUserGroupDetailEntity entity : listParam) {
            SacUserGroupDetailBO detailBO = convertFromEntity(entity);
            listResult.add(detailBO);
        }
        return listResult;
    }

    public static SacUserGroupDetailBO convertFromEntity(SacUserGroupDetailEntity param) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        SacUserGroupDetailBO sacUserGroupDetailBO = new SacUserGroupDetailBO();
        sacUserGroupDetailBO.setUserGroupName(param.getUserGroupName());
        sacUserGroupDetailBO.setDetailId(param.getDetailId());
        sacUserGroupDetailBO.setUserGroupId(param.getUserGroupId());
        sacUserGroupDetailBO.setServerOrder(param.getServerOrder());
        sacUserGroupDetailBO.setCustId(param.getCustId());
        sacUserGroupDetailBO.setPhone(param.getPhone());
        sacUserGroupDetailBO.setColumn1(param.getColumn1());
        sacUserGroupDetailBO.setColumn2(param.getColumn2());
        sacUserGroupDetailBO.setColumn3(param.getColumn3());
        sacUserGroupDetailBO.setColumn4(param.getColumn4());
        sacUserGroupDetailBO.setColumn5(param.getColumn5());
        sacUserGroupDetailBO.setColumn6(param.getColumn6());
        sacUserGroupDetailBO.setColumn7(param.getColumn7());
        sacUserGroupDetailBO.setColumn8(param.getColumn8());
        sacUserGroupDetailBO.setColumn9(param.getColumn9());
        sacUserGroupDetailBO.setColumn10(param.getColumn10());
        sacUserGroupDetailBO.setMycatOpTime(param.getMycatOpTime());
        sacUserGroupDetailBO.setOemId(param.getOemId());
        sacUserGroupDetailBO.setGroupId(param.getGroupId());
        sacUserGroupDetailBO.setOemCode(param.getOemCode());
        sacUserGroupDetailBO.setGroupCode(param.getGroupCode());
        sacUserGroupDetailBO.setCreator(param.getCreator());
        sacUserGroupDetailBO.setCreatedName(param.getCreatedName());
        sacUserGroupDetailBO.setCreatedDate(param.getCreatedDate());
        sacUserGroupDetailBO.setModifier(param.getModifier());
        sacUserGroupDetailBO.setModifyName(param.getModifyName());
        sacUserGroupDetailBO.setLastUpdatedDate(param.getLastUpdatedDate());
        sacUserGroupDetailBO.setIsEnable(param.getIsEnable());
        sacUserGroupDetailBO.setSdpUserId(param.getSdpUserId());
        sacUserGroupDetailBO.setSdpOrgId(param.getSdpOrgId());
        sacUserGroupDetailBO.setUpdateControlId(param.getUpdateControlId());
        sacUserGroupDetailBO.setUserGroupName(param.getUserGroupName());
        return sacUserGroupDetailBO;
    }

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacUserGroupDetailBO conditions(QueryWrapper wrapper) {
        wrapper.and(SAC_USER_GROUP_DETAIL_ENTITY.DETAIL_ID.eq(getDetailId(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.USER_GROUP_ID.eq(getUserGroupId(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.USER_GROUP_ID.in(this.listUserGroupId != null ? this.listUserGroupId : null,
                        CollectionUtil::isNotEmpty))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.SERVER_ORDER.eq(getServerOrder(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.CUST_ID.eq(getCustId(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.PHONE.eq(getPhone(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.IS_ENABLE.eq("1"));
        return this;
    }
}
