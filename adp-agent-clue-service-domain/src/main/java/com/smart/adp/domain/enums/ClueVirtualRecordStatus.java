package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 线索虚拟外呼电话呼叫状态
 * @Author: rik.ren
 * @Date: 2025/3/16 12:06
 **/
@Getter
@AllArgsConstructor
public enum ClueVirtualRecordStatus {
    /**
     * 成功
     */
    SUCCESS(0, "成功"),
    /**
     * 忙
     */
    BUSY(1, "忙"),
    /**
     * 无应答
     */
    NO_RESPONSE(2, "无应答"),
    /**
     * 客户提前挂机
     */
    CUSTOMER_ON_HOOK(3, "客户提前挂机"),
    /**
     * 呼叫失败
     */
    CALL_FAILED(9, "呼叫失败"),
    /**
     * 主叫号码与中间号没有关联
     */
    DoesNothing(11, "主叫号码与中间号没有关联"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static ClueVirtualRecordStatus getByCode(Integer code) {
        return Arrays.stream(ClueVirtualRecordStatus.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
