package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacUserGroupDetailBO;
import com.smart.adp.domain.entity.clue.SacUserGroupDetailEntity;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;
import com.smart.adp.domain.gateway.clue.SacUserGroupDetailGateway;
import com.smart.adp.domain.gateway.clue.SacUserGroupGateway;
import com.smart.adp.domain.service.clue.ISacUserGroupDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Stream;

import static com.smart.adp.domain.entity.clue.table.SacUserGroupDetailEntityTableDef.SAC_USER_GROUP_DETAIL_ENTITY;
import static com.smart.adp.domain.entity.clue.table.SacUserGroupEntityTableDef.SAC_USER_GROUP_ENTITY;

/**
 * @Description: 用户分组详情服务实现
 * @Author: rik.ren
 * @Date: 2025/3/9 16:01
 **/
@Service
public class SacUserGroupDetailServiceImpl implements ISacUserGroupDetailService {
    @Autowired
    private SacUserGroupDetailGateway userGroupDetailGateway;
    @Autowired
    private SacUserGroupGateway userGroupGateway;

    /**
     * 获取用户分组详情对象
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public SacUserGroupDetailEntity getEntity(SacUserGroupDetailEntity param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{SAC_USER_GROUP_DETAIL_ENTITY.USER_GROUP_ID, SAC_USER_GROUP_DETAIL_ENTITY.CUST_ID};
        }
        // 1. 查询明细
        SacUserGroupDetailEntity resultDetail = userGroupDetailGateway.findByCondition(param, needColumn);
        if (ObjectUtil.isEmpty(resultDetail)) {
            return null;
        }
        // 2. 查询分组名称
        SacUserGroupEntity groupEntity = SacUserGroupEntity.builder().userGroupId(resultDetail.getUserGroupId()).build();
        List<SacUserGroupEntity> resultListGroup = userGroupGateway.findListByUserGroupId(groupEntity,
                SAC_USER_GROUP_ENTITY.USER_GROUP_ID,
                SAC_USER_GROUP_ENTITY.USER_GROUP_NAME,
                SAC_USER_GROUP_ENTITY.USER_GROUP_DESC);

        // 使用Optional避免重复操作流
        resultListGroup.stream()
                .filter(e -> e.getUserGroupId().equals(resultDetail.getUserGroupId()))
                .findFirst()
                .ifPresent(resultGroup ->
                        resultDetail.setUserGroupName(resultGroup.getUserGroupName())
                );
        return resultDetail;
    }

    @Override
    public List<SacUserGroupDetailBO> queryGroupDetail(SacUserGroupDetailBO param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{SAC_USER_GROUP_DETAIL_ENTITY.USER_GROUP_ID, SAC_USER_GROUP_DETAIL_ENTITY.CUST_ID};
        }
        // 1. 查询明细
        List<SacUserGroupDetailEntity> resultListDetail = userGroupDetailGateway.findByCondition(param, needColumn);
        if (CollectionUtil.isEmpty(resultListDetail)) {
            return null;
        }
        return SacUserGroupDetailBO.conventFromList(resultListDetail);
    }

    /**
     * 批量获取用户分组详情集合
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public List<SacUserGroupDetailEntity> queryEntity(SacUserGroupDetailEntity param, QueryColumn... needColumn) {
        return userGroupDetailGateway.findListByCondition(param, needColumn);
    }

    /**
     * 更新用户分组
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyUserGroupDetail(SacUserGroupDetailEntity param) {
        return userGroupDetailGateway.modifyUserGroupDetail(param);
    }
}
