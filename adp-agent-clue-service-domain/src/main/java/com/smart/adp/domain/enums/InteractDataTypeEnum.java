package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 互动数据查询数据类型
 * @Author: rik.ren
 * @Date: 2025/4/09 13:32
 **/
@Getter
@AllArgsConstructor
public enum InteractDataTypeEnum {
    /**
     * 全部
     */
    ALL("all", "全部"),
    /**
     * 跟进记录
     */
    REVIEW("review", "跟进记录"),
    /**
     * 用户行为
     */
    EVENT("event", "用户行为"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static InteractDataTypeEnum getByCode(String code) {
        return Arrays.stream(InteractDataTypeEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
