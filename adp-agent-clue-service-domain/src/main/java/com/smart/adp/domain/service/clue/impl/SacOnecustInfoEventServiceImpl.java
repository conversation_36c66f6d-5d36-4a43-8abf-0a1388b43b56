package com.smart.adp.domain.service.clue.impl;

import com.smart.adp.domain.bo.clue.SacOnecustInfoEventBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.gateway.clue.SacOnecustInfoEventGateway;
import com.smart.adp.domain.service.clue.ISacOnecustInfoEventService;
import com.smart.adp.domain.valueObject.clue.SacOnecustInfoEventVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.valueObject.clue.table.SacOnecustInfoEventVOTableDef.SAC_ONECUST_INFO_EVENT_VO;

/**
 * @Description: 用户事件服务实现
 * @Author: rik.ren
 * @Date: 2025/3/14 15:46
 **/
@Service
public class SacOnecustInfoEventServiceImpl implements ISacOnecustInfoEventService {
    @Autowired
    private SacOnecustInfoEventGateway eventGateway;

    @Override
    public DomainPage<SacOnecustInfoEventBO> querycustEvent(SacOnecustInfoEventBO param) {
        SacOnecustInfoEventVO eventVO = new SacOnecustInfoEventVO();
        eventVO.setMobile(param.getMobile());
        DomainPage<SacOnecustInfoEventVO> voPageResult = eventGateway.querycustEventDomainPage(eventVO, param, null,
                SAC_ONECUST_INFO_EVENT_VO.EVENT_TIME, SAC_ONECUST_INFO_EVENT_VO.EVENT_CODE, SAC_ONECUST_INFO_EVENT_VO.EVENT_NAME,
                SAC_ONECUST_INFO_EVENT_VO.MOBILE, SAC_ONECUST_INFO_EVENT_VO.SMART_ID);
        List<SacOnecustInfoEventVO> voResult = voPageResult.getRecords();
        List<SacOnecustInfoEventBO> listEventBO = SacOnecustInfoEventBO.conventBO(voResult);
        DomainPage<SacOnecustInfoEventBO> result = new DomainPage<>(listEventBO, voPageResult.getPageNumber(),
                voPageResult.getPageSize(), voPageResult.getTotalCount());
        return result;
    }
}
