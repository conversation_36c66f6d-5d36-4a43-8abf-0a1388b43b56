package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import lombok.*;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;

import static com.smart.adp.domain.entity.clue.table.SacOnecustInfoEntityTableDef.SAC_ONECUST_INFO_ENTITY;


/**
 * @Description: 潜客客户表
 * @Author: rik.ren
 * @Date: 2025/3/5 20:01
 **/
@Table(value = "t_sac_onecust_info", schema = "csc")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode
public class SacOnecustInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Column(value = "CUST_ID")
    private String custId;

    /**
     * smartID
     */
    @Column(value = "SMART_ID")
    private String smartId;

    /**
     * 线索来源分类
     */
    @Column(value = "CLUE_SOURCE")
    private String clueSource;

    /**
     * 线索来源分类
     */
    @Column(value = "CLUE_SOURCE_CN")
    private String clueSourceCn;

    /**
     * 所在地
     */
    @Column(value = "LOCATION")
    private String location;

    /**
     * 所在地
     */
    @Column(value = "LOCATION_CN")
    private String locationCn;

    /**
     * 用户城市度
     */
    @Column(value = "USER_MATURITY")
    private String userMaturity;

    /**
     * 用户成熟度
     */
    @Column(value = "USER_MATURITY_CN")
    private String userMaturityCn;

    /**
     * 预计成交时间
     */
    @Column(value = "ESTIMATE_TIME")
    private String estimateTime;

    /**
     * 竞品车型
     */
    @Column(value = "COMPETITOR_TYPE")
    private String competitorType;

    /**
     * 预计成交时间
     */
    @Column(value = "ESTIMATE_TIME_CN")
    private String estimateTimeCn;

    /**
     * 竞品车型
     */
    @Column(value = "COMPETITOR_TYPE_CN")
    private String competitorTypeCn;

    /**
     * 跟进记录
     */
    @Column(value = "REVIEW_RECORD")
    private String reviewRecord;

    /**
     * 跟进记录
     */
    @Column(value = "REVIEW_RECORD_CN")
    private String reviewRecordCn;

    /**
     * 姓名
     */
    @Column(value = "CUST_NAME")
    private String custName;

    /**
     * 性别编码
     */
    @Column(value = "GENDER_CODE")
    private String genderCode;

    /**
     * 性别名称
     */
    @Column(value = "GENDER_NAME")
    private String genderName;

    /**
     * 昵称
     */
    @Column(value = "NICK_NAME")
    private String nickName;

    /**
     * 手机号
     */
    @Column(value = "PHONE")
    private String phone;

    /**
     * 备用手机号
     */
    @Column(value = "PHONE_STANDBY")
    private String phoneStandby;

    /**
     * 邮箱
     */
    @Column(value = "EMAIL")
    private String email;

    /**
     * 微信账号
     */
    @Column(value = "WECHAT")
    private String wechat;

    /**
     * 身份证
     */
    @Column(value = "ID_CARD")
    private String idCard;

    /**
     * 行驶证
     */
    @Column(value = "DRIVER_CARD")
    private String driverCard;

    /**
     * 驾照
     */
    @Column(value = "DRIVING_LICENSE")
    private String drivingLicense;

    /**
     * 护照
     */
    @Column(value = "PASSPORT")
    private String passport;

    /**
     * cl_sp（管理来源）
     */
    @Column(value = "source5")
    private String source5;

    /**
     * cl_sr（管理来源)
     */
    @Column(value = "source6")
    private String source6;

    /**
     * 关注车型版型
     */
    @Column(value = "attr83")
    private String attr83;

    /**
     * 投诉标签
     */
    @Column(value = "COMPLAINTS_LABEL")
    private String complaintsLabel;

    /**
     * 空闲标签
     */
    @Column(value = "FREE_LABEL")
    private String freeLabel;

    /**
     * 兴趣爱好编码（画像类第一位）
     */
    @Column(value = "USER_HOBBIES_CODE")
    private String userHobbiesCode;

    /**
     * 兴趣爱好名称（画像类第一位）
     */
    @Column(value = "USER_HOBBIES_NAME")
    private String userHobbiesName;

    /**
     * 特征编码
     */
    @Column(value = "CHARACTERISTICS_CODE")
    private String characteristicsCode;

    /**
     * 特征名称
     */
    @Column(value = "CHARACTERISTICS_NAME")
    private String characteristicsName;

    /**
     * 客户来源
     */
    @Column(value = "ONE_CUST_SOURCE")
    private String oneCustSource;

    /**
     * 扩展信息
     */
    @Column(value = "EXTEND_JSON")
    private Object extendJson;

    /**
     * 扩展字段1
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @Column(value = "COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @Column(value = "COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @Column(value = "COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @Column(value = "COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @Column(value = "COLUMN10")
    private String column10;

    /**
     * 时间戳
     */
    @Column(value = "_MYCAT_OP_TIME")
    private Long MycatOpTime;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @Column(value = "OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @Column(value = "GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column(value = "SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column(value = "SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 客户描述
     */
    @Column(value = "CUSTOMER_DESC")
    private String customerDesc;

    public SacOnecustInfoEntity conditions(QueryWrapper wrapper) {
        wrapper.and(SAC_ONECUST_INFO_ENTITY.CUST_ID.eq(getCustId(), StringUtil::hasText))
                .and(SAC_ONECUST_INFO_ENTITY.SMART_ID.eq(getSmartId(), StringUtil::hasText))
                .and(SAC_ONECUST_INFO_ENTITY.COMPETITOR_TYPE_CN.eq(getCompetitorTypeCn(), StringUtil::hasText))
                .and(SAC_ONECUST_INFO_ENTITY.PHONE.eq(getPhone(), StringUtil::hasText));
        return this;
    }
}
