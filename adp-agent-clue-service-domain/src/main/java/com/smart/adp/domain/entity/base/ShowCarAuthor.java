package com.smart.adp.domain.entity.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 展车审核实体类
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_usc_bu_showcar_author", schema = "mp")
public class ShowCarAuthor implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 展车申请审批ID
     */
    @Column("AUTH_ID")
    private String authId;

    /**
     * 展车ID
     */
    @Column("SHOW_CAR_ID")
    private String showCarId;

    /**
     * 展车关联申请单ID
     */
    @Column("APPLY_ID")
    private String applyId;

    /**
     * 申请类型: 1=展车申请;2=退役申请;3=整备申请
     */
    @Column("APPLY_TYPE")
    private String applyType;

    /**
     * 审批节点
     */
    @Column("AUTH_NODE")
    private String authNode;

    /**
     * 审批状态: 0=待审批; 1=通过; 2=驳回
     */
    @Column("AUTH_STATU")
    private String authStatu;

    /**
     * 审批说明
     */
    @Column("AUTH_REMARK")
    private String authRemark;

    /**
     * 审批人用户ID
     */
    @Column("AUTH_PERSON_ID")
    private String authPersonId;

    /**
     * 审批人名称
     */
    @Column("AUTH_PERSON_NAME")
    private String authPersonName;

    /**
     * 创建人
     */
    @Column("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @Column("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @Column("UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 审核时间
     */
    @Column("AUTH_DATE")
    private LocalDateTime authDate;

}
