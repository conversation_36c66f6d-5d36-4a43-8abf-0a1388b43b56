package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacAttachmentBO;
import com.smart.adp.domain.valueObject.clue.SacAttachmentVO;

import java.util.List;

/**
 * @Description: 附件gateway
 * @Author: rik.ren
 * @Date: 2025/3/21 17:48
 **/
public interface SacAttachmentGateway {

    /**
     * 根据对象条件查询附件
     *
     * @param param
     * @return
     */
    List<SacAttachmentVO> queryAttachment(SacAttachmentBO param, QueryColumn[] columns);
}
