package com.smart.adp.domain.entity.clue;

import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.handler.Fastjson2TypeHandler;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/21
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Table(value = "t_sac_clue_tag", schema = "csc")
public class SacClueTag {

    /**
     * id - 自增
     */
    @Id("id")
    private Long id;

    /**
     * 线索 id
     */
    @Column("clue_id")
    private String clueId;

    /**
     * 客户 id
     */
    @Column("cust_id")
    private String custId;

    /**
     * 标签 code
     *
     * @see com.smart.adp.domain.enums.ClueTagEnum
     */
    @Column("tag_code")
    private String tagCode;

    /**
     * 过期时间
     */
    @Column("expire_time")
    private LocalDateTime expireTime;

    /**
     * 创建时间<br/>
     *
     * default db time, use `on insert`?
     */
    @Column(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 扩展信息
     */
    @Column(value = "extend_json", typeHandler = Fastjson2TypeHandler.class)
    private JSONObject extendJson;

}
