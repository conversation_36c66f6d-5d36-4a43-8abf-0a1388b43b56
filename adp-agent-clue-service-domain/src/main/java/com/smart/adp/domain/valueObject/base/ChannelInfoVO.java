package com.smart.adp.domain.valueObject.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/20
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "渠道信息 VO")
public class ChannelInfoVO {

    /**
     * 渠道编码
     */
    @Schema(description = "渠道编码")
    private String code;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String name;

    /**
     * 子渠道信息
     */
    @Schema(description = "子渠道信息")
    private List<ChannelInfoVO> children = new ArrayList<>();
}
