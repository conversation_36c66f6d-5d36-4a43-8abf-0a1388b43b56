package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ClueMessageTypeEnum {
    DEFAULT("-1", "未知类型"),
    KEY_EVENT("1", "关键事件"),
    CLUE_ASSIGN("2", "线索分配"),
    TEST_DRIVE("3", "试乘试驾"),
    CODE_6("6", "客户投诉"),
    CODE_7("7", "客户投诉"),
    COMPLAINT("10", "客户投诉"),
    DEFEAT_REJECT("8", "战败驳回"),
    ACTIVITY_SUPPORT("9", "活动支持"),
    TEST_DRIVE_CANCEL("13", "试驾取消"),
    TRANSFER_APPLICATION("14", "划转申请"),
    REPLACEMENT_SERVICE("15", "置换服务"),
    FINANCE_CHANGE("16", "金融状态变更"),
    TEST_CAR_REMINDER("17", "试驾车提醒"),
    SHOW_CAR_REMINDER("18", "展车提醒");

    private final String code;
    private final String desc;

    // Getter 方法
    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static ClueMessageTypeEnum getByCode(String code) {
        return Arrays.stream(ClueMessageTypeEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findAny()
                .orElse(DEFAULT);
    }
}