package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ManageLabelEnum {

    PROCESSED("1", "线索移交-已处理"),

    CHANGE_STORE("2", "线索移交-员工换店"),

    CHANGE_EMP("3", "线索移交-员工离职"),

    ;
    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据编码获取枚举实例
     *
     * @param code 编码
     * @return 对应的枚举实例，如果不存在则返回null
     */
    public static ManageLabelEnum getByCode(String code) {
        return Arrays.stream(ManageLabelEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findAny()
                .orElse(null);
    }

    /**
     * 根据编码获取描述信息
     *
     * @param code 编码
     * @return 对应的描述，如果不存在则返回null
     */
    public static String getDescByCode(String code) {
        ManageLabelEnum enumInstance = getByCode(code);
        return enumInstance != null ? enumInstance.getDesc() : null;
    }
}
