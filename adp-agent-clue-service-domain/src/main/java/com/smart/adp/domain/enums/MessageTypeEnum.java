package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

/**
 * 逻辑删除标记枚举
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023-03-01 16:11
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum {
    KEY_EVENT("1", "关键事件"),
    CLUE_ASSIGNMENT("2", "线索分配"),
    TEST_DRIVE("3", "试乘试驾"),
    CUSTOMER_COMPLAINT_6("6", "客户投诉"),
    CUSTOMER_COMPLAINT("7", "客户投诉"),
    BATTLE_REJECTION("8", "战败驳回"),
    EVENT_SUPPORT("9", "活动支持"),
    CUSTOMER_COMPLAINT_10("10", "客户投诉"),
    TEST_DRIVE_CANCELLATION("13", "试驾取消"),
    TRANSFER_APPLICATION("14", "划转申请"),
    REPLACEMENT_SERVICE("15", "置换服务"),
    FINANCIAL_STATUS_CHANGE("16", "金融状态变更"),
    TEST_VEHICLE_REMINDER("17", "试驾车提醒"),
    SHOW_VEHICLE_REMINDER("18", "展车提醒");
    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static MessageTypeEnum getByCode(String code) {
        return Arrays.stream(MessageTypeEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }

    public static List<String> getAllCodes() {
        List<String> codes = new ArrayList<>();
        for (MessageTypeEnum event : values()) {
            codes.add(event.getCode());
        }
        return codes;
    }
}
