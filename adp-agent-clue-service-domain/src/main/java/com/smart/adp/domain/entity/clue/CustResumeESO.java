package com.smart.adp.domain.entity.clue;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.smart.adp.domain.valueObject.clue.SacOnecustResumeVO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class CustResumeESO {

    /**
     * 履历 id
     */
    @Field(type = FieldType.Keyword)
    private String resumeId;

    /**
     * scene code
     */
    @Field(type = FieldType.Keyword)
    private String senceCode;

    /**
     * 跟进人
     */
    @Field(type = FieldType.Keyword)
    private String resumePersonName;

    /**
     * 热度
     */
    @Field(type = FieldType.Keyword)
    private String heat;

    /**
     * 等级
     */
    @Field(type = FieldType.Keyword)
    private String level;

    /**
     * 内容
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_max_word")
    private String content;

    /**
     * 履历描述
     */
    @Field(type = FieldType.Keyword)
    private String resumeDesc;

    /**
     * 备注
     */
    @Field(type = FieldType.Keyword)
    private String remark;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime createdDate;

    public static CustResumeESO buildESO(SacOnecustResumeVO resume) {
        CustResumeESO eso = new CustResumeESO();
        eso.setResumeId(resume.getResumeId());
        eso.setSenceCode(resume.getSenceCode());
        eso.setResumePersonName(resume.getResumePersonName());
        eso.setHeat(resume.getColumn5());
        eso.setLevel(resume.getColumn1());
        eso.setContent(parseContent(resume.getSenceCode(), resume.getResumeDesc(), resume.getRemark()));
        eso.setResumeDesc(resume.getResumeDesc());
        eso.setRemark(resume.getRemark());
        eso.setCreatedDate(resume.getCreatedDate());
        return eso;
    }

    /**
     * 解析履历中的用户输入
     *
     * @param senceCode  场景码
     * @param resumeDesc 履历描述
     * @param remark     备注
     * @return 用户输入
     */
    private static String parseContent(String senceCode, String resumeDesc, String remark) {
        String res = null;

        try {
            switch (senceCode) {
                case "1":
                case "2":
                case "5":
                case "7":
                case "9":
                case "11":
                    res = resumeDesc;
                    break;
                case "4":
                case "8":
                    res = resumeDesc.substring(resumeDesc.indexOf("#") + 1);
                    break;
                case "6":
                    res = resumeDesc.substring(resumeDesc.indexOf("：") + 1);
                    break;
                case "14":
                case "15":
                case "16":
                case "17":
                case "18":
                case "19":
                case "20":
                    res = resumeDesc.substring(resumeDesc.indexOf("saleOrderCode") + 13);
                    break;
                case "21":
                    res = remark;
                    break;
                case "3":
                case "10":
                case "13":
                case "22":
                case "23":
                case "24":
                default:
            }
        } catch (Exception e) {
            log.warn("resume parse exception", e);
        }

        return res;
    }

    @SuppressWarnings("unchecked")
    public Map<String, Object> toMap() {
        return JSONObject.parseObject(JSONObject.toJSONString(this), Map.class);
    }
}