package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/19
 */
@Getter
@AllArgsConstructor
public enum EnableEnum {

    /**
     * 启用
     */
    ENABLE(1, "1", true),

    /**
     * 禁用
     */
    DISABLE(0, "0", false),

    ;

    private final int code;
    private final String codeStr;
    private final boolean flag;
}
