package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/4/20 13:54
 * @description 线索分配日志模型对象
 **/
@Table(value = "t_sac_clue_allocating_record", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class ClueAllocatingRecord {

    @Column("RECORD_ID")
    private String recordId;

    @Column("SERVER_ORDER")
    private String serverOrder;

    @Column("CUST_ID")
    private String custId;

    @Column("CUST_NAME")
    private String custName;

    @Column("CUST_PHONE")
    private String custPhone;

    @Column("OLD_DLR_ID")
    private String oldDlrId;

    @Column("OLD_DLR_CODE")
    private String oldDlrCode;

    @Column("OLD_DLR_SHORT_NAME")
    private String oldDlrShortName;

    @Column("OLD_SALESPERSON_ID")
    private String oldSalespersonId;

    @Column("OLD_SALESPERSON_NAME")
    private String oldSalespersonName;

    @Column("NEW_DLR_ID")
    private String newDlrId;

    @Column("NEW_DLR_CODE")
    private String newDlrCode;

    @Column("NEW_DLR_SHORT_NAME")
    private String newDlrShortName;

    @Column("NEW_SALESPERSON_ID")
    private String newSalespersonId;

    @Column("NEW_SALESPERSON_NAME")
    private String newSalespersonName;

    @Column("COLUMN1")
    private String column1;

    @Column("COLUMN2")
    private String column2;

    @Column("COLUMN3")
    private String column3;

    @Column("COLUMN4")
    private String column4;

    @Column("COLUMN5")
    private String column5;

    @Column("COLUMN6")
    private String column6;

    @Column("COLUMN7")
    private String column7;

    @Column("COLUMN8")
    private String column8;

    @Column("COLUMN9")
    private String column9;

    @Column("COLUMN10")
    private String column10;

    @Column("EXTENDS_JSON")
    private String extendsJson;

    @Column("OEM_ID")
    private String oemId;

    @Column("GROUP_ID")
    private String groupId;

    @Column("CREATOR")
    private String creator;

    @Column("CREATED_NAME")
    private String createdName;

    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    @Column("MODIFIER")
    private String modifier;

    @Column("MODIFY_NAME")
    private String modifyName;

    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    @Column("IS_ENABLE")
    private String isEnable;

    @Column("UPDATE_CONTROL_ID")
    private String updateControlId;
}
