package com.smart.adp.domain.bo.base;

import com.smart.adp.domain.entity.base.AgentEmployee;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/3/16 10:57
 * @description 员工信息对象BO
 **/
@Data
@ToString
@EqualsAndHashCode
public class AgentEmployeeBO extends AgentEmployee {

    /**
     * 门店名
     */
    private String dlrShortName;
    /**
     * 门店类型
     */
    private String dlrType;
    /**
     * 城市id
     */
    private String cityId;
    /**
     * 县区id
     */
    private String countyId;
    /**
     * 门店省id
     */
    private String provinceId;
    /**
     * 批量用户Id
     */
    private List<String> userIdList;
    /**
     * 批量员工code
     */
    private List<String> empCodeList;
    /**
     * 批量员工id
     */
    private List<String> empIdList;

}
