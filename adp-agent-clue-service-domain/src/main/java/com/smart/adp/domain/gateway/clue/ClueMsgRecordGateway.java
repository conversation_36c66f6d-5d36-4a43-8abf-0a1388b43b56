package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacClueMsgRecordBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.base.ClueMsgRecord;
import com.smart.adp.domain.valueObject.clue.SacClueMsgRecordVO;

import java.util.List;

/**
 * @Description: 消息查询gateway
 * @Author: rik.ren
 * @Date: 2025/4/15 13:26
 **/
public interface ClueMsgRecordGateway {

    /**
     * 根据实体条件查询集合
     *
     * @param boParam
     * @param needColumns
     * @return
     */
    List<SacClueMsgRecordVO> findListByCondition(SacClueMsgRecordBO boParam, QueryColumn... needColumns);

    /**
     * 根据实体条件查询分页
     *
     * @param boParam
     * @param needColumns
     * @return
     */
    DomainPage<SacClueMsgRecordVO> findPageByCondition(SacClueMsgRecordBO boParam, QueryColumn... needColumns);

    /**
     * 查询符合条件的个数
     *
     * @param boParam
     * @return
     */
    Long findCountByCondition(SacClueMsgRecordBO boParam);

    /**
     * 修改消息
     *
     * @param boParam
     * @return
     */
    Boolean modifyMsgRecord(SacClueMsgRecordBO boParam);

    List<ClueMsgRecord> findClueMsgRecords(ClueMsgRecord clueMsgRecord);

    Boolean modifyClueMsgRecord(ClueMsgRecord param);

    int saveClueMsgRecord(ClueMsgRecord clueMsgRecord);
}
