package com.smart.adp.domain.service.koc;

import com.smart.adp.domain.bo.koc.KocTagBO;
import com.smart.adp.domain.entity.koc.SacTagInfo;

import java.util.List;

/**
 * @Description: KOC标签领域服务接口
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
public interface IKocTagService {

    /**
     * 根据ID获取标签信息
     *
     * @param tagId 标签ID
     * @return 标签信息
     */
    SacTagInfo getTagById(String tagId);

    /**
     * 获取标签树结构
     *
     * @return 标签树
     */
    List<KocTagBO> getTagTree();

    /**
     * 根据父标签ID获取子标签
     *
     * @param parentTagId 父标签ID
     * @return 子标签列表
     */
    List<KocTagBO> getChildTags(String parentTagId);

    /**
     * 根据标签层级获取标签
     *
     * @param tagLevel 标签层级
     * @return 标签列表
     */
    List<KocTagBO> getTagsByLevel(Integer tagLevel);

    /**
     * 搜索标签
     *
     * @param keyword 关键字
     * @return 标签列表
     */
    List<KocTagBO> searchTags(String keyword);

    /**
     * 获取所有启用的标签
     *
     * @return 标签列表
     */
    List<KocTagBO> getAllEnabledTags();

    /**
     * 创建标签
     *
     * @param tagBO 标签信息
     * @return 是否成功
     */
    Boolean createTag(KocTagBO tagBO);

    /**
     * 更新标签
     *
     * @param tagBO 标签信息
     * @return 是否成功
     */
    Boolean updateTag(KocTagBO tagBO);

    /**
     * 删除标签
     *
     * @param tagId 标签ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean deleteTag(String tagId, String operatorId, String operatorName);

    /**
     * 批量删除标签
     *
     * @param tagIds 标签ID列表
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean batchDeleteTags(List<String> tagIds, String operatorId, String operatorName);

    /**
     * 启用标签
     *
     * @param tagId 标签ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean enableTag(String tagId, String operatorId, String operatorName);

    /**
     * 禁用标签
     *
     * @param tagId 标签ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean disableTag(String tagId, String operatorId, String operatorName);

    /**
     * 验证标签名称是否可用
     *
     * @param tagName 标签名称
     * @param parentTagId 父标签ID
     * @param excludeTagId 排除的标签ID
     * @return 是否可用
     */
    Boolean validateTagName(String tagName, String parentTagId, String excludeTagId);

    /**
     * 验证标签是否可以删除
     *
     * @param tagId 标签ID
     * @return 是否可以删除
     */
    Boolean validateTagDeletable(String tagId);

    /**
     * 构建标签完整路径
     *
     * @param tagName 标签名称
     * @param parentTagId 父标签ID
     * @return 完整路径
     */
    String buildTagFullPath(String tagName, String parentTagId);

    /**
     * 获取标签使用统计
     *
     * @param tagIds 标签ID列表
     * @return 带使用统计的标签列表
     */
    List<KocTagBO> getTagsWithUsageCount(List<String> tagIds);

    /**
     * 验证标签层级是否正确
     *
     * @param parentTagId 父标签ID
     * @param targetLevel 目标层级
     * @return 是否正确
     */
    Boolean validateTagLevel(String parentTagId, Integer targetLevel);
}
