package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.bo.clue.SacOneCustRemarkBO;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;
import com.smart.adp.domain.gateway.clue.OneCustRemarkGateway;
import com.smart.adp.domain.service.clue.ISacOneCustRemarkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.entity.clue.table.SacOneCustRemarkTableDef.SAC_ONE_CUST_REMARK;

/**
 * @Description: 线索扩展信息服务实现
 * @Author: rik.ren
 * @Date: 2025/3/12 17:37
 **/
@Service
public class SacOneCustRemarkService implements ISacOneCustRemarkService {
    @Autowired
    private OneCustRemarkGateway oneCustRemarkGateway;

    /**
     * 编辑扩展信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyCustRemark(SacOneCustRemark param) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        return oneCustRemarkGateway.modifyOneCustRemark(param);
    }

    /**
     * 查询一个remark
     *
     * @param param
     * @return
     */
    @Override
    public SacOneCustRemark queryRemark(SacOneCustRemark param) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        return oneCustRemarkGateway.findByCustId(param.getCustId(), SAC_ONE_CUST_REMARK.ALL_COLUMNS);
    }

    /**
     * 查询集合remark
     *
     * @param param
     * @return
     */
    @Override
    public List<SacOneCustRemark> queryRemark(SacOneCustRemarkBO param) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        return oneCustRemarkGateway.findByCustId(param.getListCustId(), SAC_ONE_CUST_REMARK.ALL_COLUMNS);
    }

    /**
     * 插入remark
     *
     * @param param
     * @return
     */
    public Boolean insertRemark(SacOneCustRemark param) {
        return oneCustRemarkGateway.saveOneCustRemark(param);
    }
}
