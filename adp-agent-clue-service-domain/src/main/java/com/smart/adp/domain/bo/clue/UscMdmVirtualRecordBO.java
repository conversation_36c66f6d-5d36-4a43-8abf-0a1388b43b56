package com.smart.adp.domain.bo.clue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.smart.adp.domain.bo.base.AgentEmployeeBO;
import com.smart.adp.domain.entity.clue.UscMdmVirtualRecordEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 虚拟外呼通话记录表BO。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@NoArgsConstructor
@Slf4j
@EqualsAndHashCode
public class UscMdmVirtualRecordBO extends UscMdmVirtualRecordEntity {
    /**
     * 分页属性
     */
    private Page<UscMdmVirtualRecordEntity> pageObj;
    /**
     * 门店名
     */
    private String dlrShortName;
    /**
     * 门店类型
     */
    private String dlrType;
    /**
     * 城市id
     */
    private String cityId;
    /**
     * 县区id
     */
    private String countyId;
    /**
     * 门店省id
     */
    private String provinceId;
    /**
     * 跟进人姓名
     */
    private String empName;

    /**
     * 开始时间
     */
    private LocalDateTime dateBegin;
    /**
     * 结束时间
     */
    private LocalDateTime dateEnd;

    public UscMdmVirtualRecordBO(Page<UscMdmVirtualRecordEntity> pageObj) {
        this.pageObj = pageObj;
    }

    public static UscMdmVirtualRecordBO conventBO(UscMdmVirtualRecordEntity entity, AgentEmployeeBO employeeBOMap) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        UscMdmVirtualRecordBO recordBO = new UscMdmVirtualRecordBO();
        recordBO.setRecordId(entity.getRecordId());
        recordBO.setVirtualMobile(entity.getVirtualMobile());
        recordBO.setBindMobile(entity.getBindMobile());
        recordBO.setEmpCode(entity.getEmpCode());
        recordBO.setConsumerMobile(entity.getConsumerMobile());
        recordBO.setCustId(entity.getCustId());
        recordBO.setStatus(entity.getStatus());
        recordBO.setCallType(entity.getCallType());
        recordBO.setCallStartTime(entity.getCallStartTime());
        recordBO.setCallEndTime(entity.getCallEndTime());
        recordBO.setCallRecordUrl(entity.getCallRecordUrl());
        recordBO.setCallTime(entity.getCallTime());
        recordBO.setCallId(entity.getCallId());
        recordBO.setCreator(entity.getCreator());
        recordBO.setCreatedDate(entity.getCreatedDate());
        recordBO.setModifier(entity.getModifier());
        recordBO.setLastUpdatedDate(entity.getLastUpdatedDate());
        recordBO.setIsEnable(entity.getIsEnable());
        recordBO.setUpdateControlId(entity.getUpdateControlId());
        recordBO.setAbstractContent(entity.getAbstractContent());
        return recordBO;
    }

    public static List<UscMdmVirtualRecordBO> conventBO(List<UscMdmVirtualRecordEntity> entityList,
                                                        List<AgentEmployeeBO> empResult) {
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        Map<String, AgentEmployeeBO> employeeBOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(empResult)) {
            try {
                employeeBOMap = empResult.stream().collect(Collectors.toMap(
                        AgentEmployeeBO::getEmpCode, // 指定用作key的Function
                        Function.identity() // 保留原始对象作为value
                ));
            } catch (Exception e) {
                log.error("员工转换map异常 {}", JSONObject.toJSONString(e));
            }
        }
        Map<String, AgentEmployeeBO> finalEmployeeBOMap = employeeBOMap;
        return entityList.stream()
                .map(entity -> {
                    return conventBO(entity, CollectionUtil.isEmpty(finalEmployeeBOMap) ? null :
                            finalEmployeeBOMap.get(entity.getEmpCode()));
                })
                .collect(Collectors.toList());
    }

}
