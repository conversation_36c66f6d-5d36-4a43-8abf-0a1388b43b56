package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;

import java.util.List;

/**
 * @Description: 潜客gateway接口
 * @Author: rik.ren
 * @Date: 2025/3/5 20:43
 **/
public interface OneCustInfoGateway {
    /**
     * 根据id查询
     *
     * @param id
     * @param columns
     * @return
     */
    SacOnecustInfoEntity findById(String id, QueryColumn... columns);

    /**
     * 根据 ids 查询
     *
     * @param ids id 列表
     * @param columns 查询列
     * @return entities
     */
    List<SacOnecustInfoEntity> findByIds(List<String> ids, QueryColumn... columns);

    /**
     * 根据实体条件查询集合
     *
     * @param param
     * @param needColumns
     * @return
     */
    List<SacOnecustInfoEntity> findListByCondition(SacOnecustInfoEntity param, QueryColumn... needColumns);

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns
     * @return
     */
    SacOnecustInfoEntity findByCondition(SacOnecustInfoEntity param, QueryColumn... needColumns);

    List<SacOnecustInfoEntity> findListBySmartId(String smartId);

    SacOnecustInfoEntity findOneByPhone(String phone);

    /**
     * 更新潜客表
     * @param param
     * @return
     */
    Boolean modifyOnecust(SacOnecustInfoEntity param);

    Boolean modifyOnecustInfo(SacOnecustInfoEntity param);

    Boolean saveOnecust(SacOnecustInfoEntity param);
}
