package com.smart.adp.domain.gateway.clue;

import com.smart.adp.domain.entity.clue.ActivityInfo;

import java.util.Date;

/**
 * <AUTHOR>
 * date 2025/3/5 16:30
 * @description
 **/
public interface ActivityGateway {

    ActivityInfo findActivityInfo(String activityId, String isEnable);

    ActivityInfo findActivityData(String activityId, Date date, String createTypeCode, String isEnable, String statusCode, String releaseStatusCode);
}
