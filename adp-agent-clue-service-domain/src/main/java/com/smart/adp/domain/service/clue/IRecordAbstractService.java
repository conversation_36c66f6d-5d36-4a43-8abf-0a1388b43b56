package com.smart.adp.domain.service.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.bo.clue.SacVirtualRecordAbstractBO;
import com.smart.adp.domain.valueObject.clue.SacTestRecordAbstractVO;
import com.smart.adp.domain.valueObject.clue.SacVirtualRecordAbstractVO;

import java.util.List;

/**
 * @Description: 录音摘要service
 * @Author: rik.ren
 * @Date: 2025/3/13 15:29
 **/
public interface IRecordAbstractService {
    /**
     * 保存虚拟外呼摘要内容
     *
     * @param entity
     * @return
     */
    Boolean saveVirtualAbstractContent(SacVirtualRecordAbstractVO entity);
    /**
     * 保存虚拟外呼摘要内容
     *
     * @param entity
     * @return
     */
    Boolean saveVirtualAbstractContent(SacVirtualRecordAbstractBO entity);

    /**
     * 保存试驾录音摘要内容
     *
     * @param entity
     * @return
     */
    Boolean saveTestAbstractContent(SacTestRecordAbstractVO entity);

    /**
     * 查询虚拟外呼摘要内容
     *
     * @return
     */
    List<SacVirtualRecordAbstractBO> queryVirtualRecordAbs(SacVirtualRecordAbstractBO param, QueryColumn... columns);

    /**
     * 查询试驾录音摘要内容
     *
     * @return
     */
    List<SacVirtualRecordAbstractBO> queryTestRecordAbs(SacVirtualRecordAbstractBO param, QueryOrderBy orderBy,
                                                        QueryColumn... columns);
}
