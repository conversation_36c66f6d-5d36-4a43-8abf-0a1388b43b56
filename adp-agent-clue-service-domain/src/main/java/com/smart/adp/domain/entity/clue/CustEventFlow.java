package com.smart.adp.domain.entity.clue;

import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.handler.Fastjson2TypeHandler;
import com.smart.adp.domain.context.TimeContext;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 客户事件流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(value = "t_sac_cust_event_flow", schema = "csc")
public class CustEventFlow {

    /**
     * 主键
     */
    @Id("id")
    private Long id;

    /**
     * 客户 ID
     */
    @Column("cust_id")
    private String custId;

    /**
     * 事件类型
     * @see com.smart.adp.domain.enums.CustEventEnum
     */
    @Column("type")
    private Integer type;

    /**
     * 动作后状态
     * @see com.smart.adp.domain.enums.ClueStageEnum
     */
    @Column("stage")
    private Integer stage;

    /**
     * 业务单据 ID
     */
    @Column("business_id")
    private String businessId;

    /**
     * 事件时间
     */
    @Column("event_time")
    private LocalDateTime eventTime;

    /**
     * 扩展信息
     */
    @Column(value = "extend_json", typeHandler = Fastjson2TypeHandler.class)
    private JSONObject extendJson;

    public void setEventTime(LocalDateTime dateTime) {
        if (Objects.isNull(dateTime)) {
            dateTime = TimeContext.now();
        }

        eventTime = dateTime;
    }
}
