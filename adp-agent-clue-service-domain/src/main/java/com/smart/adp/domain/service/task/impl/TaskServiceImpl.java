package com.smart.adp.domain.service.task.impl;

import com.mybatisflex.core.paginate.Page;
import com.smart.adp.domain.entity.task.OnetaskDetail;
import com.smart.adp.domain.entity.task.OnetaskInfo;
import com.smart.adp.domain.gateway.task.TaskGateway;
import com.smart.adp.domain.qry.TaskQry;
import com.smart.adp.domain.service.task.TaskService;
import com.smart.adp.domain.valueObject.task.TaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/6
 */
@Component
public class TaskServiceImpl implements TaskService {

    @Autowired
    private TaskGateway taskGateway;

    @Override
    public Page<TaskVO> page(TaskQry qry) {
        Page<TaskVO> page = taskGateway.page(qry);

        if (page.hasRecords()) {
            List<TaskVO> records = page.getRecords();
            List<String> taskIds = records.stream()
                                          .map(TaskVO::getTaskId)
                                          .collect(Collectors.toList());

            fillInfo(taskIds, records);
            fillDetail(taskIds, records);
        }

        return page;
    }

    @Override
    public TaskVO detail(Long id) {
        TaskVO vo = taskGateway.detail(id);

        if (Objects.nonNull(vo)) {
            String taskId = vo.getTaskId();
            OnetaskInfo info = taskGateway.getInfoByTaskId(taskId);
            vo.joinInfo(info);

            List<OnetaskDetail> details = taskGateway.getDetailsByTaskIds(Collections.singletonList(taskId));
            vo.joinDetails(details);
        }
        return vo;
    }

    private void fillInfo(List<String> taskIds, List<TaskVO> records) {
        Map<String, OnetaskInfo> infoMap = taskGateway.getInfosByTaskIds(taskIds)
                                                      .stream()
                                                      .collect(Collectors.toMap(OnetaskInfo::getTaskId, Function.identity()));
        records.forEach(vo -> {
            OnetaskInfo info = Optional.of(vo)
                                       .map(TaskVO::getTaskId)
                                       .map(infoMap::get)
                                       .orElse(null);
            if (Objects.isNull(info)) {
                return;
            }

            vo.joinInfo(info);
        });
    }

    private void fillDetail(List<String> taskIds, List<TaskVO> records) {
        Map<String, List<OnetaskDetail>> detailsMap = taskGateway.getDetailsByTaskIds(taskIds)
                                                                 .stream()
                                                                 .collect(Collectors.groupingBy(OnetaskDetail::getTaskId));
        records.forEach(vo -> {
            List<OnetaskDetail> details = Optional.of(vo)
                                                  .map(TaskVO::getTaskId)
                                                  .map(detailsMap::get)
                                                  .orElse(Collections.emptyList());

            vo.joinDetails(details);
        });
    }
}
