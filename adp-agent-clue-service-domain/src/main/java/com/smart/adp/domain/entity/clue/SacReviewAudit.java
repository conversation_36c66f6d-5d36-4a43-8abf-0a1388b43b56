package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@Table(value = "t_sac_review_audit", schema = "csc")
public class SacReviewAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核ID
     */
    @Id("AUDIT_ID")
    private String auditId;

    /**
     * 回访ID
     */
    @Column("REVIEW_ID")
    private String reviewId;

    /**
     * 所属组织
     */
    @Column("ORG_CODE")
    private String orgCode;

    /**
     * 所属组织名称
     */
    @Column("ORG_NAME")
    private String orgName;

    /**
     * 业务单号
     */
    @Column("BILL_CODE")
    private String billCode;

    /**
     * 审核类型编码
     */
    @Column("APPLY_TYPE_CODE")
    private String applyTypeCode;

    /**
     * 审核类型名称
     */
    @Column("APPLY_TYPE_NAME")
    private String applyTypeName;

    /**
     * 申请原因
     */
    @Column("APPLY_DESC")
    private String applyDesc;

    /**
     * 审核人员用户ID
     */
    @Column("SH_PERSON_ID")
    private String shPersonId;

    /**
     * 审核人员名称
     */
    @Column("SH_PERSON_NAME")
    private String shPersonName;

    /**
     * 审核意见
     */
    @Column("SH_DESC")
    private String shDesc;

    /**
     * 审核时间
     */
    @Column("SH_TIME")
    private LocalDateTime shTime;

    /**
     * 审核状态编码
     */
    @Column("SH_STATUS")
    private String shStatus;

    /**
     * 审核状态名称
     */
    @Column("SH_STATUS_NAME")
    private String shStatusName;

    /**
     * JSON扩展字段
     */
    @Column("EXTENDS_JSON")
    private String extendsJson;

    /**
     * 厂商标识ID
     */
    @Column("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column("GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;
}