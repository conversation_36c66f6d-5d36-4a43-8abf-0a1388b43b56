package com.smart.adp.domain.valueObject.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/9 13:26
 * @description 门店省信息
 **/
@Table(value = "t_usc_mdm_org_province", schema = "mp")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class ProvinceInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column("PROVINCE_ID")
    private String provinceId;

    @Column("PROVINCE_CODE")
    private String provinceCode;

    @Column("PROVINCE_NAME")
    private String provinceName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;
}
