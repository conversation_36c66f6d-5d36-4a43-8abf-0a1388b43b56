package com.smart.adp.domain.bo.clue;

import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import com.smart.adp.domain.entity.clue.SacUserGroupDetailEntity;
import com.smart.adp.domain.enums.ClueModifyTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description: 更新线索
 * @Author: rik.ren
 * @Date: 2025/3/7 13:26
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "更新线索入参")
public class ClueDlrModifyBO {

    /**
     * 线索 ID
     */

    private String id;
    /**
     * 线索 ID
     */

    private String custId;
    /**
     * 线索单号
     */
    private String serverOrder;
    /**
     * 更新的值对应的code
     */

    private String valueCode;
    /**
     * 更新的值
     */

    private String value;
    /**
     * 更新的类型
     *
     * @see ClueModifyTypeEnum
     */

    private Integer clueModifyType;

    /**
     * 登录人信息
     */
    private UserBusiEntity userBusiEntity;

    /**
     * 战败标签，0正常，1战败
     */
    private Integer defeatFlag = 0;

    /**
     * 构建修改用户描述
     *
     * @return
     */
    public SacOneCustRemark buildUserDescModifyParam() {
        return SacOneCustRemark.builder()
                .custId(this.custId)
                .remark(this.value)
                .updateDate(LocalDateTime.now())
                .build();
    }

    public SacUserGroupDetailEntity buildUserGroupDetailModifyParam() {
        return SacUserGroupDetailEntity.builder()
                .custId(this.custId)
                .userGroupId(this.value)
                .serverOrder(this.serverOrder)
                .lastUpdatedDate(LocalDateTime.now())
                .modifier(userBusiEntity.getEmpID())
                .modifyName(userBusiEntity.getEmpName())
                .build();
    }

    /**
     * 构建修改用户等级
     *
     * @return
     */
    public SacOneCustRemark buildUserLevelParam() {
        return SacOneCustRemark.builder()
                .custId(this.custId)
                .clueLevel(this.value)
                .updateDate(LocalDateTime.now())
                .build();
    }

    /**
     * 构建更新线索表的意向车型
     *
     * @return
     */
    public SacClueInfoDlr buildClueIntenCarTypeCodeParam() {
        return SacClueInfoDlr.builder()
                .custId(this.custId)
                .serverOrder(this.serverOrder)
                .intenCarTypeCode(this.value)
                .intenCarTypeName(this.value)
                .lastUpdatedDate(LocalDateTime.now())
                .modifier(userBusiEntity.getEmpID())
                .modifyName(userBusiEntity.getEmpName())
                .build();
    }

    /**
     * 构建开始试驾更新线索表的意向车型和开始试驾时间
     *
     * @return
     */
    public SacClueInfoDlr buildStartDrivingClueIntenCarParam() {
        return SacClueInfoDlr.builder()
                .custId(this.custId)
                .serverOrder(this.serverOrder)
                .intenCarTypeCode(this.value)
                .intenCarTypeName(this.value)
                .lastTestDriverTime(LocalDateTime.now())
                .lastUpdatedDate(LocalDateTime.now())
                .modifier(userBusiEntity.getEmpID())
                .modifyName(userBusiEntity.getEmpName())
                .build();
    }

    /**
     * 构建更新回访表的意向车型
     *
     * @return
     */
    public SacReviewBO buildReviewIntenCarTypeCodeParam() {
        SacReviewBO result = new SacReviewBO();
        result.setCustId(this.custId);
        result.setBillCode(this.serverOrder);
        result.setIntenCarTypeCode(this.value);
        result.setIntenCarTypeName(this.value);
        result.setLastUpdatedDate(LocalDateTime.now());
        result.setModifier(userBusiEntity.getEmpID());
        result.setModifyName(userBusiEntity.getEmpName());
        return result;
    }

    /**
     * 构建更新回访表的意向车型
     *
     * @return
     */
    public SacOneCustRemark buildClueRemarkIntenCarTypeCodeParam() {
        return SacOneCustRemark.builder()
                .custId(this.custId)
                .intentVehicleCode(this.value)
                .updateDate(LocalDateTime.now())
                .build();
    }

    /**
     * 构建更新扩展表代理商线索来源
     *
     * @return
     */
    public SacOneCustRemark buildClueSourceOfAgentParam() {
        return SacOneCustRemark.builder()
                .custId(this.custId)
                .clueSource(this.value)
                .updateDate(LocalDateTime.now())
                .build();
    }

    /**
     * 构建修改潜客表的更新竞品车型
     *
     * @return
     */
    public SacOnecustInfoEntity buildCompetitorModelsParam() {
        value = value.replaceFirst("^,", "").replaceFirst(",$", "");
        if (value.length() > 50) {
            throw new BusinessException(RespCode.FAIL.getCode(), "请精简竞品车型信息");
        }
        return SacOnecustInfoEntity.builder()
                .custId(this.custId)
                .competitorType(value)
                .competitorTypeCn(value)
                .lastUpdatedDate(LocalDateTime.now())
                .modifier(userBusiEntity.getEmpID())
                .modifyName(userBusiEntity.getEmpName())
                .build();
    }

    /**
     * 构建修改扩展表的更新竞品车型
     *
     * @return
     */
    public SacOneCustRemark buildRemarkCompetitorModelsParam() {
        return SacOneCustRemark.builder()
                .custId(this.custId)
                .competitiveVehicleCode(this.value)
                .updateDate(LocalDateTime.now())
                .build();
    }

    /**
     * 构建更新扩展表的用户位置
     *
     * @return
     */
    public SacOneCustRemark buildUserLocationParam() {
        return SacOneCustRemark.builder()
                .custId(this.custId)
                .location(this.value)
                .updateDate(LocalDateTime.now())
                .build();
    }

    /**
     * 更新特别关注
     *
     * @return
     */
    public SacReviewBO buildSpecialReview() {
        SacReviewBO result = new SacReviewBO();
        result.setCustId(this.custId);
        result.setBillCode(this.serverOrder);
        result.setColumn12(this.value);
        result.setLastUpdatedDate(LocalDateTime.now());
        result.setModifier(userBusiEntity.getEmpID());
        result.setModifyName(userBusiEntity.getEmpName());
        return result;
    }

    /**
     * 更新特别关注
     *
     * @return
     */
    public SacClueInfoDlr buildSpecialClue() {
        SacClueInfoDlr result = new SacClueInfoDlr();
        result.setCustId(this.custId);
        result.setServerOrder(this.serverOrder);
        result.setColumn11(this.value);
        result.setLastUpdatedDate(LocalDateTime.now());
        result.setModifier(userBusiEntity.getEmpID());
        result.setModifyName(userBusiEntity.getEmpName());
        return result;
    }
}
