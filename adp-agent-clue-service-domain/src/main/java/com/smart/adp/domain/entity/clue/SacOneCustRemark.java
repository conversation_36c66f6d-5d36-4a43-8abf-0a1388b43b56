package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Table(value = "t_sac_onecust_remark", schema = "csc")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
public class SacOneCustRemark {

    /**
     * 客户 ID
     */
    @Id("CUST_ID")
    private String custId;

    /**
     * 客户描述
     */
    @Column("REMARK")
    private String remark;

    /**
     * 用户阶段
     */
    @Column("USER_STAGE")
    private String userStage;

    /**
     * 上一个用户阶段
     */
    @Column("PREVIOUS_USER_STAGE")
    private String previousUserStage;

    /**
     * 线索等级(H/A/B/C/D/E)
     */
    @Column("CLUE_LEVEL")
    private String clueLevel;

    /**
     * 意向车型
     */
    @Column("INTENT_VEHICLE_CODE")
    private String intentVehicleCode;

    /**
     * 竞品车型
     */
    @Column("COMPETITIVE_VEHICLE_CODE")
    private String competitiveVehicleCode;

    /**
     * 所在地
     */
    @Column("LOCATION")
    private String location;

    /**
     * 线索来源
     */
    @Column("CLUE_SOURCE")
    private String clueSource;

    /**
     * 用户阶段-了解的时间
     */
    @Column("KONW_DATE")
    private LocalDateTime knowDate;

    /**
     * 用户阶段-到店的时间
     */
    @Column("TO_STORE_DATE")
    private LocalDateTime toStoreDate;

    /**
     * 用户阶段-试驾的时间
     */
    @Column("TEST_DRIVE_DATE")
    private LocalDateTime testDriveDate;

    /**
     * 用户阶段-下定的时间
     */
    @Column("PLACE_ORDER_DATE")
    private LocalDateTime placeOrderDate;

    /**
     * 上次活跃时间
     */
    @Column("LAST_ACTIVE_TIME")
    private LocalDateTime lastActiveTime;

    /**
     * 创建时间
     */
    @Column("CREATE_DATE")
    private LocalDateTime createDate;

    /**
     * 更新时间
     */
    @Column("UPDATE_DATE")
    private LocalDateTime updateDate;


}
