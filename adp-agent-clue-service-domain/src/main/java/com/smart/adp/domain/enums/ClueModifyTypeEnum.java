package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 线索更新的类型
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Getter
@AllArgsConstructor
public enum ClueModifyTypeEnum {

    /**
     * 用户描述
     */
    UserDescription(0, "用户描述"),
    /**
     * 用户标签
     */
    UserLable(1, "用户标签"),
    /**
     * 用户等级
     */
    UserLevel(2, "用户等级"),
    /**
     * 意向车型
     */
    IntendedVehicleModel(3, "意向车型"),
    /**
     * 所在地
     */
    Location(4, "所在地"),
    /**
     * 线索来源
     */
    SourceOfClues(5, "线索来源"),
    /**
     * 代理商线索来源
     */
    SourceOfAgentClues(6, "代理商线索来源"),
    /**
     * 竞品车型
     */
    CompetitorModels(7, "竞品车型"),
    /**
     * 特别关注
     */
    Special(8, "特别关注"),
    /**
     * 开始试驾更新意向车型试驾时间
     */
    StartDriving(9, "开始试驾更新意向车型试驾时间"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static ClueModifyTypeEnum getByCode(Integer code) {
        return Arrays.stream(ClueModifyTypeEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }

}
