package com.smart.adp.domain.entity.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 展车退役实体类
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_usc_bu_showcar_retire", schema = "mp")
public class ShowCarRetire implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 展车退役ID
     */
    @Column("RETIRE_APPLY_ID")
    private String retireApplyId;

    /**
     * 展车ID
     */
    @Column("SHOW_CAR_ID")
    private String showCarId;

    /**
     * 申请门店
     */
    @Column("APPLY_DLR_CODE")
    private String applyDlrCode;

    /**
     * 退役申请状态
     */
    @Column("RETIRE_CAR_STATUS")
    private String retireCarStatus;

    /**
     * 申请人
     */
    @Column("APPLY_NAME")
    private String applyName;

    /**
     * 申请日期
     */
    @Column("APPLY_DATE")
    private LocalDateTime applyDate;

    /**
     * 业务审批人
     */
    @Column("BU_AUTHER_NAME")
    private String buAutherName;

    /**
     * 业务审批日期
     */
    @Column("BU_AUTHER_DATE")
    private LocalDateTime buAutherDate;

    /**
     * 财务审批人
     */
    @Column("FI_AUTHER_NAME")
    private String fiAutherName;

    /**
     * 财务审批日期
     */
    @Column("FI_AUTHER_DATE")
    private LocalDateTime fiAutherDate;

    /**
     * 渠道审批人
     */
    @Column("BD_AUTHER_NAME")
    private String bdAutherName;

    /**
     * 渠道审批日期
     */
    @Column("BD_AUTHER_DATE")
    private LocalDateTime bdAutherDate;

    /**
     * 备注
     */
    @Column("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @Column("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @Column("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @Column("UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     *
     */
    @Column("SHOWCAR_RETIRE_CODE")
    private String showCarRetireCode;

    /**
     * 退役类型 1替换退役 2特殊退役
     */
    @Column("RETIRE_TYPE")
    private String retireType;

    /**
     * 零售订单号
     */
    @Column("RETAIL_NO")
    private String retailNo;

    /**
     * 特殊退役说明
     */
    @Column("SPECIAL_RETIRE_REMARK")
    private String specialRetireRemark;

    /**
     * 特殊退役说明附件
     */
    @Column("SPECIAL_RETIRE_URL")
    private String specialRetireUrl;

    /**
     * 明示销售协议
     */
    @Column("SALE_EXPRESS_URL")
    private String saleExpressUrl;

    /**
     * 补充折扣说明
     */
    @Column("DISCOUNT_SUP_REMARK")
    private String discountSupRemark;

    /**
     * 补充折扣附件
     */
    @Column("DISCOUNT_SUP_URL")
    private String discountSupUrl;

}
