package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.bo.clue.SacOnecustInfoEventBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.valueObject.clue.SacOnecustInfoEventVO;

/**
 * @Description: 用户事件gateway
 * @Author: rik.ren
 * @Date: 2025/3/14 15:51
 **/
public interface SacOnecustInfoEventGateway {

    DomainPage<SacOnecustInfoEventVO> querycustEventDomainPage(SacOnecustInfoEventVO param, SacOnecustInfoEventBO boParam,
                                                               QueryOrderBy orderBy,
                                                               QueryColumn... columns);
}
