package com.smart.adp.domain.bo.clue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import com.smart.adp.domain.enums.ClueStageEnum;
import com.smart.adp.domain.enums.ClueStatusEnum;
import com.smart.adp.domain.enums.CustEventEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.*;
import java.util.stream.Collectors;

import static com.smart.adp.domain.entity.clue.table.CustEventFlowTableDef.CUST_EVENT_FLOW;

/**
 * <p>
 * 客户事件流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
@Data
@ToString
@EqualsAndHashCode
public class CustEventFlowBO extends CustEventFlow {

    /**
     * 线索id集合
     */
    private List<String> listCustId;

    /**
     * 是否需要线索扩展信息
     */
    private Boolean needClueRemark;

    /**
     * 线索等级
     */
    private String LclueLevel;

    /**
     * 分页属性
     */
    private Page<CustEventFlowBO> pageObj;

    public static CustEventFlowBO conventBO(CustEventFlow entity) {
        CustEventFlowBO bo = new CustEventFlowBO();
        bo.setId(entity.getId());
        bo.setCustId(entity.getCustId());
        bo.setType(entity.getType());
        bo.setStage(entity.getStage());
        bo.setBusinessId(entity.getBusinessId());
        bo.setEventTime(entity.getEventTime());
        bo.setExtendJson(entity.getExtendJson());
        return bo;
    }

    public static List<CustEventFlowBO> conventBO(List<CustEventFlow> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        return entityList.stream().map(entity -> {
            CustEventFlowBO bo = conventBO(entity);
            return bo;
        }).collect(Collectors.toList());
    }

    public static List<CustEventFlowBO> aggregateByLatestEventTime(List<CustEventFlowBO> listResult) {
        if (listResult == null || listResult.isEmpty()) {
            return listResult;
        }

        // 使用Stream和Collectors进行聚合
        Map<Integer, CustEventFlowBO> aggregatedMap = listResult.stream().collect(Collectors.groupingBy(CustEventFlow::getStage
                , // 按 stage 分组
                Collectors.collectingAndThen(Collectors.maxBy((e1, e2) -> e1.getEventTime().compareTo(e2.getEventTime())), // 取
                        // eventTime 最大的记录
                        optional -> optional.orElse(null) // 将 Optional 转换为实际对象
                )));

        // 将Map的values转换为List
        return new ArrayList<>(aggregatedMap.values());
    }

    public static List<CustEventFlowBO> aggregateByStageOld(List<CustEventFlowBO> listResult) {
        if (CollectionUtil.isEmpty(listResult)) {
            return listResult;
        }

        // 使用 Stream 和 Collectors 进行聚合
        Map<Integer, CustEventFlowBO> aggregatedMap = listResult.stream().collect(Collectors.groupingBy(CustEventFlow::getStage
                , // 按 stage 分组
                Collectors.collectingAndThen(Collectors.toList(), // 将分组后的记录收集为 List
                        list -> {
                            if (list.isEmpty()) {
                                return null;
                            }
                            // 判断 stage 是否为 KNOW
                            if (list.get(0).getStage() == ClueStageEnum.KNOW.getCode()) {
                                // 对于 KNOW，取 eventTime 最小的记录
                                return list.stream().min(Comparator.comparing(CustEventFlow::getEventTime)).orElse(null);
                            } else {
                                // 对于其他 stage，取 eventTime 最大的记录
                                return list.stream().max(Comparator.comparing(CustEventFlow::getEventTime)).orElse(null);
                            }
                        })));

        // 将 Map 的 values 转换为 List
        return new ArrayList<>(aggregatedMap.values());
    }

    public static List<CustEventFlowBO> aggregateByStage(List<CustEventFlowBO> listResult, String statusCode) {
        if (CollectionUtil.isEmpty(listResult)) {
            return listResult;
        }

        // 处理退订逻辑：如果存在退订记录，则移除所有下定记录
        boolean hasReturn = listResult.stream().anyMatch(event -> event.getType() == CustEventEnum.RETURN.getCode());
        if (hasReturn) {
            listResult = listResult.stream()
                    .filter(event -> event.getType() != CustEventEnum.ORDER.getCode())
                    .collect(Collectors.toList());
        }
        //线索状态为非战败时，不展示战败线索
        if (Objects.nonNull(statusCode) && !ClueStatusEnum.DEFEATED.getCode().equals(statusCode)) {
            listResult = listResult.stream()
                    .filter(event -> event.getType() != CustEventEnum.DEFEAT.getCode())
                    .collect(Collectors.toList());
        }

        // 使用 Stream 和 Collectors 进行聚合
        Map<Integer, CustEventFlowBO> aggregatedMap = listResult.stream()
                .collect(Collectors.groupingBy(
                        CustEventFlow::getStage, // 按 stage 分组
                        Collectors.collectingAndThen(
                                Collectors.toList(), // 将分组后的记录收集为 List
                                list -> {
                                    if (list.isEmpty()) {
                                        return null;
                                    }
                                    // 判断 stage 是否为 KNOW
                                    if (list.get(0).getStage() == ClueStageEnum.KNOW.getCode()) {
                                        // 对于 KNOW，取 eventTime 最小的记录
                                        return list.stream()
                                                .min(Comparator.comparing(CustEventFlow::getEventTime))
                                                .orElse(null);
                                    } else if (list.get(0).getStage() == ClueStageEnum.ARRIVE.getCode()) {
                                        // 对于 ARRIVE，按优先级选择记录
                                        return getArriveEventByPriority(list);
                                    } else {
                                        // 对于其他 stage，取 eventTime 最大的记录
                                        return list.stream()
                                                .max(Comparator.comparing(CustEventFlow::getEventTime))
                                                .orElse(null);
                                    }
                                }
                        )
                ));

        // 将 Map 的 values 转换为 List
        return new ArrayList<>(aggregatedMap.values());
    }

    /**
     * 根据优先级选择到店场景的事件
     */
    private static CustEventFlowBO getArriveEventByPriority(List<CustEventFlowBO> arriveEvents) {
        // 按优先级排序：线下客流 > 用户真正到店 > 用户产生试驾
        arriveEvents.sort((e1, e2) -> {
            int priority1 = getArrivePriority(e1);
            int priority2 = getArrivePriority(e2);
            return Integer.compare(priority2, priority1); // 降序排序
        });

        // 返回优先级最高的事件
        return arriveEvents.get(0);
    }

    /**
     * 获取到店场景的优先级
     */
    private static int getArrivePriority(CustEventFlowBO event) {
        // 根据 extend_json 中的 source 字段判断优先级
        JSONObject extendJson = event.getExtendJson();
        if (extendJson != null) {
            String source = extendJson.getString("source");
            if ("CreateEventHandler".equals(source)) {
                return 3; // 线下客流，优先级最高
            } else if ("ReviewEventHandler".equals(source)) {
                return 2; // 用户真正到店
            } else if ("DriveEventHandler".equals(source)) {
                return 1; // 用户产生试驾
            }
        }
        return 0; // 默认优先级
    }


    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public CustEventFlow conditions(QueryWrapper wrapper) {
        wrapper.and(CUST_EVENT_FLOW.CUST_ID.eq(getCustId(), StringUtil::hasText))
                .and(CUST_EVENT_FLOW.CUST_ID.in(getListCustId(), CollectionUtil::isNotEmpty))
                .and(CUST_EVENT_FLOW.TYPE.eq(getType(), ObjectUtil::isNotNull));
        return this;
    }
}
