package com.smart.adp.domain.entity.order;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Table(value = "t_orc_ve_bu_sale_order_to_c", schema = "orc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class VeBuSaleOrderToC {

    /**
     * 销售订单ID
     */
    @Id(value = "SALE_ORDER_ID")
    private String saleOrderId;

    /**
     * 销售单号
     */
    @Column("SALE_ORDER_CODE")
    private String saleOrderCode;

    /**
     * ONE ID
     */
    @Column("ONE_ID")
    private String oneId;

    /**
     * 客户ID
     */
    @Column("BUY_CUST_ID")
    private String buyCustId;

    /**
     * 客户姓名
     */
    @Column("BUY_CUST_NAME")
    private String buyCustName;

    /**
     * 客户电话
     */
    @Column("BUY_CUST_PHONE")
    private String buyCustPhone;

    /**
     * 车主ID
     */
    @Column("CAR_CUST_ID")
    private String carCustId;

    /**
     * 车系ID
     */
    @Column("CAR_SERIES_ID")
    private String carSeriesId;

    /**
     * 车系编码
     */
    @Column("CAR_SERIES_CODE")
    private String carSeriesCode;

    /**
     * 车系名称
     */
    @Column("CAR_SERIES_NAME")
    private String carSeriesName;

    /**
     * 车型ID
     */
    @Column("CAR_SMALL_TYPE_ID")
    private String carSmallTypeId;

    /**
     * 车型编码
     */
    @Column("SMALL_CAR_TYPE_CODE")
    private String smallCarTypeCode;

    /**
     * 车型名称
     */
    @Column("CAR_SMALL_TYPE_NAME")
    private String carSmallTypeName;

    /**
     * 车型配置ID
     */
    @Column("CAR_CONFIG_ID")
    private String carConfigId;

    /**
     * 车型配置编码
     */
    @Column("CAR_CONFIG_CODE")
    private String carConfigCode;

    /**
     * 车型配置名称
     */
    @Column("CAR_CONFIG_CN")
    private String carConfigCn;

    /**
     * 车身颜色ID
     */
    @Column("CAR_COLOR_ID")
    private String carColorId;

    /**
     * 车身颜色编码
     */
    @Column("CAR_COLOR_CODE")
    private String carColorCode;

    /**
     * 车身颜色名称
     */
    @Column("CAR_COLOR_NAME")
    private String carColorName;

    /**
     * 内饰颜色ID
     */
    @Column("CAR_INCOLOR_ID")
    private String carIncolorId;

    /**
     * 内饰颜色编码
     */
    @Column("CAR_INCOLOR_CODE")
    private String carIncolorCode;

    /**
     * 内饰颜色名称
     */
    @Column("CAR_INCOLOR_NAME")
    private String carIncolorName;

    /**
     * 选装包ID
     */
    @Column("OPTION_ID")
    private String optionId;

    /**
     * 选装包编码
     */
    @Column("OPTION_CODE")
    private String optionCode;

    /**
     * 选装包名称
     */
    @Column("OPTION_NAME")
    private String optionName;

    /**
     * VIN
     */
    @Column("VIN")
    private String vin;

    /**
     * 销售城市编码
     */
    @Column("SALE_CITY_CODE")
    private String saleCityCode;

    /**
     * 销售城市名称
     */
    @Column("SALE_CITY_NAME")
    private String saleCityName;

    /**
     * 销售店ID
     */
    @Column("SALE_DLR_ID")
    private String saleDlrId;

    /**
     * 销售店简称
     */
    @Column("SALE_SHORT_DLR_NAME")
    private String saleShortDlrName;

    /**
     * 交付城市编码
     */
    @Column("DELIVERY_CITY_CODE")
    private String deliveryCityCode;

    /**
     * 交付城市名称
     */
    @Column("DELIVERY_CITY_NAME")
    private String deliveryCityName;

    /**
     * 交付店ID
     */
    @Column("DELIVERY_DLR_ID")
    private String deliveryDlrId;

    /**
     * 交付店简称
     */
    @Column("DELIVERY_SHORT_DLR_NAME")
    private String deliveryShortDlrName;

    /**
     * 销售顾问ID
     */
    @Column("DLR_EMP_ID")
    private String dlrEmpId;

    /**
     * 销售顾问姓名
     */
    @Column("DLR_EMP_NAME")
    private String dlrEmpName;

    /**
     * 销售顾问电话
     */
    @Column("DLR_EMP_PHONE")
    private String dlrEmpPhone;

    /**
     * CCC客服ID
     */
    @Column("CCC_SERVICE_ID")
    private String cccServiceId;

    /**
     * CCC客服姓名
     */
    @Column("CCC_SERVICE_NAME")
    private String cccServiceName;

    /**
     * CCC客服电话
     */
    @Column("CCC_SERVICE_PHONE")
    private String cccServicePhone;

    /**
     * 退订申请单号
     */
    @Column("SALE_ORDER_RETURN_CODE")
    private String saleOrderReturnCode;

    /**
     * 价格带
     */
    @Column("SALE_PRICE_INTENT")
    private String salePriceIntent;

    /**
     * 小订金额
     */
    @Column("INTENT_ORDER_AMOUNT")
    private BigDecimal intentOrderAmount;

    /**
     * 小订支付时间
     */
    @Column("SAMLL_ORDER_PAY_DATE")
    private LocalDateTime samllOrderPayDate;

    /**
     * 大定金额
     */
    @Column("DEPOSIT_AMOUNT")
    private BigDecimal depositAmount;

    /**
     * 大定支付时间
     */
    @Column("DEPOSIT_PAY_DATE")
    private LocalDateTime depositPayDate;

    /**
     * 大定渠道
     */
    @Column("DEPOSIT_TYPE")
    private String depositType;

    /**
     * 首付金额
     */
    @Column("DOWN_PAY_AMOUNT")
    private BigDecimal downPayAmount;

    /**
     * 尾款金额
     */
    @Column("BALANCE_PAY_AMOUNT")
    private BigDecimal balancePayAmount;

    /**
     * 上牌服务费
     */
    @Column("REGISTRA_AMOUNT")
    private BigDecimal registraAmount;

    /**
     * 上牌城市编码
     */
    @Column("EXPECTED_LICENSED_CITY_CODE")
    private String expectedLicensedCityCode;

    /**
     * 上牌城市名称
     */
    @Column("EXPECTED_LICENSED_CITY_NAME")
    private String expectedLicensedCityName;

    /**
     * 车款应收金额
     */
    @Column("CAR_ACCOUNT_AMOUNT")
    private BigDecimal carAccountAmount;

    /**
     * 车款已收金额
     */
    @Column("CAR_RECEIVED_AMOUNT")
    private BigDecimal carReceivedAmount;

    /**
     * 订单渠道编码
     */
    @Column("SALE_CHANNEL_CODE")
    private String saleChannelCode;

    /**
     * 订单渠道名称
     */
    @Column("SALE_CHANNEL_NAME")
    private String saleChannelName;

    /**
     * 电商订单状态编码
     */
    @Column("ESALE_STATE_CODE")
    private String esaleStateCode;

    /**
     * 电商订单状态名称
     */
    @Column("ESALE_STATE_NAME")
    private String esaleStateName;

    /**
     * 订单状态编码
     */
    @Column("SALE_ORDER_STATE")
    private String saleOrderState;

    /**
     * 订单状态名称
     */
    @Column("SALE_ORDER_STATE_CN")
    private String saleOrderStateCn;

    /**
     * 退订状态编码
     */
    @Column("RETURN_STATE_CODE")
    private String returnStateCode;

    /**
     * 退订状态名称
     */
    @Column("RETURN_STATE_NAME")
    private String returnStateName;

    /**
     * 订单下单时间
     */
    @Column("SALE_ORDER_DATE")
    private LocalDateTime saleOrderDate;

    /**
     * 备注
     */
    @Column("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @Column("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @Column("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制字段
     */
    @Column("UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 创建人姓名
     */
    @Column("CREATED_NAME")
    private String createdName;

    /**
     * 修改人姓名
     */
    @Column("MODIFY_NAME")
    private String modifyName;

    /**
     * 厂商标识ID
     */
    @Column("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column("GROUP_ID")
    private String groupId;

    /**
     * 是否购买服务包
     */
    @Column("IS_BUY_NETWORK")
    private String isBuyNetwork;

    /**
     * 服务包购买时间
     */
    @Column("BUY_NETWORK_DATE")
    private LocalDateTime buyNetworkDate;

    /**
     * 服务包金额
     */
    @Column("NETWORK_AMOUNT")
    private BigDecimal networkAmount;

    /**
     * 购买方式ID
     */
    @Column("BUY_TYPE_ID")
    private String buyTypeId;

    /**
     * 购买方式名称
     */
    @Column("BUY_TYPE_CN")
    private String buyTypeCn;

    /**
     * 销售店类型
     */
    @Column("SALE_DLR_TYPE")
    private String saleDlrType;

    /**
     * 客户性别
     */
    @Column("BUY_CUST_GENDER")
    private String buyCustGender;

    /**
     * 销售订单类型编码
     */
    @Column("SALE_TYPE_CODE")
    private String saleTypeCode;

    /**
     * 销售订单类型名称
     */
    @Column("SALE_TYPE_NAME")
    private String saleTypeName;

    /**
     * 车辆驱动类型编码
     */
    @Column("CAR_DRIVE_TYPE_CODE")
    private String carDriveTypeCode;

    /**
     * 车辆驱动类型名称
     */
    @Column("CAR_DRIVE_TYPE_NAME")
    private String carDriveTypeName;

    /**
     * 第一次收款时间(大定收款时间)
     */
    @Column("FIRST_GATHERING_DATE")
    private LocalDateTime firstGatheringDate;

    /**
     * 是否支付首付
     */
    @Column("IS_PAY_FIRST")
    private String isPayFirst;

    /**
     * 品牌编码
     */
    @Column("CAR_BRAND_CODE")
    private String carBrandCode;

    /**
     * 品牌名称
     */
    @Column("CAR_BRAND_CN")
    private String carBrandCn;

    /**
     * 分配客户经理时间
     */
    @Column("DISTRIT_EMP_DATE")
    private LocalDateTime distritEmpDate;

    /**
     * 企业名称
     */
    @Column("COMPANY_NAME")
    private String companyName;

    /**
     * 企业代码
     */
    @Column("COMPANY_CODE")
    private String companyCode;

    /**
     * 客户期望交车时间
     */
    @Column("EXPECTED_DELIVERY_TIME")
    private LocalDateTime expectedDeliveryTime;

    /**
     * 交付方式
     */
    @Column("DELIVERY_TYPE")
    private String deliveryType;

    /**
     * 交付地点
     */
    @Column("DELIVERY_ADDR")
    private String deliveryAddr;

    /**
     * 锁定时间
     */
    @Column("LOCK_TIME")
    private LocalDateTime lockTime;
    /**
     * 车辆用途
     */
    @Column("VEHICLE_PURPOSE")
    private String vehiclePurpose;

    /**
     * 车辆类型
     */
    @Column("VEHICLE_SOURCE")
    private String vehicleSource;

    /**
     * MSRP-无补贴
     */
    @Column("MSRP")
    private BigDecimal msrp;

    /**
     * 补贴金额
     */
    @Column("SUBSIDY")
    private BigDecimal subsidy;

    /**
     * MSRP-有补贴
     */
    @Column("MSRP2")
    private BigDecimal msrp2;

    /**
     * 金融方案编号
     */
    @Column("FINANCE_PLAN")
    private String financePlan;

    /**
     * 金融方案名称
     */
    @Column("FINANCE_PLAN_NAME")
    private String financePlanName;

    /**
     * 金融机构ID
     */
    @Column("FINANCE_ORG_ID")
    private String financeOrgId;

    /**
     * 金融机构名称
     */
    @Column("FINANCE_ORG")
    private String financeOrg;

    /**
     * 首付比例
     */
    @Column("DOWN_PAYMENT")
    private BigDecimal downPayment;

    /**
     * 贷款期限
     */
    @Column("LOAN_TERM")
    private String loanTerm;

    /**
     * 金融方案状态
     */
    @Column("FINANCE_PLAN_STATUS")
    private String financePlanStatus;

    /**
     * 分配时间
     */
    @Column("DELIVERY_ALLOCATE_TIME")
    private LocalDateTime deliveryAllocateTime;

    /**
     * 交付专员名字
     */
    @Column("DELIVERY_NAME")
    private String deliveryName;

    /**
     * 交付专员员工号
     */
    @Column("DELIVERY_EMPLOYEE")
    private String deliveryEmployee;

    /**
     * 交付专员手机号
     */
    @Column("DELIVERY_PHONE")
    private String deliveryPhone;

    /**
     * 牌照类别
     */
    @Column("LICENSED_TYPE")
    private String licensedType;

    /**
     * 发票未税金额
     */
    @Column("INVOICE_AMOUNT")
    private BigDecimal invoiceAmount;

    /**
     * 开票日期
     */
    @Column("INVOICE_AMOUNT_DATE")
    private LocalDateTime invoiceAmountDate;

    /**
     * 交车日期
     */
    @Column("DELIVERY_DATE")
    private LocalDateTime deliveryDate;

    /**
     * 贷款金额
     */
    @Column("LOAN_AMOUNT")
    private BigDecimal loanAmount;

    /**
     * 预计交车时间
     */
    @Column("ESTIMATED_DELIVERY_TIME")
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 预约到店时间
     */
    @Column("ARRIVAL_TIME")
    private LocalDateTime arrivalTime;

    /**
     * 车辆到店入库时间
     */
    @Column("VEHICLE_ARRIVAL_TIME")
    private LocalDateTime vehicleArrivalTime;

    /**
     * 车辆销售出库时间
     */
    @Column("VEHICLE_SALES_TIME")
    private LocalDateTime vehicleSalesTime;

    /**
     * 定金支付状态编码
     */
    @Column("DEPOSIT_PAYMENT_STATUS")
    private String depositPaymentStatus;

    /**
     * 支付渠道
     */
    @Column("PAIED_CHANNEL")
    private String paiedChannel;

    /**
     * 订单状态修改时间
     */
    @Column("ORDER_MODIFIED_TIME")
    private LocalDateTime orderModifiedTime;

    /**
     * 购买人
     */
    @Column("ORDER_USER_NAME")
    private String orderUserName;

    /**
     * 购买人电话
     */
    @Column("MOBILE_NO")
    private String mobileNo;

    /**
     * 车主姓名
     */
    @Column("OWNER_USER_NAME")
    private String ownerUserName;

    /**
     * 证件类型
     */
    @Column("ID_TYPE")
    private String idType;

    /**
     * 证件号码
     */
    @Column("ID_CARD_NO")
    private String idCardNo;

    /**
     * 支付状态
     */
    @Column("PAIED_STATUS")
    private String paiedStatus;

    /**
     * 开票状态
     */
    @Column("INVOICE_STATUS")
    private String invoiceStatus;

    /**
     * 折扣金额
     */
    @Column("DISCOUNT")
    private BigDecimal discount;

    /**
     * 适用税率
     */
    @Column("APPLICABLE_TAX_TATE")
    private BigDecimal applicableTaxTate;

    /**
     * 不含税订单金额
     */
    @Column("EXCLUDING_TAX_PRICE")
    private BigDecimal excludingTaxPrice;

    /**
     * 优惠金额
     */
    @Column("USER_RIGHTS_DISCOUNT")
    private BigDecimal userRightsDiscount;

    /**
     * 含税订单金额
     */
    @Column("ORDER_AMOUNT")
    private BigDecimal orderAmount;

    /**
     * 是否小订转大定订单
     */
    @Column("IS_TRANSFORM_ORDER")
    private String isTransformOrder;

    /**
     * 小订支付状态
     */
    @Column("INTENTION_STATUS")
    private String intentionStatus;

    /**
     * smartId
     */
    @Column("SMART_ID")
    private String smartId;

    /**
     * 下单渠道 ec
     */
    @Column("ORDER_CHANNEL")
    private String orderChannel;

    /**
     * 下单时订单类型
     */
    @Column("FIRST_ORDER_TYPE")
    private String firstOrderType;

    /**
     * 小订转大定的差额
     */
    @Column("TRANSFORM_ORDER_AMOUNT")
    private BigDecimal transformOrderAmount;

    /**
     * 退订时间
     */
    @Column("ORDER_REFUND_TIME")
    private LocalDateTime orderRefundTime;

    /**
     * 退订金额
     */
    @Column("CANCELLATION_AMOUNT")
    private BigDecimal cancellationAmount;

    /**
     * 当前订单类型 用户选择大定或者小订 1. 小订 2. 大定
     */
    @Column("CURRENT_ORDER_TYPE")
    private String currentOrderType;

    /**
     * 车辆类型名称
     */
    @Column("VEHICLE_SOURCE_NAME")
    private String vehicleSourceName;

    /**
     * 支付渠道名称
     */
    @Column("PAIED_CHANNEL_NAME")
    private String paiedChannelName;

    /**
     * 开票状态名称
     */
    @Column("INVOICE_STATUS_NAME")
    private String invoiceStatusName;

    /**
     * 证件类型名称
     */
    @Column("ID_TYPE_NAME")
    private String idTypeName;

    /**
     * 金融佣金状态名称
     */
    @Column("FINANCE_PLAN_STATUS_NAME")
    private String financePlanStatusName;

    /**
     * 已付定金
     */
    @Column("DEPOSITED_PAID")
    private BigDecimal depositedPaid;

    /**
     * 展车折扣金额
     */
    @Column("SHOWCAR_DISCOUNT")
    private BigDecimal showcarDiscount;

    /**
     * 车辆类型 1.新车；2.展车3. 特价车(现车)
     */
    @Column("CAR_SOURCE")
    private String carSource;

    /**
     * 预约交车时间
     */
    @Column("ORDER_DELIVERY_TIME")
    private LocalDateTime orderDeliveryTime;

    /**
     * 充电桩意向
     */
    @Column("INTENTION_OF_CHARGING")
    private String intentionOfCharging;

    /**
     * 客户许愿备注
     */
    @Column("CUSTOMER_WISH_REMARKS")
    private String customerWishRemarks;

    /**
     * 验车方式
     */
    @Column("ACCEPTANCE_CHECK")
    private String acceptanceCheck;

    /**
     * 支付方式
     */
    @Column("DELIVERY_METHOD")
    private String deliveryMethod;

    /**
     * 发票号
     */
    @Column("INVOICE_NO")
    private String invoiceNo;

    /**
     * 发票代码
     */
    @Column("INVOICE_CODE")
    private String invoiceCode;

    /**
     * 整车合同下载URL
     */
    @Column("CONTRACT_DOC_DOWNLOAD_URL")
    private String contractDocDownloadUrl;

    /**
     * 锁单对应门店
     */
    @Column("PLACE_ORDER_DLR_CODE")
    private String placeOrderDlrCode;

    /**
     * 冻结解冻状态  0-未冻结（解冻），1-冻结
     */
    @Column("FROZE_STATUS")
    private String frozeStatus;

    /**
     * 订单归属门店
     */
    @Column("VEST_ORDER_DLR_CODE")
    private String vestOrderDlrCode;

    /**
     * 是否代付
     */
    @Column("IS_PAY_FOR_ANOTHER")
    private String isPayForAnother;

    /**
     * 大客户合同单编号
     */
    @Column("BIG_CUSTOMER_ORDER_NO")
    private String bigCustomerOrderNo;

    /**
     * 客户编码
     */
    @Column("CUSTOMER_CODE")
    private String customerCode;

    /**
     * 是否批售
     */
    @Column("WHOLESALE_OR_NOT")
    private String wholesaleOrNot;

    /**
     * 产品专家姓名
     */
    @Column("product_name")
    private String productName;

    /**
     * 产品专家工号
     */
    @Column("product_code")
    private String productCode;

    /**
     * 归属门店产品专家工号
     */
    @Column("VEST_ORDER_PRODUCT_CODE")
    private String vestOrderProductCode;

    /**
     * 归属门店产品专家
     */
    @Column("VEST_ORDER_PRODUCT_NAME")
    private String vestOrderProductName;

    /**
     * 冻结时间
     */
    @Column("FROZE_TIME")
    private LocalDateTime frozeTime;

    /**
     * 冻结原因
     */
    @Column("FROZE_CAUSE")
    private String frozeCause;

    /**
     * 现车业务类型
     */
    @Column("CAR_SALE_BUSINESS_TYPE")
    private String carSaleBusinessType;

    /**
     * 现车业务类型名称
     */
    @Column("CAR_SALE_BUSINESS_TYPE_NAME")
    private String carSaleBusinessTypeName;

    /**
     * 数电发票号
     */
    @Column("SD_INVOICE_NO")
    private String sdInvoiceNo;
}