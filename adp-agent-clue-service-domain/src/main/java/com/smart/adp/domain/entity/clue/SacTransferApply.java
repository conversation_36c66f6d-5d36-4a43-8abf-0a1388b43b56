package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@Table(value = "t_sac_transfer_apply", schema = "csc")
public class SacTransferApply implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 申请ID
	 */
	@Id("APPLY_ID")
	private String applyId;

	/**
	 * 业务单号
	 */
	@Column("BILL_CODE")
	private String billCode;

	/**
	 * 客户ID
	 */
	@Column("CUST_ID")
	private String custId;

	/**
	 * 客户名称
	 */
	@Column("CUST_NAME")
	private String custName;

	/**
	 * 联系号码
	 */
	@Column("PHONE")
	private String phone;

	/**
	 * 调出门店编码
	 */
	@Column("OUT_DLR_CODE")
	private String outDlrCode;

	/**
	 * 调出门店名称
	 */
	@Column("OUT_DLR_NAME")
	private String outDlrName;

	/**
	 * 调入门店编码
	 */
	@Column("IN_DLR_CODE")
	private String inDlrCode;

	/**
	 * 调入门店名称
	 */
	@Column("IN_DLR_NAME")
	private String inDlrName;

	/**
	 * 申请原因
	 */
	@Column("APPLY_DESC")
	private String applyDesc;

	/**
	 * 扩展字段1
	 */
	@Column("COLUMN1")
	private String column1;

	/**
	 * 扩展字段2
	 */
	@Column("COLUMN2")
	private String column2;

	/**
	 * 扩展字段3
	 */
	@Column("COLUMN3")
	private String column3;

	/**
	 * 扩展字段4
	 */
	@Column("COLUMN4")
	private String column4;

	/**
	 * 扩展字段5
	 */
	@Column("COLUMN5")
	private String column5;

	/**
	 * 扩展字段6
	 */
	@Column("COLUMN6")
	private String column6;

	/**
	 * 扩展字段7
	 */
	@Column("COLUMN7")
	private String column7;

	/**
	 * 扩展字段8
	 */
	@Column("COLUMN8")
	private String column8;

	/**
	 * 扩展字段9
	 */
	@Column("COLUMN9")
	private String column9;

	/**
	 * 扩展字段10
	 */
	@Column("COLUMN10")
	private String column10;

	/**
	 * JSON扩展字段
	 */
	@Column("EXTENDS_JSON")
	private String extendsJson;

	/**
	 * 厂商标识ID
	 */
	@Column("OEM_ID")
	private String oemId;

	/**
	 * 集团标识ID
	 */
	@Column("GROUP_ID")
	private String groupId;

	/**
	 * 创建人ID
	 */
	@Column("CREATOR")
	private String creator;

	/**
	 * 创建人
	 */
	@Column("CREATED_NAME")
	private String createdName;

	/**
	 * 创建日期
	 */
	@Column("CREATED_DATE")
	private LocalDateTime createdDate;

	/**
	 * 修改人ID
	 */
	@Column("MODIFIER")
	private String modifier;

	/**
	 * 修改人
	 */
	@Column("MODIFY_NAME")
	private String modifyName;

	/**
	 * 最后更新日期
	 */
	@Column("LAST_UPDATED_DATE")
	private LocalDateTime lastUpdatedDate;

	/**
	 * 是否可用
	 */
	@Column("IS_ENABLE")
	private String isEnable;

	/**
	 * 并发控制ID
	 */
	@Column("UPDATE_CONTROL_ID")
	private String updateControlId;
}