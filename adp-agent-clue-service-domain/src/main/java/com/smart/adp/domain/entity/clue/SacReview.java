package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import static com.smart.adp.domain.entity.clue.table.SacReviewTableDef.SAC_REVIEW;


@Table(value = "t_sac_review", schema = "csc")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SacReview implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 回访ID
     */
    @Column("REVIEW_ID")
    private String reviewId;

    /**
     * 所属组织
     */
    @Column("ORG_CODE")
    private String orgCode;

    /**
     * 所属组织名称
     */
    @Column("ORG_NAME")
    private String orgName;

    /**
     * 回访单据类型
     */
    @Column("BILL_TYPE")
    private String billType;

    /**
     * 回访单据类型名称
     */
    @Column("BILL_TYPE_NAME")
    private String billTypeName;

    /**
     * 回访单据业务类型
     */
    @Column("BUSINESS_TYPE")
    private String businessType;

    /**
     * 回访单据业务类型名称
     */
    @Column("BUSINESS_TYPE_NAME")
    private String businessTypeName;

    /**
     * 一级信息来源编码
     */
    @Column("INFO_CHAN_M_CODE")
    private String infoChanMCode;

    /**
     * 一级信息来源名称
     */
    @Column("INFO_CHAN_M_NAME")
    private String infoChanMName;

    /**
     * 二级信息来源编码
     */
    @Column("INFO_CHAN_D_CODE")
    private String infoChanDCode;

    /**
     * 二级信息来源名称
     */
    @Column("INFO_CHAN_D_NAME")
    private String infoChanDName;

    /**
     * 三级信息来源编码
     */
    @Column("INFO_CHAN_DD_CODE")
    private String infoChanDdCode;

    /**
     * 三级信息来源名称
     */
    @Column("INFO_CHAN_DD_NAME")
    private String infoChanDdName;

    /**
     * 最低一级的信息来源编码
     */
    @Column("CHANNEL_CODE")
    private String channelCode;

    /**
     * 最低一级的信息来源名称
     */
    @Column("CHANNEL_NAME")
    private String channelName;

    /**
     * 关联业务单号
     */
    @Column("BILL_CODE")
    private String billCode;

    /**
     * 计划回访时间
     */
    @Column("PLAN_REVIEW_TIME")
    private LocalDateTime planReviewTime;

    /**
     * 实际回访时间
     */
    @Column("REVIEW_TIME")
    private LocalDateTime reviewTime;

    /**
     * 上次回访时间
     */
    @Column("LAST_REVIEW_TIME")
    private LocalDateTime lastReviewTime;

    /**
     * 超期回访时间(到这个时间算超期)
     */
    @Column("OVER_REVIEW_TIME")
    private LocalDateTime overReviewTime;

    /**
     * 计划到店时间
     */
    @Column("PLAN_COME_TIME")
    private LocalDateTime planComeTime;

    /**
     * 实际到店时间
     */
    @Column("FACT_COME_TIME")
    private LocalDateTime factComeTime;

    /**
     * 是否到店
     */
    @Column("IS_COME")
    private String isCome;

    /**
     * 分配状态
     */
    @Column("ASSIGN_STATUS")
    private String assignStatus;

    /**
     * 分配状态名称
     */
    @Column("ASSIGN_STATUS_NAME")
    private String assignStatusName;

    /**
     * 分配时间
     */
    @Column("ASSIGN_TIME")
    private LocalDateTime assignTime;

    /**
     * 分配人用户ID
     */
    @Column("ASSIGN_PERSON_ID")
    private String assignPersonId;

    /**
     * 分配人名称
     */
    @Column("ASSIGN_PERSON_NAME")
    private String assignPersonName;

    /**
     * 回访人员用户ID
     */
    @Column("REVIEW_PERSON_ID")
    private String reviewPersonId;

    /**
     * 回访人员名称
     */
    @Column("REVIEW_PERSON_NAME")
    private String reviewPersonName;

    /**
     * 回访内容
     */
    @Column("REVIEW_DESC")
    private String reviewDesc;

    /**
     * 回访状态编码
     */
    @Column("REVIEW_STATUS")
    private String reviewStatus;

    /**
     * 回访状态名称
     */
    @Column("REVIEW_STATUS_NAME")
    private String reviewStatusName;

    /**
     * 客户ID
     */
    @Column("CUST_ID")
    private String custId;

    /**
     * 客户名称
     */
    @Column("CUST_NAME")
    private String custName;

    /**
     * 客户手机号
     */
    @Column("PHONE")
    private String phone;

    /**
     * 客户性别
     */
    @Column("GENDER")
    private String gender;

    /**
     * 客户性别名称
     */
    @Column("GENDER_NAME")
    private String genderName;

    /**
     * 接触状态编码
     */
    @Column("TOUCH_STATUS")
    private String touchStatus;

    /**
     * 接触状态名称
     */
    @Column("TOUCH_STATUS_NAME")
    private String touchStatusName;

    /**
     * 异常(战败失控)原因编码
     */
    @Column("ERROR_REASON_CODE")
    private String errorReasonCode;

    /**
     * 异常(战败失控)原因名称
     */
    @Column("ERROR_REASON_NAME")
    private String errorReasonName;

    /**
     * 节点编码
     */
    @Column("NODE_CODE")
    private String nodeCode;

    /**
     * 节点名称
     */
    @Column("NODE_NAME")
    private String nodeName;

    /**
     * 下发经销商编码
     */
    @Column("SEND_DLR_CODE")
    private String sendDlrCode;

    /**
     * 下发经销商名称
     */
    @Column("SEND_DLR_SHORT_NAME")
    private String sendDlrShortName;

    /**
     * 下发时间
     */
    @Column("SEND_TIME")
    private LocalDateTime sendTime;

    /**
     * 意向级别编码
     */
    @Column("INTEN_LEVEL_CODE")
    private String intenLevelCode;

    /**
     * 意向级别名称
     */
    @Column("INTEN_LEVEL_NAME")
    private String intenLevelName;

    /**
     * 意向品牌编码
     */
    @Column("INTEN_BRAND_CODE")
    private String intenBrandCode;

    /**
     * 意向品牌名称
     */
    @Column("INTEN_BRAND_NAME")
    private String intenBrandName;

    /**
     * 意向车系编码
     */
    @Column("INTEN_SERIES_CODE")
    private String intenSeriesCode;

    /**
     * 意向车系名称
     */
    @Column("INTEN_SERIES_NAME")
    private String intenSeriesName;

    /**
     * 意向车型编码
     */
    @Column("INTEN_CAR_TYPE_CODE")
    private String intenCarTypeCode;

    /**
     * 意向车型名称
     */
    @Column("INTEN_CAR_TYPE_NAME")
    private String intenCarTypeName;

    /**
     * 扩展字段1
     * 是否自动分配：1是 0否
     */
    @Column("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @Column("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @Column("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @Column("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @Column("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @Column("COLUMN10")
    private String column10;

    /**
     * 扩展字段11
     */
    @Column("COLUMN11")
    private String column11;

    /**
     * 扩展字段12
     */
    @Column("COLUMN12")
    private String column12;

    /**
     * 扩展字段13
     */
    @Column("COLUMN13")
    private String column13;

    /**
     * 扩展字段14
     */
    @Column("COLUMN14")
    private String column14;

    /**
     * 扩展字段15
     */
    @Column("COLUMN15")
    private String column15;

    /**
     * 扩展字段16
     */
    @Column("COLUMN16")
    private String column16;

    /**
     * 扩展字段17
     */
    @Column("COLUMN17")
    private String column17;

    /**
     * 扩展字段18
     */
    @Column("COLUMN18")
    private String column18;

    /**
     * 扩展字段19
     */
    @Column("COLUMN19")
    private String column19;

    /**
     * 扩展字段20
     */
    @Column("COLUMN20")
    private String column20;

    /**
     * 大字段1
     */
    @Column("BIG_COLUMN1")
    private String bigColumn1;

    /**
     * 大字段2
     */
    @Column("BIG_COLUMN2")
    private String bigColumn2;

    /**
     * 大字段3
     */
    @Column("BIG_COLUMN3")
    private String bigColumn3;

    /**
     * 大字段4
     */
    @Column("BIG_COLUMN4")
    private String bigColumn4;

    /**
     * 大字段5
     */
    @Column("BIG_COLUMN5")
    private String bigColumn5;

    /**
     * JSON扩展字段
     */
    @Column("EXTENDS_JSON")
    private String extendsJson;

    /**
     * 厂商标识ID
     */
    @Column("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column("GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 省份编码
     */
    @Column("PROVINCE_CODE")
    private String provinceCode;

    /**
     * 省份名称
     */
    @Column("PROVINCE_NAME")
    private String provinceName;

    /**
     * 城市编码
     */
    @Column("CITY_CODE")
    private String cityCode;

    /**
     * 城市名称
     */
    @Column("CITY_NAME")
    private String cityName;

    /**
     * 区县编码
     */
    @Column("COUNTY_CODE")
    private String countyCode;

    /**
     * 区县名称
     */
    @Column("COUNTY_NAME")
    private String countyName;

    /**
     * 城市公司编码
     */
    @Column("CITY_FIRM_CODE")
    private String cityFirmCode;

    /**
     * 城市公司名称
     */
    @Column("CITY_FIRM_NAME")
    private String cityFirmName;

    /**
     * 线索移交编码
     */
    @Column("MANAGE_LABEL_CODE")
    private String manageLabelCode;

    /**
     * 线索移交名称
     */
    @Column("MANAGE_LABEL_NAME")
    private String manageLabelName;

    /**
     * 战败一级原因编码
     */
    @Column("FIRST_REASON_CODE")
    private String firstReasonCode;

    /**
     * 战败一级原因中文
     */
    @Column("FIRST_REASON_NAME")
    private String firstReasonName;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacReview conditions(QueryWrapper wrapper) {
        wrapper.and(SAC_REVIEW.REVIEW_ID.eq(getReviewId(), StringUtil::hasText))
                .and(SAC_REVIEW.CUST_ID.eq(getCustId(), StringUtil::hasText))
                .and(SAC_REVIEW.REVIEW_PERSON_ID.eq(getReviewPersonId(), StringUtil::hasText))
                .and(SAC_REVIEW.PHONE.eq(getPhone(), StringUtil::hasText))
                .and(SAC_REVIEW.INTEN_CAR_TYPE_CODE.eq(getIntenCarTypeCode(), StringUtil::hasText))
                .and(SAC_REVIEW.ASSIGN_STATUS.eq(getAssignStatus(), StringUtil::hasText))
                .and(SAC_REVIEW.BILL_CODE.eq(getBillCode(), StringUtil::hasText))
                .and(SAC_REVIEW.IS_COME.eq(getIsCome(), StringUtil::hasText))
                .and(SAC_REVIEW.COLUMN12.eq(getColumn12(), StringUtil::hasText))
                .and(SAC_REVIEW.INFO_CHAN_MCODE.eq(getInfoChanMCode(), StringUtil::hasText))
                .and(SAC_REVIEW.INFO_CHAN_DCODE.eq(getInfoChanDCode(), StringUtil::hasText))
                .and(SAC_REVIEW.ORG_CODE.eq(getOrgCode(), StringUtil::hasText))
                .and(SAC_REVIEW.REVIEW_STATUS.eq(getReviewStatus(), StringUtil::hasText))
                .and(SAC_REVIEW.INTEN_LEVEL_CODE.eq(getIntenLevelCode(), StringUtil::hasText))
                .and(SAC_REVIEW.COLUMN7.eq(getColumn7(), StringUtil::hasText))
                .and(SAC_REVIEW.COLUMN10.eq(getColumn10(), StringUtil::hasText))
                .and(SAC_REVIEW.NODE_CODE.eq(getNodeCode(), StringUtil::hasText));
        return this;
    }
}