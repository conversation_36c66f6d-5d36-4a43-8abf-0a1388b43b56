package com.smart.adp.domain.entity.koc;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.smart.adp.domain.enums.KocOperationTypeEnum;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 标签操作日志实体
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_tag_operation_log", schema = "csc")
public class SacTagOperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @Id("LOG_ID")
    private String logId;

    /**
     * 操作类型
     * @see KocOperationTypeEnum
     */
    @Column("OPERATION_TYPE")
    private Integer operationType;

    /**
     * 操作目标ID（标签ID/类型ID/备注ID）
     */
    @Column("TARGET_ID")
    private String targetId;

    /**
     * 用户smartId
     */
    @Column("SMART_ID")
    private String smartId;

    /**
     * 批量操作ID
     */
    @Column("BATCH_ID")
    private String batchId;

    /**
     * 操作人
     */
    @Column("OPERATOR")
    private String operator;

    /**
     * 操作时间
     */
    @Column("OPERATION_DATE")
    private LocalDateTime operationDate;
}
