package com.smart.adp.domain.entity.koc;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 标签操作日志实体
 * @Author: system
 * @Date: 2025/8/4
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_tag_operation_log")
public class SacTagOperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @Id
    @Column("LOG_ID")
    private String logId;

    /**
     * 操作类型
     * 1-创建标签,2-编辑标签,3-删除标签,4-下架标签,5-上架标签,
     * 6-添加达人类型,7-编辑达人类型,8-删除达人类型,
     * 9-添加标签到用户,10-从用户移除标签,11-添加达人类别到用户,12-从用户移除达人类别,
     * 13-添加备注,14-移除备注,15-编辑备注
     */
    @Column("OPERATION_TYPE")
    private Integer operationType;

    /**
     * 操作目标ID（标签ID/类型ID/备注ID）
     */
    @Column("TARGET_ID")
    private String targetId;

    /**
     * 用户smartId
     */
    @Column("SMART_ID")
    private String smartId;

    /**
     * 批量操作ID
     */
    @Column("BATCH_ID")
    private String batchId;

    /**
     * 操作人
     */
    @Column("OPERATOR")
    private String operator;

    /**
     * 操作时间
     */
    @Column("OPERATION_DATE")
    private LocalDateTime operationDate;
}
