package com.smart.adp.domain.entity.koc;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 标签操作日志实体
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_tag_operation_log")
public class SacTagOperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID 自增
     */
    @Id("log_id")
    private Long logId;

    /**
     * 操作类型
     * @see com.smart.adp.domain.enums.KocOperationTypeEnum
     */
    @Column("operation_type")
    private Integer operationType;

    /**
     * 操作目标ID（标签ID/类型ID/关系ID）
     */
    @Column("target_id")
    private Long targetId;

    /**
     * 用户smartId
     */
    @Column("smart_id")
    private String smartId;

    /**
     * 批量操作ID
     */
    @Column("batch_id")
    private String batchId;

    /**
     * 操作人
     */
    @Column("operator")
    private String operator;

    /**
     * 操作时间
     */
    @Column("operation_date")
    private LocalDateTime operationDate;
}
