package com.smart.adp.domain.service.base;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.base.AgentEmployeeBO;
import com.smart.adp.domain.entity.base.AgentEmployee;

import java.util.List;

/**
 * @Description: 员工服务
 * @Author: rik.ren
 * @Date: 2025/3/13 15:54
 **/
public interface IAgentEmployeeService {
    /**
     * 根据empId查询
     *
     * @param empId
     * @return
     */
    AgentEmployee findByEmpId(String empId, QueryColumn... needColumns);

    /**
     * 根据实体查询
     *
     * @param param
     * @return
     */
    List<AgentEmployee> findEmpInfo(AgentEmployeeBO param, QueryColumn... needColumns);

    /**
     * 根据实体查询带门店的用户信息
     *
     * @param param
     * @return
     */
    List<AgentEmployeeBO> findEmpDlrInfo(AgentEmployeeBO param, QueryColumn... needColumns);
}
