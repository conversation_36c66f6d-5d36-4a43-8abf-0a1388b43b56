package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.bo.clue.SacOnecustResumeBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.valueObject.clue.SacOnecustResumeVO;

import java.util.List;

/**
 * @Description: 线索回访gateway
 * @Author: rik.ren
 * @Date: 2025/3/13 17:40
 **/
public interface SacOnecustResumeGateway {
    /**
     * 根据客户id查询跟进记录
     *
     * @param custId
     * @return
     */
    List<SacOnecustResumeVO> queryOnecustResumeByCustId(String custId, QueryOrderBy orderBy, QueryColumn... columns);

    /**
     * 根据实体对象查询跟进记录，无分页
     *
     * @param entityParam
     * @return
     */
    List<SacOnecustResumeVO> queryOnecustResumeAll(SacOnecustResumeVO entityParam, SacOnecustResumeBO resumeBOParam,
                                                   QueryOrderBy orderBy,
                                                   QueryColumn... columns);

    /**
     * 根据实体对象查询跟进记录，有分页
     *
     * @param qryParam
     * @param orderBy
     * @param columns
     * @return
     */
    DomainPage<SacOnecustResumeVO> queryOnecustResume(SacOnecustResumeVO qryParam, SacOnecustResumeBO resumeBOParam,
                                                      QueryOrderBy orderBy,
                                                      QueryColumn... columns);

    /**
     * 根据客户 id 获取履历列表
     *
     * @param custIds 客户 id 列表
     * @param columns columns
     * @return resume list
     */
    List<SacOnecustResumeVO> queryResumeListByCustId(List<String> custIds, QueryColumn... columns);
}
