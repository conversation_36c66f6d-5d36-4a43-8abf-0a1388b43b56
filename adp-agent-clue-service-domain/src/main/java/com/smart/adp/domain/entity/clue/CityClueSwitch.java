package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/9 16:07
 * @description 城市线索开关
 **/
@Table(value = "t_sac_city_clue_switch", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class CityClueSwitch {

    @Column("SWITCH_VALUE")
    private String switchValue;

    /**
     * 专营店编码
     */
    @Column("DLR_CODE")
    private String dlrCode;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;
}
