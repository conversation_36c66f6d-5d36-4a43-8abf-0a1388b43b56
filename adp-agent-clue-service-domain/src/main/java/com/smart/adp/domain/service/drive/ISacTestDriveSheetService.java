package com.smart.adp.domain.service.drive;

import com.smart.adp.domain.bo.drive.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;

import java.util.List;

/**
 * @Description: 试驾service
 * @Author: rik.ren
 * @Date: 2025/3/15 15:07
 **/
public interface ISacTestDriveSheetService {
    /**
     * 根据条件查询试驾单
     *
     * @param param
     * @return
     */
    List<SacTestDriveSheetBO> queryTestDriveSheetList(SacTestDriveSheetBO param);

    /**
     * @param param
     * @return
     */
    DomainPage<SacTestDriveSheetBO> queryTestDriveSheetPage(SacTestDriveSheetBO param);

    /**
     * 更新试驾单
     *
     * @param param
     * @return
     */
    Boolean modifyTestDriveSheet(SacTestDriveSheetBO param);
}
