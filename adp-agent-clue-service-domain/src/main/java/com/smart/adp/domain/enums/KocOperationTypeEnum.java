package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: KOC操作类型枚举
 * @Author: system
 * @Date: 2025/8/4
 **/
@Getter
@AllArgsConstructor
public enum KocOperationTypeEnum {

    // 标签相关操作
    CREATE_TAG(1, "创建标签"),
    EDIT_TAG(2, "编辑标签"),
    DELETE_TAG(3, "删除标签"),
    DISABLE_TAG(4, "下架标签"),
    ENABLE_TAG(5, "上架标签"),

    // 达人类型相关操作
    ADD_EXPERT_TYPE(6, "添加达人类型"),
    EDIT_EXPERT_TYPE(7, "编辑达人类型"),
    DELETE_EXPERT_TYPE(8, "删除达人类型"),

    // 用户打标相关操作
    ADD_TAG_TO_USER(9, "添加标签到用户"),
    REMOVE_TAG_FROM_USER(10, "从用户移除标签"),
    ADD_EXPERT_TYPE_TO_USER(11, "添加达人类别到用户"),
    REMOVE_EXPERT_TYPE_FROM_USER(12, "从用户移除达人类别"),

    // 备注相关操作
    ADD_REMARK(13, "添加备注"),
    REMOVE_REMARK(14, "移除备注"),
    EDIT_REMARK(15, "编辑备注");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static KocOperationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (KocOperationTypeEnum operationType : values()) {
            if (operationType.getCode().equals(code)) {
                return operationType;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     */
    public static String getDescByCode(Integer code) {
        KocOperationTypeEnum operationType = getByCode(code);
        return operationType != null ? operationType.getDesc() : "";
    }

    /**
     * 判断是否为有效的操作类型
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为标签相关操作
     */
    public static boolean isTagOperation(Integer code) {
        return code != null && code >= 1 && code <= 5;
    }

    /**
     * 判断是否为达人类型相关操作
     */
    public static boolean isExpertTypeOperation(Integer code) {
        return code != null && code >= 6 && code <= 8;
    }

    /**
     * 判断是否为用户打标相关操作
     */
    public static boolean isUserTagOperation(Integer code) {
        return code != null && code >= 9 && code <= 12;
    }

    /**
     * 判断是否为备注相关操作
     */
    public static boolean isRemarkOperation(Integer code) {
        return code != null && code >= 13 && code <= 15;
    }
}
