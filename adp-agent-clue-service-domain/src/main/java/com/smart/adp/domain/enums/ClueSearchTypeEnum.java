package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/10
 */
@Getter
@AllArgsConstructor
public enum ClueSearchTypeEnum {

    /**
     * 姓名/手机号
     */
    NAME_OR_PHONE(1),

    /**
     * 跟进记录
     */
    REVIEW_RECORD(2)

    ;

    private final int code;

    public enum ClueStatus {

        ALL,

        NOT_DEFEATED,

        DEFEATED,
        ;
    }
}
