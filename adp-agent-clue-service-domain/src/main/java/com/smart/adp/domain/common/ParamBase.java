//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.smart.adp.domain.common;

import java.io.Serializable;

public class ParamBase<T> implements Serializable {
    T param;

    public ParamBase() {
    }

    public T getParam() {
        return this.param;
    }

    public void setParam(T param) {
        this.param = param;
    }
}
