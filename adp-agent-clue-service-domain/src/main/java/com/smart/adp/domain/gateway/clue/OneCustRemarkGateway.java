package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
public interface OneCustRemarkGateway {

    SacOneCustRemark findByCustId(String custId, QueryColumn... needColumns);

    List<SacOneCustRemark> findByCustId(List<String> custId, QueryColumn... needColumns);

    Boolean saveOneCustRemark(SacOneCustRemark sacOneCustRemark);

    Boolean modifyOneCustRemark(SacOneCustRemark param);
}
