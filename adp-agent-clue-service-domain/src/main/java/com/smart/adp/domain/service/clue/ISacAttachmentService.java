package com.smart.adp.domain.service.clue;

import com.smart.adp.domain.bo.clue.SacAttachmentBO;
import com.smart.adp.domain.valueObject.clue.SacAttachmentVO;

import java.util.List;

/**
 * @Description: 线索附件service
 * @Author: rik.ren
 * @Date: 2025/3/21 17:44
 **/
public interface ISacAttachmentService {
    /**
     * 根据对象条件查询附件
     *
     * @param param
     * @return
     */
    List<SacAttachmentVO> queryAttachment(SacAttachmentBO param);
}
