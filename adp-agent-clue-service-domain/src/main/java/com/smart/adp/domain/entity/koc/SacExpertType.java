package com.smart.adp.domain.entity.koc;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 达人类型实体
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_expert_type")
public class SacExpertType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 达人类型ID 自增
     */
    @Id("type_id")
    private Long typeId;

    /**
     * 达人类型名称
     */
    @Column("type_name")
    private String typeName;

    /**
     * 达人类型内容
     */
    @Column("type_content")
    private String typeContent;

    /**
     * 创建人ID
     */
    @Column("creator")
    private String creator;

    /**
     * 创建人
     */
    @Column("created_name")
    private String createdName;

    /**
     * 创建日期
     */
    @Column("created_date")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column("modifier")
    private String modifier;

    /**
     * 修改人
     */
    @Column("modify_name")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column("last_updated_date")
    private LocalDateTime lastUpdatedDate;
}
