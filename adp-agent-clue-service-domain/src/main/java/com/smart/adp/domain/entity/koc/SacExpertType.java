package com.smart.adp.domain.entity.koc;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 达人类型实体
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_expert_type", schema = "csc")
public class SacExpertType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 达人类型ID
     */
    @Id("TYPE_ID")
    private String typeId;

    /**
     * 达人类型名称
     */
    @Column("TYPE_NAME")
    private String typeName;

    /**
     * 达人类型内容
     */
    @Column("TYPE_CONTENT")
    private String typeContent;

    /**
     * 创建人ID
     */
    @Column("CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column("CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column("MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;
}
