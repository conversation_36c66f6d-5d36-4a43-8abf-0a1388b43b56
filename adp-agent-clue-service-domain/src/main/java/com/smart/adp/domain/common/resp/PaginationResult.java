
package com.smart.adp.domain.common.resp;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName PaginationResult
 * @Description 分页结果
 * <AUTHOR>
 * @Date 2023/3/2 2:32
 * @Version 1.0
 */
public class PaginationResult<T> implements Serializable {
    private static final long serialVersionUID = 2;

    /**
     *    返回结果集
     */
    protected List<T> data;

    /**
     *  返回记录数(必须初始化)
     */
    protected Long totalCount = 0L;

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 构建结果对象
     * @param data
     * @param totalCount
     * @return
     */
    public PaginationResult<T> buildResult(List<T> data, Long totalCount){
        this.setData(data);
        this.setTotalCount(totalCount);
        return this;
    }

}
