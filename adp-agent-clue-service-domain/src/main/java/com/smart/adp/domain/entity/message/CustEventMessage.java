package com.smart.adp.domain.entity.message;

import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/20
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustEventMessage {

    /**
     * 客户 ID
     */
    private String custId;

    /**
     * 事件类型
     *
     * @see com.smart.adp.domain.enums.CustEventEnum
     */
    private Integer type;

    /**
     * 业务单据 ID
     */
    private String businessId;

    /**
     * 事件时间
     */
    private LocalDateTime eventTime;

    /**
     * 扩展信息
     */
    private JSONObject extendJson;

    public CustEventFlow buildFlow() {
        CustEventFlow flow = new CustEventFlow();
        flow.setCustId(getCustId());
        flow.setType(getType());
        flow.setBusinessId(getBusinessId());
        flow.setEventTime(getEventTime());
        if (Objects.nonNull(getExtendJson())) {
            flow.setExtendJson(getExtendJson().clone());
        } else {
            flow.setExtendJson(new JSONObject());
        }
        return flow;
    }
}
