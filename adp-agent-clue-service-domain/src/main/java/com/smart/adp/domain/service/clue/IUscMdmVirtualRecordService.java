package com.smart.adp.domain.service.clue;

import com.smart.adp.domain.bo.clue.UscMdmVirtualRecordBO;
import com.smart.adp.domain.common.DomainPage;

/**
 * @Description: 虚拟外呼service
 * @Author: rik.ren
 * @Date: 2025/3/15 16:18
 **/
public interface IUscMdmVirtualRecordService {

    /**
     * 更新虚拟外呼摘要
     *
     * @param param
     * @return
     */
    Boolean modifyVirtualAbstractContent(UscMdmVirtualRecordBO param);

    /**
     * 查询虚拟外呼内容
     *
     * @param param
     * @return
     */
    DomainPage<UscMdmVirtualRecordBO> queryClueVirtualRecord(UscMdmVirtualRecordBO param);
}
