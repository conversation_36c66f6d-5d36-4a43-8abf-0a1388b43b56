package com.smart.adp.domain.valueObject.clue;

import com.smart.adp.domain.enums.ClueQueryTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/6
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "线索统计 VO")
public class ClueDlrStatisticsVO {

    /**
     * 统计类型
     */
    @Schema(description = "统计类型")
    private ClueQueryTypeEnum type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long num;
}
