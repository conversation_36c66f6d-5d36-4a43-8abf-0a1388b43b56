package com.smart.adp.domain.entity.koc;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.smart.adp.domain.enums.KocTagStatusEnum;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 标签信息实体
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_tag_info")
public class SacTagInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID 自增
     */
    @Id("tag_id")
    private Long tagId;

    /**
     * 标签名称
     */
    @Column("tag_name")
    private String tagName;

    /**
     * 父标签主键ID
     */
    @Column("parent_id")
    private Long parentId;

    /**
     * 父标签业务ID
     */
    @Column("parent_tag_id")
    private String parentTagId;

    /**
     * 标签层级：1,2,3
     */
    @Column("tag_level")
    private Integer tagLevel;

    /**
     * 完整路径
     */
    @Column("full_path")
    private String fullPath;

    /**
     * 标签状态：1-上架，0-下架
     * @see KocTagStatusEnum
     */
    @Column("tag_status")
    private Integer tagStatus;

    /**
     * 同级排序
     */
    @Column("sort_order")
    private Integer sortOrder;

    /**
     * 创建人ID
     */
    @Column("creator")
    private String creator;

    /**
     * 创建人
     */
    @Column("created_name")
    private String createdName;

    /**
     * 创建日期
     */
    @Column("created_date")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column("modifier")
    private String modifier;

    /**
     * 修改人
     */
    @Column("modify_name")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column("last_updated_date")
    private LocalDateTime lastUpdatedDate;
}
