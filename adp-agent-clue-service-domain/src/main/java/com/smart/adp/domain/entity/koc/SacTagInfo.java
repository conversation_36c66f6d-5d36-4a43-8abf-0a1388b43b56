package com.smart.adp.domain.entity.koc;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.smart.adp.domain.enums.KocTagStatusEnum;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 标签信息实体
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_tag_info", schema = "csc")
public class SacTagInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @Id("TAG_ID")
    private String tagId;

    /**
     * 标签名称
     */
    @Column("TAG_NAME")
    private String tagName;

    /**
     * 父标签ID
     */
    @Column("PARENT_TAG_ID")
    private String parentTagId;

    /**
     * 标签层级：1,2,3
     */
    @Column("TAG_LEVEL")
    private Integer tagLevel;

    /**
     * 完整路径
     */
    @Column("FULL_PATH")
    private String fullPath;

    /**
     * 标签状态：1-上架，0-下架
     * @see KocTagStatusEnum
     */
    @Column("TAG_STATUS")
    private Integer tagStatus;

    /**
     * 同级排序
     */
    @Column("SORT_ORDER")
    private Integer sortOrder;

    /**
     * 创建人ID
     */
    @Column("CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column("CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column("MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;
}
