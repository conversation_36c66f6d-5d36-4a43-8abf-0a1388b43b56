package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 摘要内容类型
 * @Author: rik.ren
 * @Date: 2025/3/13 14:55
 **/
@Getter
@AllArgsConstructor
public enum AbstractType {
    /**
     * 虚拟外呼摘要
     */
    VIRTUALCALL(1, "虚拟外呼摘要"),
    /**
     * 试驾录音
     */
    TESTRECORD(2, "试驾录音"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static AbstractType getByCode(Integer code) {
        return Arrays.stream(AbstractType.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
