package com.smart.adp.domain.bo.clue;

import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.enums.DefeatFlagEnum;
import lombok.Data;

/**
 * @Description: 线索BO
 * @Author: rik.ren
 * @Date: 2025/4/23 16:51
 **/
@Data
public class SacClueInfoDlrBO extends SacClueInfoDlr {
    /**
     * 线索标签
     * 战败标签，0非战败，1全部
     *
     * @see com.smart.adp.domain.enums.DefeatFlagEnum
     */
    private Integer defeatFlag;

    /**
     * 库名称
     */
    private String dbName;

    /**
     * 新的意向车
     */
    private String newIntentionCar;

    public String getDbName() {
        if (DefeatFlagEnum.DEFEAT.getCode().equals(defeatFlag)) {
            dbName = "csc";
        } else {
            dbName = "adp_leads";
        }
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public ClueDlrModifyBO buildStartDrivingModifyBO(String newIntentionCar) {
        ClueDlrModifyBO modifyBO = new ClueDlrModifyBO();
        modifyBO.setCustId(getCustId());
        modifyBO.setValue(newIntentionCar);
        modifyBO.setDefeatFlag(DefeatFlagEnum.DEFEAT.getCode());
        modifyBO.setUserBusiEntity(UserInfoContext.get());
        return modifyBO;
    }
}
