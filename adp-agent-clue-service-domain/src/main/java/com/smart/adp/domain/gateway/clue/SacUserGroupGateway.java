package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;

import java.util.List;

/**
 * @Description: 用户分组gateway接口
 * @Author: rik.ren
 * @Date: 2025/3/9 16:03
 **/
public interface SacUserGroupGateway {
    /**
     * 查询全部分组
     *
     * @param param
     * @param needColumns
     * @return
     */
    List<SacUserGroupEntity> findListByCreator(SacUserGroupEntity param, QueryColumn... needColumns);

    /**
     * 根据实体条件查询集合
     *
     * @param param
     * @param needColumns
     * @return
     */
    List<SacUserGroupEntity> findListByUserGroupId(SacUserGroupEntity param, QueryColumn... needColumns);

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns
     * @return
     */
    SacUserGroupEntity findByCondition(SacUserGroupEntity param, QueryColumn... needColumns);
}
