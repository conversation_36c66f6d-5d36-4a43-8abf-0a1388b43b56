package com.smart.adp.domain.service.base;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;

import java.util.List;

/**
 * @Description: 基础服务接口
 * @Author: rik.ren
 * @Date: 2025/3/9 17:35
 **/
public interface IBaseService {
    /**
     * 根据创建人获取他的分组
     *
     * @param param
     * @param needColumn
     * @return
     */
    List<SacUserGroupEntity> queryUserGroup(SacUserGroupEntity param, QueryColumn... needColumn);
}
