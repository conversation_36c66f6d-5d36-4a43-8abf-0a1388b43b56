package com.smart.adp.domain.valueObject.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/6
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "任务提示 VO")
public class TaskTipsVO {

    @Schema(description = "是否存在未完成")
    private boolean unfinished;

    @Schema(description = "是否存在逾期")
    private boolean overdue;
}
