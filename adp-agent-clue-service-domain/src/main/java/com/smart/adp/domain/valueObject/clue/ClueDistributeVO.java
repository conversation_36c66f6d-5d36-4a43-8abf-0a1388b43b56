package com.smart.adp.domain.valueObject.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/4/24 13:43
 * @description 线索分配返参
 **/
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "线索分配返参 VO")
public class ClueDistributeVO implements Serializable {

    /**
     * 回访任务id
     */
    @Schema(description = "回访任务id")
    private String reviewId;
    /**
     * 分配时间
     */
    @Schema(description = "分配时间")
    private LocalDateTime assignTime;

    /**
     * 回访人员id，产品专家userId
     */
    @Schema(description = "回访人员id，产品专家userId")
    private String reviewPersonId;

    /**
     * 回访人员名称
     */
    @Schema(description = "回访人员名称")
    private String reviewPersonName;
}
