package com.smart.adp.domain.enums;

/**
 * 线索message枚举
 */
public enum ClueMessageEnum {
    CLUE_NOT_EXIST("1", "店端线索不重复！"),
    CLUE_EXIST_REVIEW_PERSON("0", "该客户已在当前门店，负责产品专家："),
    CLUE_EXIST_NO_REVIEW_PERSON("0", "该客户已在当前门店，请及时分配给产品专家！"),
    CLUE_BASE_DLR_07("0", "该客户已存在，请勿重复添加"),
    CLUE_BASE_DLR_05("CLUE-BASE-DLR-05", "重复留资"),
    CLUE_BASE_DLR_02("CLUE-BASE-DLR-02", "店端线索保存失败"),
    CLUE_BASE_DLR_01("CLUE-BASE-DLR-01", "店端线索保存成功"),
    ;

    private final String code;

    private final String desc;

    ClueMessageEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举项
     *
     * @param code
     * @return
     */
    public static ClueMessageEnum getByCode(String code) {
        for (ClueMessageEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No ClueStatus with code: " + code);
    }

    /**
     * 根据描述获取枚举
     *
     * @param desc
     * @return
     */
    public static ClueMessageEnum fromDesc(String desc) {
        for (ClueMessageEnum status : values()) {
            if (status.getDesc().equals(desc)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No ClueStatus with desc: " + desc);
    }

}
