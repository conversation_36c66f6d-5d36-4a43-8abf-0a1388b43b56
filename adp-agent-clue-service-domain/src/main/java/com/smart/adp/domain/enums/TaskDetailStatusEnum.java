package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/6
 */
@Getter
@AllArgsConstructor
public enum TaskDetailStatusEnum {

    /**
     * 未完成
     */
    UNFINISHED(0, "0"),

    /**
     * 已完成
     */
    FINISHED(1, "1"),

    /**
     * 逾期完成
     */
    OVERDUE_FINISHED(2, "2"),

    ;

    private final int code;

    private final String codeStr;
}
