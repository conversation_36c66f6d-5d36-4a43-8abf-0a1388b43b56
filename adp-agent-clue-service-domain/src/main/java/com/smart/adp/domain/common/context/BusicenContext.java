package com.smart.adp.domain.common.context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

public class BusicenContext {

    public static final String BUSICEN_QUERY_ORDERS = "busicen_query_orders";
    public static final String BUSICEN_ERRORS = "busicen_errors";
    public static final String BUSICEN_RBG = "busicen_rbg";
    public static final String BUSICEN_TRACE = "busicen_trace";
    public static final String BUSICEN_DOMAIN = "busicen_domain";
    public static final String BUSICEN_EXCEL = "busicen_excel";

    private static List<Consumer<String>> initCallBacks = new ArrayList<>();

    private static List<Consumer<String>> releaseCallBacks = new ArrayList<>();

    private static final ThreadLocal<Map<String, Object>> CONTEXT = new ThreadLocal<Map<String, Object>>();

    private static final ThreadLocal<String> TOKEN = new ThreadLocal<String>();


    static {
        addInitCallBack(tk -> {
            //UserEntity userEntity = (UserEntity) SessionHelper.get(tk);
            //CURRENTUSER.set(userEntity);
        });
    }

    public static void addInitCallBack(Consumer<String> consumer) {
        if (!initCallBacks.contains(consumer)) {
            initCallBacks.add(consumer);
        }
    }

    public static void addReleaseCallBack(Consumer<String> consumer) {
        if (!releaseCallBacks.contains(consumer)) {
            releaseCallBacks.add(consumer);
        }
    }

    public static void setToken(String token) {
        TOKEN.set(token);//设置token
        //执行初始化回调
        if (initCallBacks != null) {
            initCallBacks.forEach(m -> {
                m.accept(token);
            });
        }
    }

    public static String getToken() {
        return TOKEN.get();
    }

    public static Map<String, Object> getContext() {
        return CONTEXT.get();
    }

    public static void setContext(Map<String, Object> context) {
        CONTEXT.set(context);
    }

    public static void remove() {
        if (releaseCallBacks != null) {
            releaseCallBacks.forEach(m -> {
                m.accept(getToken());
            });
        }

        CONTEXT.remove();
        TOKEN.remove();
    }

}
