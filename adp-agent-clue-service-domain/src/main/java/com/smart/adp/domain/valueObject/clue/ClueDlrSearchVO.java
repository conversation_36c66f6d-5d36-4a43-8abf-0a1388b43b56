package com.smart.adp.domain.valueObject.clue;

import cn.hutool.extra.cglib.CglibUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.domain.entity.clue.CustResumeESO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/13
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
@Schema(description = "线索搜索 VO")
public class ClueDlrSearchVO extends ClueDlrListVO {

    @Schema(description = "线索ID")
    private String clueId;

    @Schema(description = "得分")
    private Float score;

    @Schema(description = "履历列表")
    private List<CustResumeESO> custResumeList;

    @Schema(description = "排序因子")
    private Object[] sort;

    @Schema(description = "文档ID")
    private String docId;

    @JsonIgnore
    private Map<String, HighlightField> highlightMap;

    /**
     * join
     *
     * @param searchList es list
     * @param list db list
     */
    public static void join(List<ClueDlrSearchVO> searchList, List<ClueDlrListVO> list) {
        if (CollectionUtil.isEmpty(searchList)) {
            return;
        }

        Map<String, ClueDlrListVO> voMap = list.stream()
                                               .collect(Collectors.toMap(ClueDlrListVO::getId, Function.identity(), (v1, v2) -> v2));

        searchList.removeIf(searchVO -> {
            ClueDlrListVO vo = Optional.ofNullable(searchVO.getClueId())
                                       .map(voMap::get)
                                       .orElse(null);

            if (Objects.isNull(vo)) {
                return true;
            }

            searchVO.join(vo);
//            searchVO.handleHighlight();
            return false;
        });
    }

    /**
     * 高亮字段回显
     */
    private void handleHighlight() {
        getHighlightMap().forEach((k, v) -> {
            String text = Optional.ofNullable(v)
                                   .map(HighlightField::fragments)
                                   .map(arr -> arr[0])
                                   .map(Text::string)
                                   .orElse(null);
            if (Objects.isNull(text)) {
                return;
            }

            switch (k) {
                case "phone.ngram":
                    setPhone(text);
                    break;
                case "custName":
                    setCustName(text);
                    break;
                default:
            }
        });
    }

    private void join(ClueDlrListVO vo) {
        CglibUtil.copy(vo, this);
    }
}
