package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Description: 消息已读未读枚举
 * @Author: rik.ren
 * @Date: 2025/4/15 14:55
 **/
@Getter
@AllArgsConstructor
public enum ClueMsgReadEnum {

    /**
     * 已读
     */
    READ("1", "已读"),

    /**
     * 未读
     */
    UN_READ("0", "未读"),
    ;

    private final String code;
    private final String desc;

    public static ClueMsgReadEnum getByCode(String code) {
        return Arrays.stream(ClueMsgReadEnum.values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .findAny()
                .orElse(null);
    }
}
