package com.smart.adp.domain.service.base.impl;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.common.annotation.SmartADPCache;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.enums.ClueQueryTypeEnum;
import com.smart.adp.domain.enums.ReviewStatusEnum;
import com.smart.adp.domain.enums.StatisticsTypeEnum;
import com.smart.adp.domain.gateway.clue.ClueDlrGateway;
import com.smart.adp.domain.gateway.drive.SacTestDriveSheetGateway;
import com.smart.adp.domain.qry.ClueDlrQry;
import com.smart.adp.domain.service.base.WorkbenchService;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.domain.valueObject.base.StatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.smart.adp.domain.common.constants.BizConstants.*;
import static com.smart.adp.domain.entity.clue.table.SacAllClueInfoDlrTableDef.SAC_ALL_CLUE_INFO_DLR;
import static com.smart.adp.domain.entity.clue.table.SacReviewTableDef.SAC_REVIEW;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
@Service
public class WorkbenchServiceImpl implements WorkbenchService {

    @Autowired
    public ClueDlrGateway clueDlrGateway;

    @Autowired
    public SacTestDriveSheetGateway testDriveSheetGateway;

    @Override
    @SmartADPCache(value = "statistics", key = "#type.toString().concat('|').concat(#user.getUserID())", expire = 30)
    public StatisticsVO doStatistics(StatisticsTypeEnum type, UserBusiEntity user) {
        long num = LONG_DEFAULT_VALUE;
        switch (type) {
            case WAIT_FOR_ASSIGN:
            case NO_REVIEW:
            case WAIT_FOR_REVIEW:
            case NO_ORDER_WAIT_FOR_REVIEW:
            case ALREADY_ORDER_WAIT_FOR_REVIEW:
            case OVERDUE:
            case HOT:
            case WARM:
            case COLD:
                num = clueStatistics(type, user);
                break;
            case ACTIVATES_DEFEATED:
            case REVIEW_TASK:
            case TRANSFER_AUDIT:
            case DEFEATED_AUDIT:
                num = clueOtherStatistics(type, user);
                break;
            case DRIVE_TASK:
            case DRIVE:
                num = driveStatistics(type, user);
                break;
            default:
        }
        return new StatisticsVO(type, num);
    }

    /**
     * 线索表相关统计
     *
     * @param type 统计类型
     * @param user 当前用户
     * @return long
     */
    private long clueStatistics(StatisticsTypeEnum type, UserBusiEntity user) {
        long num = LONG_DEFAULT_VALUE;
        ClueDlrQry qry = new ClueDlrQry();
        qry.setDlrCode(user.getDlrCode());
        if (UserUtil.isProductExpert(user.getStationId())) {
            qry.setReviewPersonId(user.getUserID());
        }

        QueryWrapper wrapper = new QueryWrapper();

        switch (type) {
            case WAIT_FOR_ASSIGN:
                // 和老逻辑保持一致
                wrapper.select(QueryMethods.distinct(SAC_REVIEW.PHONE))
                       .from(SAC_REVIEW.as("r"))
                       .innerJoin(SAC_ALL_CLUE_INFO_DLR.as("c")).on(SAC_REVIEW.REVIEW_ID.eq(SAC_ALL_CLUE_INFO_DLR.REVIEW_ID))
                       .and(SAC_REVIEW.REVIEW_STATUS.in("0", "1", "3"))
                       .and(SAC_REVIEW.ORG_CODE.eq(user.getDlrCode()))
                       .and(SAC_REVIEW.BILL_TYPE.eq("DLRCLUE"))
                       .and(SAC_REVIEW.ASSIGN_STATUS.eq("0"))
                       .and(SAC_REVIEW.COLUMN19.isNull());
                num = clueDlrGateway.countByWrapper(wrapper);
                break;
            case NO_REVIEW:
                qry.setQryType(ClueQueryTypeEnum.NOT_DEFEATED);
                qry.setReviewStatus(ReviewStatusEnum.NO_REVIEW);
                num = clueDlrGateway.countByQry(qry);
                break;
            case WAIT_FOR_REVIEW:
                qry.setQryType(ClueQueryTypeEnum.NOT_DEFEATED);
                qry.setReviewStatus(ReviewStatusEnum.WAIT_FOR_REVIEW);
                num = clueDlrGateway.countByQry(qry);
                break;
            case NO_ORDER_WAIT_FOR_REVIEW:
                qry.setQryType(ClueQueryTypeEnum.WAIT_TO_CONVERT);
                qry.setReviewStatus(ReviewStatusEnum.WAIT_FOR_REVIEW);
                num = clueDlrGateway.countByQry(qry);
                break;
            case ALREADY_ORDER_WAIT_FOR_REVIEW:
                qry.setQryType(ClueQueryTypeEnum.ORDER);
                qry.setReviewStatus(ReviewStatusEnum.WAIT_FOR_REVIEW);
                num = clueDlrGateway.countByQry(qry);
                break;
            case OVERDUE:
                qry.setQryType(ClueQueryTypeEnum.NOT_DEFEATED);
                qry.setReviewStatus(ReviewStatusEnum.OVERDUE);
                num = clueDlrGateway.countByQry(qry);
                break;
            case HOT:
                wrapper.and(SAC_ALL_CLUE_INFO_DLR.DLR_CODE.eq(user.getDlrCode(), StringUtil::hasText))
                       .and(SAC_ALL_CLUE_INFO_DLR.REVIEW_PERSON_ID.eq(user.getUserID(), UserUtil::productExpertValid))
                       .and(SAC_ALL_CLUE_INFO_DLR.COLUMN5.eq(CLUE_HOT_CODE))
                       .and(SAC_ALL_CLUE_INFO_DLR.COLUMN19.isNull());
                num = clueDlrGateway.countAllByWrapper(wrapper);
                break;
            case WARM:
                wrapper.and(SAC_ALL_CLUE_INFO_DLR.DLR_CODE.eq(user.getDlrCode(), StringUtil::hasText))
                       .and(SAC_ALL_CLUE_INFO_DLR.REVIEW_PERSON_ID.eq(user.getUserID(), UserUtil::productExpertValid))
                       .and(SAC_ALL_CLUE_INFO_DLR.COLUMN5.eq(CLUE_WARM_CODE))
                       .and(SAC_ALL_CLUE_INFO_DLR.COLUMN19.isNull());
                num = clueDlrGateway.countAllByWrapper(wrapper);
                break;
            case COLD:
                wrapper.and(SAC_ALL_CLUE_INFO_DLR.DLR_CODE.eq(user.getDlrCode(), StringUtil::hasText))
                       .and(SAC_ALL_CLUE_INFO_DLR.REVIEW_PERSON_ID.eq(user.getUserID(), UserUtil::productExpertValid))
                       .and(SAC_ALL_CLUE_INFO_DLR.COLUMN5.eq(CLUE_COLD_CODE))
                       .and(SAC_ALL_CLUE_INFO_DLR.COLUMN19.isNull());
                num = clueDlrGateway.countAllByWrapper(wrapper);
                break;
            default:
        }

        return num;
    }

    /**
     * 线索相关统计
     *
     * @param type 统计类型
     * @param user 当前用户
     * @return long
     */
    private long clueOtherStatistics(StatisticsTypeEnum type, UserBusiEntity user) {
        long num = LONG_DEFAULT_VALUE;
        switch (type) {
            case ACTIVATES_DEFEATED:
                num = clueDlrGateway.activatesDefeatedNum(user);
                break;
            case TRANSFER_AUDIT:
                num = clueDlrGateway.transferAuditNum(user);
                break;
            case DEFEATED_AUDIT:
                num = clueDlrGateway.defeatedAuditNum(user);
                break;
            case REVIEW_TASK:
                num = clueDlrGateway.reviewTaskNum(user);
                break;
            default:
        }
        return num;
    }

    /**
     * 试驾相关统计
     *
     * @param type 统计类型
     * @param user 当前用户
     * @return long
     */
    private long driveStatistics(StatisticsTypeEnum type, UserBusiEntity user) {
        long num = LONG_DEFAULT_VALUE;
        switch (type) {
            case DRIVE_TASK:
                num = testDriveSheetGateway.toDoTestDriveTaskNum(user);
                break;
            case DRIVE:
                num = testDriveSheetGateway.toDoTestDriveNum(user);
                break;
            default:
        }
        return num;
    }
}
