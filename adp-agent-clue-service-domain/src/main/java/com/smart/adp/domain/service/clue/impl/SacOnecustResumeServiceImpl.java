package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacAttachmentBO;
import com.smart.adp.domain.bo.clue.SacOnecustResumeBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.gateway.clue.SacOnecustResumeGateway;
import com.smart.adp.domain.service.base.ILookUpService;
import com.smart.adp.domain.service.clue.ISacAttachmentService;
import com.smart.adp.domain.service.clue.ISacOnecustResumeService;
import com.smart.adp.domain.valueObject.base.LookUpInfo;
import com.smart.adp.domain.valueObject.clue.SacAttachmentVO;
import com.smart.adp.domain.valueObject.clue.SacOnecustResumeVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.smart.adp.domain.valueObject.clue.table.SacOnecustResumeVOTableDef.SAC_ONECUST_RESUME_VO;

/**
 * @Description: 线索跟进记录service实现
 * @Author: rik.ren
 * @Date: 2025/3/13 17:39
 **/
@Service
public class SacOnecustResumeServiceImpl implements ISacOnecustResumeService {
    @Autowired
    private SacOnecustResumeGateway onecustResumeGateway;
    @Autowired
    private ILookUpService lookUpService;
    @Autowired
    private ISacAttachmentService attachmentService;

    /**
     * 根据客户id查询跟进记录
     *
     * @param boParam
     * @return
     */
    @Override
    public DomainPage<SacOnecustResumeBO> queryOnecustResumeByCustId(SacOnecustResumeBO boParam) {
        // 查询回访表
        QueryColumn[] needColumn = {SAC_ONECUST_RESUME_VO.CUST_ID, SAC_ONECUST_RESUME_VO.SMART_ID,
                SAC_ONECUST_RESUME_VO.CLUE_LEVEL_CODE, SAC_ONECUST_RESUME_VO.RESUME_ID, SAC_ONECUST_RESUME_VO.DLR_CODE_OWNER,
                SAC_ONECUST_RESUME_VO.DLR_NAME_OWNER, SAC_ONECUST_RESUME_VO.RESUME_PERSON_CODE,
                SAC_ONECUST_RESUME_VO.RESUME_PERSON_NAME, SAC_ONECUST_RESUME_VO.RESUME_DESC, SAC_ONECUST_RESUME_VO.REMARK,
                SAC_ONECUST_RESUME_VO.SALE_ORDER_CODE, SAC_ONECUST_RESUME_VO.CREATED_DATE, SAC_ONECUST_RESUME_VO.SENCE_CODE,
                SAC_ONECUST_RESUME_VO.SENCE_NAME, SAC_ONECUST_RESUME_VO.BUSS_TIME, SAC_ONECUST_RESUME_VO.BUSS_START_TIME,
                SAC_ONECUST_RESUME_VO.BUSS_END_TIME, SAC_ONECUST_RESUME_VO.ARRIVAL_NUM, SAC_ONECUST_RESUME_VO.RELATION_BILL_ID,
                SAC_ONECUST_RESUME_VO.ARRIVAL_TIME, SAC_ONECUST_RESUME_VO.ARRIVAL_END_TIME, SAC_ONECUST_RESUME_VO.COLUMN10,
                SAC_ONECUST_RESUME_VO.COLUMN1, SAC_ONECUST_RESUME_VO.COLUMN5, SAC_ONECUST_RESUME_VO.COLUMN6,
                SAC_ONECUST_RESUME_VO.ARRIVAL_METHOD, SAC_ONECUST_RESUME_VO.UPDATE_CONTROL_ID};

        SacOnecustResumeVO entity = SacOnecustResumeVO.builder().custId(boParam.getCustId()).build();
        DomainPage<SacOnecustResumeVO> pageResult = onecustResumeGateway.queryOnecustResume(entity, boParam, null, needColumn);
        List<SacOnecustResumeVO> listResume = ObjectUtil.isEmpty(pageResult) ? null : pageResult.getRecords();
        if (CollectionUtil.isEmpty(listResume)) {
            return null;
        }
        // 拼装数据
        List<SacOnecustResumeBO> listSacOnecustResumeBO = BeanUtil.copyToList(listResume, SacOnecustResumeBO.class);
        List<String> arrivalMethodResult =
                listResume.stream().map(SacOnecustResumeVO::getArrivalMethod).filter(StringUtils::isNotEmpty
                ).collect(Collectors.toList());

        // 查询字典表
        List<LookUpInfo> listLookUpInfo = lookUpService.findLookUpInfo("VE1113", arrivalMethodResult);
        if (CollectionUtil.isNotEmpty(listLookUpInfo)) {
            Map<String, LookUpInfo> lookUpMap = listLookUpInfo.stream().collect(Collectors.toMap(LookUpInfo::getLookUpValueCode
                    , Function.identity()));
            listSacOnecustResumeBO.forEach(item -> {
                item.setArrivalMethodName(ObjectUtil.isEmpty(lookUpMap.get(item.getArrivalMethod())) ? "" :
                        lookUpMap.get(item.getArrivalMethod()).getLookUpValueName().toString());
            });
        }

        // 查询附件表
        listSacOnecustResumeBO = processResumesAttachment(listSacOnecustResumeBO);
        DomainPage<SacOnecustResumeBO> result = new DomainPage<>(listSacOnecustResumeBO, pageResult.getPageNumber(),
                pageResult.getPageSize(), pageResult.getTotalCount());
        return result;
    }

    /**
     * 根据实体对象查询跟进记录
     *
     * @param entityParam
     * @return
     */
    @Override
    public List<SacOnecustResumeBO> queryOnecustResume(SacOnecustResumeBO entityParam) {
        return null;
    }

    private List<SacOnecustResumeVO> processResumeBOList(List<SacOnecustResumeVO> listSacOnecustResumeVO) {
        return listSacOnecustResumeVO.stream()
                // 过滤掉relationBillId为空或null的对象
                .filter(vo -> StringUtils.isNotBlank(vo.getRelationBillId()))
                // 拆分包含逗号的relationBillId
                .flatMap(vo -> {
                    String relationBillId = vo.getRelationBillId();
                    String[] ids = StringUtils.split(relationBillId, ',');

                    if (ids.length == 1) {
                        // 无逗号，保留原对象
                        return Stream.of(vo);
                    } else {
                        // 拆分并创建新对象
                        return Arrays.stream(ids).filter(StringUtils::isNotBlank) // 过滤空字符串
                                .map(id -> {
                                    // 创建新对象并复制属性
                                    SacOnecustResumeVO newVo = new SacOnecustResumeVO();
                                    BeanUtils.copyProperties(vo, newVo);
                                    newVo.setRelationBillId(id.trim()); // 去除空格
                                    return newVo;
                                });
                    }
                }).collect(Collectors.toList());
    }

    public List<SacOnecustResumeBO> processResumesAttachment(List<SacOnecustResumeBO> listSacOnecustResumeBO) {
        // 1. 获取所有需要查询的attachmentId（包含拆分后的ID）
        List<String> relationBillIdResult = listSacOnecustResumeBO.stream()
                .map(SacOnecustResumeVO::getRelationBillId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(relationBillIdResult)) {
            return listSacOnecustResumeBO;
        }
        // 2. 拆分并收集所有有效ID（新增逻辑）
        Set<String> allAttachmentIds = relationBillIdResult.stream()
                .flatMap(ids -> Arrays.stream(ids.split(","))) // 拆分逗号分隔符
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(allAttachmentIds)) {
            return listSacOnecustResumeBO;
        }

        // 3. 构建查询参数（保持原有BO结构）
        SacAttachmentBO attachmentBO = new SacAttachmentBO();
        attachmentBO.setAttachmentIdList(new ArrayList<>(allAttachmentIds)); // 替换为拆分后的ID集合

        // 4. 执行批量查询
        List<SacAttachmentVO> listSacAttachmentVO = attachmentService.queryAttachment(attachmentBO);
        if (CollectionUtils.isEmpty(listSacAttachmentVO)) {
            return listSacOnecustResumeBO;
        }

        // 5. 构建快速查找映射表（调整映射方式）
        Map<String, String> idToPathMap = listSacAttachmentVO.stream()
                .collect(Collectors.toMap(
                        SacAttachmentVO::getAttachmentId,
                        SacAttachmentVO::getFilePath,
                        (oldVal, newVal) -> oldVal // 处理重复ID的情况
                ));

        // 6. 填充文件路径列表（支持多ID拆分）
        return listSacOnecustResumeBO.parallelStream()
                .peek(resumeBO -> {
                    String relationBillId = resumeBO.getRelationBillId();
                    if (StringUtils.isBlank(relationBillId)) {
                        resumeBO.setFilePathlist(Collections.emptyList());
                        return;
                    }

                    List<String> filePaths = Arrays.stream(relationBillId.split(","))
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .map(idToPathMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    resumeBO.setFilePathlist(filePaths);
                })
                .collect(Collectors.toList());
    }
}
