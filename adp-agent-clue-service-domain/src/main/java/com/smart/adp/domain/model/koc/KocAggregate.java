package com.smart.adp.domain.model.koc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.smart.adp.domain.bo.koc.KocTagBO;
import com.smart.adp.domain.bo.koc.KocUserInfoBO;
import com.smart.adp.domain.bo.koc.KocUserQueryBO;
import com.smart.adp.domain.bo.koc.KocUserTagBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.koc.SacTagOperationLog;
import com.smart.adp.domain.enums.KocOperationTypeEnum;
import com.smart.adp.domain.gateway.koc.KocOperationLogGateway;
import com.smart.adp.domain.service.koc.IKocTagService;
import com.smart.adp.domain.service.koc.IKocUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: KOC聚合根 - 封装KOC打标系统的核心业务逻辑
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Slf4j
@Service
public class KocAggregate {

    @Autowired
    private IKocTagService kocTagService;

    @Autowired
    private IKocUserService kocUserService;

    @Autowired
    private KocOperationLogGateway kocOperationLogGateway;

    /**
     * 查询用户信息（支持多种查询方式）
     *
     * @param queryBO 查询条件
     * @return 用户信息列表或分页结果
     */
    public DomainPage<KocUserInfoBO> queryUsers(KocUserQueryBO queryBO) {
        if (ObjectUtil.isEmpty(queryBO)) {
            log.warn("查询用户失败：查询条件为空");
            return new DomainPage<>();
        }

        // 如果有关键字，优先使用关键字搜索
        if (StrUtil.isNotBlank(queryBO.getKeyword())) {
            List<KocUserInfoBO> users = kocUserService.searchUsers(queryBO.getKeyword());
            if (CollectionUtil.isEmpty(users)) {
                return new DomainPage<>();
            }

            // 如果只有一个用户，获取完整信息
            if (users.size() == 1) {
                KocUserInfoBO fullInfo = kocUserService.getUserFullInfo(users.get(0).getSmartId());
                users.set(0, fullInfo);
            }

            // 简单分页处理
            return buildSimplePage(users, queryBO.getPageNum(), queryBO.getPageSize());
        }

        // 使用分页查询
        return kocUserService.getUserPage(queryBO);
    }

    /**
     * 获取用户完整信息
     *
     * @param smartId 用户smartId
     * @return 用户完整信息
     */
    public KocUserInfoBO getUserFullInfo(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            log.warn("获取用户完整信息失败：smartId为空");
            return null;
        }

        return kocUserService.getUserFullInfo(smartId);
    }

    /**
     * 获取标签树结构
     *
     * @return 标签树
     */
    public List<KocTagBO> getTagTree() {
        return kocTagService.getTagTree();
    }

    /**
     * 为用户添加标签
     *
     * @param smartId 用户smartId
     * @param tagId 标签ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addTagToUser(String smartId, String tagId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(tagId)) {
            log.warn("为用户添加标签失败：参数为空");
            return false;
        }

        // 执行添加标签操作
        Boolean result = kocUserService.addTagToUser(smartId, tagId, operatorId, operatorName);
        
        if (result) {
            // 记录操作日志
            recordOperationLog(KocOperationTypeEnum.ADD_TAG_TO_USER.getCode(), tagId, smartId, 
                              null, operatorName);
            log.info("为用户添加标签成功, smartId={}, tagId={}, operator={}", smartId, tagId, operatorName);
        } else {
            log.warn("为用户添加标签失败, smartId={}, tagId={}, operator={}", smartId, tagId, operatorName);
        }

        return result;
    }

    /**
     * 批量为用户添加标签
     *
     * @param smartIds 用户smartId列表
     * @param tagIds 标签ID列表
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchAddTagsToUsers(List<String> smartIds, List<String> tagIds, String operatorId, String operatorName) {
        if (CollectionUtil.isEmpty(smartIds) || CollectionUtil.isEmpty(tagIds)) {
            log.warn("批量为用户添加标签失败：参数为空");
            return false;
        }

        // 生成批量操作ID
        String batchId = IdUtil.fastUUID();

        // 执行批量添加标签操作
        Boolean result = kocUserService.batchAddTagsToUsers(smartIds, tagIds, operatorId, operatorName);
        
        if (result) {
            // 批量记录操作日志
            List<SacTagOperationLog> operationLogs = new ArrayList<>();
            for (String smartId : smartIds) {
                for (String tagId : tagIds) {
                    SacTagOperationLog operationLog = buildOperationLog(
                            KocOperationTypeEnum.ADD_TAG_TO_USER.getCode(), tagId, smartId, batchId, operatorName);
                    operationLogs.add(operationLog);
                }
            }
            kocOperationLogGateway.batchSave(operationLogs);
            
            log.info("批量为用户添加标签成功, userCount={}, tagCount={}, batchId={}, operator={}", 
                    smartIds.size(), tagIds.size(), batchId, operatorName);
        } else {
            log.warn("批量为用户添加标签失败, userCount={}, tagCount={}, operator={}", 
                    smartIds.size(), tagIds.size(), operatorName);
        }

        return result;
    }

    /**
     * 从用户移除标签
     *
     * @param smartId 用户smartId
     * @param tagId 标签ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeTagFromUser(String smartId, String tagId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(tagId)) {
            log.warn("从用户移除标签失败：参数为空");
            return false;
        }

        // 执行移除标签操作
        Boolean result = kocUserService.removeTagFromUser(smartId, tagId, operatorId, operatorName);
        
        if (result) {
            // 记录操作日志
            recordOperationLog(KocOperationTypeEnum.REMOVE_TAG_FROM_USER.getCode(), tagId, smartId, 
                              null, operatorName);
            log.info("从用户移除标签成功, smartId={}, tagId={}, operator={}", smartId, tagId, operatorName);
        } else {
            log.warn("从用户移除标签失败, smartId={}, tagId={}, operator={}", smartId, tagId, operatorName);
        }

        return result;
    }

    /**
     * 为用户添加达人类型
     *
     * @param smartId 用户smartId
     * @param expertTypeId 达人类型ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addExpertTypeToUser(String smartId, String expertTypeId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(expertTypeId)) {
            log.warn("为用户添加达人类型失败：参数为空");
            return false;
        }

        // 执行添加达人类型操作
        Boolean result = kocUserService.addExpertTypeToUser(smartId, expertTypeId, operatorId, operatorName);
        
        if (result) {
            // 记录操作日志
            recordOperationLog(KocOperationTypeEnum.ADD_EXPERT_TYPE_TO_USER.getCode(), expertTypeId, smartId, 
                              null, operatorName);
            log.info("为用户添加达人类型成功, smartId={}, expertTypeId={}, operator={}", smartId, expertTypeId, operatorName);
        } else {
            log.warn("为用户添加达人类型失败, smartId={}, expertTypeId={}, operator={}", smartId, expertTypeId, operatorName);
        }

        return result;
    }

    /**
     * 从用户移除达人类型
     *
     * @param smartId 用户smartId
     * @param expertTypeId 达人类型ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeExpertTypeFromUser(String smartId, String expertTypeId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(expertTypeId)) {
            log.warn("从用户移除达人类型失败：参数为空");
            return false;
        }

        // 执行移除达人类型操作
        Boolean result = kocUserService.removeExpertTypeFromUser(smartId, expertTypeId, operatorId, operatorName);
        
        if (result) {
            // 记录操作日志
            recordOperationLog(KocOperationTypeEnum.REMOVE_EXPERT_TYPE_FROM_USER.getCode(), expertTypeId, smartId, 
                              null, operatorName);
            log.info("从用户移除达人类型成功, smartId={}, expertTypeId={}, operator={}", smartId, expertTypeId, operatorName);
        } else {
            log.warn("从用户移除达人类型失败, smartId={}, expertTypeId={}, operator={}", smartId, expertTypeId, operatorName);
        }

        return result;
    }

    /**
     * 为用户添加备注
     *
     * @param smartId 用户smartId
     * @param remarkContent 备注内容
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addRemarkToUser(String smartId, String remarkContent, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(remarkContent)) {
            log.warn("为用户添加备注失败：参数为空");
            return false;
        }

        // 执行添加备注操作
        Boolean result = kocUserService.addRemarkToUser(smartId, remarkContent, operatorId, operatorName);
        
        if (result) {
            // 记录操作日志
            recordOperationLog(KocOperationTypeEnum.ADD_REMARK.getCode(), null, smartId, 
                              null, operatorName);
            log.info("为用户添加备注成功, smartId={}, operator={}", smartId, operatorName);
        } else {
            log.warn("为用户添加备注失败, smartId={}, operator={}", smartId, operatorName);
        }

        return result;
    }

    /**
     * 记录操作日志
     *
     * @param operationType 操作类型
     * @param targetId 目标ID
     * @param smartId 用户smartId
     * @param batchId 批量操作ID
     * @param operator 操作人
     */
    private void recordOperationLog(Integer operationType, String targetId, String smartId, String batchId, String operator) {
        try {
            SacTagOperationLog operationLog = buildOperationLog(operationType, targetId, smartId, batchId, operator);
            kocOperationLogGateway.save(operationLog);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    /**
     * 构建操作日志对象
     */
    private SacTagOperationLog buildOperationLog(Integer operationType, String targetId, String smartId, String batchId, String operator) {
        return SacTagOperationLog.builder()
                .logId(IdUtil.fastUUID())
                .operationType(operationType)
                .targetId(targetId)
                .smartId(smartId)
                .batchId(batchId)
                .operator(operator)
                .operationDate(LocalDateTime.now())
                .build();
    }

    /**
     * 构建简单分页结果
     */
    private DomainPage<KocUserInfoBO> buildSimplePage(List<KocUserInfoBO> users, Integer pageNum, Integer pageSize) {
        if (CollectionUtil.isEmpty(users)) {
            return new DomainPage<>();
        }

        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        int total = users.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        List<KocUserInfoBO> pageData = new ArrayList<>();
        if (startIndex < total) {
            pageData = users.subList(startIndex, endIndex);
        }

        DomainPage<KocUserInfoBO> page = new DomainPage<>();
        page.setRecords(pageData);
        page.setTotal((long) total);
        page.setCurrent((long) pageNum);
        page.setSize((long) pageSize);
        page.setPages((long) Math.ceil((double) total / pageSize));

        return page;
    }
}
