package com.smart.adp.domain.gateway.base;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.valueObject.base.LookUpInfo;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/3/5 16:30
 * @description
 **/
public interface LookUpGateway {

    LookUpInfo findLookUpInfo(String lookUpTypeCode, String lookUpValueCode, QueryColumn... needColumn);

    /**
     * 获取缓存中的配置信息
     *
     * @param lookUpTypeCode
     * @param lookUpValueCode
     * @param needColumn
     * @return
     */
    LookUpInfo findLookUpInfoWithCache(String lookUpTypeCode, String lookUpValueCode, QueryColumn... needColumn);

    LookUpInfo findLookUpConfig(String lookUpTypeCode, QueryColumn... needColumn);

    List<LookUpInfo> findLookUpByTypeCode(String lookUpTypeCode, QueryColumn... needColumn);

    List<LookUpInfo> findLookUp(String lookUpTypeCode, List<String> lookUpValueCode, QueryColumn... needColumn);

    List<LookUpInfo> findLookUp(LookUpInfo entity, QueryColumn... needColumn);

    /**
     * 通过code获取信息
     *
     * @param lookUpTypeCode
     * @return
     */
    List<LookUpInfo> findByTypeCode(String lookUpTypeCode);

    List<LookUpInfo> findByType(String type, QueryColumn... needColumn);
}
