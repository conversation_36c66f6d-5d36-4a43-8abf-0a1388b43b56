package com.smart.adp.domain.common.resp;

import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/23
 */
@Slf4j
public class RespHelper {

    /**
     * 获取限流响应
     */
    public static <T> RespBody<T> rateLimitRes() {
        return RespBody.build(RespCode.TOO_MANY_REQUESTS, null);
    }

    /**
     * action with 限流降级
     *
     * @param action -
     * @return com.smart.adp.domain.common.resp.RespBody<T>
     */
    public static <T> RespBody<T> rateLimitFallback(Supplier<RespBody<T>> action) {
        try {
            return action.get();
        } catch (Exception e) {
            log.error("exception and fallback", e);
            return RespHelper.rateLimitRes();
        }
    }
}
