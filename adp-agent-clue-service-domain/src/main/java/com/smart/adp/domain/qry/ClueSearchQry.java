package com.smart.adp.domain.qry;

import com.smart.adp.domain.common.constants.IndexNameConstant;
import com.smart.adp.domain.enums.ClueSearchTypeEnum;
import com.smart.adp.domain.enums.ClueStatusEnum;
import com.smart.adp.domain.utils.IndexNameGenerator;
import com.smart.adp.domain.utils.UserUtil;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.InnerHitBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.lang.Nullable;

import java.util.Collections;
import java.util.Objects;

import static com.smart.adp.domain.common.constants.BizConstants.*;
import static com.smart.adp.domain.common.constants.StringConstant.ES_HIGHLIGHT_POST;
import static com.smart.adp.domain.common.constants.StringConstant.ES_HIGHLIGHT_PRE;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/10
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
public class ClueSearchQry extends PageQry {

    /**
     * 搜索类型
     */
    private ClueSearchTypeEnum qryType;

    /**
     * 线索类型
     */
    private ClueSearchTypeEnum.ClueStatus clueStatus;

    /**
     * 搜索内容
     */
    private String searchContent;

    /**
     * 门店编码
     */
    private String dlrCode;

    /**
     * 人员编码
     */
    private String userId;

    /**
     * search_after
     */
    private Object[] searchAfter;

    public SearchRequest buildRequest() {
        String finalIndex = IndexNameGenerator.getFinalIndex(IndexNameConstant.AGENT_CLUE_INDEX_NAME);
        SearchRequest searchRequest = new SearchRequest(finalIndex);

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("dlrCode", getDlrCode()));
        if (UserUtil.productExpertValid()) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("reviewPersonId", getUserId()));
        }

        handleClueStatus(boolQueryBuilder);

        boolQueryBuilder.filter(QueryBuilders.termQuery("isEnable", Boolean.TRUE));

        buildMatch(getQryType(), getSearchContent(), boolQueryBuilder);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.fetchSource(new String[]{"_score", "clueId"}, null)
                           .query(boolQueryBuilder)
//                           .highlighter(buildHighlight(getQryType()))
                           .size(getPageSize());

        buildSort(searchSourceBuilder, getQryType());

        if (Objects.nonNull(getSearchAfter())) {
            searchSourceBuilder.searchAfter(getSearchAfter());
        }

        searchRequest.source(searchSourceBuilder);

        return searchRequest;
    }

    /**
     * 处理线索状态筛选
     *
     * @param boolQueryBuilder -
     */
    private void handleClueStatus(BoolQueryBuilder boolQueryBuilder) {
        ClueSearchTypeEnum.ClueStatus clueStatus = getClueStatus();
        if (Objects.isNull(clueStatus)) {
            clueStatus = ClueSearchTypeEnum.ClueStatus.ALL;
        }

        if (ClueSearchTypeEnum.ClueStatus.NOT_DEFEATED.equals(clueStatus)) {
            // 非战败：排除战败状态
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("statusCode", ClueStatusEnum.DEFEATED.getCode()));
        } else if (ClueSearchTypeEnum.ClueStatus.DEFEATED.equals(clueStatus)) {
            // 战败：只包含战败状态
            boolQueryBuilder.filter(QueryBuilders.termQuery("statusCode", ClueStatusEnum.DEFEATED.getCode()));
        }
    }

    /**
     * 构建排序
     *
     * @param searchSourceBuilder -
     * @param qryType 查询类型
     */
    private void buildSort(SearchSourceBuilder searchSourceBuilder, ClueSearchTypeEnum qryType) {
        if (ClueSearchTypeEnum.NAME_OR_PHONE.equals(qryType)) {
            // TODO 状态 添加数值子 mapping
            Script script = new Script(
                ScriptType.INLINE,
                "painless",
                "try { " +
                    "  return Long.parseLong(params._source['statusCode']); " +
                    "} catch (Exception e) { " +
                    "  return 20L; " + // 非数值排在最后
                    "}",
                Collections.emptyMap()
            );

            ScriptSortBuilder statusSort = new ScriptSortBuilder(script, ScriptSortBuilder.ScriptSortType.NUMBER);
            statusSort.order(SortOrder.ASC);

            searchSourceBuilder.sort(statusSort);
        } else if (ClueSearchTypeEnum.REVIEW_RECORD.equals(qryType)) {
            searchSourceBuilder.sort("_score", SortOrder.DESC);
        }

        searchSourceBuilder.sort(new FieldSortBuilder("lastReviewTime").order(SortOrder.DESC).missing(0L))
                           .sort("clueId", SortOrder.DESC);
    }

    /**
     * 构建高亮
     *
     * @param qryType 查询类型
     */
    @Nullable
    private HighlightBuilder buildHighlight(ClueSearchTypeEnum qryType) {
        if (ClueSearchTypeEnum.NAME_OR_PHONE.equals(qryType)) {
            return new HighlightBuilder().field(new HighlightBuilder.Field("phone.ngram")
                                             .preTags(ES_HIGHLIGHT_PRE).postTags(ES_HIGHLIGHT_POST))
                                         .field(new HighlightBuilder.Field("custName")
                                             .preTags(ES_HIGHLIGHT_PRE).postTags(ES_HIGHLIGHT_POST));
        }

        return null;
    }

    /**
     * 构建分词条件
     *
     * @param qryType          查询类型
     * @param searchContent    查询内容
     * @param boolQueryBuilder builder
     */
    private void buildMatch(ClueSearchTypeEnum qryType, String searchContent, BoolQueryBuilder boolQueryBuilder) {
        switch (qryType) {
            case NAME_OR_PHONE:
                boolQueryBuilder.should(QueryBuilders.matchQuery("phone.ngram", searchContent).boost(1.0f))
                                .should(QueryBuilders.matchPhraseQuery("custName", searchContent).boost(1.0f))
                                .minimumShouldMatch(1);
                break;
            case REVIEW_RECORD:
                // nested highlight
                HighlightBuilder highlight = new HighlightBuilder().field(new HighlightBuilder.Field(ES_CLUE_RESUME_DESC_PATH))
                                                                   .numOfFragments(0)
                                                                   .preTags(ES_HIGHLIGHT_PRE)
                                                                   .postTags(ES_HIGHLIGHT_POST);

                InnerHitBuilder innerHitBuilder =
                    new InnerHitBuilder(CLUE_SEARCH_RESUME_INNER_HIT_NAME).setSize(ES_INNER_HIT_SIZE)
                                                                          .setHighlightBuilder(highlight);

                boolQueryBuilder.must(
                    QueryBuilders.nestedQuery(
                        "custResumeList",
                        QueryBuilders.boolQuery()
                                     .should(QueryBuilders.matchPhraseQuery(ES_CLUE_RESUME_DESC_PATH, getSearchContent()).boost(5.0f))
                                     .should(QueryBuilders.matchQuery(ES_CLUE_RESUME_DESC_PATH, getSearchContent()).boost(1.0f))
                                     .minimumShouldMatch(1),
                        ScoreMode.Max
                    ).innerHit(innerHitBuilder)
                );
                break;
            default:
                throw new IllegalArgumentException("unknown search type");
        }
    }
}
