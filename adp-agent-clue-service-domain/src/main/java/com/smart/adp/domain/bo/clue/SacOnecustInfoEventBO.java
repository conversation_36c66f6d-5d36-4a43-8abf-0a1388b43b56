package com.smart.adp.domain.bo.clue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.smart.adp.domain.valueObject.clue.SacOnecustInfoEventVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 客户事件表BO
 * @Author: rik.ren
 * @Date: 2025/3/13 19:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SacOnecustInfoEventBO extends SacOnecustInfoEventVO {

    private Page<SacOnecustInfoEventBO> page;

    /**
     * 事件发生开始时间
     */
    private LocalDateTime eventTimeBegin;
    /**
     * 事件发生结束时间
     */
    private LocalDateTime eventTimeEnd;

    /**
     * 事件code集合
     */
    private List<String> listEventCode;

    public SacOnecustInfoEventBO(Page<SacOnecustInfoEventBO> page) {
        this.page = page;
    }

    public static SacOnecustInfoEventBO conventBO(SacOnecustInfoEventVO vo) {
        if (ObjectUtil.isEmpty(vo)) {
            return null;
        }
        SacOnecustInfoEventBO sacOnecustInfoEventBO = new SacOnecustInfoEventBO();
        sacOnecustInfoEventBO.setInfoEventId(vo.getInfoEventId());
        sacOnecustInfoEventBO.setEventCode(vo.getEventCode());
        sacOnecustInfoEventBO.setEventName(vo.getEventName());
        sacOnecustInfoEventBO.setMobile(vo.getMobile());
        sacOnecustInfoEventBO.setEventTime(vo.getEventTime());
        sacOnecustInfoEventBO.setSmartId(vo.getSmartId());
        sacOnecustInfoEventBO.setCreator(vo.getCreator());
        sacOnecustInfoEventBO.setCreatedDate(vo.getCreatedDate());
        sacOnecustInfoEventBO.setModifier(vo.getModifier());
        sacOnecustInfoEventBO.setLastUpdatedDate(vo.getLastUpdatedDate());
        sacOnecustInfoEventBO.setIsEnable(vo.getIsEnable());
        sacOnecustInfoEventBO.setUpdateControlId(vo.getUpdateControlId());
        return sacOnecustInfoEventBO;
    }

    public static List<SacOnecustInfoEventBO> conventBO(List<SacOnecustInfoEventVO> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        return entityList.stream()
                .map(entity -> {
                    SacOnecustInfoEventBO bo = conventBO(entity);
                    return bo;
                })
                .collect(Collectors.toList());
    }
}
