package com.smart.adp.domain.gateway.drive;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.bo.drive.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.entity.drive.SacTestDriveSheetEntity;

import java.util.List;

/**
 * @Description: 试驾gateway
 * @Author: rik.ren
 * @Date: 2025/3/15 15:26
 **/
public interface SacTestDriveSheetGateway {
    /**
     * 查询试驾单
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns
     * @return
     */
    DomainPage<SacTestDriveSheetEntity> queryTestDriveSheet(SacTestDriveSheetEntity param, SacTestDriveSheetBO boParam,
                                                            QueryOrderBy orderBy,
                                                            QueryColumn... columns);

    /**
     * 查询试驾单
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns
     * @return
     */
    List<SacTestDriveSheetEntity> queryTestDriveSheetList(SacTestDriveSheetEntity param, SacTestDriveSheetBO boParam,
                                                          QueryOrderBy orderBy,
                                                          QueryColumn... columns);

    /**
     * 更新试驾单
     *
     * @param param
     * @return
     */
    Boolean updateTestDriveSheet(SacTestDriveSheetEntity param);

    /**
     * 插入试驾单
     *
     * @param param
     * @return
     */
    Boolean insertTestDriveSheet(SacTestDriveSheetEntity param);

    /**
     * 未完成试驾任务数量
     *
     * @param user 当前用户
     * @return long
     */
    long toDoTestDriveTaskNum(UserBusiEntity user);

    /**
     * 未完成试驾单数量
     *
     * @param user 当前用户
     * @return long
     */
    long toDoTestDriveNum(UserBusiEntity user);
}
