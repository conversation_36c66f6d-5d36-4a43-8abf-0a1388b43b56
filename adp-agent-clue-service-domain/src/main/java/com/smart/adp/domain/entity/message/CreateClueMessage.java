package com.smart.adp.domain.entity.message;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/3/10 15:07
 * @description 线索创建发送cdp消息模型
 **/
@Data
public class CreateClueMessage implements Serializable {

    private static final long serialVersionUID = 1L;


    private Boolean cityOpenFlag;

    /**
     * 分析请求来源，默认adp2.0
     */
    private String tag = "adp2.0";

    /**
     * dlrCode
     */
    private String dlrCode;

    /**
     * dlrShortName
     */
    private String dlrShortName;

    /**
     * dlrId
     */
    private String dlrId;

    /**
     * 系统来源
     */
    private String systemSource;

    /**
     * 状态code
     */
    private String statusCode;

    /**
     * 状态描述
     */
    private String statusName;

    /**
     * 回访人员用户ID
     */
    private String reviewPersonId;

    /**
     * 销售顾问
     */
    private String reviewPersonName;


    /**
     * 手机号
     */
    private String phone;

    private String countyCode;

    private String countyName;

    private String provinceCode;

    private String provinceName;

    private String cityName;

    private String sellerCode;

    private String custName;

    private String channelName;

    private String firstChannel;

    private String channelCode;

    private String infoChanDCode;

    private String infoChanDDesc;

    private String businessHeatName;

    private String businessHeatCode;

    private String focusOfCarPurchase;

    private String intenCarTypeName;

    private String innerColorName;

    private String planBuyDateName;

    private String outColorName;

    // 是否增换购
    private String isAddBuy;

    private String cSellerId;

    private String cSellerPhone;
    private String cSellerUrl;

    private String remark;

    private String isNotCreated;

    private String genderName;

    /**
     * 是否战败激活
     */
    private Boolean existDefeatClue;

}
