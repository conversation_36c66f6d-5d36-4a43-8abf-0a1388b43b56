package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@Table(value = "t_sac_transfer_audit", schema = "csc")
public class SacTransferAudit implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 审核ID
	 */
	@Id("AUDIT_ID")
	private String auditId;

	/**
	 * 申请ID
	 */
	@Column("APPLY_ID")
	private String applyId;

	/**
	 * 审核类型编码
	 */
	@Column("APPLY_TYPE_CODE")
	private String applyTypeCode;

	/**
	 * 审核类型名称
	 */
	@Column("APPLY_TYPE_NAME")
	private String applyTypeName;

	/**
	 * 审核人员用户ID
	 */
	@Column("SH_PERSON_ID")
	private String shPersonId;

	/**
	 * 审核人员名称
	 */
	@Column("SH_PERSON_NAME")
	private String shPersonName;

	/**
	 * 审核意见
	 */
	@Column("SH_DESC")
	private String shDesc;

	/**
	 * 审核时间
	 */
	@Column("SH_TIME")
	private LocalDateTime shTime;

	/**
	 * 审核状态编码
	 */
	@Column("SH_STATUS")
	private String shStatus;

	/**
	 * 审核状态名称
	 */
	@Column("SH_STATUS_NAME")
	private String shStatusName;

	/**
	 * 扩展字段1
	 */
	@Column("COLUMN1")
	private String column1;

	/**
	 * 扩展字段2
	 */
	@Column("COLUMN2")
	private String column2;

	/**
	 * 扩展字段3
	 */
	@Column("COLUMN3")
	private String column3;

	/**
	 * 扩展字段4
	 */
	@Column("COLUMN4")
	private String column4;

	/**
	 * 扩展字段5
	 */
	@Column("COLUMN5")
	private String column5;

	/**
	 * JSON扩展字段
	 */
	@Column("EXTENDS_JSON")
	private String extendsJson;

	/**
	 * 厂商标识ID
	 */
	@Column("OEM_ID")
	private String oemId;

	/**
	 * 集团标识ID
	 */
	@Column("GROUP_ID")
	private String groupId;

	/**
	 * 创建人ID
	 */
	@Column("CREATOR")
	private String creator;

	/**
	 * 创建人
	 */
	@Column("CREATED_NAME")
	private String createdName;

	/**
	 * 创建日期
	 */
	@Column("CREATED_DATE")
	private LocalDateTime createdDate;

	/**
	 * 修改人ID
	 */
	@Column("MODIFIER")
	private String modifier;

	/**
	 * 修改人
	 */
	@Column("MODIFY_NAME")
	private String modifyName;

	/**
	 * 最后更新日期
	 */
	@Column("LAST_UPDATED_DATE")
	private LocalDateTime lastUpdatedDate;

	/**
	 * 是否可用
	 */
	@Column("IS_ENABLE")
	private String isEnable;

	/**
	 * 并发控制ID
	 */
	@Column("UPDATE_CONTROL_ID")
	private String updateControlId;

}