package com.smart.adp.domain.helper;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Slf4j
public class MD5Util {

    public static String encoderToken(String source) {
        String token = "";

        try {
            token = encodeHex(MessageDigest.getInstance("MD5").digest(source.getBytes()));
        } catch (NoSuchAlgorithmException var3) {
            log.error("com.smart.adp.domain.helper.MD5Util.encoderToken.exception,source={}", source, var3);
        }
        return token;
    }

    private static String encodeHex(byte[] bytes) {
        StringBuffer buffer = new StringBuffer(bytes.length * 2);
        for (int i = 0; i < bytes.length; ++i) {
            if ((bytes[i] & 255) < 16) {
                buffer.append("0");
            }
            buffer.append(Long.toString((long) (bytes[i] & 255), 16));
        }
        return buffer.toString();
    }

    public static String md5(String encryptStr) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            String result = "";
            byte[] temp = md5.digest(encryptStr.getBytes(Charset.forName("UTF-8")));

            for (int i = 0; i < temp.length; ++i) {
                result = result + Integer.toHexString(255 & temp[i] | -256).substring(6);
            }
            return result.toLowerCase();
        } catch (NoSuchAlgorithmException var5) {
            log.error("com.smart.adp.domain.helper.MD5Util.md5.noSuchAlgorithmException,encryptStr={}", encryptStr, var5);
        } catch (Exception var6) {
            log.error("com.smart.adp.domain.helper.MD5Util.md5.exception,source={}", encryptStr, var6);
        }

        return encryptStr;
    }
}

