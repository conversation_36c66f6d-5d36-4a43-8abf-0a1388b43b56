package com.smart.adp.domain.gateway.koc;

import com.smart.adp.domain.bo.koc.KocUserInfoBO;
import com.smart.adp.domain.bo.koc.KocUserQueryBO;
import com.smart.adp.domain.bo.koc.KocUserTagBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.koc.SacUserTagRel;

import java.util.List;

/**
 * @Description: KOC用户网关接口
 * @Author: system
 * @Date: 2025/8/4
 **/
public interface KocUserGateway {

    /**
     * 根据smartId查询用户标签关系
     *
     * @param smartId 用户smartId
     * @return 用户标签关系列表
     */
    List<SacUserTagRel> findBySmartId(String smartId);

    /**
     * 根据手机号查询用户标签关系
     *
     * @param phone 手机号
     * @return 用户标签关系列表
     */
    List<SacUserTagRel> findByPhone(String phone);

    /**
     * 根据条件查询用户标签关系
     *
     * @param condition 查询条件
     * @return 用户标签关系列表
     */
    List<SacUserTagRel> findByCondition(SacUserTagRel condition);

    /**
     * 分页查询用户信息
     *
     * @param queryBO 查询条件
     * @return 分页结果
     */
    DomainPage<KocUserInfoBO> findUserPage(KocUserQueryBO queryBO);

    /**
     * 根据关键字搜索用户
     *
     * @param keyword 关键字（手机号/昵称/smartId）
     * @return 用户信息列表
     */
    List<KocUserInfoBO> searchUsers(String keyword);

    /**
     * 根据标签ID查询用户
     *
     * @param tagId 标签ID
     * @return 用户信息列表
     */
    List<KocUserInfoBO> findUsersByTagId(String tagId);

    /**
     * 根据达人类型ID查询用户
     *
     * @param expertTypeId 达人类型ID
     * @return 用户信息列表
     */
    List<KocUserInfoBO> findUsersByExpertTypeId(String expertTypeId);

    /**
     * 保存用户标签关系
     *
     * @param userTagRel 用户标签关系
     * @return 是否成功
     */
    Boolean saveUserTagRel(SacUserTagRel userTagRel);

    /**
     * 批量保存用户标签关系
     *
     * @param userTagRels 用户标签关系列表
     * @return 是否成功
     */
    Boolean batchSaveUserTagRel(List<SacUserTagRel> userTagRels);

    /**
     * 删除用户标签关系
     *
     * @param relId 关系ID
     * @return 是否成功
     */
    Boolean deleteUserTagRel(String relId);

    /**
     * 根据条件删除用户标签关系
     *
     * @param condition 删除条件
     * @return 是否成功
     */
    Boolean deleteByCondition(SacUserTagRel condition);

    /**
     * 批量删除用户标签关系
     *
     * @param relIds 关系ID列表
     * @return 是否成功
     */
    Boolean batchDeleteUserTagRel(List<String> relIds);

    /**
     * 更新用户标签关系
     *
     * @param userTagRel 用户标签关系
     * @return 是否成功
     */
    Boolean updateUserTagRel(SacUserTagRel userTagRel);

    /**
     * 检查用户标签关系是否存在
     *
     * @param smartId 用户smartId
     * @param refId 引用ID
     * @param relType 关系类型
     * @return 是否存在
     */
    Boolean existsUserTagRel(String smartId, String refId, Integer relType);

    /**
     * 根据用户和关系类型查询
     *
     * @param smartId 用户smartId
     * @param relType 关系类型
     * @return 用户标签关系列表
     */
    List<SacUserTagRel> findBySmartIdAndRelType(String smartId, Integer relType);

    /**
     * 统计用户的标签数量
     *
     * @param smartId 用户smartId
     * @return 标签数量
     */
    Integer countUserTags(String smartId);

    /**
     * 统计用户的达人类型数量
     *
     * @param smartId 用户smartId
     * @return 达人类型数量
     */
    Integer countUserExpertTypes(String smartId);

    /**
     * 统计用户的备注数量
     *
     * @param smartId 用户smartId
     * @return 备注数量
     */
    Integer countUserRemarks(String smartId);
}
