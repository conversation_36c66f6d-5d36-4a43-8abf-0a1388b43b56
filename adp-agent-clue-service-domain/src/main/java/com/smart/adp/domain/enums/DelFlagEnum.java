package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 逻辑删除标记枚举
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023-03-01 16:11
 */
@Getter
@AllArgsConstructor
public enum DelFlagEnum {

    NORMAL(0, "正常"),
    DELETED(1, "删除"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static DelFlagEnum getByCode(Integer code) {
        return Arrays.stream(DelFlagEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findAny()
                .orElse(null);
    }
}
