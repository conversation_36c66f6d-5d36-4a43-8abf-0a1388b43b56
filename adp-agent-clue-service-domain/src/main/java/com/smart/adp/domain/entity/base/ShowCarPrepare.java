package com.smart.adp.domain.entity.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 展车部署实体类
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_usc_bu_showcar_prepare", schema = "mp")
public class ShowCarPrepare implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 展车整备ID
     */
    @Column("SHOWCAR_PREPARE_ID")
    private String showCarPrepareId;

    /**
     * 展车整备申请单号
     */
    @Column("SHOW_CARPREPARE_NO")
    private String showCarPrepareNo;

    /**
     * 展车申请ID
     */
    @Column("SHOW_CARAPPLY_ID")
    private String showCarApplyId;

    /**
     * VIN
     */
    @Column("VIN")
    private String vin;

    /**
     * 申请门店
     */
    @Column("APPLY_DLR_CODE")
    private String applyDlrCode;

    /**
     * 审批状态: 0=申请; 1=待业务审核; 2=待财务审核; 3=待渠道审核; 4=完成
     */
    @Column("PREPARE_STATUS")
    private String prepareStatus;

    /**
     * 审批人
     */
    @Column("AUTHER_MAN")
    private String autherMan;

    /**
     * 审批日期
     */
    @Column("AUTHER_DATE")
    private LocalDateTime autherDate;

    /**
     * 驳回原因
     */
    @Column("REJECT_RESON")
    private String rejectReson;

    /**
     * 创建人
     */
    @Column("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @Column("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @Column("UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 调拨单号
     */
    @Column("TRANSFER_ORDER_CODE")
    private String transferOrderCode;

    /**
     * 备注说明
     */
    @Column("REMARK")
    private String remark;

}
