package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import lombok.*;

import java.time.LocalDateTime;

import static com.smart.adp.domain.entity.clue.table.SacUserGroupDetailEntityTableDef.SAC_USER_GROUP_DETAIL_ENTITY;


/**
 * @Description: 用户分组详情
 * @Author: rik.ren
 * @Date: 2025/3/9 15:48
 **/
@Table(value = "t_sac_user_group_detail", schema = "csc")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SacUserGroupDetailEntity {

    /**
     * 分组明细id
     */
    @Id
    private String detailId;

    /**
     * 分组ID
     */
    @Column(value = "USER_GROUP_ID")
    private String userGroupId;

    /**
     * 线索编码
     */
    @Column(value = "SERVER_ORDER")
    private String serverOrder;

    /**
     * 客户ID
     */
    @Column(value = "CUST_ID")
    private String custId;

    /**
     * 客户手机号
     */
    @Column(value = "PHONE")
    private String phone;

    /**
     * 扩展字段1
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @Column(value = "COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @Column(value = "COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @Column(value = "COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @Column(value = "COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @Column(value = "COLUMN10")
    private String column10;

    /**
     * 时间戳
     */
    @Column(value = "_MYCAT_OP_TIME")
    private Long MycatOpTime;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @Column(value = "OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @Column(value = "GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column(value = "SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column(value = "SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 用户分组名称
     */
    @Column(ignore = true)
    private String userGroupName;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacUserGroupDetailEntity conditions(QueryWrapper wrapper) {
        wrapper.and(SAC_USER_GROUP_DETAIL_ENTITY.DETAIL_ID.eq(getDetailId(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.USER_GROUP_ID.eq(getUserGroupId(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.SERVER_ORDER.eq(getServerOrder(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.CUST_ID.eq(getCustId(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.PHONE.eq(getPhone(), StringUtil::hasText))
                .and(SAC_USER_GROUP_DETAIL_ENTITY.IS_ENABLE.eq("1"));
        return this;
    }
}
