package com.smart.adp.domain.enums;

import com.mybatisflex.core.util.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/11
 */
@Getter
@AllArgsConstructor
public enum StatisticsTypeEnum {

    /**
     * 待分配
     */
    WAIT_FOR_ASSIGN(1),

    /**
     * 未跟进
     */
    NO_REVIEW(2),

    /**
     * 待跟进
     */
    WAIT_FOR_REVIEW(3),

    /**
     * 逾期
     */
    OVERDUE(4),

    /**
     * 跟进任务
     */
    REVIEW_TASK(5),

    /**
     * 划转审核
     */
    TRANSFER_AUDIT(6),

    /**
     * 战败审核
     */
    DEFEATED_AUDIT(7),

    /**
     * 战败激活
     */
    ACTIVATES_DEFEATED(8),

    /**
     * HOT
     */
    HOT(9),

    /**
     * WARM
     */
    WARM(10),

    /**
     * COLD
     */
    COLD(11),

    /**
     * 未成交待跟进
     */
    NO_ORDER_WAIT_FOR_REVIEW(12),

    /**
     * 下定待跟进
     */
    ALREADY_ORDER_WAIT_FOR_REVIEW(13),



    /**
     * 未完成任务
     */
    DRIVE_TASK(101),

    /**
     * 试乘试驾
     */
    DRIVE(102),

    ;

    private final int code;

    public static final Set<StatisticsTypeEnum> STORE_MANAGER_STATISTICS =
            CollectionUtil.newHashSet(WAIT_FOR_ASSIGN, NO_REVIEW, WAIT_FOR_REVIEW, OVERDUE, REVIEW_TASK, TRANSFER_AUDIT, DEFEATED_AUDIT, ACTIVATES_DEFEATED);

    public static final Set<StatisticsTypeEnum> PRODUCT_EXPERT_STATISTICS =
            CollectionUtil.newHashSet(NO_REVIEW, WAIT_FOR_REVIEW, OVERDUE, REVIEW_TASK, ACTIVATES_DEFEATED);
}
