package com.smart.adp.domain.entity.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/6 13:23
 * @description 代理商门店实体信息
 **/
@Table(value = "t_usc_mdm_org_dlr", schema = "mp")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class AgentDlrInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column("DLR_ID")
    private String dlrId;

    @Column("DLR_CODE")
    private String dlrCode;

    @Column("DLR_SHORT_NAME")
    private String dlrShortName;
    
    @Column("DLR_FULL_NAME")
    private String dlrFullName;

    @Column("DLR_TYPE")
    private String dlrType;

    @Column("CREATOR")
    private String creator;

    /**
     * 门店省id
     */
    @Column("PROVINCE_ID")
    private String provinceId;

    /**
     * 城市id
     */
    @Column("CITY_ID")
    private String cityId;

    /**
     * 县区id
     */
    @Column("COUNTY_ID")
    private String countyId;

    @Column("CREATED_DATE")
    private LocalDateTime createdDate;
}
