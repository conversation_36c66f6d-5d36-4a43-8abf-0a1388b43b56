package com.smart.adp.domain.enums;

import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryTable;
import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.domain.common.constants.StringConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

import static com.smart.adp.domain.entity.clue.table.SacAllClueInfoDlrTableDef.SAC_ALL_CLUE_INFO_DLR;
import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;
import static com.smart.adp.domain.entity.clue.table.SacOneCustRemarkTableDef.SAC_ONE_CUST_REMARK;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/6
 */
@Getter
@AllArgsConstructor
public enum ClueQueryTypeEnum {

    /**
     * 未战败
     */
    NOT_DEFEATED(0, QueryCondition::createEmpty, SAC_CLUE_INFO_DLR),

    /**
     * 待转化
     */
    WAIT_TO_CONVERT(1,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
            CollectionUtil.newArrayList(ClueStageEnum.KNOW.getCodeStr(), ClueStageEnum.ARRIVE.getCodeStr(), ClueStageEnum.DRIVE.getCodeStr())),
            SAC_CLUE_INFO_DLR),

    /**
     * H
     */
    H(2,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
                                  CollectionUtil.newArrayList(ClueStageEnum.KNOW.getCodeStr(), ClueStageEnum.ARRIVE.getCodeStr(), ClueStageEnum.DRIVE.getCodeStr()))
                          .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(ClueLevelEnum.H.toString())),
            SAC_CLUE_INFO_DLR),

    /**
     * A
     */
    A(3,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
                                  CollectionUtil.newArrayList(ClueStageEnum.KNOW.getCodeStr(), ClueStageEnum.ARRIVE.getCodeStr(), ClueStageEnum.DRIVE.getCodeStr()))
                          .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(ClueLevelEnum.A.toString())),
            SAC_CLUE_INFO_DLR),

    /**
     * B
     */
    B(4,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
                                  CollectionUtil.newArrayList(ClueStageEnum.KNOW.getCodeStr(), ClueStageEnum.ARRIVE.getCodeStr(), ClueStageEnum.DRIVE.getCodeStr()))
                          .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(ClueLevelEnum.B.toString())),
            SAC_CLUE_INFO_DLR),

    /**
     * C
     */
    C(5,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
                                  CollectionUtil.newArrayList(ClueStageEnum.KNOW.getCodeStr(), ClueStageEnum.ARRIVE.getCodeStr(), ClueStageEnum.DRIVE.getCodeStr()))
                          .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(ClueLevelEnum.C.toString())),
            SAC_CLUE_INFO_DLR),

    /**
     * E
     */
    E(6,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
                                  CollectionUtil.newArrayList(ClueStageEnum.KNOW.getCodeStr(), ClueStageEnum.ARRIVE.getCodeStr(), ClueStageEnum.DRIVE.getCodeStr()))
                          .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(ClueLevelEnum.E.toString())),
            SAC_CLUE_INFO_DLR),

    /**
     * F
     */
    F(7,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
                                  CollectionUtil.newArrayList(ClueStageEnum.KNOW.getCodeStr(), ClueStageEnum.ARRIVE.getCodeStr(), ClueStageEnum.DRIVE.getCodeStr()))
                          .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(ClueLevelEnum.F.toString())),
            SAC_CLUE_INFO_DLR),

    /**
     * N
     */
    N(8,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
                                  CollectionUtil.newArrayList(ClueStageEnum.KNOW.getCodeStr(), ClueStageEnum.ARRIVE.getCodeStr(), ClueStageEnum.DRIVE.getCodeStr()))
                          .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(ClueLevelEnum.N.toString())),
            SAC_CLUE_INFO_DLR),

    /**
     * 未填写
     */
    NO_LEVEL(9,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
                                  CollectionUtil.newArrayList(ClueStageEnum.KNOW.getCodeStr(), ClueStageEnum.ARRIVE.getCodeStr(), ClueStageEnum.DRIVE.getCodeStr()))
                          .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.isNull()
                                                             .or(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(StringConstant.EMPTY))),
            SAC_CLUE_INFO_DLR),

    /**
     * 已下定
     */
    ORDER(10,
            () -> QueryCondition.create(SAC_CLUE_INFO_DLR.COLUMN18, SqlConsts.IN,
            CollectionUtil.newArrayList(ClueStageEnum.ORDER.getCodeStr(), ClueStageEnum.DELIVERY.getCodeStr())),
            SAC_CLUE_INFO_DLR),

    /**
     * 战败
     */
    DEFEATED(11, () -> QueryCondition.create(SAC_CLUE_INFO_DLR.STATUS_CODE, ClueStatusEnum.DEFEATED.getCode()), SAC_ALL_CLUE_INFO_DLR),

    /**
     * 全部
     */
    ALL(12, QueryCondition::createEmpty, SAC_ALL_CLUE_INFO_DLR),

    ;

    private final int code;
    private final Supplier<QueryCondition> conditionSupplier;
    private final QueryTable table;

    /**
     * 是否包含战败
     *
     * @return boolean
     */
    public boolean includeDefeat() {
        return ClueQueryTypeEnum.DEFEATED.equals(this) || ClueQueryTypeEnum.ALL.equals(this);
    }
}
