package com.smart.adp.domain.bo.koc;

import com.smart.adp.domain.entity.koc.SacExpertType;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: KOC达人类型BO
 * @Author: system
 * @Date: 2025/8/4
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KocExpertTypeBO extends SacExpertType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 使用次数
     */
    private Integer useCount;

    /**
     * 是否选中（用于前端展示）
     */
    private Boolean selected;

    /**
     * 构建达人类型BO
     */
    public static KocExpertTypeBO buildFromEntity(SacExpertType entity) {
        if (entity == null) {
            return null;
        }
        
        return KocExpertTypeBO.builder()
                .typeId(entity.getTypeId())
                .typeName(entity.getTypeName())
                .typeContent(entity.getTypeContent())
                .creator(entity.getCreator())
                .createdName(entity.getCreatedName())
                .createdDate(entity.getCreatedDate())
                .modifier(entity.getModifier())
                .modifyName(entity.getModifyName())
                .lastUpdatedDate(entity.getLastUpdatedDate())
                .selected(false)
                .build();
    }

    /**
     * 转换为实体对象
     */
    public SacExpertType toEntity() {
        return SacExpertType.builder()
                .typeId(this.getTypeId())
                .typeName(this.getTypeName())
                .typeContent(this.getTypeContent())
                .creator(this.getCreator())
                .createdName(this.getCreatedName())
                .createdDate(this.getCreatedDate())
                .modifier(this.getModifier())
                .modifyName(this.getModifyName())
                .lastUpdatedDate(this.getLastUpdatedDate())
                .build();
    }
}
