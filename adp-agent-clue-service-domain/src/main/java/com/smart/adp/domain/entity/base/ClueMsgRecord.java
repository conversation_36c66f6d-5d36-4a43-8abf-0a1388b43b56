package com.smart.adp.domain.entity.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/4/17 13:34
 * @description 线索消息模型对象
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_clue_msg_record", schema = "csc")
public class ClueMsgRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    @Column("MESSAGE_ID")
    private String messageId;

    /**
     * 0未读，1已读
     */
    @Column("IS_READ")
    private String isRead;

    /**
     * 经销商编码
     */
    @Column("DLR_CODE")
    private String dlrCode;

    /**
     * 手机号
     */
    @Column("PHONE")
    private String phone;

    /**
     * 消息类型
     */
    @Column("MESSAGE_TYPE")
    private String messageType;

    /**
     * 关键字ID
     */
    @Column("BUSI_KEYVALUE")
    private String busiKeyValue;

    /**
     * 消息接收人
     */
    @Column("RECEIVE_EMP_ID")
    private String receiveEmpId;

    /**
     * 消息内容
     */
    @Column("MESSAGE_CONTENT")
    private String messageContent;

    @Column("RELATION_BILL_ID")
    private String relationBillId;

    /**
     * 扩展信息
     */
    @Column("EXTEND_JSON")
    private String extendJson;

    @Column("MODIFIER")
    private String modify;

    @Column("MODIFY_NAME")
    private String modifyName;

    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    @Column("CREATED_NAME")
    private String createdName;

    @Column("CREATOR")
    private String creator;


    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;


    @Column("UPDATE_CONTROL_ID")
    private String updateControlId;
}
