package com.smart.adp.domain.service.base.impl;

import com.smart.adp.domain.valueObject.base.LookUpInfo;
import com.smart.adp.domain.gateway.base.LookUpGateway;
import com.smart.adp.domain.service.base.ILookUpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 字典表service实现
 * @Author: rik.ren
 * @Date: 2025/3/13 19:10
 **/
@Service
public class LookUpServiceImpl implements ILookUpService {
    @Autowired
    private LookUpGateway lookUpGateway;

    @Override
    public LookUpInfo findLookUpInfo(String lookUpTypeCode, String lookUpValueCode) {
        return lookUpGateway.findLookUpInfo(lookUpTypeCode, lookUpValueCode);
    }

    @Override
    public List<LookUpInfo> findLookUpInfo(String lookUpTypeCode) {
        return lookUpGateway.findLookUpByTypeCode(lookUpTypeCode);
    }

    @Override
    public List<LookUpInfo> findLookUpInfo(LookUpInfo param) {
        return lookUpGateway.findLookUp(param);
    }

    @Override
    public List<LookUpInfo> findLookUpInfo(String lookUpTypeCode, List<String> lookUpValueCode) {
        return lookUpGateway.findLookUp(lookUpTypeCode, lookUpValueCode);
    }

    @Override
    public List<LookUpInfo> findLookUpList(LookUpInfo param) {
        return lookUpGateway.findByTypeCode(param.getLookUpTypeCode());
    }

}
