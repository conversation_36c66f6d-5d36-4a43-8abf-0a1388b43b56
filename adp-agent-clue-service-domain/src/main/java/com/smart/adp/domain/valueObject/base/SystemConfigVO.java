package com.smart.adp.domain.valueObject.base;

import lombok.Data;

/**
 * <AUTHOR>
 * date 2025/3/3 20:12
 * @description
 **/
@Data
public class SystemConfigVO {
    private String configValueId;
    private String configId;
    private String configRange;
    private String configRangeName;
    private String orgCode;
    private String orgName;
    private String configCode;
    private String configName;
    private String valueCode;
    private String valueName;
    private String configDesc;
}
