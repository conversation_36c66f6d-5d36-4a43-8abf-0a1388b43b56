package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.bo.clue.SacVirtualRecordAbstractBO;
import com.smart.adp.domain.entity.base.AgentEmployee;
import com.smart.adp.domain.gateway.clue.IRecordAbstractGateway;
import com.smart.adp.domain.service.base.IAgentEmployeeService;
import com.smart.adp.domain.service.clue.IRecordAbstractService;
import com.smart.adp.domain.valueObject.clue.SacTestRecordAbstractVO;
import com.smart.adp.domain.valueObject.clue.SacVirtualRecordAbstractVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.entity.base.table.AgentEmployeeTableDef.AGENT_EMPLOYEE;

/**
 * @Description: 录音摘要service实现
 * @Author: rik.ren
 * @Date: 2025/3/13 15:32
 **/
@Service
public class RecordAbstractServiceImpl implements IRecordAbstractService {
    @Autowired
    private IRecordAbstractGateway recordAbstractGateway;
    @Autowired
    private IAgentEmployeeService agentEmployeeService;


    /**
     * 保存虚拟外呼摘要内容
     *
     * @param entity
     * @return
     */
    @Override
    public Boolean saveVirtualAbstractContent(SacVirtualRecordAbstractVO entity) {
        // 1. 根据员工iD查询员工姓名
        AgentEmployee empResult = agentEmployeeService.findByEmpId(entity.getEmpId(), AGENT_EMPLOYEE.EMP_NAME);
        // 2. 更新实体并保存
        entity.setEmpName(ObjectUtil.isEmpty(empResult) ? null : empResult.getEmpName());
        return recordAbstractGateway.saveVirtualAbstractContent(entity);
    }

    /**
     * 保存虚拟外呼摘要内容
     *
     * @param entity
     * @return
     */
    @Override
    public Boolean saveVirtualAbstractContent(SacVirtualRecordAbstractBO entity) {
        // 1. 根据员工iD查询员工姓名
        AgentEmployee empResult = agentEmployeeService.findByEmpId(entity.getEmpId(), AGENT_EMPLOYEE.EMP_NAME);
        // 2. 更新实体并保存
        entity.setEmpName(ObjectUtil.isEmpty(empResult) ? null : empResult.getEmpName());

        return recordAbstractGateway.saveVirtualAbstractContent(entity);
    }

    /**
     * 保存试驾录音摘要内容
     *
     * @param entity
     * @return
     */
    @Override
    public Boolean saveTestAbstractContent(SacTestRecordAbstractVO entity) {
        // 1. 根据员工iD查询员工姓名
        AgentEmployee empResult = agentEmployeeService.findByEmpId(entity.getEmpId(), AGENT_EMPLOYEE.EMP_NAME);
        // 2. 更新实体并保存
        entity.setEmpName(ObjectUtil.isEmpty(empResult) ? null : empResult.getEmpName());
        return recordAbstractGateway.saveTestAbstractContent(entity);
    }

    /**
     * 查询虚拟外呼摘要内容
     *
     * @param param
     * @param columns
     * @return
     */
    @Override
    public List<SacVirtualRecordAbstractBO> queryVirtualRecordAbs(SacVirtualRecordAbstractBO param, QueryColumn... columns) {
        Page<SacVirtualRecordAbstractVO> resultPage = recordAbstractGateway.queryVirtualRecordAbs(param, param.getPageObj()
                , null, columns);
        return SacVirtualRecordAbstractBO.conventBO(resultPage.getRecords());
    }

    /**
     * 查询试驾录音摘要内容
     *
     * @param param
     * @param orderBy
     * @param columns
     * @return
     */
    @Override
    public List<SacVirtualRecordAbstractBO> queryTestRecordAbs(SacVirtualRecordAbstractBO param, QueryOrderBy orderBy,
                                                               QueryColumn... columns) {
        return null;
    }
}
