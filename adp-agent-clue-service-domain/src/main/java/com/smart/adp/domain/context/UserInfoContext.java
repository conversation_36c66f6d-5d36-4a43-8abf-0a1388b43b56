package com.smart.adp.domain.context;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.Objects;

/**
 * <p>
 * 用户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/19
 */
public class UserInfoContext {

    private static final String KEY_PREFIX = "MPJAVA-oracle-nacos#sessionCache#busicen";
    private static final String JSON_START = "{";
    private static final Duration TOKEN_EX = Duration.ofMinutes(30);

    private static final TransmittableThreadLocal<UserBusiEntity> USER_INFO_TTL = new TransmittableThreadLocal<UserBusiEntity>() {
        @Override
        protected UserBusiEntity initialValue() {
            throw new BusinessException(RespCode.FAIL.getCode(), "登录已过期，请重新登录后重试！");
        }
    };

    public static void init(StringRedisTemplate stringRedisTemplate, String token) {
        if (Objects.isNull(token) || token.isEmpty()) {
            throw new BusinessException(RespCode.FAIL.getCode(), "用户未登录,请登录后在使用！");
        }

        String value = stringRedisTemplate.opsForValue()
                .get(KEY_PREFIX + token);
        if (Objects.nonNull(value)) {
            String json = value.substring(value.indexOf(JSON_START));
            UserBusiEntity userInfo = JSONObject.parseObject(json, UserBusiEntity.class);
            USER_INFO_TTL.set(userInfo);
            // renew
            stringRedisTemplate.opsForValue()
                    .set(KEY_PREFIX + token, value, TOKEN_EX);
        }
    }

    public static UserBusiEntity get() {
        return USER_INFO_TTL.get();
    }

    public static void set(UserBusiEntity userBusiEntity) {
        USER_INFO_TTL.set(userBusiEntity);
    }

    public static void remove() {
        USER_INFO_TTL.remove();
    }
}
