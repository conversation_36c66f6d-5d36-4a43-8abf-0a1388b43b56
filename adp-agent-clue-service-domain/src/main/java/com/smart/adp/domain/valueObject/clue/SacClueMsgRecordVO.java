package com.smart.adp.domain.valueObject.clue;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.smart.adp.domain.enums.ClueMsgReadEnum;
import lombok.*;

import java.lang.Long;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;

/**
 * 店端线索消息提醒表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_clue_msg_record", schema = "csc")
public class SacClueMsgRecordVO {
    /**
     * 消息ID
     */
    @Id(keyType = KeyType.Generator, value = "uuid")
    private String messageId;

    /**
     * 0未读，1已读
     *
     * @see ClueMsgReadEnum
     */
    @Column(value = "IS_READ")
    private String isRead;

    /**
     * 经销商编码
     */
    @Column(value = "DLR_CODE")
    private String dlrCode;

    /**
     * 手机号码
     */
    @Column(value = "PHONE")
    private String phone;

    /**
     * 场景编码 0试乘试驾
     */
    @Column(value = "MESSAGE_TYPE")
    private String messageType;

    /**
     * 关键字ID
     */
    @Column(value = "BUSI_KEYVALUE")
    private String busiKeyvalue;

    /**
     * 消息接收人
     */
    @Column(value = "RECEIVE_EMP_ID")
    private String receiveEmpId;

    /**
     * 消息内容
     */
    @Column(value = "MESSAGE_CONTENT")
    private String messageContent;

    /**
     * 关联单据ID
     */
    @Column(value = "RELATION_BILL_ID")
    private String relationBillId;

    /**
     * 扩展信息
     */
    @Column(value = "EXTEND_JSON")
    private Object extendJson;

    /**
     * 扩展字段1
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 时间戳
     */
    @Column(value = "_MYCAT_OP_TIME")
    private Long MycatOpTime;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @Column(value = "OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @Column(value = "GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE", isLogicDelete = true)
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column(value = "SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column(value = "SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;
}
