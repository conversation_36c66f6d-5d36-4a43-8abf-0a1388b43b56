package com.smart.adp.domain.service.koc.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.smart.adp.domain.bo.koc.KocUserInfoBO;
import com.smart.adp.domain.bo.koc.KocUserQueryBO;
import com.smart.adp.domain.bo.koc.KocUserTagBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.koc.SacUserTagRel;
import com.smart.adp.domain.enums.KocRelTypeEnum;
import com.smart.adp.domain.gateway.koc.KocUserGateway;
import com.smart.adp.domain.gateway.koc.UCUserGateway;
import com.smart.adp.domain.service.koc.IKocUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: KOC用户领域服务实现
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Slf4j
@Service
public class KocUserServiceImpl implements IKocUserService {

    @Autowired
    private KocUserGateway kocUserGateway;

    @Autowired
    private UCUserGateway ucUserGateway;

    @Override
    public KocUserInfoBO getUserBySmartId(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return null;
        }
        return ucUserGateway.findBySmartId(smartId);
    }

    @Override
    public KocUserInfoBO getUserByPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return null;
        }
        return ucUserGateway.findByPhone(phone);
    }

    @Override
    public List<KocUserInfoBO> searchUsers(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new ArrayList<>();
        }
        return ucUserGateway.searchUsers(keyword);
    }

    @Override
    public DomainPage<KocUserInfoBO> getUserPage(KocUserQueryBO queryBO) {
        if (ObjectUtil.isEmpty(queryBO)) {
            return new DomainPage<>();
        }
        return kocUserGateway.findUserPage(queryBO);
    }

    @Override
    public List<KocUserTagBO> getUserTags(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return new ArrayList<>();
        }

        List<SacUserTagRel> tagRels = kocUserGateway.findBySmartIdAndRelType(smartId, KocRelTypeEnum.TAG.getCode());
        if (CollectionUtil.isEmpty(tagRels)) {
            return new ArrayList<>();
        }

        return tagRels.stream()
                .map(KocUserTagBO::buildFromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<KocUserTagBO> getUserExpertTypes(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return new ArrayList<>();
        }

        List<SacUserTagRel> expertTypeRels = kocUserGateway.findBySmartIdAndRelType(smartId, KocRelTypeEnum.EXPERT_TYPE.getCode());
        if (CollectionUtil.isEmpty(expertTypeRels)) {
            return new ArrayList<>();
        }

        return expertTypeRels.stream()
                .map(KocUserTagBO::buildFromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<KocUserTagBO> getUserRemarks(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return new ArrayList<>();
        }

        List<SacUserTagRel> remarkRels = kocUserGateway.findBySmartIdAndRelType(smartId, KocRelTypeEnum.REMARK.getCode());
        if (CollectionUtil.isEmpty(remarkRels)) {
            return new ArrayList<>();
        }

        return remarkRels.stream()
                .map(KocUserTagBO::buildFromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Boolean addTagToUser(String smartId, String tagId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(tagId)) {
            log.warn("为用户添加标签失败：参数为空, smartId={}, tagId={}", smartId, tagId);
            return false;
        }

        // 验证用户是否存在
        if (!validateUserExists(smartId)) {
            log.warn("为用户添加标签失败：用户不存在, smartId={}", smartId);
            return false;
        }

        // 验证标签关系是否已存在
        if (validateUserTagRelExists(smartId, tagId, KocRelTypeEnum.TAG.getCode())) {
            log.warn("为用户添加标签失败：标签关系已存在, smartId={}, tagId={}", smartId, tagId);
            return false;
        }

        // 获取用户信息
        KocUserInfoBO userInfo = getUserBySmartId(smartId);
        if (ObjectUtil.isEmpty(userInfo)) {
            log.warn("为用户添加标签失败：获取用户信息失败, smartId={}", smartId);
            return false;
        }

        // 构建用户标签关系
        SacUserTagRel userTagRel = buildUserTagRel(smartId, userInfo.getPhone(), userInfo.getNickName(),
                KocRelTypeEnum.TAG.getCode(), tagId, null, operatorId, operatorName);

        return kocUserGateway.saveUserTagRel(userTagRel);
    }

    @Override
    public Boolean batchAddTagsToUsers(List<String> smartIds, List<String> tagIds, String operatorId, String operatorName) {
        if (CollectionUtil.isEmpty(smartIds) || CollectionUtil.isEmpty(tagIds)) {
            log.warn("批量为用户添加标签失败：参数为空");
            return false;
        }

        List<SacUserTagRel> userTagRels = new ArrayList<>();
        
        for (String smartId : smartIds) {
            // 验证用户是否存在
            if (!validateUserExists(smartId)) {
                log.warn("批量为用户添加标签失败：用户不存在, smartId={}", smartId);
                continue;
            }

            // 获取用户信息
            KocUserInfoBO userInfo = getUserBySmartId(smartId);
            if (ObjectUtil.isEmpty(userInfo)) {
                log.warn("批量为用户添加标签失败：获取用户信息失败, smartId={}", smartId);
                continue;
            }

            for (String tagId : tagIds) {
                // 验证标签关系是否已存在
                if (validateUserTagRelExists(smartId, tagId, KocRelTypeEnum.TAG.getCode())) {
                    log.debug("批量为用户添加标签：标签关系已存在，跳过, smartId={}, tagId={}", smartId, tagId);
                    continue;
                }

                // 构建用户标签关系
                SacUserTagRel userTagRel = buildUserTagRel(smartId, userInfo.getPhone(), userInfo.getNickName(),
                        KocRelTypeEnum.TAG.getCode(), tagId, null, operatorId, operatorName);
                userTagRels.add(userTagRel);
            }
        }

        if (CollectionUtil.isEmpty(userTagRels)) {
            log.warn("批量为用户添加标签失败：没有有效的用户标签关系");
            return false;
        }

        return kocUserGateway.batchSaveUserTagRel(userTagRels);
    }

    @Override
    public Boolean removeTagFromUser(String smartId, String tagId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(tagId)) {
            log.warn("从用户移除标签失败：参数为空, smartId={}, tagId={}", smartId, tagId);
            return false;
        }

        // 构建删除条件
        SacUserTagRel condition = SacUserTagRel.builder()
                .smartId(smartId)
                .refId(tagId)
                .relType(KocRelTypeEnum.TAG.getCode())
                .build();

        return kocUserGateway.deleteByCondition(condition);
    }

    @Override
    public Boolean batchRemoveTagsFromUsers(List<String> smartIds, List<String> tagIds, String operatorId, String operatorName) {
        if (CollectionUtil.isEmpty(smartIds) || CollectionUtil.isEmpty(tagIds)) {
            log.warn("批量从用户移除标签失败：参数为空");
            return false;
        }

        boolean allSuccess = true;
        for (String smartId : smartIds) {
            for (String tagId : tagIds) {
                Boolean result = removeTagFromUser(smartId, tagId, operatorId, operatorName);
                if (!result) {
                    allSuccess = false;
                    log.warn("批量从用户移除标签部分失败, smartId={}, tagId={}", smartId, tagId);
                }
            }
        }

        return allSuccess;
    }

    @Override
    public Boolean addExpertTypeToUser(String smartId, String expertTypeId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(expertTypeId)) {
            log.warn("为用户添加达人类型失败：参数为空, smartId={}, expertTypeId={}", smartId, expertTypeId);
            return false;
        }

        // 验证用户是否存在
        if (!validateUserExists(smartId)) {
            log.warn("为用户添加达人类型失败：用户不存在, smartId={}", smartId);
            return false;
        }

        // 验证达人类型关系是否已存在
        if (validateUserTagRelExists(smartId, expertTypeId, KocRelTypeEnum.EXPERT_TYPE.getCode())) {
            log.warn("为用户添加达人类型失败：达人类型关系已存在, smartId={}, expertTypeId={}", smartId, expertTypeId);
            return false;
        }

        // 获取用户信息
        KocUserInfoBO userInfo = getUserBySmartId(smartId);
        if (ObjectUtil.isEmpty(userInfo)) {
            log.warn("为用户添加达人类型失败：获取用户信息失败, smartId={}", smartId);
            return false;
        }

        // 构建用户达人类型关系
        SacUserTagRel userTagRel = buildUserTagRel(smartId, userInfo.getPhone(), userInfo.getNickName(),
                KocRelTypeEnum.EXPERT_TYPE.getCode(), expertTypeId, null, operatorId, operatorName);

        return kocUserGateway.saveUserTagRel(userTagRel);
    }

    @Override
    public Boolean batchAddExpertTypesToUsers(List<String> smartIds, List<String> expertTypeIds, String operatorId, String operatorName) {
        if (CollectionUtil.isEmpty(smartIds) || CollectionUtil.isEmpty(expertTypeIds)) {
            log.warn("批量为用户添加达人类型失败：参数为空");
            return false;
        }

        List<SacUserTagRel> userTagRels = new ArrayList<>();
        
        for (String smartId : smartIds) {
            // 验证用户是否存在
            if (!validateUserExists(smartId)) {
                log.warn("批量为用户添加达人类型失败：用户不存在, smartId={}", smartId);
                continue;
            }

            // 获取用户信息
            KocUserInfoBO userInfo = getUserBySmartId(smartId);
            if (ObjectUtil.isEmpty(userInfo)) {
                log.warn("批量为用户添加达人类型失败：获取用户信息失败, smartId={}", smartId);
                continue;
            }

            for (String expertTypeId : expertTypeIds) {
                // 验证达人类型关系是否已存在
                if (validateUserTagRelExists(smartId, expertTypeId, KocRelTypeEnum.EXPERT_TYPE.getCode())) {
                    log.debug("批量为用户添加达人类型：达人类型关系已存在，跳过, smartId={}, expertTypeId={}", smartId, expertTypeId);
                    continue;
                }

                // 构建用户达人类型关系
                SacUserTagRel userTagRel = buildUserTagRel(smartId, userInfo.getPhone(), userInfo.getNickName(),
                        KocRelTypeEnum.EXPERT_TYPE.getCode(), expertTypeId, null, operatorId, operatorName);
                userTagRels.add(userTagRel);
            }
        }

        if (CollectionUtil.isEmpty(userTagRels)) {
            log.warn("批量为用户添加达人类型失败：没有有效的用户达人类型关系");
            return false;
        }

        return kocUserGateway.batchSaveUserTagRel(userTagRels);
    }

    @Override
    public Boolean removeExpertTypeFromUser(String smartId, String expertTypeId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(expertTypeId)) {
            log.warn("从用户移除达人类型失败：参数为空, smartId={}, expertTypeId={}", smartId, expertTypeId);
            return false;
        }

        // 构建删除条件
        SacUserTagRel condition = SacUserTagRel.builder()
                .smartId(smartId)
                .refId(expertTypeId)
                .relType(KocRelTypeEnum.EXPERT_TYPE.getCode())
                .build();

        return kocUserGateway.deleteByCondition(condition);
    }

    @Override
    public Boolean batchRemoveExpertTypesFromUsers(List<String> smartIds, List<String> expertTypeIds, String operatorId, String operatorName) {
        if (CollectionUtil.isEmpty(smartIds) || CollectionUtil.isEmpty(expertTypeIds)) {
            log.warn("批量从用户移除达人类型失败：参数为空");
            return false;
        }

        boolean allSuccess = true;
        for (String smartId : smartIds) {
            for (String expertTypeId : expertTypeIds) {
                Boolean result = removeExpertTypeFromUser(smartId, expertTypeId, operatorId, operatorName);
                if (!result) {
                    allSuccess = false;
                    log.warn("批量从用户移除达人类型部分失败, smartId={}, expertTypeId={}", smartId, expertTypeId);
                }
            }
        }

        return allSuccess;
    }

    @Override
    public Boolean addRemarkToUser(String smartId, String remarkContent, String operatorId, String operatorName) {
        if (StrUtil.isBlank(smartId) || StrUtil.isBlank(remarkContent)) {
            log.warn("为用户添加备注失败：参数为空, smartId={}, remarkContent={}", smartId, remarkContent);
            return false;
        }

        // 验证用户是否存在
        if (!validateUserExists(smartId)) {
            log.warn("为用户添加备注失败：用户不存在, smartId={}", smartId);
            return false;
        }

        // 获取用户信息
        KocUserInfoBO userInfo = getUserBySmartId(smartId);
        if (ObjectUtil.isEmpty(userInfo)) {
            log.warn("为用户添加备注失败：获取用户信息失败, smartId={}", smartId);
            return false;
        }

        // 构建用户备注关系
        SacUserTagRel userTagRel = buildUserTagRel(smartId, userInfo.getPhone(), userInfo.getNickName(),
                KocRelTypeEnum.REMARK.getCode(), null, remarkContent, operatorId, operatorName);

        return kocUserGateway.saveUserTagRel(userTagRel);
    }

    @Override
    public Boolean updateUserRemark(String relId, String remarkContent, String operatorId, String operatorName) {
        if (StrUtil.isBlank(relId) || StrUtil.isBlank(remarkContent)) {
            log.warn("更新用户备注失败：参数为空, relId={}, remarkContent={}", relId, remarkContent);
            return false;
        }

        SacUserTagRel userTagRel = SacUserTagRel.builder()
                .relId(relId)
                .remarkContent(remarkContent)
                .modifier(operatorId)
                .modifyName(operatorName)
                .lastUpdatedDate(LocalDateTime.now())
                .build();

        return kocUserGateway.updateUserTagRel(userTagRel);
    }

    @Override
    public Boolean removeUserRemark(String relId, String operatorId, String operatorName) {
        if (StrUtil.isBlank(relId)) {
            log.warn("删除用户备注失败：关系ID为空");
            return false;
        }

        return kocUserGateway.deleteUserTagRel(relId);
    }

    @Override
    public Boolean validateUserExists(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return false;
        }
        return ucUserGateway.existsBySmartId(smartId);
    }

    @Override
    public Boolean validateUserTagRelExists(String smartId, String refId, Integer relType) {
        if (StrUtil.isBlank(smartId) || relType == null) {
            return false;
        }
        return kocUserGateway.existsUserTagRel(smartId, refId, relType);
    }

    @Override
    public KocUserInfoBO getUserFullInfo(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return null;
        }

        // 获取用户基本信息
        KocUserInfoBO userInfo = getUserBySmartId(smartId);
        if (ObjectUtil.isEmpty(userInfo)) {
            return null;
        }

        // 获取用户标签
        List<KocUserTagBO> userTags = getUserTags(smartId);
        // 获取用户达人类型
        List<KocUserTagBO> userExpertTypes = getUserExpertTypes(smartId);
        // 获取用户备注
        List<KocUserTagBO> userRemarks = getUserRemarks(smartId);

        // 设置统计信息
        userInfo.setTagCount(CollectionUtil.size(userTags));
        userInfo.setExpertTypeCount(CollectionUtil.size(userExpertTypes));
        userInfo.setRemarkCount(CollectionUtil.size(userRemarks));

        return userInfo;
    }

    /**
     * 构建用户标签关系对象
     */
    private SacUserTagRel buildUserTagRel(String smartId, String phone, String nickName, Integer relType,
                                          String refId, String remarkContent, String operatorId, String operatorName) {
        LocalDateTime now = LocalDateTime.now();
        return SacUserTagRel.builder()
                .relId(IdUtil.fastUUID())
                .smartId(smartId)
                .phone(phone)
                .nickName(nickName)
                .relType(relType)
                .refId(refId)
                .remarkContent(remarkContent)
                .creator(operatorId)
                .createdName(operatorName)
                .createdDate(now)
                .modifier(operatorId)
                .modifyName(operatorName)
                .lastUpdatedDate(now)
                .build();
    }
}
