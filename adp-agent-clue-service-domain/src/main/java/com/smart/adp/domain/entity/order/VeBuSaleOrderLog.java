package com.smart.adp.domain.entity.order;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/26
 */
@Table(value = "t_orc_ve_bu_sale_order_log", schema = "orc")
public class VeBuSaleOrderLog {

    /**
     * 订单日志 ID
     */
    @Id("SALE_ORDER_LOG_ID")
    private String saleOrderLogId;

    /**
     * 销售订单 ID
     */
    @Column("SALE_ORDER_ID")
    private String saleOrderId;

    /**
     * 销售订单号
     */
    @Column("SALE_ORDER_CODE")
    private String saleOrderCode;

    /**
     * 销售订单状态
     */
    @Column("SALE_ORDER_STATE")
    private String saleOrderState;

    /**
     * 日志记录时间
     */
    @Column("LOG_DATE")
    private LocalDateTime logDate;

    /**
     * VIN
     */
    @Column("VIN")
    private String vin;

}
