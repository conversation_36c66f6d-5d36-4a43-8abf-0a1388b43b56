package com.smart.adp.domain.strategy.queryInteractData;

import com.mybatisflex.core.paginate.Page;
import com.smart.adp.domain.bo.clue.InteractDataBO;
import com.smart.adp.domain.bo.clue.SacOnecustInfoEventBO;
import com.smart.adp.domain.bo.clue.SacOnecustResumeBO;
import com.smart.adp.domain.bo.clue.UscMdmVirtualRecordBO;
import com.smart.adp.domain.bo.drive.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.constants.BizConstants;
import com.smart.adp.domain.enums.DataSourceType;
import com.smart.adp.domain.enums.EventTypeEnum;
import com.smart.adp.domain.enums.InteractDataTypeEnum;
import com.smart.adp.domain.service.clue.ISacOnecustInfoEventService;
import com.smart.adp.domain.service.clue.ISacOnecustResumeService;
import com.smart.adp.domain.service.clue.IUscMdmVirtualRecordService;
import com.smart.adp.domain.service.drive.ISacTestDriveSheetService;
import com.smart.adp.domain.utils.TimeUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;

@Component
@RequiredArgsConstructor
public class AllDataTypeIneractStrategy implements QueryIneractStrategy {
    @Autowired
    private ISacOnecustInfoEventService eventService;
    @Autowired
    private IUscMdmVirtualRecordService virtualRecordService;
    @Autowired
    private ISacOnecustResumeService onecustResumeService;
    @Autowired
    private ISacTestDriveSheetService testDriveSheetService;

    /**
     * 根据条件构建查询
     *
     * @param param
     * @return
     */
    @Override
    public List<Callable<DomainPage<?>>> buildQueryTasks(InteractDataBO param) {
        List<Callable<DomainPage<?>>> tasks = new ArrayList<>();

        // 客户行为处理
        if (!InteractDataTypeEnum.REVIEW.getCode().equals(param.getDataType())) {
            tasks.add(() -> wrapResult(eventService.querycustEvent(buildSacOnecustInfoEventBO(param)),
                    DataSourceType.CUSTOMER_EVENT
            ));
        }

        // 跟进方式处理，senceCode没有设定枚举值，就用all代替全部，没传也是全部
        if (("all".equals(param.getSenceCode()) || StringUtils.isBlank(param.getSenceCode()) && !InteractDataTypeEnum.EVENT.getCode().equals(param.getDataType()))) {
            // 虚拟外呼
            tasks.add(() -> wrapResult(virtualRecordService.queryClueVirtualRecord(buildUscMdmVirtualRecordBO(param)),
                    DataSourceType.VIRTUAL_RECORD
            ));
            // 试驾录音
            tasks.add(() -> wrapResult(testDriveSheetService.queryTestDriveSheetPage(buildSacTestDriveSheetBO(param)),
                    DataSourceType.TEST_DRIVE
            ));
            // 跟进场景
            tasks.add(() -> wrapResult(onecustResumeService.queryOnecustResumeByCustId(buildSacOnecustResumeBO(param)),
                    DataSourceType.FOLLOW_RECORD
            ));
        } else if (!InteractDataTypeEnum.EVENT.getCode().equals(param.getDataType())) {
            tasks.addAll(getAllFollowTasks(param));
        }
        return tasks;
    }

    private List<Callable<DomainPage<?>>> getAllFollowTasks(InteractDataBO param) {
        return Arrays.asList(
                () -> wrapResult(onecustResumeService.queryOnecustResumeByCustId(buildSacOnecustResumeBO(param)),
                        DataSourceType.FOLLOW_RECORD));
    }

    private TypedDomainPage<?> wrapResult(DomainPage<?> page, DataSourceType type) {
        return new TypedDomainPage<>(page, type);
    }

    public SacOnecustInfoEventBO buildSacOnecustInfoEventBO(InteractDataBO param) {
        SacOnecustInfoEventBO eventBOObj = new SacOnecustInfoEventBO(new Page<>(param.getPageNumber(), param.getPageSize()));
        eventBOObj.setMobile(param.getPhone());
        eventBOObj.setEventTimeBegin(param.getBeginTime());
        eventBOObj.setEventTimeEnd(TimeUtils.handleEnd(param.getEndTime()));
        if (StringUtils.isNotEmpty(param.getEventType()) && EventTypeEnum.CRUX.getCode().equals(param.getEventType())) {
            eventBOObj.setListEventCode(BizConstants.CRUX_EVENT_LIST);
        } else if (StringUtils.isNotEmpty(param.getEventType()) && EventTypeEnum.OTHER.getCode().equals(param.getEventType())) {
            eventBOObj.setListEventCode(BizConstants.OTHER_EVENT_LIST);
        }
        return eventBOObj;
    }

    public SacTestDriveSheetBO buildSacTestDriveSheetBO(InteractDataBO param) {
        SacTestDriveSheetBO testDriveSheetBOObj = new SacTestDriveSheetBO(new Page<>(param.getPageNumber(),
                param.getPageSize()));
        testDriveSheetBOObj.setCustomerId(param.getCustId());
        testDriveSheetBOObj.setDateBegin(param.getBeginTime());
        testDriveSheetBOObj.setDateEnd(TimeUtils.handleEnd(param.getEndTime()));
        return testDriveSheetBOObj;
    }

    public SacOnecustResumeBO buildSacOnecustResumeBO(InteractDataBO param) {
        SacOnecustResumeBO resumeBoObj = new SacOnecustResumeBO(new Page<>(param.getPageNumber(), param.getPageSize()));
        resumeBoObj.setCustId(param.getCustId());
        resumeBoObj.setCreateDateBegin(param.getBeginTime());
        resumeBoObj.setCreateDateEnd(TimeUtils.handleEnd(param.getEndTime()));
        resumeBoObj.setListSenceCode(StringUtils.isNotBlank(param.getSenceCode()) ? Arrays.asList(param.getSenceCode().split(",")) : null);
        resumeBoObj.setSearchContent(param.getSearchContent());
        return resumeBoObj;
    }

    public UscMdmVirtualRecordBO buildUscMdmVirtualRecordBO(InteractDataBO param) {
        UscMdmVirtualRecordBO vrBOObj = new UscMdmVirtualRecordBO(new Page<>(param.getPageNumber(),
                param.getPageSize()));
        vrBOObj.setCustId(param.getCustId());
        vrBOObj.setConsumerMobile(param.getPhone());
        vrBOObj.setDateBegin(param.getBeginTime());
        vrBOObj.setDateEnd(TimeUtils.handleEnd(param.getEndTime()));
        return vrBOObj;
    }
}