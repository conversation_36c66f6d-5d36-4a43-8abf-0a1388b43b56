package com.smart.adp.domain.bo.koc;

import com.smart.adp.domain.entity.koc.SacUserTagRel;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: KOC用户打标BO
 * @Author: system
 * @Date: 2025/8/4
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KocUserTagBO extends SacUserTagRel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户基本信息
     */
    private KocUserInfoBO userInfo;

    /**
     * 标签信息
     */
    private KocTagBO tagInfo;

    /**
     * 达人类型信息
     */
    private KocExpertTypeBO expertTypeInfo;

    /**
     * 操作类型名称
     */
    private String relTypeName;

    /**
     * 批量操作的用户ID列表
     */
    private List<String> smartIds;

    /**
     * 批量操作的标签ID列表
     */
    private List<String> tagIds;

    /**
     * 批量操作的达人类型ID列表
     */
    private List<String> expertTypeIds;

    /**
     * 批量操作ID
     */
    private String batchId;

    /**
     * 构建用户标签关系BO
     */
    public static KocUserTagBO buildFromEntity(SacUserTagRel entity) {
        if (entity == null) {
            return null;
        }
        
        return KocUserTagBO.builder()
                .relId(entity.getRelId())
                .smartId(entity.getSmartId())
                .phone(entity.getPhone())
                .nickName(entity.getNickName())
                .relType(entity.getRelType())
                .refId(entity.getRefId())
                .remarkContent(entity.getRemarkContent())
                .creator(entity.getCreator())
                .createdName(entity.getCreatedName())
                .createdDate(entity.getCreatedDate())
                .modifier(entity.getModifier())
                .modifyName(entity.getModifyName())
                .lastUpdatedDate(entity.getLastUpdatedDate())
                .build();
    }

    /**
     * 获取关系类型名称
     */
    public String getRelTypeName() {
        if (relType == null) {
            return "";
        }
        switch (relType) {
            case 1:
                return "标签";
            case 2:
                return "达人类型";
            case 3:
                return "备注";
            default:
                return "未知类型";
        }
    }

    /**
     * 转换为实体对象
     */
    public SacUserTagRel toEntity() {
        return SacUserTagRel.builder()
                .relId(this.getRelId())
                .smartId(this.getSmartId())
                .phone(this.getPhone())
                .nickName(this.getNickName())
                .relType(this.getRelType())
                .refId(this.getRefId())
                .remarkContent(this.getRemarkContent())
                .creator(this.getCreator())
                .createdName(this.getCreatedName())
                .createdDate(this.getCreatedDate())
                .modifier(this.getModifier())
                .modifyName(this.getModifyName())
                .lastUpdatedDate(this.getLastUpdatedDate())
                .build();
    }
}
