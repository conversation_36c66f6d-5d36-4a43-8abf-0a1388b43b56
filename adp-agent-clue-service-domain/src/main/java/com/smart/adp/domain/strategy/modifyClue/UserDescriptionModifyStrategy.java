package com.smart.adp.domain.strategy.modifyClue;

import com.smart.adp.domain.bo.clue.ClueDlrModifyBO;
import com.smart.adp.domain.service.clue.ISacOneCustRemarkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 更新用户描述
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Service
@Slf4j
public class UserDescriptionModifyStrategy extends ClueModifyStrategySuper implements ClueModifyStrategy {

    @Autowired
    private ISacOneCustRemarkService sacOneCustRemarkService;

    @Override
    public Boolean modify(ClueDlrModifyBO param) {
        log.info("更新用户描述: {}", param.getValue());
        Boolean modifyResult;
        // 先判断数据是否存在
        if (!this.isExistRemark(param.getCustId())) {
            // 如果数据不存在，插入数据
            modifyResult = this.inserRemark(param.buildUserDescModifyParam());
        } else {
            modifyResult = sacOneCustRemarkService.modifyCustRemark(param.buildUserDescModifyParam());
        }
        return modifyResult;
    }
}