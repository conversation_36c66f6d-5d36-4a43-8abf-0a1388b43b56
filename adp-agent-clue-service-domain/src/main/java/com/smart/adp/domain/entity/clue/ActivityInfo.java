package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/5 20:48
 * @description 活动表
 **/
@Table(value = "t_acc_bu_activity", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class ActivityInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动创建类型编码
     */
    @Column("CREATE_TYPE_CODE")
    private String createTypeCode;

    /**
     * 活动名称
     */
    @Column("ACTIVITY_NAME")
    private String activityName;

    /**
     * 线索渠道编码
     */
    @Column("INFO_CHAN_D_CODE")
    private String infoChanDCode;

    /**
     * 线索渠道名称'
     */
    @Column("INFO_CHAN_D_NAME")
    private String infoChanDName;

    /**
     * 渠道描述
     */
    @Column("INFO_CHAN_D_DESC")
    private String infoChanDDesc;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * 活动id
     */
    @Column(value = "ACTIVITY_ID")
    private String activityId;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * endTime
     */
    @Column(value = "END_TIME")
    private LocalDateTime endTime;

    /**
     * endTime
     */
    @Column(value = "RELEASE_STATUS_CODE")
    private String releaseStatusCode;

    /**
     * statusCode
     */
    @Column(value = "STATUS_CODE")
    private String statusCode;

    /**
     * lastUpdatedDate
     */
    @Column(value = "LAST_UPDATED_DATE")
    private String lastUpdatedDate;
}
