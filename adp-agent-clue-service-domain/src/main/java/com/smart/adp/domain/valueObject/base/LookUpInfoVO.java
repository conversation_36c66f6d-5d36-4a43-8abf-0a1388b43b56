package com.smart.adp.domain.valueObject.base;

/**
 * <AUTHOR>
 * date 2025/3/5 16:20
 * @description
 **/
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 值列表
 * t_prc_mds_lookup_value
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "值列表 VO")
public class LookUpInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 值名称
     */
    @Schema(description = "值名称")
    private String lookupValueName;

    /**
     * 值类型编码
     */
    @Schema(description = "值类型编码")
    private String lookupTypeCode;

    /**
     * 值类型编码
     */
    @Schema(description = "值类型编码")
    private String lookupTypeName;

    /**
     * 值编码
     */
    @Schema(description = "值编码")
    private String lookupValueCode;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private String orderNo;

    /**
     * 专营店ID
     */
    @Schema(description = "专营店ID")
    private String dlrId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 是否可用
     */
    @Schema(description = "是否可用")
    private String isEnable;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    private LocalDateTime createdDate;
}
