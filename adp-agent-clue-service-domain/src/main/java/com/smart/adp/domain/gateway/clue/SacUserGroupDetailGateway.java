package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacUserGroupDetailBO;
import com.smart.adp.domain.entity.clue.SacUserGroupDetailEntity;

import java.util.List;

/**
 * @Description: 用户分组明细gateway接口
 * @Author: rik.ren
 * @Date: 2025/3/9 16:03
 **/
public interface SacUserGroupDetailGateway {
    /**
     * 根据实体条件查询集合
     *
     * @param param
     * @param needColumns
     * @return
     */
    List<SacUserGroupDetailEntity> findListByCondition(SacUserGroupDetailEntity param, QueryColumn... needColumns);

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns
     * @return
     */
    SacUserGroupDetailEntity findByCondition(SacUserGroupDetailEntity param, QueryColumn... needColumns);

    /**
     * 查询用户所在分组
     * @param param
     * @param needColumns
     * @return
     */
    List<SacUserGroupDetailEntity> findByCondition(SacUserGroupDetailBO param, QueryColumn... needColumns);

    /**
     * 更新线索对应的用户分组
     * @param param
     * @return
     */
    Boolean modifyUserGroupDetail(SacUserGroupDetailEntity param);
}
