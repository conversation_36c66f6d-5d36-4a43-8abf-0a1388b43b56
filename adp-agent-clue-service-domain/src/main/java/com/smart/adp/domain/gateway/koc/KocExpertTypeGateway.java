package com.smart.adp.domain.gateway.koc;

import com.smart.adp.domain.bo.koc.KocExpertTypeBO;
import com.smart.adp.domain.entity.koc.SacExpertType;

import java.util.List;

/**
 * @Description: KOC达人类型网关接口
 * @Author: system
 * @Date: 2025/8/4
 **/
public interface KocExpertTypeGateway {

    /**
     * 根据ID查询达人类型信息
     *
     * @param typeId 达人类型ID
     * @return 达人类型信息
     */
    SacExpertType findById(String typeId);

    /**
     * 根据条件查询达人类型列表
     *
     * @param condition 查询条件
     * @return 达人类型列表
     */
    List<SacExpertType> findByCondition(SacExpertType condition);

    /**
     * 查询所有达人类型
     *
     * @return 达人类型列表
     */
    List<SacExpertType> findAll();

    /**
     * 根据达人类型名称模糊查询
     *
     * @param typeName 达人类型名称
     * @return 达人类型列表
     */
    List<SacExpertType> findByNameLike(String typeName);

    /**
     * 根据达人类型内容查询
     *
     * @param typeContent 达人类型内容
     * @return 达人类型信息
     */
    SacExpertType findByContent(String typeContent);

    /**
     * 保存达人类型信息
     *
     * @param expertType 达人类型信息
     * @return 是否成功
     */
    Boolean save(SacExpertType expertType);

    /**
     * 更新达人类型信息
     *
     * @param expertType 达人类型信息
     * @return 是否成功
     */
    Boolean update(SacExpertType expertType);

    /**
     * 删除达人类型信息
     *
     * @param typeId 达人类型ID
     * @return 是否成功
     */
    Boolean deleteById(String typeId);

    /**
     * 批量删除达人类型
     *
     * @param typeIds 达人类型ID列表
     * @return 是否成功
     */
    Boolean batchDelete(List<String> typeIds);

    /**
     * 检查达人类型内容是否存在
     *
     * @param typeContent 达人类型内容
     * @param excludeTypeId 排除的达人类型ID（用于编辑时排除自己）
     * @return 是否存在
     */
    Boolean existsByContent(String typeContent, String excludeTypeId);

    /**
     * 检查达人类型名称是否存在
     *
     * @param typeName 达人类型名称
     * @param excludeTypeId 排除的达人类型ID（用于编辑时排除自己）
     * @return 是否存在
     */
    Boolean existsByName(String typeName, String excludeTypeId);

    /**
     * 统计达人类型使用次数
     *
     * @param typeId 达人类型ID
     * @return 使用次数
     */
    Integer countUsage(String typeId);

    /**
     * 批量统计达人类型使用次数
     *
     * @param typeIds 达人类型ID列表
     * @return 使用次数映射
     */
    List<KocExpertTypeBO> batchCountUsage(List<String> typeIds);
}
