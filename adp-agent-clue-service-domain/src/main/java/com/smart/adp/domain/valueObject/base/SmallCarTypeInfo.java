package com.smart.adp.domain.valueObject.base;

/**
 * <AUTHOR>
 * date 2025/3/5 16:20
 * @description
 **/

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 值列表
 * t_prc_mdm_small_car_type
 */
@Table(value = "t_prc_mdm_small_car_type", schema = "mp")
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SmallCarTypeInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车型小类编码
     */
    @Column("SMALL_CAR_TYPE_CODE")
    private String carTypeCode;

    /**
     * 车型小类中文名称
     */
    @Column("SMALL_CAR_TYPE_CN")
    private String carTypeCodeCN;

    /**
     * 车型小类编码描述解释
     */
    @Column("COLUMN10")
    private String carTypeCodeDesc;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE")
    private String isEnable;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

}
