package com.smart.adp.domain.service.clue;

import com.smart.adp.domain.bo.clue.SacOnecustResumeBO;
import com.smart.adp.domain.common.DomainPage;

import java.util.List;

/**
 * @Description: 线索回访service
 * @Author: rik.ren
 * @Date: 2025/3/13 17:36
 **/
public interface ISacOnecustResumeService {
    /**
     * 根据客户id查询跟进记录
     *
     * @param param
     * @return
     */
    DomainPage<SacOnecustResumeBO> queryOnecustResumeByCustId(SacOnecustResumeBO param);

    /**
     * 根据实体对象查询跟进记录
     *
     * @param entityParam
     * @return
     */
    List<SacOnecustResumeBO> queryOnecustResume(SacOnecustResumeBO entityParam);
}
