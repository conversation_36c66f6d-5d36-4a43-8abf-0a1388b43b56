package com.smart.adp.domain.valueObject.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/11 22:58
 * @description 店端线索创建前置校验接口
 **/
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "店端线索创建前置校验 VO")
public class ClueDlrCheckVO {

    @Schema(description = "校验 code")
    private String code;

    @Schema(description = "校验信息")
    private String msg;

    @Schema(description = "客户id")
    private String custId;

    @Schema(description = "smartId")
    private String smartId;

    @Schema(description = "意向级别编码")
    private String intenLevelCode;

    @Schema(description = "创建时间")
    private LocalDateTime createdDate;
}
