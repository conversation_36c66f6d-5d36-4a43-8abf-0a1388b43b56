package com.smart.adp.domain.strategy.modifyClue;

import com.smart.adp.domain.bo.clue.ClueDlrModifyBO;
import com.smart.adp.domain.service.clue.IOneCustInfoService;
import com.smart.adp.domain.service.clue.ISacOneCustRemarkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 更新竞品车型
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Component
@Slf4j
public class CompetitorModelsModifyStrategy extends ClueModifyStrategySuper implements ClueModifyStrategy {
    @Autowired
    private IOneCustInfoService oneCustInfoService;
    @Autowired
    private ISacOneCustRemarkService oneCustRemarkService;

    @Override
    public Boolean modify(ClueDlrModifyBO param) {
        log.info("更新竞品车型: {}", param.getValue());
        Boolean modifyOneCustResult = oneCustInfoService.modifyCompetitorModels(param.buildCompetitorModelsParam());
        Boolean modifyRemarkResult;
        // 先判断数据是否存在
        if (!this.isExistRemark(param.getCustId())) {
            // 如果数据不存在，插入数据
            modifyRemarkResult = this.inserRemark(param.buildRemarkCompetitorModelsParam());
        } else {
            modifyRemarkResult = oneCustRemarkService.modifyCustRemark(param.buildRemarkCompetitorModelsParam());
        }
        return modifyOneCustResult && modifyRemarkResult;
    }
}