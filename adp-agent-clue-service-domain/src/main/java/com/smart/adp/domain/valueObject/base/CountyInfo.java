package com.smart.adp.domain.valueObject.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/9 13:26
 * @description 县区信息对象
 **/
@Table(value = "t_usc_mdm_org_community", schema = "mp")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class CountyInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column("COUNTY_ID")
    private String countyId;

    @Column("CITY_ID")
    private String cityId;

    @Column("COUNTY_CODE")
    private String countyCode;

    @Column("COUNTY_NAME")
    private String countyName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;
}
