package com.smart.adp.domain.qry;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.CollectionUtil;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.context.TimeContext;
import com.smart.adp.domain.enums.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mybatisflex.core.query.QueryMethods.exists;
import static com.mybatisflex.core.query.QueryMethods.selectOne;
import static com.smart.adp.domain.entity.clue.table.SacAllClueInfoDlrTableDef.SAC_ALL_CLUE_INFO_DLR;
import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;
import static com.smart.adp.domain.entity.clue.table.SacOneCustRemarkTableDef.SAC_ONE_CUST_REMARK;
import static com.smart.adp.domain.entity.clue.table.SacReviewTableDef.SAC_REVIEW;
import static com.smart.adp.domain.entity.order.table.VeBuSaleOrderToCTableDef.VE_BU_SALE_ORDER_TO_C;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/3
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
public class ClueDlrQry extends PageQry {

    /**
     * 查询类型
     */
    private ClueQueryTypeEnum qryType;

    /**
     * 线索状态
     */
    private String statusCode;

    /**
     * 线索等级 - 营销助手维护
     */
    private String level;

    /**
     * 跟进状态
     */
    private ReviewStatusEnum reviewStatus;

    /**
     * 搜索内容
     */
    private String searchStr;

    /**
     * 阶段集合
     */
    private List<ClueStageEnum> stages;

    /**
     * 活跃时间
     */
    private ClueActiveEnum active;

    /**
     * 意向车型
     */
    private String intentionCarType;

    /**
     * 地区编码列表
     */
    private List<String> areaCodeList;

    /**
     * 二级渠道编码
     */
    private String channelMCode;

    /**
     * 热度
     */
    private String heat;

    /**
     * 三级渠道编码列表
     */
    private List<String> channelCodeList;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 跟进时间开始
     */
    private LocalDateTime reviewTimeStart;

    /**
     * 跟进时间结束
     */
    private LocalDateTime reviewTimeEnd;

    /**
     * 逾期时间开始
     */
    private LocalDateTime overdueTimeStart;

    /**
     * 逾期时间结束
     */
    private LocalDateTime overdueTimeEnd;

    /**
     * 分配时间开始
     */
    private LocalDateTime allocateTimeStart;

    /**
     * 分配时间结束
     */
    private LocalDateTime allocateTimeEnd;

    /**
     * 战败时间开始
     */
    private LocalDateTime defeatTimeStart;

    /**
     * 战败时间结束
     */
    private LocalDateTime defeatTimeEnd;

    /**
     * 订单状态列表
     */
    private List<String> orderStatusList;

    /**
     * 排序方式
     */
    @JSONField(serialize = false, deserialize = false)
    private ClueSortEnum sort;

    /**
     * 门店编码
     */
    private String dlrCode;

    /**
     * 回访人员 ID 列表
     */
    private List<String> reviewPersonIds;

    /**
     * 线索 ID 列表
     */
    private Set<String> clueIds;

    public List<String> getStages() {
        if (CollectionUtil.isEmpty(stages)) {
            return Collections.emptyList();
        }
        return stages.stream()
                     .map(ClueStageEnum::getCodeStr)
                     .collect(Collectors.toList());
    }

    public ClueSortEnum getSort() {
        if (Objects.isNull(sort)) {
            return ClueSortEnum.OVERDUE_DESC;
        }

        return sort;
    }

    public void setReviewPersonId(String reviewPersonId) {
        if (StringUtil.noText(reviewPersonId)) {
            setReviewPersonIds(Collections.emptyList());
        }

        setReviewPersonIds(CollectionUtil.newArrayList(reviewPersonId));
    }

    /**
     * 查询表
     */
    public ClueDlrQry from(QueryWrapper wrapper) {
        wrapper.from(getQryType().getTable().as("c"), SAC_REVIEW.as("r"))
               .leftJoin(SAC_ONE_CUST_REMARK.as("cr")).on(SAC_REVIEW.CUST_ID.eq(SAC_ONE_CUST_REMARK.CUST_ID));

        return this;
    }

    /**
     * 战败查询表
     */
    public ClueDlrQry defeatFrom(QueryWrapper wrapper) {
        wrapper.from(getQryType().getTable().as("c")).hint("NO_INDEX(c idx_LAST_REVIEW_TIME_CREATED_DATE)")
               .leftJoin(SAC_ONE_CUST_REMARK.as("cr")).on(SAC_ALL_CLUE_INFO_DLR.CUST_ID.eq(SAC_ONE_CUST_REMARK.CUST_ID));

        return this;
    }

    /**
     * 查询条件
     */
    public ClueDlrQry conditions(QueryWrapper wrapper) {
        wrapper.and(SAC_REVIEW.REVIEW_ID.eq(SAC_CLUE_INFO_DLR.REVIEW_ID))
               .and(SAC_CLUE_INFO_DLR.STATUS_CODE.eq(getStatusCode(), StringUtil::hasText))
               .and(SAC_REVIEW.ORG_CODE.eq(getDlrCode(), StringUtil::hasText))
               .and(getQryType().getConditionSupplier().get())
               .and(SAC_CLUE_INFO_DLR.COLUMN18.in(getStages(), CollectionUtil::isNotEmpty))
               .and(SAC_REVIEW.REVIEW_PERSON_ID.in(getReviewPersonIds(), CollectionUtil::isNotEmpty))
               .and(SAC_REVIEW.INTEN_CAR_TYPE_NAME.eq(getIntentionCarType(), StringUtil::hasText))
               .and(SAC_ONE_CUST_REMARK.LOCATION.in(getAreaCodeList(), CollectionUtil::isNotEmpty))
               .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(getLevel(), StringUtil::hasText))
               .and(SAC_CLUE_INFO_DLR.INFO_CHAN_MCODE.eq(getChannelMCode(), StringUtil::hasText))
               .and(SAC_CLUE_INFO_DLR.INFO_CHAN_DCODE.in(getChannelCodeList(), CollectionUtil::isNotEmpty))
               .and(SAC_CLUE_INFO_DLR.COLUMN6.eq(getHeat(), StringUtil::hasText))
               .and(SAC_REVIEW.CREATED_DATE.ge(getCreateTimeStart(), Objects::nonNull))
               .and(SAC_REVIEW.CREATED_DATE.le(getCreateTimeEnd(), Objects::nonNull))
               .and(SAC_REVIEW.LAST_REVIEW_TIME.ge(getReviewTimeStart(), Objects::nonNull))
               .and(SAC_REVIEW.LAST_REVIEW_TIME.le(getReviewTimeEnd(), Objects::nonNull))
               .and(SAC_REVIEW.PLAN_REVIEW_TIME.ge(getOverdueTimeStart(), Objects::nonNull))
               .and(SAC_REVIEW.PLAN_REVIEW_TIME.le(getOverdueTimeEnd(), Objects::nonNull))
               .and(SAC_CLUE_INFO_DLR.ALLOCATE_TIME.ge(getAllocateTimeStart(), Objects::nonNull))
               .and(SAC_CLUE_INFO_DLR.ALLOCATE_TIME.le(getAllocateTimeEnd(), Objects::nonNull))
               .and(SAC_CLUE_INFO_DLR.ID.in(getClueIds(), CollectionUtil::isNotEmpty))
               // dcc
               .and(SAC_REVIEW.COLUMN19.isNull());
        reviewStatusCondition(wrapper);
        orderCondition(wrapper);
        searchCondition(wrapper);
        activeCondition(wrapper);

        return this;
    }

    /**
     * 活跃度筛选
     */
    private void activeCondition(QueryWrapper wrapper) {
        ClueActiveEnum e = getActive();
        if (Objects.isNull(e)) {
            return;
        }

        LocalDateTime todayStart = TimeContext.now()
                                              .toLocalDate()
                                              .atStartOfDay();
        switch (e) {
            case ACTIVE_IN_TODAY:
                wrapper.and(SAC_ONE_CUST_REMARK.LAST_ACTIVE_TIME.ge(todayStart));
                break;
            case ACTIVE_IN_ONE_DAY:
                wrapper.and(SAC_ONE_CUST_REMARK.LAST_ACTIVE_TIME.ge(todayStart.minusDays(1)));
                break;
            case ACTIVE_IN_THREE_DAYS:
                wrapper.and(SAC_ONE_CUST_REMARK.LAST_ACTIVE_TIME.ge(todayStart.minusDays(3)));
                break;
            case ACTIVE_IN_SEVEN_DAYS:
                wrapper.and(SAC_ONE_CUST_REMARK.LAST_ACTIVE_TIME.ge(todayStart.minusDays(7)));
                break;
            case ACTIVE_IN_FIFTEEN_DAYS:
                wrapper.and(SAC_ONE_CUST_REMARK.LAST_ACTIVE_TIME.ge(todayStart.minusDays(15)));
                break;
            case ACTIVE_IN_THIRTY_DAYS:
                wrapper.and(SAC_ONE_CUST_REMARK.LAST_ACTIVE_TIME.ge(todayStart.minusDays(30)));
                break;
            default:
        }
    }

    /**
     * 战败查询条件
     */
    public ClueDlrQry defeatConditions(QueryWrapper wrapper) {
        wrapper.and(SAC_ALL_CLUE_INFO_DLR.STATUS_CODE.eq(getStatusCode(), StringUtil::hasText))
               .and(SAC_ALL_CLUE_INFO_DLR.DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
               .and(getQryType().getConditionSupplier().get())
               .and(SAC_ALL_CLUE_INFO_DLR.COLUMN18.in(getStages(), CollectionUtil::isNotEmpty))
               .and(SAC_ALL_CLUE_INFO_DLR.REVIEW_PERSON_ID.in(getReviewPersonIds(), CollectionUtil::isNotEmpty))
               .and(SAC_ALL_CLUE_INFO_DLR.INTEN_CAR_TYPE_NAME.eq(getIntentionCarType(), StringUtil::hasText))
               .and(SAC_ONE_CUST_REMARK.LOCATION.in(getAreaCodeList(), CollectionUtil::isNotEmpty))
               .and(SAC_ONE_CUST_REMARK.CLUE_LEVEL.eq(getLevel(), StringUtil::hasText))
               .and(SAC_ALL_CLUE_INFO_DLR.INFO_CHAN_MCODE.eq(getChannelMCode(), StringUtil::hasText))
               .and(SAC_ALL_CLUE_INFO_DLR.INFO_CHAN_DCODE.in(getChannelCodeList(), CollectionUtil::isNotEmpty))
               .and(SAC_ALL_CLUE_INFO_DLR.COLUMN6.eq(getHeat(), StringUtil::hasText))
               .and(SAC_ALL_CLUE_INFO_DLR.CREATED_DATE.ge(getCreateTimeStart(), Objects::nonNull))
               .and(SAC_ALL_CLUE_INFO_DLR.CREATED_DATE.le(getCreateTimeEnd(), Objects::nonNull))
               .and(SAC_ALL_CLUE_INFO_DLR.LAST_REVIEW_TIME.ge(getReviewTimeStart(), Objects::nonNull))
               .and(SAC_ALL_CLUE_INFO_DLR.LAST_REVIEW_TIME.le(getReviewTimeEnd(), Objects::nonNull))
               .and(SAC_ALL_CLUE_INFO_DLR.ALLOCATE_TIME.ge(getAllocateTimeStart(), Objects::nonNull))
               .and(SAC_ALL_CLUE_INFO_DLR.ALLOCATE_TIME.le(getAllocateTimeEnd(), Objects::nonNull))
               .and(SAC_ALL_CLUE_INFO_DLR.LAST_REVIEW_TIME.ge(getDefeatTimeStart(), Objects::nonNull))
               .and(SAC_ALL_CLUE_INFO_DLR.LAST_REVIEW_TIME.le(getDefeatTimeEnd(), Objects::nonNull))
               .and(SAC_ALL_CLUE_INFO_DLR.ID.in(getClueIds(), CollectionUtil::isNotEmpty))
               // dcc
               .and(SAC_ALL_CLUE_INFO_DLR.COLUMN19.isNull());
        orderCondition(wrapper);
        defeatSearchCondition(wrapper);
        activeCondition(wrapper);

        return this;
    }

    /**
     * 订单 条件
     */
    private void orderCondition(QueryWrapper wrapper) {
        if (CollectionUtil.isEmpty(getOrderStatusList())) {
            return;
        }

        wrapper.and(exists(selectOne().from(VE_BU_SALE_ORDER_TO_C)
                                      .and(VE_BU_SALE_ORDER_TO_C.BUY_CUST_ID.eq(SAC_CLUE_INFO_DLR.CUST_ID))
                                      .and(VE_BU_SALE_ORDER_TO_C.SALE_ORDER_STATE.in(getOrderStatusList()))));
    }

    /**
     * 搜索 条件
     */
    private void searchCondition(QueryWrapper wrapper) {
        String str = this.getSearchStr();
        if (StringUtil.hasText(str)) {
            if (StringUtils.isNumeric(str)) {
                wrapper.and(SAC_REVIEW.PHONE.like(str));
            } else {
                wrapper.and(SAC_REVIEW.CUST_NAME.like(str));
            }
        }
    }

    /**
     * 战败 搜索 条件
     */
    private void defeatSearchCondition(QueryWrapper wrapper) {
        String str = this.getSearchStr();
        if (StringUtil.hasText(str)) {
            if(StringUtils.isNumeric(str)){
                wrapper.and(SAC_ALL_CLUE_INFO_DLR.PHONE.like(str));
            } else {
                wrapper.and(SAC_ALL_CLUE_INFO_DLR.CUST_NAME.like(str));
            }
        }
    }

    /**
     * 回访状态 条件
     */
    private void reviewStatusCondition(QueryWrapper wrapper) {
        ReviewStatusEnum reviewStatusEnum = getReviewStatus();
        if (Objects.isNull(reviewStatusEnum)) {
            return;
        }

        LocalDateTime now = TimeContext.now();
        switch (reviewStatusEnum) {
            case WAIT_FOR_REVIEW:
                wrapper.and(SAC_REVIEW.PLAN_REVIEW_TIME.ge(now))
                       .and(SAC_REVIEW.PLAN_REVIEW_TIME.le(now.plusDays(1)));
                break;
            case NO_REVIEW:
                wrapper.and(SAC_REVIEW.LAST_REVIEW_TIME.isNull());
                break;
            case IN_REVIEW:
                wrapper.and(SAC_REVIEW.LAST_REVIEW_TIME.isNotNull())
                       .and(SAC_REVIEW.PLAN_REVIEW_TIME.ge(now));
                break;
            case OVERDUE:
                wrapper.and(SAC_REVIEW.PLAN_REVIEW_TIME.lt(now));
                break;
            default:
        }
    }

    /**
     * 是否包含订单信息
     *
     * @return boolean
     */
    public boolean aboutOrder() {
        return CollectionUtil.isNotEmpty(getOrderStatusList());
    }

    /**
     * 排序
     */
    public ClueDlrQry orderBy(QueryWrapper wrapper) {
        wrapper.orderBy(getSort().getOrderBy());

        return this;
    }

    /**
     * build count cache key
     *
     * @return key
     */
    public String countCacheKey() {
        return JSONObject.toJSONString(this);
    }
}
