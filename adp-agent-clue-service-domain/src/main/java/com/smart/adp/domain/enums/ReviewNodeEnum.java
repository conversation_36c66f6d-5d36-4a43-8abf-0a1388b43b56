package com.smart.adp.domain.enums;

/**
 * 回访节点
 * <AUTHOR>
 *
 */
public enum ReviewNodeEnum {
	/**
	 * 新建回访
	 */
	New("New","新建回访"),
	
	/**
	 * 继续跟进
	 */
	Save("Save","继续跟进"),
	
	/**
	 * 战败申请
	 */
	Defeat("Defeat","战败申请"),
	
	/**
	 * 失控申请
	 */
	Notctrl("Notctrl","失控申请"),
	
	/**
	 * 无效申请
	 */
	Invalid("Invalid","无效申请"),
	
	/**
	 * 战败申请驳回
	 */
	Undefeat("Undefeat","战败申请驳回"),
	
	/**
	 * 失控申请驳回
	 */
	Unnotctrl("Unnotctrl","失控申请驳回"),
	
	/**
	 * 无效申请驳回
	 */
	Uninvalid("Uninvalid","无效申请驳回"),
	
	/**
	 * 战败
	 */
	Defeated("Defeated","战败"),
	
	/**
	 * 失控
	 */
	Notctrled("Notctrled","失控"),
	
	/**
	 * 无效
	 */
	Invalided("Invalided","无效"),
	
	/**
	 * 下发专营店
	 */
	Send("Send","下发专营店"),
	
	/**
	 * 预约到店
	 */
	Come("Come","预约到店"),
	
	/**
	 * 结束
	 */
	End("End","结束");
	
	
	private String code;
	private String msg;
	
	private ReviewNodeEnum(String code, String msg) {
		this.code = code;
		this.msg = msg;
	}
	
	public String getCode() {
		return code;
	}
	
	public String getMsg() {
		return msg;
	}
	
	//根据result获取枚举
	public static ReviewNodeEnum fromString(String code) {
	    for (ReviewNodeEnum b : ReviewNodeEnum.values()) {
	        if (b.getCode().equalsIgnoreCase(code)) {
	            return b;
	        }
	    }
	    return null;
	}
}

