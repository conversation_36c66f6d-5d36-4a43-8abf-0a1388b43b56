package com.smart.adp.domain.bo.clue;

import cn.hutool.core.collection.CollUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.valueObject.clue.SacClueMsgRecordVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

import static com.smart.adp.domain.valueObject.clue.table.SacClueMsgRecordVOTableDef.SAC_CLUE_MSG_RECORD_VO;

/**
 * @Description: 线索消息BO
 * @Author: rik.ren
 * @Date: 2025/4/15 15:39
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SacClueMsgRecordBO extends SacClueMsgRecordVO {
    /**
     * 分页对象
     */
    private DomainPage<SacClueMsgRecordVO> page;
    /**
     * 开始时间
     */
    private LocalDateTime createdDateBegin;
    /**
     * 结束时间
     */
    private LocalDateTime createdDateEnd;
    /**
     * 消息类型集合
     */
    private List<String> listMessageType;
    /**
     * 门店集合
     */
    private List<String> listDlrCode;
    /**
     * 消息id集合
     */
    private List<String> listMessageId;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacClueMsgRecordVO conditions(QueryWrapper wrapper) {
        wrapper.and(SAC_CLUE_MSG_RECORD_VO.RECEIVE_EMP_ID.eq(getReceiveEmpId(), StringUtil::hasText))
                .and(SAC_CLUE_MSG_RECORD_VO.DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
                .and(SAC_CLUE_MSG_RECORD_VO.IS_READ.eq(getIsRead(), StringUtil::hasText))
                .and(SAC_CLUE_MSG_RECORD_VO.PHONE.eq(getPhone(), StringUtil::hasText))
                .and(SAC_CLUE_MSG_RECORD_VO.MESSAGE_TYPE.eq(getMessageType(), StringUtil::hasText))
                .and(SAC_CLUE_MSG_RECORD_VO.BUSI_KEYVALUE.eq(getBusiKeyvalue(), StringUtil::hasText))
                .and(SAC_CLUE_MSG_RECORD_VO.MESSAGE_ID.eq(getMessageId(), StringUtil::hasText))
                .and(SAC_CLUE_MSG_RECORD_VO.MESSAGE_TYPE.in(listMessageType, CollUtil::isNotEmpty))
                .and(SAC_CLUE_MSG_RECORD_VO.DLR_CODE.in(listDlrCode, CollUtil::isNotEmpty));
        return this;
    }
}
