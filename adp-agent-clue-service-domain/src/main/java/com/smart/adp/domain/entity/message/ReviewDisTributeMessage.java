package com.smart.adp.domain.entity.message;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * date 2025/4/14 17:27
 * @description 线索分配DTO
 **/
@Data
public class ReviewDisTributeMessage implements Serializable {

    private String reviewId;

    private String dlrCode;

    private String dlrShortName;

    private Date allocateTime;

    private LocalDateTime assignTime;

    private String reviewPersonId;

    private String reviewPersonName;

    private String reviewPersonPhone;

    private String scenario;

    private String messageType;

    private Boolean isDefeatedDis;

    /**
     * 分配人，店长
     */
    private String assignPersonId;

    /**
     * 分配人员工姓名
     */
    private String assignPersonName;
}
