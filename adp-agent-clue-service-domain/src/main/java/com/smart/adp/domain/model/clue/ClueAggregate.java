package com.smart.adp.domain.model.clue;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacClueInfoDlrBO;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.enums.ClueModifyTypeEnum;
import com.smart.adp.domain.enums.DefeatFlagEnum;
import com.smart.adp.domain.service.clue.IClueDlrService;
import com.smart.adp.domain.strategy.modifyClue.ClueModifyStrategy;
import com.smart.adp.domain.strategy.modifyClue.ClueModifyStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;

/**
 * @Description: 线索聚合根
 * @Author: rik.ren
 * @Date: 2025/5/30 10:20
 **/
@Slf4j
@Service
public class ClueAggregate {

    @Autowired
    private IClueDlrService clueDlrService;

    @Autowired
    private ClueModifyStrategyFactory strategyFactory;

    /**
     * 开始试驾时，更新意向车型和最近一次试驾时间
     *
     * @param param
     * @return
     */
    public String modifyIntentionCarForStartDriving(SacClueInfoDlrBO param) {
        SacClueInfoDlrBO queryClueBo = new SacClueInfoDlrBO();
        queryClueBo.setDefeatFlag(DefeatFlagEnum.DEFEAT.getCode());
        queryClueBo.setCustId(param.getCustId());
        QueryColumn[] needColumn = {SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.INTEN_CAR_TYPE_CODE,
                SAC_CLUE_INFO_DLR.LAST_TEST_DRIVER_TIME};
        SacClueInfoDlr queryClueResult = clueDlrService.getEntity(queryClueBo, needColumn);
        if (ObjectUtil.isEmpty(queryClueResult)) {
            return null;
        }
        // 如果现在的意向车型不包含新的意向车型，就加进去
        String newIntenCarTypeCode = null;
        if (StringUtils.isNotBlank(queryClueResult.getIntenCarTypeCode()) && !queryClueResult.getIntenCarTypeCode().contains(param.getNewIntentionCar())) {
            newIntenCarTypeCode = String.join(",", queryClueResult.getIntenCarTypeCode(),
                    param.getNewIntentionCar());
        } else {
            newIntenCarTypeCode = param.getNewIntentionCar();
        }
        queryClueResult.setLastTestDriverTime(param.getLastTestDriverTime());
        ClueModifyStrategy strategy = strategyFactory.getStrategy(ClueModifyTypeEnum.StartDriving.getCode());
        strategy.modify(param.buildStartDrivingModifyBO(newIntenCarTypeCode));
        return newIntenCarTypeCode;
    }
}
