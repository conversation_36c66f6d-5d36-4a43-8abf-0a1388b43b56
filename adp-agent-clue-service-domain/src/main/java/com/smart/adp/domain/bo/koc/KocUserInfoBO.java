package com.smart.adp.domain.bo.koc;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: KOC用户信息BO
 * @Author: system
 * @Date: 2025/8/4
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KocUserInfoBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户smartId
     */
    private String smartId;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 用户性别
     */
    private String gender;

    /**
     * 用户年龄
     */
    private Integer age;

    /**
     * 用户城市
     */
    private String city;

    /**
     * 用户省份
     */
    private String province;

    /**
     * 用户注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 用户最后活跃时间
     */
    private LocalDateTime lastActiveTime;

    /**
     * 用户标签列表
     */
    private List<KocTagBO> tags;

    /**
     * 用户达人类型列表
     */
    private List<KocExpertTypeBO> expertTypes;

    /**
     * 用户备注列表
     */
    private List<String> remarks;

    /**
     * 标签数量
     */
    private Integer tagCount;

    /**
     * 达人类型数量
     */
    private Integer expertTypeCount;

    /**
     * 备注数量
     */
    private Integer remarkCount;

    /**
     * 是否为VIP用户
     */
    private Boolean isVip;

    /**
     * 用户等级
     */
    private String userLevel;

    /**
     * 用户积分
     */
    private Integer points;
}
