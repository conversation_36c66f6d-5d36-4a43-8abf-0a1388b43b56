package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.clue.SacClueInfoDlrBO;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.common.constants.StringConstant;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.entity.clue.CustResumeESO;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueTag;
import com.smart.adp.domain.enums.ClueQueryTypeEnum;
import com.smart.adp.domain.enums.ClueSearchTypeEnum;
import com.smart.adp.domain.gateway.clue.ClueDlrGateway;
import com.smart.adp.domain.gateway.clue.ClueTagGateway;
import com.smart.adp.domain.qry.ClueDlrQry;
import com.smart.adp.domain.qry.ClueSearchQry;
import com.smart.adp.domain.service.clue.IClueDlrService;
import com.smart.adp.domain.utils.ClueActiveInfoVOUtils;
import com.smart.adp.domain.valueObject.clue.ClueActiveInfoVO;
import com.smart.adp.domain.valueObject.clue.ClueDlrListVO;
import com.smart.adp.domain.valueObject.clue.ClueDlrSearchVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

import static com.smart.adp.domain.common.constants.BizConstants.*;
import static com.smart.adp.domain.entity.clue.table.SacClueTagTableDef.SAC_CLUE_TAG;

/**
 * @Description: 线索实现类
 * @Author: rik.ren
 * @Date: 2025/3/7 13:27
 **/
@Slf4j
@Service
public class ClueDlrServiceImpl implements IClueDlrService {

    @Autowired
    private ClueDlrGateway clueDlrGateway;

    @Autowired
    private ClueTagGateway tagGateway;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RestHighLevelClient esClient;

    /**
     * 获取潜客详情对象
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public SacClueInfoDlr getEntity(SacClueInfoDlr param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        return clueDlrGateway.findByCondition(param, needColumn);
    }

    /**
     * 获取线索信息
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public SacClueInfoDlr getEntity(SacClueInfoDlrBO param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        return clueDlrGateway.findByCondition(param, needColumn);
    }

    /**
     * 批量获取潜客详情集合
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public List<SacClueInfoDlr> queryEntity(SacClueInfoDlr param, QueryColumn... needColumn) {
        return null;
    }

    /**
     * 更新线索表的用户等级
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyClueUserLevel(SacClueInfoDlr param) {
        return clueDlrGateway.modifyClueInfo(param);
    }

    /**
     * 更新线索表的用户意向车型
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyClueIntendedVehicleModel(SacClueInfoDlr param) {
        // 更新数据库
        Boolean modifyResult = clueDlrGateway.modifyClueInfo(param);
        // todo 更新缓存

        return modifyResult;
    }

    /**
     * 更新代理商线索来源
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyClueSourceOfAgent(SacClueInfoDlr param) {
        // 更新数据库
        Boolean modifyResult = clueDlrGateway.modifyClueInfo(param);
        // todo 更新缓存

        return modifyResult;
    }

    /**
     * 更新代理商线索来源
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyClueUserLocation(SacClueInfoDlr param) {
        // 更新数据库
        Boolean modifyResult = clueDlrGateway.modifyClueInfo(param);
        // todo 更新缓存

        return modifyResult;
    }

    /**
     * 更新特别关注
     * 如果adp_leads库的线索表没有，那么会更新csc库的线索表
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifySpecial(SacClueInfoDlr param) {
        // 更新数据库
        return clueDlrGateway.modifyClueInfo(param);
    }

    /**
     * 根据手机号查询线索的活跃信息
     *
     * @param phone
     * @return
     */
    @SneakyThrows
    @Override
    public ClueActiveInfoVO queryClueActive(String phone) {
        // 1. 根据手机号查询活跃信息
        List<ClueActiveInfoVO> listClueActiveInfoVO = clueDlrGateway.queryClueActiveInfo(Arrays.asList(phone));
        if (CollectionUtil.isEmpty(listClueActiveInfoVO)) {
            return null;
        }
        ClueActiveInfoVO result = listClueActiveInfoVO.get(0);
        // 2. 根据1点到24点，计算出最活跃的时间段
        String activeTimePeriod = ClueActiveInfoVOUtils.getMostActiveTimeRange(result);
        result.setActiveTimePeriod(activeTimePeriod);
        return result;
    }

    /**
     * 根据手机号查询线索的活跃信息
     *
     * @param listPhone
     * @return
     */
    @Override
    public List<ClueActiveInfoVO> queryClueActive(List<String> listPhone) {
        List<ClueActiveInfoVO> result = clueDlrGateway.queryClueActiveInfo(listPhone);
        if (CollectionUtil.isEmpty(result)) {
            return Collections.emptyList();
        }
        return result;
    }

    @Override
    public Page<ClueDlrListVO> page(ClueDlrQry qry) {
        long count = clueDlrGateway.countByQry(qry);
        Page<ClueDlrListVO> page = Page.of(qry.getPageIndex(), qry.getPageSize(), count);

        if (count > 0L) {
            if (qry.getQryType().includeDefeat()) {
                clueDlrGateway.defeatPage(qry, page);
                page.getRecords().forEach(ClueDlrListVO::handleDefeatStage);
            } else {
                clueDlrGateway.page(qry, page);
                page.getRecords().forEach(ClueDlrListVO::calcRemainTime);
            }
            // post process
            fillTag(page.getRecords());
            page.getRecords().forEach(ClueDlrListVO::fill);
        }

        return page;
    }

    @Override
    public List<ClueDlrListVO> list(ClueDlrQry qry) {
        if (Objects.isNull(qry)) {
            return Collections.emptyList();
        }

        List<ClueDlrListVO> list;
        if (qry.getQryType().includeDefeat()) {
            list = clueDlrGateway.defeatList(qry);
            list.forEach(ClueDlrListVO::handleDefeatStage);
        } else {
            list = clueDlrGateway.list(qry);
            list.forEach(ClueDlrListVO::calcRemainTime);
        }
        // post process
        fillTag(list);
        list.forEach(ClueDlrListVO::fill);
        return list;
    }

    /**
     * 填充线索标签
     *
     * @param records data
     */
    private void fillTag(List<ClueDlrListVO> records) {
        if (Objects.isNull(records) || records.isEmpty()) {
            return;
        }
        List<String> ids = records.stream()
                                  .map(ClueDlrListVO::getId)
                                  .collect(Collectors.toList());

        Map<String, Set<String>> redisTagMap = getRedisTag(ids);
        QueryWrapper wrapper = QueryWrapper.create()
                                           .select(SAC_CLUE_TAG.CLUE_ID, SAC_CLUE_TAG.TAG_CODE)
                                           .and(SAC_CLUE_TAG.CLUE_ID.in(ids));

        Map<String, List<SacClueTag>> tagMap = tagGateway.queryList(wrapper)
                                                         .stream()
                                                         .collect(Collectors.groupingBy(SacClueTag::getClueId));
        for (ClueDlrListVO vo : records) {
            String id = vo.getId();
            Set<String> redisTags = redisTagMap.getOrDefault(id, Collections.emptySet());
            Set<String> dbTags = tagMap.getOrDefault(id, Collections.emptyList())
                                       .stream()
                                       .map(SacClueTag::getTagCode)
                                       .collect(Collectors.toSet());
            dbTags.addAll(redisTags);
            Set<String> deleteTags = redisTags.stream()
                                              .filter(tag -> tag.startsWith(NEGATIVE_PREFIX))
                                              .map(tag -> tag.substring(1))
                                              .collect(Collectors.toSet());
            dbTags.removeAll(deleteTags);
            vo.setTagList(dbTags);
        }
    }

    @SuppressWarnings("unchecked")
    public Map<String, Set<String>> getRedisTag(List<String> ids) {
        List<byte[]> keyBytes = ids.stream()
                                   .map(clueTagRedisKey)
                                   .map(key -> redisTemplate.getStringSerializer().serialize(key))
                                   .collect(Collectors.toList());
        List<Object> pipelineRes = redisTemplate.executePipelined((RedisCallback<?>) connection -> {
            for (byte[] keyByte : keyBytes) {
                connection.sMembers(keyByte);
            }
            return null;
        });

        Map<String, Set<String>> map = new HashMap<>();
        for (int i = 0; i < ids.size(); i++) {
            map.put(ids.get(i), (Set<String>) pipelineRes.get(i));
        }
        return map;
    }

    public PageVO<ClueDlrSearchVO> search(ClueSearchQry qry) {
        List<ClueDlrSearchVO> res = new ArrayList<>();
        PageVO<ClueDlrSearchVO> pageVO = PageVO.of(res, qry.getPageIndex(), qry.getPageSize());

        try {
            SearchRequest request = qry.buildRequest();
            SearchResponse response = esClient.search(request, RequestOptions.DEFAULT);

            SearchHits hits = response.getHits();
            Assert.notNull(hits, "ES hits is null.");

            pageVO.setTotalRow(Optional.of(hits)
                                       .map(SearchHits::getTotalHits)
                                       .map(v -> v.value)
                                       .orElse(0L));

            for (SearchHit hit : hits.getHits()) {
                ClueDlrSearchVO clueVO = JSONObject.parseObject(hit.getSourceAsString(), ClueDlrSearchVO.class);
                clueVO.setDocId(hit.getId());
                clueVO.setScore(hit.getScore());
                clueVO.setSort(hit.getSortValues());
//                clueVO.setHighlightMap(hit.getHighlightFields());

                if (ClueSearchTypeEnum.REVIEW_RECORD.equals(qry.getQryType())) {
                    handleInnerHits(hit, clueVO);
                }

                res.add(clueVO);
            }
        } catch (Exception e) {
            log.error("{} search exception", StringConstant.CLUE_LOG_PREFIX, e);
            throw new BusinessException(RespCode.DATABASE_EXCEPTION.getCode(), "搜索异常");
        }

        return pageVO;
    }

    /**
     * handle inner hit
     *
     * @param hit    es hit
     * @param clueVO vo
     */
    private static void handleInnerHits(SearchHit hit, ClueDlrSearchVO clueVO) {
        SearchHit[] resumeHits = Optional.ofNullable(hit)
                                         .map(SearchHit::getInnerHits)
                                         .map(hits -> hits.get(CLUE_SEARCH_RESUME_INNER_HIT_NAME))
                                         .map(SearchHits::getHits)
                                         .orElse(null);
        if (Objects.isNull(resumeHits)) {
            clueVO.setCustResumeList(Collections.emptyList());
            return;
        }

        List<CustResumeESO> resumes = new ArrayList<>(resumeHits.length);
        for (SearchHit resumeHit : resumeHits) {
            CustResumeESO resumeESO = JSONObject.parseObject(resumeHit.getSourceAsString(), CustResumeESO.class);

            String highlightDesc = Optional.of(resumeHit)
                                           .map(SearchHit::getHighlightFields)
                                           .map(m -> m.get(ES_CLUE_RESUME_DESC_PATH))
                                           .map(HighlightField::fragments)
                                           .map(arr -> arr[0])
                                           .map(Text::string)
                                           .orElse(null);
            if (Objects.nonNull(highlightDesc)) {
                resumeESO.setContent(highlightDesc);
            }

            resumes.add(resumeESO);
        }
        clueVO.setCustResumeList(resumes);
    }

    @Override
    public List<ClueDlrListVO> list(Set<String> clueIds) {
        if (CollectionUtil.isEmpty(clueIds)) {
            return Collections.emptyList();
        }

        ClueDlrQry qry = new ClueDlrQry();
        qry.setQryType(ClueQueryTypeEnum.NOT_DEFEATED);
        qry.setClueIds(clueIds);
        List<ClueDlrListVO> list = clueDlrGateway.list(qry);

        for (ClueDlrListVO vo : list) {
            clueIds.remove(vo.getId());
        }

        // all
        if (!clueIds.isEmpty()) {
            qry.setQryType(ClueQueryTypeEnum.ALL);
            qry.setClueIds(clueIds);
            List<ClueDlrListVO> allList = clueDlrGateway.defeatList(qry);

            list.addAll(allList);
        }
        return list;
    }

}
