package com.smart.adp.domain.entity.task;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@Table(value = "t_sac_onetask_user", schema = "csc")
public class OnetaskUser {

    /**
     * ID
     */
    @Id("id")
    private Long id;

    /**
     * 任务 ID
     */
    @Column("task_id")
    private String taskId;

    /**
     * 任务人员 ID
     */
    @Column("task_person_id")
    private String taskPersonId;

    /**
     * 任务人员名称
     */
    @Column("task_person_name")
    private String taskPersonName;

    /**
     * 任务人员门店编码
     */
    @Column("task_person_dlr_code")
    private String taskPersonDlrCode;

    /**
     * 任务开始时间
     */
    @Column("buss_start_time")
    private LocalDateTime bussStartTime;

    /**
     * 任务结束时间
     */
    @Column("buss_end_time")
    private LocalDateTime bussEndTime;

    /**
     * 任务状态-人员维度
     */
    @Column("status")
    private Integer status;

    /**
     * 未完成任务数
     */
    @Column("todo_count")
    private Integer todoCount;

    /**
     * 创建时间
     */
    @Column("created_date")
    private LocalDateTime createdDate;

    /**
     * 更新时间
     */
    @Column("last_updated_date")
    private LocalDateTime lastUpdatedDate;

    /**
     * 删除时间
     */
    @Column("deleted_date")
    private LocalDateTime deletedDate;
}