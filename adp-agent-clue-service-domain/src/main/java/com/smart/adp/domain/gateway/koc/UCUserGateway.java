package com.smart.adp.domain.gateway.koc;

import com.smart.adp.domain.bo.koc.KocUserInfoBO;

import java.util.List;

/**
 * @Description: UC用户网关接口
 * @Author: system
 * @Date: 2025/8/4
 **/
public interface UCUserGateway {

    /**
     * 根据smartId查询用户信息
     *
     * @param smartId 用户smartId
     * @return 用户信息
     */
    KocUserInfoBO findBySmartId(String smartId);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone 手机号
     * @return 用户信息
     */
    KocUserInfoBO findByPhone(String phone);

    /**
     * 根据昵称模糊查询用户信息
     *
     * @param nickName 昵称
     * @return 用户信息列表
     */
    List<KocUserInfoBO> findByNickNameLike(String nickName);

    /**
     * 根据关键字搜索用户（支持手机号、昵称、smartId）
     *
     * @param keyword 关键字
     * @return 用户信息列表
     */
    List<KocUserInfoBO> searchUsers(String keyword);

    /**
     * 批量根据smartId查询用户信息
     *
     * @param smartIds smartId列表
     * @return 用户信息列表
     */
    List<KocUserInfoBO> findBySmartIds(List<String> smartIds);

    /**
     * 批量根据手机号查询用户信息
     *
     * @param phones 手机号列表
     * @return 用户信息列表
     */
    List<KocUserInfoBO> findByPhones(List<String> phones);

    /**
     * 检查用户是否存在
     *
     * @param smartId 用户smartId
     * @return 是否存在
     */
    Boolean existsBySmartId(String smartId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @return 是否存在
     */
    Boolean existsByPhone(String phone);

    /**
     * 根据城市查询用户
     *
     * @param city 城市
     * @return 用户信息列表
     */
    List<KocUserInfoBO> findByCity(String city);

    /**
     * 根据省份查询用户
     *
     * @param province 省份
     * @return 用户信息列表
     */
    List<KocUserInfoBO> findByProvince(String province);

    /**
     * 根据用户等级查询用户
     *
     * @param userLevel 用户等级
     * @return 用户信息列表
     */
    List<KocUserInfoBO> findByUserLevel(String userLevel);

    /**
     * 查询VIP用户
     *
     * @return VIP用户列表
     */
    List<KocUserInfoBO> findVipUsers();
}
