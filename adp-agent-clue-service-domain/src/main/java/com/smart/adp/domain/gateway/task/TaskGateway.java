package com.smart.adp.domain.gateway.task;

import com.mybatisflex.core.paginate.Page;
import com.smart.adp.domain.entity.task.OnetaskDetail;
import com.smart.adp.domain.entity.task.OnetaskInfo;
import com.smart.adp.domain.qry.TaskQry;
import com.smart.adp.domain.valueObject.task.TaskVO;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/5
 */
public interface TaskGateway {

    Page<TaskVO> page(TaskQry qry);

    List<OnetaskInfo> getInfosByTaskIds(List<String> taskIds);

    List<OnetaskDetail> getDetailsByTaskIds(List<String> taskIds);

    boolean existByQry(TaskQry qry);

    long countByQry(TaskQry qry);

    TaskVO detail(Long id);

    OnetaskInfo getInfoByTaskId(String taskId);
}
