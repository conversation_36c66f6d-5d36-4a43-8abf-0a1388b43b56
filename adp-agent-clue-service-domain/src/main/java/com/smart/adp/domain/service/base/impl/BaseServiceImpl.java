package com.smart.adp.domain.service.base.impl;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;
import com.smart.adp.domain.service.base.IBaseService;
import com.smart.adp.domain.service.clue.ISacUserGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 基础服务实现
 * @Author: rik.ren
 * @Date: 2025/3/9 17:39
 **/
@Service
public class BaseServiceImpl implements IBaseService {
    @Autowired
    private ISacUserGroupService sacUserGroupService;

    /**
     * 根据创建人获取他的分组
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public List<SacUserGroupEntity> queryUserGroup(SacUserGroupEntity param, QueryColumn... needColumn) {
        return sacUserGroupService.queryEntity(param, needColumn);
    }
}
