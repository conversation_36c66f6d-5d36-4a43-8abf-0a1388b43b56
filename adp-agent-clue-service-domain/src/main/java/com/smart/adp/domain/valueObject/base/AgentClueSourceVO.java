package com.smart.adp.domain.valueObject.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * date 2025/3/12 14:26
 * @description 代理商线索来源 VO
 **/
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "代理商线索来源 VO")
public class AgentClueSourceVO {

    /**
     * 线索来源名称
     */
    @Schema(description = "线索来源名称 如:展厅进店")
    private String clueSourceName;

    /**
     * 线索来源类型
     */
    @Schema(description = "线索来源类型 如1-表示展厅进店")
    private String clueSourceType;
}
