package com.smart.adp.domain.strategy.modifyClue;

import com.smart.adp.domain.bo.clue.ClueDlrModifyBO;
import com.smart.adp.domain.service.clue.ISacOneCustRemarkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 更新用户位置
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Component
@Slf4j
public class LocationModifyStrategy extends ClueModifyStrategySuper implements ClueModifyStrategy {

    @Autowired
    private ISacOneCustRemarkService oneCustRemarkService;

    @Override
    public Boolean modify(ClueDlrModifyBO param) {
        log.info("更新用户位置: {}", param.getValue());
        Boolean modifyResult;
        // 先判断数据是否存在
        if (!this.isExistRemark(param.getCustId())) {
            // 如果数据不存在，插入数据
            modifyResult = this.inserRemark(param.buildUserLocationParam());
        } else {
            modifyResult = oneCustRemarkService.modifyCustRemark(param.buildUserLocationParam());
        }
        // 更新缓存
        return modifyResult;
    }
}