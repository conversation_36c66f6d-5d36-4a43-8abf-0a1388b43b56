package com.smart.adp.domain.enums;

import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryOrderBy;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nonnull;
import java.util.Arrays;
import java.util.Objects;

import static com.smart.adp.domain.entity.clue.table.SacReviewTableDef.SAC_REVIEW;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/10
 */
@Getter
@AllArgsConstructor
public enum ClueSortEnum {

    /**
     * 逾期时间倒序
     */
    OVERDUE_DESC(1, new QueryOrderBy(SAC_REVIEW.PLAN_REVIEW_TIME, SqlConsts.DESC)),

    /**
     * 逾期时间正序
     */
    OVERDUE_ASC(2, new QueryOrderBy(SAC_REVIEW.PLAN_REVIEW_TIME)),

    /**
     * 创建时间倒序
     */
    CREATE_DESC(3, new QueryOrderBy(SAC_REVIEW.CREATED_DATE, SqlConsts.DESC)),

    /**
     * 创建时间正序
     */
    CREATE_ASC(4, new QueryOrderBy(SAC_REVIEW.CREATED_DATE)),

    ;

    private final int code;
    private final QueryOrderBy orderBy;

    @Nonnull
    public static ClueSortEnum getByCode(Integer code) {
        return Arrays.stream(ClueSortEnum.values())
                     .filter(e -> Objects.equals(e.getCode(), code))
                     .findAny()
                     .orElse(ClueSortEnum.OVERDUE_DESC);
    }
}
