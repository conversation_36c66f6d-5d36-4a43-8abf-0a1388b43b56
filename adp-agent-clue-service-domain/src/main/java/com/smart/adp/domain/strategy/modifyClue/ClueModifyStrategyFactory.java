package com.smart.adp.domain.strategy.modifyClue;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 根据 clueModifyType 获取对应的策略
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Component
public class ClueModifyStrategyFactory {

    private final Map<Integer, ClueModifyStrategy> strategyMap = new HashMap<>();

    @Autowired
    public ClueModifyStrategyFactory(Map<Integer, ClueModifyStrategy> clueModifyStrategyMap) {
        clueModifyStrategyMap.forEach((key, value) -> this.strategyMap.put(key, value));
    }

    /**
     * 从map中获取对应的执行者，map是在项目启动时加载进去的，方法在ClueModifyStrategyConfig
     * @param clueModifyType
     * @return
     */
    public ClueModifyStrategy getStrategy(Integer clueModifyType) {
        return strategyMap.get(clueModifyType);
    }
}