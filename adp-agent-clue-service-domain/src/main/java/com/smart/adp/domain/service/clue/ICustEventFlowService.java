package com.smart.adp.domain.service.clue;

import com.smart.adp.domain.bo.clue.CustEventFlowBO;

import java.util.List;

/**
 * @Description: 线索用户旅程service
 * @Author: rik.ren
 * @Date: 2025/3/17 1:29
 **/
public interface ICustEventFlowService {

    /**
     * 根据客户id查询旅程事件
     *
     * @param boParam
     * @return
     */
    List<CustEventFlowBO> queryEventByCondition(CustEventFlowBO boParam);

    /**
     * 根据客户id查询旅程集合，只查询每个custId的最新的一条数据
     *
     * @param boParam
     * @return
     */
    List<CustEventFlowBO> queryEventByCustIdLast(CustEventFlowBO boParam);
}
