package com.smart.adp.domain.qry;

import com.smart.adp.domain.enums.TaskQryTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/6
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
public class TaskQry extends PageQry {

    /**
     * 查询类型
     */
    private TaskQryTypeEnum qryType;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 截止时间开始
     */
    private LocalDateTime endTimeStart;

    /**
     * 截止时间结束
     */
    private LocalDateTime endTimeEnd;

    /**
     * 专家 ID 列表
     */
    private List<String> personIds;

    /**
     * 门店编码
     */
    private String dlrCode;
}
