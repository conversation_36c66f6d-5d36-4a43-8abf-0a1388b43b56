package com.smart.adp.domain.valueObject.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/3/12 15:00
 * @description 竞品车型配置信息
 **/
@Table(value = "competitive_car_models", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class CompetitiveCarModels implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Id("id")
    private Long id;

    /**
     * 竞品车型名称
     */
    @Column("car_model_name")
    private String carModelName;

    /**
     * 竞品车型类型
     */
    @Column("car_model_type")
    private String carModelType;

}
