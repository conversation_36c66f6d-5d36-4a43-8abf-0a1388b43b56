package com.smart.adp.domain.entity.clue;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 历史线索表
 * t_sac_clue_info_dlr_his
 */
@Table(value = "t_sac_clue_info_dlr_his", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class SacHistoryClueInfoDlr implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID <br>
     * 原工程 StringHelper.GetGUID()
     */
    @Id("ID")
    private String id;

    /**
     * 线索单号
     */
    @Column("SERVER_ORDER")
    private String serverOrder;

    /**
     * 总部线索单号
     */
    @Column("PV_SERVER_ORDER")
    private String pvServerOrder;

    /**
     * 客户ID
     */
    @Column("CUST_ID")
    private String custId;

    /**
     * 客户名称
     */
    @Column("CUST_NAME")
    private String custName;

    /**
     * 联系号码
     */
    @Column("PHONE")
    private String phone;

    /**
     * 备用联系号码
     */
    @Column("PHONE_BACKUP")
    private String phoneBackup;

    /**
     * 意向级别编码
     */
    @Column("INTEN_LEVEL_CODE")
    private String intenLevelCode;

    /**
     * 意向级别名称
     */
    @Column("INTEN_LEVEL_NAME")
    private String intenLevelName;

    /**
     * 意向品牌编码
     */
    @Column("INTEN_BRAND_CODE")
    private String intenBrandCode;

    /**
     * 意向品牌名称
     */
    @Column("INTEN_BRAND_NAME")
    private String intenBrandName;

    /**
     * 意向车系编码
     */
    @Column("INTEN_SERIES_CODE")
    private String intenSeriesCode;

    /**
     * 意向车系名称
     */
    @Column("INTEN_SERIES_NAME")
    private String intenSeriesName;

    /**
     * 意向车型编码
     */
    @Column("INTEN_CAR_TYPE_CODE")
    private String intenCarTypeCode;

    /**
     * 意向车型名称
     */
    @Column("INTEN_CAR_TYPE_NAME")
    private String intenCarTypeName;

    /**
     * 选装包编码
     */
    @Column("INTEN_OPTION_PACKAGE_CODE")
    private String intenOptionPackageCode;

    /**
     * 选装包名称
     */
    @Column("INTEN_OPTION_PACKAGE_NAME")
    private String intenOptionPackageName;

    /**
     * 内饰色编码
     */
    @Column("INNER_COLOR_CODE")
    private String innerColorCode;

    /**
     * 内饰色名称
     */
    @Column("INNER_COLOR_NAME")
    private String innerColorName;

    /**
     * 外观色编码
     */
    @Column("OUT_COLOR_CODE")
    private String outColorCode;

    /**
     * 外观色名称
     */
    @Column("OUT_COLOR_NAME")
    private String outColorName;

    /**
     * 经销商编码
     */
    @Column("DLR_CODE")
    private String dlrCode;

    /**
     * 经销商名称
     */
    @Column("DLR_SHORT_NAME")
    private String dlrShortName;

    /**
     * 来源系统编码
     */
    @Column("SOURCE_SYSTEMT_CODE")
    private String sourceSystemtCode;

    /**
     * 来源系统名称
     */
    @Column("SOURCE_SYSTEMT_NAME")
    private String sourceSystemtName;

    /**
     * 下发时间
     */
    @Column("RECEIVE_TIME")
    private LocalDateTime receiveTime;

    /**
     * 处理标签名称
     */
    @Column("CUS_SOURCE")
    private String cusSource;

    /**
     * 外部线索编码
     */
    @Column("SOURCE_SERVER_ORDER")
    private String sourceServerOrder;

    /**
     * 一级信息来源编码
     */
    @Column("INFO_CHAN_M_CODE")
    private String infoChanMCode;

    /**
     * 一级信息来源名称
     */
    @Column("INFO_CHAN_M_NAME")
    private String infoChanMName;

    /**
     * 二级信息来源编码
     */
    @Column("INFO_CHAN_D_CODE")
    private String infoChanDCode;

    /**
     * 二级信息来源名称
     */
    @Column("INFO_CHAN_D_NAME")
    private String infoChanDName;

    /**
     * 三级信息来源编码
     */
    @Column("INFO_CHAN_DD_CODE")
    private String infoChanDdCode;

    /**
     * 三级信息来源名称
     */
    @Column("INFO_CHAN_DD_NAME")
    private String infoChanDdName;

    /**
     * 最低一级的信息来源编码
     */
    @Column("CHANNEL_CODE")
    private String channelCode;

    /**
     * 最低一级的信息来源名称
     */
    @Column("CHANNEL_NAME")
    private String channelName;

    /**
     * 性别编码
     */
    @Column("GENDER_CODE")
    private String genderCode;

    /**
     * 性别名称
     */
    @Column("GENDER_NAME")
    private String genderName;

    /**
     * 状态编码
     */
    @Column("STATUS_CODE")
    private String statusCode;

    /**
     * 状态名称
     */
    @Column("STATUS_NAME")
    private String statusName;

    /**
     * 处理节点编码
     */
    @Column("DEAL_NODE_CODE")
    private String dealNodeCode;

    /**
     * 处理节点名称
     */
    @Column("DEAL_NODE_NAME")
    private String dealNodeName;

    /**
     * 回访记录ID
     */
    @Column("REVIEW_ID")
    private String reviewId;

    /**
     * 第一次回访时间
     */
    @Column("FIRST_REVIEW_TIME")
    private LocalDateTime firstReviewTime;

    /**
     * 最后一次回访时间
     */
    @Column("LAST_REVIEW_TIME")
    private LocalDateTime lastReviewTime;

    /**
     * 分配时间
     */
    @Column("ASSIGN_TIME")
    private LocalDateTime assignTime;

    /**
     * 回访人员名称
     */
    @Column("REVIEW_PERSON_NAME")
    private String reviewPersonName;

    /**
     * 回访人员用户ID
     */
    @Column("REVIEW_PERSON_ID")
    private String reviewPersonId;

    /**
     * 扩展字段1
     * 如："column1": "2周以内"
     * 业务含义字段 planBuyDateName
     */
    @Column("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     * 如："column2": "1"
     * 业务含义字段 planBuyDate
     */
    @Column("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     * 业务含义字段 testDriveDateName
     */
    @Column("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     * 业务含义字段 testDriveDate
     */
    @Column("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     * 如："column5": "Hot"
     * 业务含义字段 businessHeatName
     */
    @Column("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     * 如："column6": "Hot"
     * 业务含义字段 businessHeatCode
     */
    @Column("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     * 业务含义字段 carPurchaseBudget
     */
    @Column("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     * 分析线上数据存储
     * 业务含义字段 activityId
     */
    @Column("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     * 业务含义字段 clueScore
     */
    @Column("COLUMN9")
    private String column9;

    /**
     * 生产报文排查存储是smartId，
     * 如："column10":"1894638727512567808"
     * 业务含义字段 smartId
     */
    @Column("COLUMN10")
    private String column10;

    /**
     * 扩展字段11，表示特别关注，1：特别关注
     * 业务含义字段 isSpecial
     */
    @Column("COLUMN11")
    private String column11;

    /**
     * 扩展字段12
     */
    @Column("COLUMN12")
    private String column12;

    /**
     * 扩展字段13
     */
    @Column("COLUMN13")
    private String column13;

    /**
     * 扩展字段14
     */
    @Column("COLUMN14")
    private String column14;

    /**
     * 扩展字段15
     * 定期跟进含义类似
     */
    @Column("COLUMN15")
    private String column15;

    /**
     * 扩展字段16
     */
    @Column("COLUMN16")
    private String column16;

    /**
     * 扩展字段17
     */
    @Column("COLUMN17")
    private String column17;

    /**
     * 扩展字段18
     */
    @Column("COLUMN18")
    private String column18;

    /**
     * 扩展字段19
     */
    @Column("COLUMN19")
    private String column19;

    /**
     * 是否注销线索 1 注销
     */
    @Column("COLUMN20")
    private String column20;

    /**
     * 大字段1
     */
    @Column("BIG_COLUMN1")
    private String bigColumn1;

    /**
     * 大字段2
     */
    @Column("BIG_COLUMN2")
    private String bigColumn2;

    /**
     * 大字段3
     */
    @Column("BIG_COLUMN3")
    private String bigColumn3;

    /**
     * 大字段4
     */
    @Column("BIG_COLUMN4")
    private String bigColumn4;

    /**
     * 大字段5
     */
    @Column("BIG_COLUMN5")
    private String bigColumn5;

    /**
     * JSON扩展字段
     */
    @Column("EXTENDS_JSON")
    private String extendsJson;

    /**
     * 厂商标识ID
     */
    @Column("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column("GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用 - isLogicDelete
     */
    @Column(value = "IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制ID - 使用 version 替代
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 省份编码
     */
    @Column("PROVINCE_CODE")
    private String provinceCode;

    /**
     * 省份名称
     */
    @Column("PROVINCE_NAME")
    private String provinceName;

    /**
     * 城市编码
     */
    @Column("CITY_CODE")
    private String cityCode;

    /**
     * 城市名称
     */
    @Column("CITY_NAME")
    private String cityName;

    /**
     * 区县编码
     */
    @Column("COUNTY_CODE")
    private String countyCode;

    /**
     * 区县名称
     */
    @Column("COUNTY_NAME")
    private String countyName;

    /**
     * 城市公司编码
     */
    @Column("CITY_FIRM_CODE")
    private String cityFirmCode;

    /**
     * 城市公司名称
     */
    @Column("CITY_FIRM_NAME")
    private String cityFirmName;

    /**
     * 处理标签编码
     */
    @Column("MANAGE_LABEL_CODE")
    private String manageLabelCode;

    /**
     * 处理标签名称
     */
    @Column("MANAGE_LABEL_NAME")
    private String manageLabelName;

    /**
     * 开放状态
     */
    @Column("OPEN_STATUS")
    private String openStatus;

    /**
     * 最近一次到店时间
     */
    @Column("LAST_ARRIVAL_TIME")
    private Date lastArrivalTime;

    /**
     * 最近一次试驾时间
     */
    @Column("LAST_TESTDRIVER_TIME")
    private Date lastTestDriverTime;

    /**
     * 第一次到店时间
     */
    @Column("FIRST_ARRIVAL_TIME")
    private Date firstArrivalTime;

    /**
     * 第一次试驾时间
     */
    @Column("FIRST_TESTDRIVER_TIME")
    private Date firstTestDriverTime;

    /**
     * 线索分配时间
     */
    @Column("ALLOCATE_TIME")
    private Date allocateTime;
}