package com.smart.adp.domain.context;

import com.smart.adp.domain.gateway.base.LookUpGateway;
import com.smart.adp.domain.valueObject.base.ChannelInfoVO;
import com.smart.adp.domain.valueObject.base.LookUpInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

import static com.smart.adp.domain.valueObject.base.table.LookUpInfoTableDef.LOOK_UP_INFO;

/**
 * <p>
 * 渠道信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/20
 */
@Slf4j
@Component
@DependsOn("flexConfig")
public class Channel {

    private static final ChannelInfoVO ROOT = new ChannelInfoVO();
    private static final String SECOND_TYPE = "ADP_CLUE_049";
    private static final String THIRD_TYPE = "ADP_CLUE_072";

    @Autowired
    private LookUpGateway lookUpGateway;

    @PostConstruct
    public void init() {
        Map<String, ChannelInfoVO> map = new HashMap<>();
        List<LookUpInfo> secondChannels = lookUpGateway.findByType(SECOND_TYPE, LOOK_UP_INFO.LOOK_UP_VALUE_CODE, LOOK_UP_INFO.LOOK_UP_VALUE_NAME);
        secondChannels.forEach(channel -> {
            ChannelInfoVO vo = new ChannelInfoVO();
            vo.setCode(channel.getLookUpValueCode());
            vo.setName(channel.getLookUpValueName());
            ROOT.getChildren()
                .add(vo);
            map.put(channel.getLookUpValueCode(), vo);
        });
        List<LookUpInfo> thirdChannels = lookUpGateway.findByType(THIRD_TYPE, LOOK_UP_INFO.LOOK_UP_VALUE_CODE, LOOK_UP_INFO.LOOK_UP_VALUE_NAME, LOOK_UP_INFO.ATTRIBUTE1);
        thirdChannels.forEach(channel -> {
            ChannelInfoVO vo = new ChannelInfoVO();
            vo.setCode(channel.getLookUpValueCode());
            vo.setName(channel.getLookUpValueName());
            ChannelInfoVO parent = Optional.ofNullable(channel.getAttribute1())
                                           .map(map::get)
                                           .orElse(null);
            if (Objects.nonNull(parent)) {
                parent.getChildren()
                      .add(vo);
            }
        });
    }

    public static List<ChannelInfoVO> getChannelInfo() {
        return ROOT.getChildren();
    }
}
