package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/18
 */
@Getter
@AllArgsConstructor
public enum ClueActiveEnum {

    /**
     * 今日活跃
     */
    ACTIVE_IN_TODAY(1, "今日活跃"),

    /**
     * 1 天活跃
     */
    ACTIVE_IN_ONE_DAY(2, "1天活跃"),

    /**
     * 3 天活跃
     */
    ACTIVE_IN_THREE_DAYS(3, "3天活跃"),

    /**
     * 7 天活跃
     */
    ACTIVE_IN_SEVEN_DAYS(4, "7天活跃"),

    /**
     * 15 天活跃
     */
    ACTIVE_IN_FIFTEEN_DAYS(5, "15天活跃"),

    /**
     * 30 天活跃
     */
    ACTIVE_IN_THIRTY_DAYS(6, "30天活跃"),

    ;

    private final int code;
    private final String desc;

    public static ClueActiveEnum getByCode(Integer code) {
        return Arrays.stream(ClueActiveEnum.values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .findAny()
                .orElse(null);
    }
}
