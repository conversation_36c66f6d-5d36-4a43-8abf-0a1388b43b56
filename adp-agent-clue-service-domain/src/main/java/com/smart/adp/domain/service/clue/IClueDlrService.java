package com.smart.adp.domain.service.clue;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacClueInfoDlrBO;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.qry.ClueDlrQry;
import com.smart.adp.domain.qry.ClueSearchQry;
import com.smart.adp.domain.valueObject.clue.ClueActiveInfoVO;
import com.smart.adp.domain.valueObject.clue.ClueDlrListVO;
import com.smart.adp.domain.valueObject.clue.ClueDlrSearchVO;

import java.util.List;
import java.util.Set;

/**
 * @Description: 线索服务
 * @Author: rik.ren
 * @Date: 2025/3/7 13:26
 **/
public interface IClueDlrService {
    /**
     * 获取潜客详情对象
     *
     * @param param
     * @param needColumn
     * @return
     */
    SacClueInfoDlr getEntity(SacClueInfoDlr param, QueryColumn... needColumn);

    /**
     * 获取潜客详情对象
     *
     * @param param
     * @param needColumn
     * @return
     */
    SacClueInfoDlr getEntity(SacClueInfoDlrBO param, QueryColumn... needColumn);

    /**
     * 批量获取潜客详情集合
     *
     * @param param
     * @param needColumn
     * @return
     */
    List<SacClueInfoDlr> queryEntity(SacClueInfoDlr param, QueryColumn... needColumn);

    /**
     * 更新线索表的用户等级
     *
     * @param param
     * @return
     */
    Boolean modifyClueUserLevel(SacClueInfoDlr param);

    /**
     * 更新线索表的用户意向车型
     *
     * @param param
     * @return
     */
    Boolean modifyClueIntendedVehicleModel(SacClueInfoDlr param);

    /**
     * 更新代理商线索来源
     *
     * @param param
     * @return
     */
    Boolean modifyClueSourceOfAgent(SacClueInfoDlr param);

    /**
     * 更新用户位置
     *
     * @param param
     * @return
     */
    Boolean modifyClueUserLocation(SacClueInfoDlr param);

    /**
     * 更新特别关注
     *
     * @param param
     * @return
     */
    Boolean modifySpecial(SacClueInfoDlr param);

    /**
     * 根据手机号查询线索的活跃信息
     *
     * @param phone
     * @return
     */
    ClueActiveInfoVO queryClueActive(String phone);

    /**
     * 根据手机号批量查询线索的活跃信息
     *
     * @param listPhone
     * @return
     */
    List<ClueActiveInfoVO> queryClueActive(List<String> listPhone);

    Page<ClueDlrListVO> page(ClueDlrQry qry);

    List<ClueDlrListVO> list(ClueDlrQry qry);

    PageVO<ClueDlrSearchVO> search(ClueSearchQry qry);

    /**
     * 线索批量查询 - 兜底式双查
     *
     * @param clueIds 线索 ID 列表
     * @return data
     */
    List<ClueDlrListVO> list(Set<String> clueIds);
}
