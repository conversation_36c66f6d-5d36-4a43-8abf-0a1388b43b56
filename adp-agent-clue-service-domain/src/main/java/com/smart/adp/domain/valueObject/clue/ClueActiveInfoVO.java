package com.smart.adp.domain.valueObject.clue;

import lombok.*;

import java.io.Serializable;


/**
 * @Description: 线索活跃信息
 * @Author: rik.ren
 * @Date: 2025/3/11 20:25
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ClueActiveInfoVO implements Serializable {
    /**
     * 手机号
     */
    private String phone;
    /**
     * smartId
     */
    private String smartId;
    /**
     * 7天内活跃的天数
     */
    private Integer activeDays7d;
    /**
     * 30天内活跃的天数
     */
    private Integer activeDays30d;
    /**
     * 用户成熟度
     */
    private String matureDegree;
    private Long touchTimes;
    private Long activeTimesIn1h;
    private Long activeTimesIn2h;
    private Long activeTimesIn3h;
    private Long activeTimesIn4h;
    private Long activeTimesIn5h;
    private Long activeTimesIn6h;
    private Long activeTimesIn7h;
    private Long activeTimesIn8h;
    private Long activeTimesIn9h;
    private Long activeTimesIn10h;
    private Long activeTimesIn11h;
    private Long activeTimesIn12h;
    private Long activeTimesIn13h;
    private Long activeTimesIn14h;
    private Long activeTimesIn15h;
    private Long activeTimesIn16h;
    private Long activeTimesIn17h;
    private Long activeTimesIn18h;
    private Long activeTimesIn19h;
    private Long activeTimesIn20h;
    private Long activeTimesIn21h;
    private Long activeTimesIn22h;
    private Long activeTimesIn23h;
    private Long activeTimesIn24h;
    /**
     * custId
     */
    private String custId;

    /**
     * 活跃时间段
     */
    private String activeTimePeriod;
}
