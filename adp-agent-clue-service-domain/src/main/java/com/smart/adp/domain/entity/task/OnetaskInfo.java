package com.smart.adp.domain.entity.task;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@Table(value = "t_sac_onetask_info", schema = "csc")
public class OnetaskInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @Id("TASK_ID")
    private String taskId;

    /**
     * 任务标题
     */
    @Column("TASK_TITLE")
    private String taskTitle;

    /**
     * 任务描述
     */
    @Column("TASK_DESCRIBE")
    private String taskDescribe;

    /**
     * 任务类型编码 值列表:ADP_CLUE_035
     */
    @Column("TASK_TYPE_CODE")
    private String taskTypeCode;

    /**
     * 任务类型名称 值列表:ADP_CLUE_035
     */
    @Column("TASK_TYPE_NAME")
    private String taskTypeName;

    /**
     * 任务状态编码 值列表:ADP_CLUE_036
     */
    @Column("TASK_STATE_CODE")
    private String taskStateCode;

    /**
     * 任务状态名称 值列表:ADP_CLUE_036
     */
    @Column("TASK_STATE_NAME")
    private String taskStateName;

    /**
     * 任务是否认证
     */
    @Column("TASK_ATTESTATION_IS")
    private String taskAttestationIs;

    /**
     * 任务是否重复
     */
    @Column("TASK_REPEAT_IS")
    private String taskRepeatIs;

    /**
     * 作业时间
     */
    @Column("BUSS_TIME")
    private LocalDateTime bussTime;

    /**
     * 作业开始时间
     */
    @Column("BUSS_START_TIME")
    private LocalDateTime bussStartTime;

    /**
     * 作业结束时间
     */
    @Column("BUSS_END_TIME")
    private LocalDateTime bussEndTime;

    /**
     * 扩展信息
     */
    @Column("EXTEND_JSON")
    private String extendJson;

    /**
     * 扩展字段1
     */
    @Column("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column("COLUMN5")
    private String column5;

    /**
     * 时间戳
     */
    @Column("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @Column("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @Column("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @Column("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;
}