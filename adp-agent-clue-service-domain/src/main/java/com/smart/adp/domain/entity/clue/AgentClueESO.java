package com.smart.adp.domain.entity.clue;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "#{@indexNameGenerator.getIndex(T(com.smart.adp.domain.common.constants.IndexNameConstant).AGENT_CLUE_INDEX_NAME)}")
@Setting(settingPath = "es-settings.json")
public class AgentClueESO {

    /**
     * 线索 id
     */
    @Field(type = FieldType.Keyword)
    private String clueId;

    /**
     * 客户 id
     */
    @Id
    @Field(type = FieldType.Keyword)
    private String custId;

    /**
     * 客户名称
     */
    @Field(type = FieldType.Text, analyzer = "ik_smart", searchAnalyzer = "ik_smart")
    private String custName;

    /**
     * 手机号
     */
    @MultiField(
        mainField = @Field(type = FieldType.Keyword),
        otherFields = {
            @InnerField(suffix = "ngram", type = FieldType.Text, analyzer = "phone_ngram_analyzer", searchAnalyzer = "keyword")
        }
    )
    private String phone;

    /**
     * 线索状态
     */
    @Field(type = FieldType.Keyword)
    private String statusCode;

    /**
     * 门店编码
     */
    @Field(type = FieldType.Keyword)
    private String dlrCode;

    /**
     * 回访人员 id
     */
    @Field(type = FieldType.Keyword)
    private String reviewPersonId;

    /**
     * 上次跟进时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime lastReviewTime;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime createdDate;

    /**
     * 是否有效
     */
    @Field(type = FieldType.Boolean)
    private Boolean isEnable;

    /**
     * 履历列表
     */
    @Field(type = FieldType.Nested)
    private List<CustResumeESO> custResumeList;

    public static AgentClueESO buildESO(SacClueInfoDlr clue) {
        AgentClueESO eso = new AgentClueESO();
        eso.setClueId(clue.getId());
        eso.setCustId(clue.getCustId());
        eso.setCustName(clue.getCustName());
        eso.setPhone(clue.getPhone());
        eso.setStatusCode(clue.getStatusCode());
        eso.setDlrCode(clue.getDlrCode());
        eso.setReviewPersonId(clue.getReviewPersonId());
        eso.setLastReviewTime(clue.getLastReviewTime());
        eso.setCreatedDate(clue.getCreatedDate());
        eso.setIsEnable(Boolean.TRUE);
        return eso;
    }

    @SuppressWarnings("unchecked")
    public Map<String, Object> toMap() {
        return JSONObject.parseObject(JSONObject.toJSONString(this), Map.class);
    }
}