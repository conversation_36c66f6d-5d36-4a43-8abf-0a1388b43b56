package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 线索虚拟外呼电话类型
 * @Author: rik.ren
 * @Date: 2025/3/16 12:06
 **/
@Getter
@AllArgsConstructor
public enum ClueVirtualRecordType {
    /**
     * 主叫
     */
    CALLER(0, "主叫"),
    /**
     * 被叫
     */
    CALLED(1, "被叫"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static ClueVirtualRecordType getByCode(Integer code) {
        return Arrays.stream(ClueVirtualRecordType.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
