package com.smart.adp.domain.enums;

/**
 * 分配状态枚举 
 * <AUTHOR>
 *
 */
public enum ReviewAssignStatusEnum {
	
	/**
	 * 待分配
	 */
	unAssign("0","待分配"),
	
	/**
	 * 已分配
	 */
	assignEd("1","已分配");
	
	private String result;

	private String msg;
	
	ReviewAssignStatusEnum(String result, String msg) {
		this.result = result;
		this.msg = msg;
	}

	public String getResult() {
		return result;
	}

	public String getMsg() {
		return msg;
	}
	
	//根据result获取枚举
	public static ReviewAssignStatusEnum fromString(String code) {
        for (ReviewAssignStatusEnum b : ReviewAssignStatusEnum.values()) {
            if (b.getResult().equalsIgnoreCase(code)) {
                return b;
            }
        }
        return null;
	}
}
