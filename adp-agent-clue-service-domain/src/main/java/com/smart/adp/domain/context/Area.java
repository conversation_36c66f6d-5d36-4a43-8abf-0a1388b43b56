package com.smart.adp.domain.context;

import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.util.CollectionUtil;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.common.constants.StringConstant;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 地区信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/12
 */
@Slf4j
@Component
public class Area {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${areaJsonUrl:https://app-obs-uat.smartchina.com.cn/json/areaCode/areaCode.json}")
    private String url;

    /**
     * index
     * code -> node
     */
    private static final Map<String, Node> CODE_MAP = new HashMap<>();

    /**
     * area root
     */
    private static final Node ROOT = new Node();

    @PostConstruct
    public void init() {
        StopWatch watch = new StopWatch("Area init");
        watch.start("get json");
        String str = restTemplate.getForObject(url, String.class);
        watch.stop();

        watch.start("parse json");
        JSONArray array = JSONArray.parseArray(str);
        if (Objects.isNull(array)) {
            throw new IllegalArgumentException("Area init: get json failed.");
        }
        watch.stop();

        watch.start("build tree");
        ROOT.addChildren(array);
        watch.stop();
        log.info(watch.prettyPrint());
    }

    /**
     * 获取地区完整路径
     *
     * @param code 编码
     * @param delimiter 分隔符
     * @return 完整路径
     */
    public static String fullPath(String code, String delimiter) {
        if (StringUtil.noText(code)) {
            return StringConstant.EMPTY;
        }
        if (StringUtil.noText(delimiter)) {
            delimiter = StrPool.SLASH;
        }

        ArrayDeque<Node> path = new ArrayDeque<>();
        Node node = CODE_MAP.get(code);

        while (Objects.nonNull(node) && ROOT != node) {
            path.addFirst(node);
            node = node.getParent();
        }

        return path.stream()
                   .map(Node::getRegionName)
                   .collect(Collectors.joining(delimiter));
    }

    /**
     * 获取当前和所有子节点的 code
     *
     * @param code 编码
     * @return code list
     */
    public static List<String> selfAndChildrenCode(String code) {
        if (StringUtil.noText(code)) {
            return Collections.emptyList();
        }

        Node node = CODE_MAP.get(code);
        if (Objects.isNull(node)) {
            return Collections.emptyList();
        }

        return subtreeCode(node);
    }

    private static List<String> subtreeCode(Node node) {
        List<String> res = new ArrayList<>();
        res.add(node.getAdCode());
        if (CollectionUtil.isEmpty(node.getChildren())) {
            return res;
        }

        for (Node child : node.getChildren()) {
            res.addAll(subtreeCode(child));
        }
        return res;
    }

    @Getter
    @Setter
    @ToString
    @EqualsAndHashCode
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Node {
        private Integer regionId;
        private String regionName;
        private String adCode;
        private Node parent;
        @ToString.Exclude
        private List<Node> children;

        /**
         * 添加子节点
         *
         * @param jsonArray json 数组
         */
        public void addChildren(JSONArray jsonArray) {
            if (Objects.isNull(jsonArray)) {
                return;
            }

            List<Node> list = new ArrayList<>();
            for (Object item : jsonArray) {
                Node node = new Node();

                JSONObject jsonObject = JSONObject.parseObject(item.toString());
                node.setRegionId(jsonObject.getInteger("regionId"));
                node.setRegionName(jsonObject.getString("regionName"));
                String code = jsonObject.getString("adCode");
                node.setAdCode(code);

                node.setParent(this);

                node.addChildren(jsonObject.getJSONArray("children"));

                CODE_MAP.put(code, node);
                list.add(node);
            }
            setChildren(list);
        }
    }
}
