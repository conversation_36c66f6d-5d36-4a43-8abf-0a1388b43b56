package com.smart.adp.domain.bo.clue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.entity.clue.SacReview;
import com.smart.adp.domain.entity.clue.SacReviewHis;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 跟进BO
 * @Author: rik.ren
 * @Date: 2025/3/17 11:24
 **/
@Data
@ToString
public class SacReviewHisBO extends SacReviewHis {

    /**
     * 客户描述
     */
    private String remark;

    /**
     * 用户阶段
     */
    private String userStage;

    /**
     * 上一个用户阶段
     */
    private String previousUserStage;

    /**
     * 线索等级(H/A/B/C/D/E)
     */
    private String clueLevel;

    /**
     * 意向车型
     */
    private String intentVehicleCode;

    /**
     * 竞品车型
     */
    private String competitiveVehicleCode;

    /**
     * 所在地
     */
    private String location;

    /**
     * 线索来源
     */
    private String clueSource;

    /**
     * 用户阶段-了解的时间
     */
    private LocalDateTime knowDate;

    /**
     * 用户阶段-到店的时间
     */
    private LocalDateTime toStoreDate;

    /**
     * 用户阶段-试驾的时间
     */
    private LocalDateTime testDriveDate;

    /**
     * 用户阶段-下定的时间
     */
    private LocalDateTime placeOrderDate;

    /**
     * 战败标签
     *
     * @see com.smart.adp.domain.enums.DefeatFlagEnum
     */
    private Integer defeatFlag = 0;

    public static SacReviewHisBO conventBO(SacReview entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        SacReviewHisBO sacReviewBO = new SacReviewHisBO();
        sacReviewBO.setReviewId(entity.getReviewId());
        sacReviewBO.setOrgCode(entity.getOrgCode());
        sacReviewBO.setOrgName(entity.getOrgName());
        sacReviewBO.setBillType(entity.getBillType());
        sacReviewBO.setBillTypeName(entity.getBillTypeName());
        sacReviewBO.setBusinessType(entity.getBusinessType());
        sacReviewBO.setBusinessTypeName(entity.getBusinessTypeName());
        sacReviewBO.setInfoChanMCode(entity.getInfoChanMCode());
        sacReviewBO.setInfoChanMName(entity.getInfoChanMName());
        sacReviewBO.setInfoChanDCode(entity.getInfoChanDCode());
        sacReviewBO.setInfoChanDName(entity.getInfoChanDName());
        sacReviewBO.setInfoChanDdCode(entity.getInfoChanDdCode());
        sacReviewBO.setInfoChanDdName(entity.getInfoChanDdName());
        sacReviewBO.setChannelCode(entity.getChannelCode());
        sacReviewBO.setChannelName(entity.getChannelName());
        sacReviewBO.setBillCode(entity.getBillCode());
        sacReviewBO.setPlanReviewTime(entity.getPlanReviewTime());
        sacReviewBO.setReviewTime(entity.getReviewTime());
        sacReviewBO.setLastReviewTime(entity.getLastReviewTime());
        sacReviewBO.setOverReviewTime(entity.getOverReviewTime());
        sacReviewBO.setPlanComeTime(entity.getPlanComeTime());
        sacReviewBO.setFactComeTime(entity.getFactComeTime());
        sacReviewBO.setIsCome(entity.getIsCome());
        sacReviewBO.setAssignStatus(entity.getAssignStatus());
        sacReviewBO.setAssignStatusName(entity.getAssignStatusName());
        sacReviewBO.setAssignTime(entity.getAssignTime());
        sacReviewBO.setAssignPersonId(entity.getAssignPersonId());
        sacReviewBO.setAssignPersonName(entity.getAssignPersonName());
        sacReviewBO.setReviewPersonId(entity.getReviewPersonId());
        sacReviewBO.setReviewPersonName(entity.getReviewPersonName());
        sacReviewBO.setReviewDesc(entity.getReviewDesc());
        sacReviewBO.setReviewStatus(entity.getReviewStatus());
        sacReviewBO.setReviewStatusName(entity.getReviewStatusName());
        sacReviewBO.setCustId(entity.getCustId());
        sacReviewBO.setCustName(entity.getCustName());
        sacReviewBO.setPhone(entity.getPhone());
        sacReviewBO.setGender(entity.getGender());
        sacReviewBO.setGenderName(entity.getGenderName());
        sacReviewBO.setTouchStatus(entity.getTouchStatus());
        sacReviewBO.setTouchStatusName(entity.getTouchStatusName());
        sacReviewBO.setErrorReasonCode(entity.getErrorReasonCode());
        sacReviewBO.setErrorReasonName(entity.getErrorReasonName());
        sacReviewBO.setNodeCode(entity.getNodeCode());
        sacReviewBO.setNodeName(entity.getNodeName());
        sacReviewBO.setSendDlrCode(entity.getSendDlrCode());
        sacReviewBO.setSendDlrShortName(entity.getSendDlrShortName());
        sacReviewBO.setSendTime(entity.getSendTime());
        sacReviewBO.setIntenLevelCode(entity.getIntenLevelCode());
        sacReviewBO.setIntenLevelName(entity.getIntenLevelName());
        sacReviewBO.setIntenBrandCode(entity.getIntenBrandCode());
        sacReviewBO.setIntenBrandName(entity.getIntenBrandName());
        sacReviewBO.setIntenSeriesCode(entity.getIntenSeriesCode());
        sacReviewBO.setIntenSeriesName(entity.getIntenSeriesName());
        sacReviewBO.setIntenCarTypeCode(entity.getIntenCarTypeCode());
        sacReviewBO.setIntenCarTypeName(entity.getIntenCarTypeName());
        sacReviewBO.setColumn1(entity.getColumn1());
        sacReviewBO.setColumn2(entity.getColumn2());
        sacReviewBO.setColumn3(entity.getColumn3());
        sacReviewBO.setColumn4(entity.getColumn4());
        sacReviewBO.setColumn5(entity.getColumn5());
        sacReviewBO.setColumn6(entity.getColumn6());
        sacReviewBO.setColumn7(entity.getColumn7());
        sacReviewBO.setColumn8(entity.getColumn8());
        sacReviewBO.setColumn9(entity.getColumn9());
        sacReviewBO.setColumn10(entity.getColumn10());
        sacReviewBO.setColumn11(entity.getColumn11());
        sacReviewBO.setColumn12(entity.getColumn12());
        sacReviewBO.setColumn13(entity.getColumn13());
        sacReviewBO.setColumn14(entity.getColumn14());
        sacReviewBO.setColumn15(entity.getColumn15());
        sacReviewBO.setColumn16(entity.getColumn16());
        sacReviewBO.setColumn17(entity.getColumn17());
        sacReviewBO.setColumn18(entity.getColumn18());
        sacReviewBO.setColumn19(entity.getColumn19());
        sacReviewBO.setColumn20(entity.getColumn20());
        sacReviewBO.setBigColumn1(entity.getBigColumn1());
        sacReviewBO.setBigColumn2(entity.getBigColumn2());
        sacReviewBO.setBigColumn3(entity.getBigColumn3());
        sacReviewBO.setBigColumn4(entity.getBigColumn4());
        sacReviewBO.setBigColumn5(entity.getBigColumn5());
        sacReviewBO.setExtendsJson(entity.getExtendsJson());
        sacReviewBO.setOemId(entity.getOemId());
        sacReviewBO.setGroupId(entity.getGroupId());
        sacReviewBO.setCreator(entity.getCreator());
        sacReviewBO.setCreatedName(entity.getCreatedName());
        sacReviewBO.setCreatedDate(entity.getCreatedDate());
        sacReviewBO.setModifier(entity.getModifier());
        sacReviewBO.setModifyName(entity.getModifyName());
        sacReviewBO.setLastUpdatedDate(entity.getLastUpdatedDate());
        sacReviewBO.setIsEnable(entity.getIsEnable());
        sacReviewBO.setUpdateControlId(entity.getUpdateControlId());
        sacReviewBO.setProvinceCode(entity.getProvinceCode());
        sacReviewBO.setProvinceName(entity.getProvinceName());
        sacReviewBO.setCityCode(entity.getCityCode());
        sacReviewBO.setCityName(entity.getCityName());
        sacReviewBO.setCountyCode(entity.getCountyCode());
        sacReviewBO.setCountyName(entity.getCountyName());
        sacReviewBO.setCityFirmCode(entity.getCityFirmCode());
        sacReviewBO.setCityFirmName(entity.getCityFirmName());
        sacReviewBO.setManageLabelCode(entity.getManageLabelCode());
        sacReviewBO.setManageLabelName(entity.getManageLabelName());
        sacReviewBO.setFirstReasonCode(entity.getFirstReasonCode());
        sacReviewBO.setFirstReasonName(entity.getFirstReasonName());
        return sacReviewBO;

    }

    public static List<SacReviewHisBO> conventBO(List<SacReview> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        return entityList.stream()
                .map(entity -> {
                    SacReviewHisBO bo = conventBO(entity);
                    return bo;
                })
                .collect(Collectors.toList());
    }

    public SacReviewBO convent(SacReviewHisBO hisBO) {
        if (ObjectUtil.isEmpty(hisBO)) {
            return null;
        }
        SacReviewBO sacReviewBO = new SacReviewBO();
        sacReviewBO.setRemark(hisBO.getRemark());
        sacReviewBO.setUserStage(hisBO.getUserStage());
        sacReviewBO.setPreviousUserStage(hisBO.getPreviousUserStage());
        sacReviewBO.setClueLevel(hisBO.getClueLevel());
        sacReviewBO.setIntentVehicleCode(hisBO.getIntentVehicleCode());
        sacReviewBO.setCompetitiveVehicleCode(hisBO.getCompetitiveVehicleCode());
        sacReviewBO.setLocation(hisBO.getLocation());
        sacReviewBO.setClueSource(hisBO.getClueSource());
        sacReviewBO.setKnowDate(hisBO.getKnowDate());
        sacReviewBO.setToStoreDate(hisBO.getToStoreDate());
        sacReviewBO.setTestDriveDate(hisBO.getTestDriveDate());
        sacReviewBO.setPlaceOrderDate(hisBO.getPlaceOrderDate());
        sacReviewBO.setDefeatFlag(hisBO.getDefeatFlag());
        sacReviewBO.setReviewId(hisBO.getReviewId());
        sacReviewBO.setOrgCode(hisBO.getOrgCode());
        sacReviewBO.setOrgName(hisBO.getOrgName());
        sacReviewBO.setBillType(hisBO.getBillType());
        sacReviewBO.setBillTypeName(hisBO.getBillTypeName());
        sacReviewBO.setBusinessType(hisBO.getBusinessType());
        sacReviewBO.setBusinessTypeName(hisBO.getBusinessTypeName());
        sacReviewBO.setInfoChanMCode(hisBO.getInfoChanMCode());
        sacReviewBO.setInfoChanMName(hisBO.getInfoChanMName());
        sacReviewBO.setInfoChanDCode(hisBO.getInfoChanDCode());
        sacReviewBO.setInfoChanDName(hisBO.getInfoChanDName());
        sacReviewBO.setInfoChanDdCode(hisBO.getInfoChanDdCode());
        sacReviewBO.setInfoChanDdName(hisBO.getInfoChanDdName());
        sacReviewBO.setChannelCode(hisBO.getChannelCode());
        sacReviewBO.setChannelName(hisBO.getChannelName());
        sacReviewBO.setBillCode(hisBO.getBillCode());
        sacReviewBO.setPlanReviewTime(hisBO.getPlanReviewTime());
        sacReviewBO.setReviewTime(hisBO.getReviewTime());
        sacReviewBO.setLastReviewTime(hisBO.getLastReviewTime());
        sacReviewBO.setOverReviewTime(hisBO.getOverReviewTime());
        sacReviewBO.setPlanComeTime(hisBO.getPlanComeTime());
        sacReviewBO.setFactComeTime(hisBO.getFactComeTime());
        sacReviewBO.setIsCome(hisBO.getIsCome());
        sacReviewBO.setAssignStatus(hisBO.getAssignStatus());
        sacReviewBO.setAssignStatusName(hisBO.getAssignStatusName());
        sacReviewBO.setAssignTime(hisBO.getAssignTime());
        sacReviewBO.setAssignPersonId(hisBO.getAssignPersonId());
        sacReviewBO.setAssignPersonName(hisBO.getAssignPersonName());
        sacReviewBO.setReviewPersonId(hisBO.getReviewPersonId());
        sacReviewBO.setReviewPersonName(hisBO.getReviewPersonName());
        sacReviewBO.setReviewDesc(hisBO.getReviewDesc());
        sacReviewBO.setReviewStatus(hisBO.getReviewStatus());
        sacReviewBO.setReviewStatusName(hisBO.getReviewStatusName());
        sacReviewBO.setCustId(hisBO.getCustId());
        sacReviewBO.setCustName(hisBO.getCustName());
        sacReviewBO.setPhone(hisBO.getPhone());
        sacReviewBO.setGender(hisBO.getGender());
        sacReviewBO.setGenderName(hisBO.getGenderName());
        sacReviewBO.setTouchStatus(hisBO.getTouchStatus());
        sacReviewBO.setTouchStatusName(hisBO.getTouchStatusName());
        sacReviewBO.setErrorReasonCode(hisBO.getErrorReasonCode());
        sacReviewBO.setErrorReasonName(hisBO.getErrorReasonName());
        sacReviewBO.setNodeCode(hisBO.getNodeCode());
        sacReviewBO.setNodeName(hisBO.getNodeName());
        sacReviewBO.setSendDlrCode(hisBO.getSendDlrCode());
        sacReviewBO.setSendDlrShortName(hisBO.getSendDlrShortName());
        sacReviewBO.setSendTime(hisBO.getSendTime());
        sacReviewBO.setIntenLevelCode(hisBO.getIntenLevelCode());
        sacReviewBO.setIntenLevelName(hisBO.getIntenLevelName());
        sacReviewBO.setIntenBrandCode(hisBO.getIntenBrandCode());
        sacReviewBO.setIntenBrandName(hisBO.getIntenBrandName());
        sacReviewBO.setIntenSeriesCode(hisBO.getIntenSeriesCode());
        sacReviewBO.setIntenSeriesName(hisBO.getIntenSeriesName());
        sacReviewBO.setIntenCarTypeCode(hisBO.getIntenCarTypeCode());
        sacReviewBO.setIntenCarTypeName(hisBO.getIntenCarTypeName());
        sacReviewBO.setColumn1(hisBO.getColumn1());
        sacReviewBO.setColumn2(hisBO.getColumn2());
        sacReviewBO.setColumn3(hisBO.getColumn3());
        sacReviewBO.setColumn4(hisBO.getColumn4());
        sacReviewBO.setColumn5(hisBO.getColumn5());
        sacReviewBO.setColumn6(hisBO.getColumn6());
        sacReviewBO.setColumn7(hisBO.getColumn7());
        sacReviewBO.setColumn8(hisBO.getColumn8());
        sacReviewBO.setColumn9(hisBO.getColumn9());
        sacReviewBO.setColumn10(hisBO.getColumn10());
        sacReviewBO.setColumn11(hisBO.getColumn11());
        sacReviewBO.setColumn12(hisBO.getColumn12());
        sacReviewBO.setColumn13(hisBO.getColumn13());
        sacReviewBO.setColumn14(hisBO.getColumn14());
        sacReviewBO.setColumn15(hisBO.getColumn15());
        sacReviewBO.setColumn16(hisBO.getColumn16());
        sacReviewBO.setColumn17(hisBO.getColumn17());
        sacReviewBO.setColumn18(hisBO.getColumn18());
        sacReviewBO.setColumn19(hisBO.getColumn19());
        sacReviewBO.setColumn20(hisBO.getColumn20());
        sacReviewBO.setBigColumn1(hisBO.getBigColumn1());
        sacReviewBO.setBigColumn2(hisBO.getBigColumn2());
        sacReviewBO.setBigColumn3(hisBO.getBigColumn3());
        sacReviewBO.setBigColumn4(hisBO.getBigColumn4());
        sacReviewBO.setBigColumn5(hisBO.getBigColumn5());
        sacReviewBO.setExtendsJson(hisBO.getExtendsJson());
        sacReviewBO.setOemId(hisBO.getOemId());
        sacReviewBO.setGroupId(hisBO.getGroupId());
        sacReviewBO.setCreator(hisBO.getCreator());
        sacReviewBO.setCreatedName(hisBO.getCreatedName());
        sacReviewBO.setCreatedDate(hisBO.getCreatedDate());
        sacReviewBO.setModifier(hisBO.getModifier());
        sacReviewBO.setModifyName(hisBO.getModifyName());
        sacReviewBO.setLastUpdatedDate(hisBO.getLastUpdatedDate());
        sacReviewBO.setIsEnable(hisBO.getIsEnable());
        sacReviewBO.setUpdateControlId(hisBO.getUpdateControlId());
        sacReviewBO.setProvinceCode(hisBO.getProvinceCode());
        sacReviewBO.setProvinceName(hisBO.getProvinceName());
        sacReviewBO.setCityCode(hisBO.getCityCode());
        sacReviewBO.setCityName(hisBO.getCityName());
        sacReviewBO.setCountyCode(hisBO.getCountyCode());
        sacReviewBO.setCountyName(hisBO.getCountyName());
        sacReviewBO.setCityFirmCode(hisBO.getCityFirmCode());
        sacReviewBO.setCityFirmName(hisBO.getCityFirmName());
        sacReviewBO.setManageLabelCode(hisBO.getManageLabelCode());
        sacReviewBO.setManageLabelName(hisBO.getManageLabelName());
        sacReviewBO.setFirstReasonCode(hisBO.getFirstReasonCode());
        sacReviewBO.setFirstReasonName(hisBO.getFirstReasonName());
        return sacReviewBO;

    }
}
