package com.smart.adp.domain.enums;

import com.smart.adp.domain.entity.clue.SacClueTag;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Consumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/21
 */
@Getter
@AllArgsConstructor
public enum ClueTagEnum {

    /**
     * 战败再分配
     */
    DEFEATED_ALLOCATE("d_allocate", table -> {}),

    ;

    private final String code;
    private final Consumer<SacClueTag> tableFill;
}
