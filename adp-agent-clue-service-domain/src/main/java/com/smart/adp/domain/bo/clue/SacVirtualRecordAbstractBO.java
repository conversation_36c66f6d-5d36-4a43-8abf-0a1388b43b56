package com.smart.adp.domain.bo.clue;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.paginate.Page;
import com.smart.adp.domain.valueObject.clue.SacVirtualRecordAbstractVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 线索的虚拟外呼的摘要内容 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@NoArgsConstructor
public class SacVirtualRecordAbstractBO extends SacVirtualRecordAbstractVO {

    /**
     * 分页属性
     */
    private Page<SacVirtualRecordAbstractVO> pageObj;

    public SacVirtualRecordAbstractBO(Page<SacVirtualRecordAbstractVO> pageObj) {
        this.pageObj = pageObj;
    }

    public static SacVirtualRecordAbstractBO conventBO(SacVirtualRecordAbstractVO entity) {
        SacVirtualRecordAbstractBO bo = new SacVirtualRecordAbstractBO();
        bo.setId(entity.getId());
        bo.setCustId(entity.getCustId());
        bo.setRecordId(entity.getRecordId());
        bo.setEmpId(entity.getEmpId());
        bo.setEmpName(entity.getEmpName());
        bo.setAbstractContent(entity.getAbstractContent());
        bo.setCreatedDate(entity.getCreatedDate());
        return bo;

    }

    public static List<SacVirtualRecordAbstractBO> conventBO(List<SacVirtualRecordAbstractVO> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        return entityList.stream()
                .map(entity -> {
                    SacVirtualRecordAbstractBO bo = conventBO(entity);
                    return bo;
                })
                .collect(Collectors.toList());
    }
}
