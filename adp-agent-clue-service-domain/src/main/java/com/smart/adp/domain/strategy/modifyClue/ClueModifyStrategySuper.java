package com.smart.adp.domain.strategy.modifyClue;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;
import com.smart.adp.domain.service.clue.ISacOneCustRemarkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 更新线索内容的父类
 * @Author: rik.ren
 * @Date: 2025/3/14 14:21
 **/
@Service
public class ClueModifyStrategySuper {
    @Autowired
    private ISacOneCustRemarkService oneCustRemarkService;

    /**
     * 判断remark中线索数据是否存在
     *
     * @param custId
     * @return
     */
    public Boolean isExistRemark(String custId) {
        SacOneCustRemark entity = oneCustRemarkService.queryRemark(SacOneCustRemark.builder().custId(custId).build());
        return ObjectUtil.isNotEmpty(entity);
    }

    /**
     * 插入remark
     *
     * @param param
     * @return
     */
    public Boolean inserRemark(SacOneCustRemark param) {
        return oneCustRemarkService.insertRemark(param);
    }
}
