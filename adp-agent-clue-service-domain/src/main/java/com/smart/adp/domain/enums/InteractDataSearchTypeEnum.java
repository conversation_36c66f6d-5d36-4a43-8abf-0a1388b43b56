package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 互动数据搜索类型枚举
 * @Author: rik.ren
 * @Date: 2025/3/18 15:49
 **/
@Getter
@AllArgsConstructor
public enum InteractDataSearchTypeEnum {
    /**
     * 默认列表
     */
    DEFAULT(0, "默认列表"),
    /**
     * 主动搜索
     */
    CONDITION(1, "主动搜索"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static InteractDataSearchTypeEnum getByCode(Integer code) {
        return Arrays.stream(InteractDataSearchTypeEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
