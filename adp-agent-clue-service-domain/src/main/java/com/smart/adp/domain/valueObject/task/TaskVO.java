package com.smart.adp.domain.valueObject.task;

import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.domain.common.constants.StringConstant;
import com.smart.adp.domain.entity.task.OnetaskDetail;
import com.smart.adp.domain.entity.task.OnetaskInfo;
import com.smart.adp.domain.enums.TaskDetailStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/5
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "任务VO")
public class TaskVO {

    /**
     * 任务用户表 ID
     */
    @Schema(description = "任务用户表 ID")
    private Long id;

    /**
     * 任务 ID
     */
    @Schema(description = "任务 ID")
    private String taskId;

    /**
     * 任务标题
     */
    @Schema(description = "任务标题")
    private String taskTitle;

    /**
     * 任务描述
     */
    @Schema(description = "任务描述")
    private String taskDescribe;

    /**
     * 任务人员 ID
     */
    @Schema(description = "任务人员 ID")
    private String taskPersonId;

    /**
     * 任务人员名称
     */
    @Schema(description = "任务人员名称")
    private String taskPersonName;

    /**
     * 任务人员门店编码
     */
    @Schema(description = "任务人员门店编码")
    private String taskPersonDlrCode;

    /**
     * 任务状态-人员维度
     * @see TaskDetailStatusEnum
     */
    @Schema(description = "任务状态-人员维度")
    private Integer status;

    /**
     * 是否必须跟进
     */
    @Schema(description = "是否必须跟进")
    private String taskAttestationIs;

    /**
     * 附件路径
     */
    @Schema(description = "附件路径")
    private String taskFilePath;

    /**
     * 任务开始时间
     */
    @Schema(description = "任务开始时间")
    private LocalDateTime bussStartTime;

    /**
     * 任务结束时间
     */
    @Schema(description = "任务结束时间")
    private LocalDateTime bussEndTime;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private LocalDateTime createdDate;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime finishTime;

    /**
     * 任务明细列表
     */
    @Schema(description = "任务明细列表")
    private List<TaskDetailVO> details;

    /**
     * 填充任务信息
     *
     * @param info po
     */
    public void joinInfo(OnetaskInfo info) {
        if (Objects.isNull(info)) {
            return;
        }

        setTaskTitle(info.getTaskTitle());
        setTaskDescribe(info.getTaskDescribe());
        setTaskAttestationIs(info.getTaskAttestationIs());

        String filePath = Optional.ofNullable(info.getExtendJson())
                                  .map(JSONObject::parseObject)
                                  .map(json -> json.getString("taskFilePath"))
                                  .orElse(StringConstant.EMPTY);
        setTaskFilePath(filePath);
    }

    /**
     * 填充任务明细信息
     *
     * @param details -
     */
    public void joinDetails(List<OnetaskDetail> details) {
        if (CollectionUtil.isEmpty(details)) {
            setDetails(Collections.emptyList());
        }

        List<TaskDetailVO> detailVOs = details.stream()
                                              .map(TaskDetailVO::fromPO)
                                              .collect(Collectors.toList());
        setDetails(detailVOs);

        LocalDateTime finishTime = details.stream()
                                          .filter(detail -> !TaskDetailStatusEnum.UNFINISHED.getCodeStr().equals(detail.getStateCode()))
                                          .map(OnetaskDetail::getBussTime)
                                          .max(LocalDateTime::compareTo)
                                          .orElse(null);
        setFinishTime(finishTime);
    }
}
