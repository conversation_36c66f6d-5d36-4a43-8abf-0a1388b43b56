package com.smart.adp.domain.service.base;

import com.smart.adp.domain.valueObject.base.LookUpInfo;

import java.util.List;

/**
 * @Description: 字典表service
 * @Author: rik.ren
 * @Date: 2025/3/13 19:02
 **/
public interface ILookUpService {

    LookUpInfo findLookUpInfo(String lookUpTypeCode, String lookUpValueCode);

    List<LookUpInfo> findLookUpInfo(String lookUpTypeCode);

    List<LookUpInfo> findLookUpInfo(LookUpInfo param);

    List<LookUpInfo> findLookUpInfo(String lookUpTypeCode, List<String> lookUpValueCode);

    List<LookUpInfo> findLookUpList(LookUpInfo param);
}
