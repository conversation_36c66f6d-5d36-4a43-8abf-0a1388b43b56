package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nullable;
import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/4
 */
@Getter
@AllArgsConstructor
public enum ReviewStatusEnum {

    /**
     * 待跟进
     */
    WAIT_FOR_REVIEW(1),

    /**
     * 未跟进
     */
    NO_REVIEW(2),

    /**
     * 跟进中
     */
    IN_REVIEW(3),

    /**
     * 逾期
     */
    OVERDUE(4),
    ;

    private final int code;

    /**
     * code -> enum
     *
     * @param code code
     * @return enum
     */
    @Nullable
    public static ReviewStatusEnum getByCode(Integer code) {
        return Arrays.stream(ReviewStatusEnum.values())
                     .filter(e -> Objects.equals(e.getCode(), code))
                     .findAny()
                     .orElse(null);
    }
}
