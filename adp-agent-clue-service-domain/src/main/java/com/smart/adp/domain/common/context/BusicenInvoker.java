package com.smart.adp.domain.common.context;

import com.smart.adp.domain.common.ListResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.function.Supplier;


public class BusicenInvoker<T> implements IBusicenInvoke<T> {

    public static final Logger log = LoggerFactory.getLogger(BusicenInvoker.class);

    static boolean printStackTrace = true;

    T result;

    Throwable expt;

    public static <M> BusicenInvoker<ListResult<M>> doList(Supplier<ListResult<M>> supplier) {
        BusicenInvoker<ListResult<M>> busicenInvoker = new BusicenInvoker<>();
        busicenInvoker.invoke(supplier);
        if (busicenInvoker.expt != null) {
            ListResultStack<M> resultTemp = new ListResultStack<>();
            resultTemp.setMsg("查询失败");
            resultTemp.setResult("0");
            if (busicenInvoker.result == null) {
                busicenInvoker.result = resultTemp;
            }
            if (busicenInvoker.expt instanceof BusicenException) {
                busicenInvoker.result.setMsg(busicenInvoker.expt.getMessage());
            }
            if (printStackTrace) {
                String trace = throwToStr(busicenInvoker.expt);
                resultTemp.setStackTrace(trace);
            }
        }
        return busicenInvoker;
    }

    public static <T> BusicenInvoker<T> doo(Supplier<T> supplier) {
        BusicenInvoker<T> busicenInvoker = new BusicenInvoker<>();
        busicenInvoker.invoke(supplier);
        return busicenInvoker;
    }

    @Override
    public IBusicenInvoke<T> defErr(T t) {
        if (result == null) {
            result = t;
        }
        return this;
    }

    @Override
    public IBusicenInvoke<T> invoke(Supplier<T> supplier) {
        try {
            result = supplier.get();
        } catch (Throwable e) {
            log.error("调用异常", e);
            expt = e;

        }
        return this;
    }

    @Override
    public IBusicenInvoke<T> optResult(BusicenInvokeConsumer<T> consumer) {
        consumer.accept(result, expt);
        return this;
    }

    @Override
    public T result() {
        return result;
    }

    public static String throwToStr(Throwable t) {
        if (t == null) {
            return null;
        }
        try (StringWriter sw = new StringWriter(); PrintWriter pw = new PrintWriter(sw, true)) {
            t.printStackTrace(pw);
            pw.flush();
            sw.flush();
            return sw.getBuffer().toString();
        } catch (IOException e) {
            return "";
        }
    }

    public interface ResultExceptionStack {

        String getStackTrace();

        void setStackTrace(String stackTrace);
    }

    public static class ListResultStack<T> extends ListResult<T> implements ResultExceptionStack {
        private String stackTrace;

        @Override
        public String getStackTrace() {
            return stackTrace;
        }

        @Override
        public void setStackTrace(String stackTrace) {
            this.stackTrace = stackTrace;
        }
    }

}
