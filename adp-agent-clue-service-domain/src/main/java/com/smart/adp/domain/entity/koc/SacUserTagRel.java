package com.smart.adp.domain.entity.koc;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 用户标签关系实体（标签、达人类型、备注）
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_user_tag_rel")
public class SacUserTagRel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关系ID 自增
     */
    @Id("rel_id")
    private Long relId;

    /**
     * 用户smartId
     */
    @Column("smart_id")
    private String smartId;

    /**
     * 用户手机号
     */
    @Column("phone")
    private String phone;

    /**
     * 用户昵称
     */
    @Column("nick_name")
    private String nickName;

    /**
     * 关系类型：1-标签 2-达人类型 3-备注
     * @see com.smart.adp.domain.enums.KocRelTypeEnum
     */
    @Column("rel_type")
    private Integer relType;

    /**
     * 引用ID（标签ID/类型ID）
     */
    @Column("ref_id")
    private Long refId;

    /**
     * 备注内容（仅当REL_TYPE=3时有效）
     */
    @Column("remark_content")
    private String remarkContent;

    /**
     * 创建人ID
     */
    @Column("creator")
    private String creator;

    /**
     * 创建人
     */
    @Column("created_name")
    private String createdName;

    /**
     * 创建日期
     */
    @Column("created_date")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column("modifier")
    private String modifier;

    /**
     * 修改人
     */
    @Column("modify_name")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column("last_updated_date")
    private LocalDateTime lastUpdatedDate;
}
