package com.smart.adp.domain.entity.koc;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 用户标签关系实体（标签、达人类型、备注）
 * @Author: system
 * @Date: 2025/8/4
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_user_tag_rel")
public class SacUserTagRel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关系ID
     */
    @Id
    @Column("REL_ID")
    private String relId;

    /**
     * 用户smartId
     */
    @Column("SMART_ID")
    private String smartId;

    /**
     * 用户手机号
     */
    @Column("PHONE")
    private String phone;

    /**
     * 用户昵称
     */
    @Column("NICK_NAME")
    private String nickName;

    /**
     * 关系类型：1-标签 2-达人类型 3-备注
     */
    @Column("REL_TYPE")
    private Integer relType;

    /**
     * 引用ID（标签ID/类型ID）
     */
    @Column("REF_ID")
    private String refId;

    /**
     * 备注内容（仅当REL_TYPE=3时有效）
     */
    @Column("REMARK_CONTENT")
    private String remarkContent;

    /**
     * 创建人ID
     */
    @Column("CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column("CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column("MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;
}
