package com.smart.adp.domain.service.koc;

import com.smart.adp.domain.bo.koc.KocUserInfoBO;
import com.smart.adp.domain.bo.koc.KocUserQueryBO;
import com.smart.adp.domain.bo.koc.KocUserTagBO;
import com.smart.adp.domain.common.DomainPage;

import java.util.List;

/**
 * @Description: KOC用户领域服务接口
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
public interface IKocUserService {

    /**
     * 根据smartId获取用户信息
     *
     * @param smartId 用户smartId
     * @return 用户信息
     */
    KocUserInfoBO getUserBySmartId(String smartId);

    /**
     * 根据手机号获取用户信息
     *
     * @param phone 手机号
     * @return 用户信息
     */
    KocUserInfoBO getUserByPhone(String phone);

    /**
     * 搜索用户
     *
     * @param keyword 关键字（手机号/昵称/smartId）
     * @return 用户信息列表
     */
    List<KocUserInfoBO> searchUsers(String keyword);

    /**
     * 分页查询用户信息
     *
     * @param queryBO 查询条件
     * @return 分页结果
     */
    DomainPage<KocUserInfoBO> getUserPage(KocUserQueryBO queryBO);

    /**
     * 获取用户的标签信息
     *
     * @param smartId 用户smartId
     * @return 用户标签信息
     */
    List<KocUserTagBO> getUserTags(String smartId);

    /**
     * 获取用户的达人类型信息
     *
     * @param smartId 用户smartId
     * @return 用户达人类型信息
     */
    List<KocUserTagBO> getUserExpertTypes(String smartId);

    /**
     * 获取用户的备注信息
     *
     * @param smartId 用户smartId
     * @return 用户备注信息
     */
    List<KocUserTagBO> getUserRemarks(String smartId);

    /**
     * 为用户添加标签
     *
     * @param smartId 用户smartId
     * @param tagId 标签ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean addTagToUser(String smartId, String tagId, String operatorId, String operatorName);

    /**
     * 批量为用户添加标签
     *
     * @param smartIds 用户smartId列表
     * @param tagIds 标签ID列表
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean batchAddTagsToUsers(List<String> smartIds, List<String> tagIds, String operatorId, String operatorName);

    /**
     * 从用户移除标签
     *
     * @param smartId 用户smartId
     * @param tagId 标签ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean removeTagFromUser(String smartId, String tagId, String operatorId, String operatorName);

    /**
     * 批量从用户移除标签
     *
     * @param smartIds 用户smartId列表
     * @param tagIds 标签ID列表
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean batchRemoveTagsFromUsers(List<String> smartIds, List<String> tagIds, String operatorId, String operatorName);

    /**
     * 为用户添加达人类型
     *
     * @param smartId 用户smartId
     * @param expertTypeId 达人类型ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean addExpertTypeToUser(String smartId, String expertTypeId, String operatorId, String operatorName);

    /**
     * 批量为用户添加达人类型
     *
     * @param smartIds 用户smartId列表
     * @param expertTypeIds 达人类型ID列表
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean batchAddExpertTypesToUsers(List<String> smartIds, List<String> expertTypeIds, String operatorId, String operatorName);

    /**
     * 从用户移除达人类型
     *
     * @param smartId 用户smartId
     * @param expertTypeId 达人类型ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean removeExpertTypeFromUser(String smartId, String expertTypeId, String operatorId, String operatorName);

    /**
     * 批量从用户移除达人类型
     *
     * @param smartIds 用户smartId列表
     * @param expertTypeIds 达人类型ID列表
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean batchRemoveExpertTypesFromUsers(List<String> smartIds, List<String> expertTypeIds, String operatorId, String operatorName);

    /**
     * 为用户添加备注
     *
     * @param smartId 用户smartId
     * @param remarkContent 备注内容
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean addRemarkToUser(String smartId, String remarkContent, String operatorId, String operatorName);

    /**
     * 更新用户备注
     *
     * @param relId 关系ID
     * @param remarkContent 备注内容
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean updateUserRemark(String relId, String remarkContent, String operatorId, String operatorName);

    /**
     * 删除用户备注
     *
     * @param relId 关系ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否成功
     */
    Boolean removeUserRemark(String relId, String operatorId, String operatorName);

    /**
     * 验证用户是否存在
     *
     * @param smartId 用户smartId
     * @return 是否存在
     */
    Boolean validateUserExists(String smartId);

    /**
     * 验证用户标签关系是否存在
     *
     * @param smartId 用户smartId
     * @param refId 引用ID
     * @param relType 关系类型
     * @return 是否存在
     */
    Boolean validateUserTagRelExists(String smartId, String refId, Integer relType);

    /**
     * 获取用户完整信息（包含标签、达人类型、备注）
     *
     * @param smartId 用户smartId
     * @return 用户完整信息
     */
    KocUserInfoBO getUserFullInfo(String smartId);
}
