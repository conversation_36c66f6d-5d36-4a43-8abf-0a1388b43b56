package com.smart.adp.domain.valueObject.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * date 2025/3/12 14:26
 * @description 竞品车型VO
 **/
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "竞品车型 VO")
public class CompetitiveCarModelVO {

    /**
     * 竞品车型名称
     */
    @Schema(description = "竞品车型名称 如：特斯拉")
    private String carModelName;

    /**
     * 竞品车型类型
     */
    @Schema(description = "竞品车型类型 如1-特斯拉")
    private String carModelType;
}
