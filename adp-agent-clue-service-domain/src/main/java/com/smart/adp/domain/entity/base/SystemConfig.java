package com.smart.adp.domain.entity.base;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/3/6 20:47
 * @description 系统配置
 **/
@Table(value = "t_sac_system_config", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class SystemConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column("CONFIG_ID")
    private String configId;

    @Column("CONFIG_RANGE")
    private String configRange;

    @Column("CONFIG_CODE")
    private String configCode;

    @Column("CONFIG_NAME")
    private String configName;

    @Column("CONFIG_DESC")
    private String configDesc;

    @Column("IS_ENABLE")
    private String isEnable;

}
