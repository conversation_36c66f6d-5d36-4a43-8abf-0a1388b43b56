package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.bo.clue.UscMdmVirtualRecordBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.clue.UscMdmVirtualRecordEntity;

import java.util.List;

/**
 * @Description: 虚拟外呼gateway
 * @Author: rik.ren
 * @Date: 2025/3/15 16:16
 **/
public interface UscMdmVirtualRecordGateway {
    /**
     * 更新虚拟外呼内容
     *
     * @param param
     * @return
     */
    Boolean updateVirtualRecord(UscMdmVirtualRecordEntity param);

    /**
     * 查询虚拟外呼
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns
     * @return
     */
    DomainPage<UscMdmVirtualRecordEntity> queryVirtualRecord(UscMdmVirtualRecordEntity param, UscMdmVirtualRecordBO boParam,
                                                             QueryOrderBy orderBy,
                                                             QueryColumn... columns);

    /**
     * 查询虚拟外呼
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns
     * @return
     */
    List<UscMdmVirtualRecordEntity> queryVirtualRecordList(UscMdmVirtualRecordEntity param, UscMdmVirtualRecordBO boParam,
                                                           QueryOrderBy orderBy,
                                                           QueryColumn... columns);
}
