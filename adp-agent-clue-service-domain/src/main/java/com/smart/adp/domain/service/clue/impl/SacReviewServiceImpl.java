package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import com.smart.adp.domain.bo.clue.SacReviewHisBO;
import com.smart.adp.domain.enums.DefeatFlagEnum;
import com.smart.adp.domain.gateway.clue.ReviewGateway;
import com.smart.adp.domain.service.clue.ISacReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.entity.clue.table.SacOneCustRemarkTableDef.SAC_ONE_CUST_REMARK;
import static com.smart.adp.domain.entity.clue.table.SacReviewHisTableDef.SAC_REVIEW_HIS;
import static com.smart.adp.domain.entity.clue.table.SacReviewTableDef.SAC_REVIEW;

/**
 * @Description: 回访domain服务实现
 * @Author: rik.ren
 * @Date: 2025/3/6 17:35
 **/
@Service
public class SacReviewServiceImpl implements ISacReviewService {
    @Autowired
    private ReviewGateway reviewGateway;

    /**
     * 获取回访详情对象
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public SacReviewBO getReviewInfo(SacReviewBO param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        SacReviewBO entity = null;
        if (DefeatFlagEnum.NORMAL.getCode().equals(param.getDefeatFlag())) {
            if (ObjectUtil.isEmpty(needColumn)) {
                needColumn = new QueryColumn[]{SAC_REVIEW.REVIEW_ID, SAC_REVIEW.INFO_CHAN_MCODE, SAC_REVIEW.INFO_CHAN_MNAME,
                        SAC_REVIEW.INFO_CHAN_DNAME, SAC_REVIEW.INFO_CHAN_DCODE, SAC_REVIEW.BILL_CODE, SAC_REVIEW.PLAN_REVIEW_TIME,
                        SAC_REVIEW.REVIEW_TIME, SAC_REVIEW.ASSIGN_STATUS, SAC_REVIEW.ASSIGN_TIME, SAC_REVIEW.REVIEW_PERSON_ID,
                        SAC_REVIEW.REVIEW_PERSON_NAME, SAC_REVIEW.REVIEW_DESC, SAC_REVIEW.REVIEW_STATUS,
                        SAC_REVIEW.REVIEW_STATUS_NAME,
                        SAC_REVIEW.CUST_ID, SAC_REVIEW.PHONE, SAC_REVIEW.INTEN_LEVEL_CODE, SAC_REVIEW.INTEN_LEVEL_NAME,
                        SAC_REVIEW.INTEN_BRAND_CODE, SAC_REVIEW.INTEN_BRAND_NAME, SAC_REVIEW.INTEN_SERIES_CODE,
                        SAC_REVIEW.INTEN_SERIES_NAME, SAC_REVIEW.INTEN_CAR_TYPE_CODE, SAC_REVIEW.INTEN_CAR_TYPE_NAME,
                        SAC_REVIEW.COLUMN12, SAC_REVIEW.UPDATE_CONTROL_ID, SAC_REVIEW.COLUMN1,
                        SAC_REVIEW.COLUMN2, SAC_REVIEW.COLUMN3, SAC_REVIEW.COLUMN4,
                        SAC_REVIEW.COLUMN5, SAC_REVIEW.COLUMN6, SAC_REVIEW.COLUMN7,
                        SAC_REVIEW.COLUMN8, SAC_REVIEW.COLUMN9, SAC_REVIEW.COLUMN10,
                        SAC_REVIEW.COLUMN11, SAC_REVIEW.COLUMN12, SAC_REVIEW.COLUMN13,
                        SAC_REVIEW.COLUMN14, SAC_REVIEW.COLUMN15, SAC_REVIEW.GENDER, SAC_REVIEW.GENDER_NAME,
                        SAC_ONE_CUST_REMARK.CLUE_LEVEL, SAC_ONE_CUST_REMARK.LOCATION, SAC_ONE_CUST_REMARK.INTENT_VEHICLE_CODE,
                        SAC_ONE_CUST_REMARK.COMPETITIVE_VEHICLE_CODE, SAC_ONE_CUST_REMARK.CLUE_SOURCE,
                        SAC_ONE_CUST_REMARK.REMARK};
            }
            entity = reviewGateway.findByCondition(param, needColumn);
        } else if (DefeatFlagEnum.DEFEAT.getCode().equals(param.getDefeatFlag())) {
            SacReviewHisBO sacReviewHisBO = param.convent(param);
            SacReviewHisBO reviewHisInfo = getReviewHisInfo(sacReviewHisBO);
            entity = ObjectUtil.isEmpty(reviewHisInfo) ? null : reviewHisInfo.convent(reviewHisInfo);
        }
        return entity;
    }

    /**
     * 获取跟进历史记录
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public SacReviewHisBO getReviewHisInfo(SacReviewHisBO param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{SAC_REVIEW_HIS.REVIEW_ID, SAC_REVIEW_HIS.INFO_CHAN_MCODE,
                    SAC_REVIEW_HIS.INFO_CHAN_MNAME,
                    SAC_REVIEW_HIS.INFO_CHAN_DNAME, SAC_REVIEW_HIS.INFO_CHAN_DCODE, SAC_REVIEW_HIS.BILL_CODE,
                    SAC_REVIEW_HIS.PLAN_REVIEW_TIME,
                    SAC_REVIEW_HIS.REVIEW_TIME, SAC_REVIEW_HIS.ASSIGN_STATUS, SAC_REVIEW_HIS.ASSIGN_TIME,
                    SAC_REVIEW_HIS.REVIEW_PERSON_ID,
                    SAC_REVIEW_HIS.REVIEW_PERSON_NAME, SAC_REVIEW_HIS.REVIEW_DESC, SAC_REVIEW_HIS.REVIEW_STATUS,
                    SAC_REVIEW_HIS.REVIEW_STATUS_NAME,
                    SAC_REVIEW_HIS.CUST_ID, SAC_REVIEW_HIS.PHONE, SAC_REVIEW_HIS.INTEN_LEVEL_CODE,
                    SAC_REVIEW_HIS.INTEN_LEVEL_NAME,
                    SAC_REVIEW_HIS.INTEN_BRAND_CODE, SAC_REVIEW_HIS.INTEN_BRAND_NAME, SAC_REVIEW_HIS.INTEN_SERIES_CODE,
                    SAC_REVIEW_HIS.INTEN_SERIES_NAME, SAC_REVIEW_HIS.INTEN_CAR_TYPE_CODE, SAC_REVIEW_HIS.INTEN_CAR_TYPE_NAME,
                    SAC_REVIEW_HIS.COLUMN12, SAC_REVIEW_HIS.UPDATE_CONTROL_ID, SAC_REVIEW_HIS.COLUMN1,
                    SAC_REVIEW_HIS.COLUMN2, SAC_REVIEW_HIS.COLUMN3, SAC_REVIEW_HIS.COLUMN4,
                    SAC_REVIEW_HIS.COLUMN5, SAC_REVIEW_HIS.COLUMN6, SAC_REVIEW_HIS.COLUMN7,
                    SAC_REVIEW_HIS.COLUMN8, SAC_REVIEW_HIS.COLUMN9, SAC_REVIEW_HIS.COLUMN10,
                    SAC_REVIEW_HIS.COLUMN11, SAC_REVIEW_HIS.COLUMN12, SAC_REVIEW_HIS.COLUMN13,
                    SAC_REVIEW_HIS.COLUMN14, SAC_REVIEW_HIS.COLUMN15, SAC_REVIEW_HIS.GENDER, SAC_REVIEW_HIS.GENDER_NAME,
                    SAC_ONE_CUST_REMARK.CLUE_LEVEL, SAC_ONE_CUST_REMARK.LOCATION, SAC_ONE_CUST_REMARK.INTENT_VEHICLE_CODE,
                    SAC_ONE_CUST_REMARK.COMPETITIVE_VEHICLE_CODE, SAC_ONE_CUST_REMARK.CLUE_SOURCE, SAC_ONE_CUST_REMARK.REMARK};
        }
        return reviewGateway.findByCondition(param, needColumn);
    }

    /**
     * 批量获取潜客详情集合
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public List<SacReviewBO> queryReviewInfo(SacReviewBO param, QueryColumn... needColumn) {
        return null;
    }

    /**
     * 更新回访表的用户意向车型
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyReviewIntendedVehicleModel(SacReviewBO param) {
        // 更新数据库
        Boolean modifyResult = reviewGateway.modifyReview(param);
        // todo 更新缓存

        return modifyResult;
    }

    /**
     * 更新回访表
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyReview(SacReviewBO param) {
        // 更新数据库
        Boolean modifyResult = reviewGateway.modifyReview(param);
        return modifyResult;
    }
}
