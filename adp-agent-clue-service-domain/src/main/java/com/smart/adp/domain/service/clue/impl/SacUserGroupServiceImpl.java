package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;
import com.smart.adp.domain.gateway.clue.SacUserGroupGateway;
import com.smart.adp.domain.service.clue.ISacUserGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.entity.clue.table.SacUserGroupEntityTableDef.SAC_USER_GROUP_ENTITY;

/**
 * @Description: 用户分组服务实现
 * @Author: rik.ren
 * @Date: 2025/3/9 16:01
 **/
@Service
public class SacUserGroupServiceImpl implements ISacUserGroupService {
    @Autowired
    private SacUserGroupGateway userGroupGateway;


    /**
     * 批量获取用户分组集合
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public List<SacUserGroupEntity> queryEntity(SacUserGroupEntity param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{SAC_USER_GROUP_ENTITY.USER_GROUP_ID,
                    SAC_USER_GROUP_ENTITY.USER_GROUP_NAME,
                    SAC_USER_GROUP_ENTITY.USER_GROUP_DESC};
        }
        List<SacUserGroupEntity> resultListGroup = userGroupGateway.findListByCreator(param, needColumn);
        return resultListGroup;
    }
}
