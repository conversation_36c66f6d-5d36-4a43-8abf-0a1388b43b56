package com.smart.adp.domain.service.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.clue.SacClueMsgRecordBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.valueObject.clue.SacClueMsgRecordVO;

import java.util.List;

/**
 * @Description: 用户事件service
 * @Author: rik.ren
 * @Date: 2025/3/14 15:46
 **/
public interface ISacClueMsgRecordService {
    /**
     * 根据实体条件查询集合
     *
     * @param boParam
     * @param needColumns
     * @return
     */
    List<SacClueMsgRecordVO> findListByCondition(SacClueMsgRecordBO boParam, QueryColumn... needColumns);

    /**
     * 根据实体条件查询分页
     *
     * @param boParam
     * @param needColumns
     * @return
     */
    DomainPage<SacClueMsgRecordVO> findPageByCondition(SacClueMsgRecordBO boParam, QueryColumn... needColumns);

    /**
     * 查询符合条件的个数
     *
     * @param boParam
     * @return
     */
    Long findCountByCondition(SacClueMsgRecordBO boParam);

    /**
     * 标记消息已读
     *
     * @param boParam
     * @return
     */
    Boolean markEmpMsgRead(SacClueMsgRecordBO boParam);
}
