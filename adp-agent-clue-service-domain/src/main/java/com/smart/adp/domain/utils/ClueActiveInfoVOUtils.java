package com.smart.adp.domain.utils;

import com.smart.adp.domain.valueObject.clue.ClueActiveInfoVO;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: 获取线索活跃时间段
 * 计算用户最活跃的时间段：
 * 场景是，不同时间段有相同活跃次数，那么这些候选时间段中
 * 如果候选时间段中有中午（11~13点）的，则选择中午时间段。
 * 如果没有中午时间段，但有晚上（18~21点）的，则选择晚上时间段。
 * 如果既没有中午也没有晚上时间段，则选择最靠近中午或晚上的时间段，且中午的优先级高于晚上。
 * @Author: rik.ren
 * @Date: 2025/3/11 20:25
 **/
public class ClueActiveInfoVOUtils {

    // 使用静态代码块预加载字段信息
    private static final List<FieldInfo> FIELD_INFOS;

    static {
        FIELD_INFOS = new ArrayList<>();
        Class<?> clazz = ClueActiveInfoVO.class;
        Pattern pattern = Pattern.compile("activeTimesIn(\\d+)h");

        for (Field field : clazz.getDeclaredFields()) {
            String fieldName = field.getName();
            Matcher matcher = pattern.matcher(fieldName);
            if (matcher.matches()) {
                int hour = Integer.parseInt(matcher.group(1));
                field.setAccessible(true);
                FIELD_INFOS.add(new FieldInfo(field, hour));
            }
        }
    }

    // 获取最活跃的时间段
    public static String getMostActiveTimeRange(ClueActiveInfoVO vo) throws IllegalAccessException {
        List<TimeSlot> slots = new ArrayList<>();

        // 提取活跃次数
        for (FieldInfo fieldInfo : FIELD_INFOS) {
            Long value = (Long) fieldInfo.field.get(vo);
            long count = value != null ? value : 0L;
            slots.add(new TimeSlot(fieldInfo.hour, count));
        }

        // 找到最大值
        long maxCount = slots.stream()
                .mapToLong(slot -> slot.count)
                .max()
                .orElse(0L);

        // 如果所有字段都是 0 或 null，返回空字符串，表示没有最活跃的时间段
        if (maxCount == 0) {
            return "";
        }

        // 筛选候选时间段
        List<TimeSlot> maxSlots = slots.stream()
                .filter(slot -> slot.count == maxCount)
                .collect(Collectors.toList());

        // 优先级处理
        TimeSlot selectedSlot = selectPrioritySlot(maxSlots);
        return selectedSlot != null ? selectedSlot.timeRange : "";
    }

    // 选择优先级最高的时间段
    private static TimeSlot selectPrioritySlot(List<TimeSlot> slots) {
        // 优先选择中午 11~13 点
        List<TimeSlot> noonSlots = slots.stream()
                .filter(slot -> slot.hour >= 11 && slot.hour <= 13)
                .collect(Collectors.toList());
        if (!noonSlots.isEmpty()) {
            return noonSlots.get(0);
        }

        // 其次选择晚上 18~21 点
        List<TimeSlot> eveningSlots = slots.stream()
                .filter(slot -> slot.hour >= 18 && slot.hour <= 21)
                .collect(Collectors.toList());
        if (!eveningSlots.isEmpty()) {
            return eveningSlots.get(0);
        }

        // 如果没有优先时间段，选择最靠近中午或晚上的时间段
        return getClosestToPriority(slots);
    }

    // 获取最靠近中午或晚上的时间段
    private static TimeSlot getClosestToPriority(List<TimeSlot> slots) {
        // 定义优先时间段的中心点
        int noonCenter = 12; // 中午的中心点
        int eveningCenter = 19; // 晚上的中心点

        // 计算每个时间段到优先时间段的距离
        Map<TimeSlot, Integer> distances = new HashMap<>();
        for (TimeSlot slot : slots) {
            int distanceToNoon = Math.abs(slot.hour - noonCenter);
            int distanceToEvening = Math.abs(slot.hour - eveningCenter);
            // 优先中午，所以中午的权重更高
            int minDistance = Math.min(distanceToNoon * 10, distanceToEvening);
            distances.put(slot, minDistance);
        }

        // 选择距离最小的
        return distances.entrySet().stream()
                .min(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    // 字段信息
    private static class FieldInfo {
        Field field;
        int hour;

        FieldInfo(Field field, int hour) {
            this.field = field;
            this.hour = hour;
        }
    }

    // 时间段信息
    private static class TimeSlot {
        int hour;
        long count;
        String timeRange;

        TimeSlot(int hour, long count) {
            this.hour = hour;
            this.count = count;
            this.timeRange = getTimeRange(hour);
        }

        private String getTimeRange(int hour) {
            int start = hour;
            int end = (hour + 1) % 24;
            return String.format("%d~%d", start, end);
        }
    }
}