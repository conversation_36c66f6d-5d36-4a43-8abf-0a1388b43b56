package com.smart.adp.domain.valueObject.clue;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.bo.clue.SacOnecustResumeBO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Objects;

import static com.smart.adp.domain.valueObject.clue.table.SacOnecustResumeVOTableDef.SAC_ONECUST_RESUME_VO;

/**
 * 客户履历信息表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Table(value = "t_sac_onecust_resume", schema = "csc")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SacOnecustResumeVO {

    /**
     * 履历ID
     */
    @Id()
    private String resumeId;

    /**
     * CUST_ID
     */
    @Column(value = "CUST_ID")
    private String custId;

    /**
     * SMARTID
     */
    @Column(value = "SMART_ID")
    private String smartId;

    /**
     * 意向级别(L0-L5)
     */
    @Column(value = "CLUE_LEVEL_CODE")
    private String clueLevelCode;

    /**
     * 数据所属门店编码
     */
    @Column(value = "DLR_CODE_OWNER")
    private String dlrCodeOwner;

    /**
     * 数据所属门店名称
     */
    @Column(value = "DLR_NAME_OWNER")
    private String dlrNameOwner;

    /**
     * 跟进人员编码
     */
    @Column(value = "RESUME_PERSON_CODE")
    private String resumePersonCode;

    /**
     * 跟进人员名称
     */
    @Column(value = "RESUME_PERSON_NAME")
    private String resumePersonName;

    /**
     * 场景编码 值列表:ADP_CLUE_001
     */
    @Column(value = "SENCE_CODE")
    private String senceCode;

    /**
     * 场景名称 值列表:ADP_CLUE_001
     */
    @Column(value = "SENCE_NAME")
    private String senceName;

    /**
     * 客户履历内容
     */
    @Column(value = "RESUME_DESC")
    private String resumeDesc;

    /**
     * 客户履历备注
     */
    @Column(value = "REMARK")
    private String remark;

    /**
     * 订单编号
     */
    @Column(value = "SALE_ORDER_CODE")
    private String saleOrderCode;

    /**
     * 作业时间
     */
    @Column(value = "BUSS_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime bussTime;

    /**
     * 作业开始时间
     */
    @Column(value = "BUSS_START_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime bussStartTime;

    /**
     * 作业结束时间
     */
    @Column(value = "BUSS_END_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime bussEndTime;

    /**
     * 关联单据ID
     */
    @Column(value = "RELATION_BILL_ID")
    private String relationBillId;

    /**
     * 到店同行人数
     */
    @Column(value = "ARRIVAL_NUM")
    private String arrivalNum;

    /**
     * 到店时间
     */
    @Column(value = "ARRIVAL_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime arrivalTime;

    /**
     * 离店时间
     */
    @Column(value = "ARRIVAL_END_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime arrivalEndTime;

    /**
     * 到店方式 值列表VE1113
     */
    @Column(value = "ARRIVAL_METHOD")
    private String arrivalMethod;

    /**
     * 扩展字段1，线索等级(H/A/B/C/D/E)
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 热度编码
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 热度名称
     */
    @Column(value = "COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @Column(value = "COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @Column(value = "COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @Column(value = "COLUMN9")
    private String column9;

    /**
     * 扩展字段10，跟进时维护的线索等级HABCD
     */
    @Column(value = "COLUMN10")
    private String column10;

    /**
     * 时间戳
     */
    @Column(value = "_MYCAT_OP_TIME")
    private Long MycatOpTime;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @Column(value = "OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @Column(value = "GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column(value = "SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column(value = "SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacOnecustResumeVO conditions(QueryWrapper wrapper, SacOnecustResumeBO resumeBO) {
        wrapper.and(SAC_ONECUST_RESUME_VO.CUST_ID.eq(getCustId(), StringUtil::hasText))
                .and(SAC_ONECUST_RESUME_VO.SMART_ID.eq(getSmartId(), StringUtil::hasText))
                .and(SAC_ONECUST_RESUME_VO.SENCE_CODE.in(resumeBO.getListSenceCode(), CollUtil::isNotEmpty))
                .and(SAC_ONECUST_RESUME_VO.DLR_CODE_OWNER.eq(getDlrCodeOwner(), StringUtil::hasText))
                .and(SAC_ONECUST_RESUME_VO.SALE_ORDER_CODE.eq(getSaleOrderCode(), StringUtil::hasText))
                .and(SAC_ONECUST_RESUME_VO.RELATION_BILL_ID.eq(getRelationBillId(), StringUtil::hasText))
                .and(SAC_ONECUST_RESUME_VO.RESUME_PERSON_CODE.eq(getResumePersonCode(), StringUtil::hasText))
                .and(SAC_ONECUST_RESUME_VO.CREATED_DATE.ge(resumeBO.getCreateDateBegin(), Objects::nonNull))
                .and(SAC_ONECUST_RESUME_VO.CREATED_DATE.le(resumeBO.getCreateDateEnd(), Objects::nonNull))
                .and(SAC_ONECUST_RESUME_VO.BUSS_TIME.ge(resumeBO.getBussTimeBegin(), Objects::nonNull))
                .and(SAC_ONECUST_RESUME_VO.BUSS_TIME.le(resumeBO.getBussEndTime(), Objects::nonNull))
                .and(SAC_ONECUST_RESUME_VO.ARRIVAL_TIME.ge(getArrivalTime(), Objects::nonNull))
                .and(SAC_ONECUST_RESUME_VO.ARRIVAL_END_TIME.le(getArrivalEndTime(), Objects::nonNull))
                .and(SAC_ONECUST_RESUME_VO.RESUME_DESC.like(resumeBO.getSearchContent(), StringUtil::hasText));
        return this;
    }
}
