package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 互动数据业务类型
 * @Author: rik.ren
 * @Date: 2025/3/16 14:55
 **/
@Getter
@AllArgsConstructor
public enum InteractDataBizType {
    /**
     * 用户事件
     */
    USER_EVENT(0, "用户事件"),
    /**
     * 虚拟外呼录音
     */
    VIRTUAL_RECORD(1, "虚拟外呼录音"),
    /**
     * 跟进记录
     */
    CLUE_RESUME(2, "跟进记录"),
    /**
     * 试驾信息
     */
    TEST_DRIVE(3, "试驾信息"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static InteractDataBizType getByCode(Integer code) {
        return Arrays.stream(InteractDataBizType.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
