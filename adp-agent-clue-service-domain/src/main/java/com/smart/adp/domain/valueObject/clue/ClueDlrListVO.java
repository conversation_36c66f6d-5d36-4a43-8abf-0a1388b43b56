package com.smart.adp.domain.valueObject.clue;

import com.smart.adp.domain.context.TimeContext;
import com.smart.adp.domain.enums.ClueActiveEnum;
import com.smart.adp.domain.enums.ClueStageEnum;
import com.smart.adp.domain.enums.ClueStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Set;

import static com.smart.adp.domain.common.constants.StringConstant.TIME_FORMAT;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/3
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "线索列表 VO")
public class ClueDlrListVO {

    /**
     * ID
     */
    @Schema(description = "线索 ID")
    private String id;

    /**
     * 线索等级 - 营销助手维护
     */
    @Schema(description = "线索等级")
    private String level;

    /**
     * 客户 ID
     */
    @Schema(description = "客户 ID")
    private String custId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String custName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 一级信息来源名称
     */
    @Schema(description = "一级信息来源名称")
    private String channelMName;

    /**
     * 二级信息来源名称
     */
    @Schema(description = "二级信息来源名称")
    private String channelDName;

    /**
     * 阶段
     */
    @Schema(description = "阶段")
    private Integer stage;

    /**
     * 线索状态，10 战败状态
     */
    @Schema(description = "线索状态，10 战败状态")
    private String statusCode;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createdDate;

    /**
     * 逾期时间
     */
    @Schema(description = "逾期时间")
    private LocalDateTime overdueTime;

    /**
     * 分配时间
     */
    @Schema(description = "分配时间")
    private LocalDateTime allocateTime;

    /**
     * 剩余时间
     */
    @Schema(description = "剩余时间")
    private String remainTime;

    /**
     * 客户描述
     */
    @Schema(description = "客户描述")
    private String custDesc;

    /**
     * review id
     */
    @Schema(description = "回访 id")
    private String reviewId;

    /**
     * 回访类型
     */
    @Schema(description = "回访类型")
    private String reviewType;

    /**
     * 回访反馈
     */
    @Schema(description = "回访反馈")
    private String reviewDesc;

    /**
     * 意向车型
     */
    @Schema(description = "意向车型")
    private String intentionCarType;

    /**
     * 意向车型描述
     */
    @Schema(description = "意向车型描述")
    private String intentionCarTypeDesc;

    /**
     * 活跃度
     */
    @Schema(description = "上次活跃时间")
    private LocalDateTime lastActiveTime;

    /**
     * 活跃度
     */
    @Schema(description = "活跃度")
    private Integer activity;

    /**
     * 线索单号
     */
    @Schema(description = "线索单号")
    private String serverOrder;

    /**
     * 产品专家名称
     */
    @Schema(description = "产品专家名称")
    private String reviewPersonName;

    /**
     * 最后一次跟进时间
     */
    @Schema(description = "最后一次跟进时间")
    private String lastReviewTime;

    /**
     * 线索移交标识
     * 1 已处理
     * 2 换店
     * 3 离职
     */
    @Schema(description = "线索移交标识")
    private String manageLabelCode;

    /**
     * 线索标签列表
     */
    @Schema(description = "线索标签列表")
    private Set<String> tagList;

    /**
     * 热度
     */
    @Schema(description = "热度")
    private String heat;

    /**
     * 字段填充
     */
    public void fill() {
        calcActivity();
    }

    /**
     * 活跃度计算
     */
    private void calcActivity() {
        LocalDateTime activeTime = getLastActiveTime();
        if (Objects.isNull(activeTime)) {
            return;
        }

        LocalDateTime todayStart = TimeContext.now()
                                              .toLocalDate()
                                              .atStartOfDay();
        if (activeTime.isAfter(todayStart)) {
            setActivity(ClueActiveEnum.ACTIVE_IN_TODAY.getCode());
        } else if (activeTime.isAfter(todayStart.minusDays(7))) {
            setActivity(ClueActiveEnum.ACTIVE_IN_SEVEN_DAYS.getCode());
        } else if (activeTime.isAfter(todayStart.minusDays(30))) {
            setActivity(ClueActiveEnum.ACTIVE_IN_THIRTY_DAYS.getCode());
        }
    }

    /**
     * 计算剩余时间
     */
    public void calcRemainTime() {
        LocalDateTime now = TimeContext.now();
        LocalDateTime time = getOverdueTime();
        if (Objects.nonNull(time) && time.isAfter(now) && time.isBefore(now.plusDays(1))) {
            Duration duration = Duration.between(now, time);
            setRemainTime(String.format(TIME_FORMAT, duration.toHours(), duration.toMinutes() % 60, duration.getSeconds() % 60));
        }
    }

    /**
     * 战败阶段处理
     */
    public void handleDefeatStage() {
        if (ClueStatusEnum.DEFEATED.getCode().equals(getStatusCode())) {
            setStage(ClueStageEnum.DEFEAT.getCode());
        }
    }
}
