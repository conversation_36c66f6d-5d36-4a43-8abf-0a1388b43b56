package com.smart.adp.domain.bo.clue;

import com.mybatisflex.core.paginate.Page;
import com.smart.adp.domain.valueObject.clue.SacOnecustResumeVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 线索回访BO
 * @Author: rik.ren
 * @Date: 2025/3/13 19:27
 **/
@Data
@NoArgsConstructor
@ToString
public class SacOnecustResumeBO extends SacOnecustResumeVO {
    /**
     * 分页属性
     */
    private Page<SacOnecustResumeVO> pageObj;
    /**
     * 扩展的字段
     */
    private String arrivalMethodName;

    /**
     * 创建开始时间
     */
    private LocalDateTime createDateBegin;
    /**
     * 创建结束时间
     */
    private LocalDateTime createDateEnd;

    /**
     * 创建开始时间
     */
    private LocalDateTime bussTimeBegin;
    /**
     * 创建结束时间
     */
    private LocalDateTime bussTimeEnd;

    /**
     * 搜索关键字
     */
    private String searchContent;

    /**
     * 多选场景码
     */
    private List<String> listSenceCode;

    /**
     * 多选场景码
     */
    private List<String> filePathlist;

    public SacOnecustResumeBO(Page<SacOnecustResumeVO> pageObj) {
        this.pageObj = pageObj;
    }

}
