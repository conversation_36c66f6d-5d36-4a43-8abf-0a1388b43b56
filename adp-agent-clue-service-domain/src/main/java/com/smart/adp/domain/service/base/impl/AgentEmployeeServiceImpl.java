package com.smart.adp.domain.service.base.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.base.AgentEmployeeBO;
import com.smart.adp.domain.entity.base.AgentEmployee;
import com.smart.adp.domain.gateway.clue.AgentEmployeeGateway;
import com.smart.adp.domain.service.base.IAgentEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.entity.base.table.AgentDlrInfoTableDef.AGENT_DLR_INFO;
import static com.smart.adp.domain.entity.base.table.AgentEmployeeTableDef.AGENT_EMPLOYEE;

/**
 * @Description: 员工服务实现
 * @Author: rik.ren
 * @Date: 2025/3/13 15:56
 **/
@Service
public class AgentEmployeeServiceImpl implements IAgentEmployeeService {
    @Autowired
    private AgentEmployeeGateway agentEmployeeGateway;

    /**
     * 根据empId查询
     *
     * @param empId
     * @return
     */
    @Override
    public AgentEmployee findByEmpId(String empId, QueryColumn... needColumns) {
        AgentEmployee entityObj = AgentEmployee.builder().empId(empId).build();
        List<AgentEmployee> resultList = agentEmployeeGateway.findEmployee(entityObj, null, needColumns);
        if (CollectionUtil.isEmpty(resultList)) {
            return null;
        }
        return resultList.get(0);
    }

    /**
     * 根据实体查询
     *
     * @param boParam
     * @return
     */
    @Override
    public List<AgentEmployee> findEmpInfo(AgentEmployeeBO boParam, QueryColumn... needColumns) {
        AgentEmployee employeeEntity = BeanUtil.copyProperties(boParam, AgentEmployee.class);
        List<AgentEmployee> resultList = agentEmployeeGateway.findEmployee(employeeEntity, boParam, needColumns);
        if (CollectionUtil.isEmpty(resultList)) {
            return null;
        }
        return resultList;
    }

    /**
     * 根据实体查询
     *
     * @param boParam
     * @return
     */
    @Override
    public List<AgentEmployeeBO> findEmpDlrInfo(AgentEmployeeBO boParam, QueryColumn... needColumns) {
        AgentEmployee employeeEntity = BeanUtil.copyProperties(boParam, AgentEmployee.class);
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{AGENT_EMPLOYEE.ALL_COLUMNS, AGENT_DLR_INFO.DLR_SHORT_NAME,
                    AGENT_DLR_INFO.DLR_FULL_NAME, AGENT_DLR_INFO.CITY_ID, AGENT_DLR_INFO.DLR_TYPE, AGENT_DLR_INFO.COUNTY_ID,
                    AGENT_DLR_INFO.PROVINCE_ID};
        }
        List<AgentEmployeeBO> resultList = agentEmployeeGateway.findEmployeeDlr(employeeEntity, boParam, needColumns);
        if (CollectionUtil.isEmpty(resultList)) {
            return null;
        }
        return resultList;

    }
}
