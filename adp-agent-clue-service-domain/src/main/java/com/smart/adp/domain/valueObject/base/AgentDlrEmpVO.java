package com.smart.adp.domain.valueObject.base;

import com.mybatisflex.annotation.Column;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * date 2025/4/9 15:40
 * @description 代理商门店员工信息VO
 **/
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "代理商门店员工信息 VO")
public class AgentDlrEmpVO {

    /**
     * 员工userId
     */
    @Schema(description = "员工userId")
    private String userId;

    /**
     * 员工姓名
     */
    @Schema(description = "员工姓名")
    private String empName;

    /**
     * 职员编码
     */
    @Schema(description = "员工编码")
    private String empCode;

    /**
     * 专营店编码
     */
    @Schema(description = "专营店编码")
    private String dlrCode;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 性别编码
     */
    @Schema(description = "性别编码，0:女，1：男")
    private String genderCode;

    /**
     * 角色名称：产品专家或者店长
     */
    @Schema(description = "角色名称：产品专家或者店长")
    private String roleName;
}
