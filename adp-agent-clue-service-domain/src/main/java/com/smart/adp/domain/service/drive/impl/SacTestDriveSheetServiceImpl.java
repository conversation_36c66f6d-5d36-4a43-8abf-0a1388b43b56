package com.smart.adp.domain.service.drive.impl;


import cn.hutool.core.bean.BeanUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.bo.drive.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.drive.SacTestDriveSheetEntity;
import com.smart.adp.domain.gateway.drive.SacTestDriveSheetGateway;
import com.smart.adp.domain.service.drive.ISacTestDriveSheetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.entity.drive.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;

/**
 * 试乘试驾单表 服务层实现。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Service
public class SacTestDriveSheetServiceImpl implements ISacTestDriveSheetService {
    @Autowired
    private SacTestDriveSheetGateway testDriveSheetGateway;

    /**
     * 根据条件查询试驾单
     *
     * @param param
     * @return
     */
    @Override
    public List<SacTestDriveSheetBO> queryTestDriveSheetList(SacTestDriveSheetBO param) {
        return null;
    }

    /**
     * 根据条件查询试驾单
     *
     * @param param
     * @return
     */
    @Override
    public DomainPage<SacTestDriveSheetBO> queryTestDriveSheetPage(SacTestDriveSheetBO param) {
        SacTestDriveSheetEntity entity = BeanUtil.copyProperties(param, SacTestDriveSheetEntity.class);
        QueryColumn[] needColumn = {SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_SHEET_ID,
                SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID,
                SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS, SAC_TEST_DRIVE_SHEET_ENTITY.COLUMN10,
                SAC_TEST_DRIVE_SHEET_ENTITY.START_TIME, SAC_TEST_DRIVE_SHEET_ENTITY.END_TIME,
                SAC_TEST_DRIVE_SHEET_ENTITY.DLR_NAME, SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_NAME,
                SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_ID, SAC_TEST_DRIVE_SHEET_ENTITY.COLUMN1,
                SAC_TEST_DRIVE_SHEET_ENTITY.RECORD_ID};
        DomainPage<SacTestDriveSheetEntity> pageResult = testDriveSheetGateway.queryTestDriveSheet(entity, param, null,
                needColumn);
        DomainPage<SacTestDriveSheetBO> result =
                new DomainPage<>(SacTestDriveSheetBO.conventBO(pageResult.getRecords()),
                        pageResult.getPageNumber(),
                        pageResult.getPageSize(), pageResult.getTotalCount());
        return result;
    }

    /**
     * 更新试驾单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyTestDriveSheet(SacTestDriveSheetBO param) {
        SacTestDriveSheetEntity entity = BeanUtil.copyProperties(param, SacTestDriveSheetEntity.class);
        return testDriveSheetGateway.updateTestDriveSheet(entity);
    }
}