package com.smart.adp.domain.gateway.clue;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.valueObject.clue.SacTestRecordAbstractVO;
import com.smart.adp.domain.valueObject.clue.SacVirtualRecordAbstractVO;

import java.util.List;

/**
 * @Description: 录音摘要gateway
 * @Author: rik.ren
 * @Date: 2025/3/13 15:32
 **/
public interface IRecordAbstractGateway {
    /**
     * 报错虚拟外呼摘要内容
     *
     * @param entity
     * @return
     */
    Boolean saveVirtualAbstractContent(SacVirtualRecordAbstractVO entity);

    /**
     * 保存试驾录音摘要内容
     *
     * @param entity
     * @return
     */
    Boolean saveTestAbstractContent(SacTestRecordAbstractVO entity);

    /**
     * 查询虚拟外呼摘要内容
     *
     * @return
     */
    Page<SacVirtualRecordAbstractVO> queryVirtualRecordAbs(SacVirtualRecordAbstractVO param,
                                                           Page<SacVirtualRecordAbstractVO> pageParam, QueryOrderBy orderBy,
                                                           QueryColumn... columns);

    /**
     * 查询试驾录音摘要内容
     *
     * @return
     */
    List<SacTestRecordAbstractVO> queryTestRecordAbs();
}
