package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nonnull;
import java.util.Arrays;
import java.util.Objects;

/**
 * @Description: 用户旅程枚举
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 */
@Getter
@AllArgsConstructor
public enum CustEventFlowEnum {

    /**
     * 下发
     */
    CREATE(1, "了解"),

    /**
     * 跟进
     */
    REVIEW(2, "到店"),

    /**
     * 试驾执行
     */
    DRIVE(3, "试驾"),

    /**
     * 下定
     */
    ORDER(4, "下定"),

    /**
     * 退订
     */
    RETURN(5, "交车"),

    /**
     * 交付
     */
    DELIVERY(6, "战败"),

    /**
     * 战败 - 不体现在阶段
     */
    DEFEAT(6, "defeatEventHandler"),

    ;

    private final int code;
    private final String handlerName;

    @Nonnull
    public static CustEventFlowEnum getByCode(Integer code) {
        return Arrays.stream(CustEventFlowEnum.values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .findAny()
                .orElseThrow(() -> new IllegalArgumentException("cust event code illegal: " + code));
    }
}
