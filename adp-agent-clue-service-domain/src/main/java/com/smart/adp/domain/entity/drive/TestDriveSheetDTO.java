package com.smart.adp.domain.entity.drive;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import org.springframework.data.annotation.Transient;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * <p>
 * 试驾 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024/5/30
 */
@Data
@Table(value = "t_sac_test_drive_sheet", schema = "csc")
public class TestDriveSheetDTO {

    @Field(type = FieldType.Keyword)
    @Id("TEST_DRIVE_SHEET_ID")
    private String testDriveSheetId;
    @Transient
    @Column("CUSTOMER_ID")
    private String customerId;
    @Column("APPOINTMENT_ID")
    @Field(type = FieldType.Keyword)
    private String appointmentId;
    @Field(type = FieldType.Keyword)
    @Column("SALES_CONSULTANT_ID")
    private String salesConsultantId;
    @Column("START_TIME")
    private String startTime;
    @Column("END_TIME")
    private String endTime;
}
