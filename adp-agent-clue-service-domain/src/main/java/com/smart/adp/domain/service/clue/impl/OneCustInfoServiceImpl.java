package com.smart.adp.domain.service.clue.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import com.smart.adp.domain.gateway.clue.OneCustInfoGateway;
import com.smart.adp.domain.service.clue.IOneCustInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 潜客接口实现类
 * @Author: rik.ren
 * @Date: 2025/3/5 20:34
 **/
@Service
public class OneCustInfoServiceImpl implements IOneCustInfoService {
    @Autowired
    private OneCustInfoGateway oneCustInfoGateway;

    @Override
    public SacOnecustInfoEntity getEntity(SacOnecustInfoEntity param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        return oneCustInfoGateway.findByCondition(param, needColumn);
    }

    @Override
    public List<SacOnecustInfoEntity> queryEntity(SacOnecustInfoEntity param, QueryColumn... needColumn) {
        return null;
    }

    /**
     * 更新潜客表
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyCustomerDesc(SacOnecustInfoEntity param) {
        return oneCustInfoGateway.modifyOnecust(param);
    }

    /**
     * 更新竞品车型
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyCompetitorModels(SacOnecustInfoEntity param) {
        return oneCustInfoGateway.modifyOnecust(param);
    }
}
