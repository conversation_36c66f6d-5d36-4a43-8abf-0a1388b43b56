package com.smart.adp.domain.common.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description: smart缓存注解
 * @Author: rik.ren
 * @Date: 2025/3/7 16:38
 **/
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SmartADPCache {
    String value();          // 缓存名称（类似 @Cacheable）

    String key() default "";   // 缓存键（SpEL 表达式）

    long expire() default -1; // 过期时间（默认-1表示不设置）

    TimeUnit timeUnit() default TimeUnit.SECONDS; // 时间单位
}
