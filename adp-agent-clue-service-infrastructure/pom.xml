<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.smart.adp</groupId>
        <artifactId>adp-agent-clue-service-parent</artifactId>
        <version>1.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>adp-agent-clue-service-infrastructure</artifactId>
    <packaging>jar</packaging>
    <name>adp-agent-clue-service-infrastructure</name>
    <properties>
        <maven.install.skip>true</maven.install.skip>
    </properties>
    <dependencies>
        <!-- modules依赖 -->
        <dependency>
            <groupId>com.smart.adp</groupId>
            <artifactId>adp-agent-clue-service-domain</artifactId>
        </dependency>
        <!--end-->
        <dependency>
            <groupId>com.smart</groupId>
            <artifactId>smart-elf-starter-feignclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.smart</groupId>
            <artifactId>smart-elf-starter-rabbitmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <!--Excel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.shyiko</groupId>
            <artifactId>mysql-binlog-connector-java</artifactId>
        </dependency>
    </dependencies>
</project>
