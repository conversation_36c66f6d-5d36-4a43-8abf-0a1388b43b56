<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smart.adp.infrastructure.repository.clue.ClueDlrMapper">

    <sql id="clueFields">
        ID,
        SERVER_ORDER,
        PV_SERVER_ORDER,
        CUST_ID,
        CUST_NAME,
        PHONE,
        PHONE_BACKUP,
        INTEN_LEVEL_CODE,
        INTEN_LEVEL_NAME,
        INTEN_BRAND_CODE,
        INTEN_BRAND_NAME,
        INTEN_SERIES_CODE,
        INTEN_SERIES_NAME,
        INTEN_CAR_TYPE_CODE,
        INTEN_CAR_TYPE_NAME,
        INTEN_OPTION_PACKAGE_CODE,
        INTEN_OPTION_PACKAGE_NAME,
        INNER_COLOR_CODE,
        INNER_COLOR_NAME,
        OUT_COLOR_CODE,
        OUT_COLOR_NAME,
        DLR_CODE,
        DLR_SHORT_NAME,
        SOURCE_SYSTEMT_CODE,
        SOURCE_SYSTEMT_NAME,
        RECEIVE_TIME,
        SOURCE_SERVER_ORDER,
        INFO_CHAN_M_CODE,
        INFO_CHAN_M_NAME,
        INFO_CHAN_D_CODE,
        INFO_CHAN_D_NAME,
        INFO_CHAN_DD_CODE,
        INFO_CHAN_DD_NAME,
        CHANNEL_CODE,
        CHANNEL_NAME,
        GENDER_CODE,GENDER_NAME,
        STATUS_CODE,
        STATUS_NAME,
        DEAL_NODE_CODE,
        DEAL_NODE_NAME,
        REVIEW_ID,
        FIRST_REVIEW_TIME,
        LAST_REVIEW_TIME,
        EXTENDS_JSON,
        COLUMN20,
        COLUMN1,
        COLUMN2,
        COLUMN5,
        COLUMN6,
        COLUMN18,
        OEM_ID,
        GROUP_ID,
        CREATOR,
        CREATED_NAME,
        CREATED_DATE,
        MODIFIER,
        MODIFY_NAME,
        LAST_UPDATED_DATE,
        IS_ENABLE,
        UPDATE_CONTROL_ID,
        ASSIGN_TIME,
        REVIEW_PERSON_NAME,
        REVIEW_PERSON_ID,
        PROVINCE_CODE,
        PROVINCE_NAME,
        CITY_CODE,
        CITY_NAME,
        COUNTY_CODE,
        COUNTY_NAME,
        CUS_SOURCE
    </sql>

    <insert id="insertClueInfo">
        insert into adp_leads.t_sac_clue_info_dlr(
        <include refid="clueFields"></include>
        ) values (
        #{param.id}
        , #{param.serverOrder}
        , #{param.pvServerOrder}
        , #{param.custId}
        , #{param.custName}
        , #{param.phone}
        , #{param.phoneBackup}
        , #{param.intenLevelCode}
        , #{param.intenLevelName}
        , #{param.intenBrandCode}
        , #{param.intenBrandName}
        , #{param.intenSeriesCode}
        , #{param.intenSeriesName}
        , #{param.intenCarTypeCode}
        , #{param.intenCarTypeName}
        , #{param.intenOptionPackageCode}
        , #{param.intenOptionPackageName}
        , #{param.innerColorCode}
        , #{param.innerColorName}
        , #{param.outColorCode}
        , #{param.outColorName}
        , #{param.dlrCode}
        , #{param.dlrShortName}
        , #{param.sourceSystemtCode}
        , #{param.sourceSystemtName}
        , #{param.receiveTime}
        , #{param.sourceServerOrder}
        , #{param.infoChanMCode}
        , #{param.infoChanMName}
        , #{param.infoChanDCode}
        , #{param.infoChanDName}
        , #{param.infoChanDdCode}
        , #{param.infoChanDdName}
        , #{param.channelCode}
        , #{param.channelName}
        , #{param.genderCode}
        , #{param.genderName}
        , #{param.statusCode}
        , #{param.statusName}
        , #{param.dealNodeCode}
        , #{param.dealNodeName}
        , #{param.reviewId}
        , #{param.firstReviewTime}
        , #{param.lastReviewTime}
        , #{param.extendsJson}
        , #{param.column20}
        , #{param.column1}
        , #{param.column2}
        , #{param.column5}
        , #{param.column6}
        , #{param.column18}
        , #{param.oemId}
        , #{param.groupId}
        , #{param.creator}
        , #{param.createdName}
        , #{param.createdDate}
        , #{param.modifier}
        , #{param.modifyName}
        , #{param.lastUpdatedDate}
        , #{param.isEnable}
        , #{param.updateControlId}
        , #{param.assignTime}
        , #{param.reviewPersonName}
        , #{param.reviewPersonId}
        , #{param.provinceCode}
        , #{param.provinceName}
        , #{param.cityCode}
        , #{param.cityName}
        , #{param.countyCode}
        , #{param.countyName}
        , #{param.cusSource}
        )
    </insert>

    <update id="updateClueInfo">
        update adp_leads.t_sac_clue_info_dlr
        <set>
            LAST_UPDATED_DATE=sysdate(),
            UPDATE_CONTROL_ID=uuid(),
            <if test="param.provinceCode !=null">PROVINCE_CODE=#{param.provinceCode},</if>
            <if test="param.provinceName !=null">PROVINCE_NAME=#{param.provinceName},</if>
            <if test="param.cityCode !=null">CITY_CODE=#{param.cityCode},</if>
            <if test="param.cityName !=null">CITY_NAME=#{param.cityName},</if>
            <if test="param.countyCode !=null">COUNTY_CODE=#{param.countyCode},</if>
            <if test="param.countyName !=null">COUNTY_NAME=#{param.countyName},</if>
            <if test="param.id !=null and param.id !=''">ID=#{param.id},</if>
            <if test="param.serverOrder !=null and param.serverOrder !=''">SERVER_ORDER=#{param.serverOrder},</if>
            <if test="param.openStatus !=null and param.openStatus !=''">OPEN_STATUS=#{param.openStatus},</if>
            <if test="param.pvServerOrder !=null and param.pvServerOrder !=''">PV_SERVER_ORDER=#{param.pvServerOrder},
            </if>
            <if test="param.custId !=null and param.custId !=''">CUST_ID=#{param.custId},</if>
            <if test="param.custName !=null and param.custName !=''">CUST_NAME=#{param.custName},</if>
            <if test="param.phone !=null and param.phone !=''">PHONE=#{param.phone},</if>
            <if test="param.phoneBackup !=null and param.phoneBackup !=''">PHONE_BACKUP=#{param.phoneBackup},</if>
            <if test="param.intenLevelCode !=null and param.intenLevelCode !=''">
                INTEN_LEVEL_CODE=#{param.intenLevelCode},
            </if>
            <if test="param.intenLevelName !=null and param.intenLevelName !=''">
                INTEN_LEVEL_NAME=#{param.intenLevelName},
            </if>
            <if test="param.intenBrandCode !=null and param.intenBrandCode !=''">
                INTEN_BRAND_CODE=#{param.intenBrandCode},
            </if>
            <if test="param.intenBrandName !=null and param.intenBrandName !=''">
                INTEN_BRAND_NAME=#{param.intenBrandName},
            </if>
            <if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">
                INTEN_SERIES_CODE=#{param.intenSeriesCode},
            </if>
            <if test="param.intenSeriesName !=null and param.intenSeriesName !=''">
                INTEN_SERIES_NAME=#{param.intenSeriesName},
            </if>
            <if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">
                INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode},
            </if>
            <if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">
                INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName},
            </if>
            <if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">
                INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode},
            </if>
            <if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">
                INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName},
            </if>
            <if test="param.innerColorCode !=null and param.innerColorCode !=''">
                INNER_COLOR_CODE=#{param.innerColorCode},
            </if>
            <if test="param.innerColorName !=null and param.innerColorName !=''">
                INNER_COLOR_NAME=#{param.innerColorName},
            </if>
            <if test="param.outColorCode !=null and param.outColorCode !=''">OUT_COLOR_CODE=#{param.outColorCode},</if>
            <if test="param.outColorName !=null and param.outColorName !=''">OUT_COLOR_NAME=#{param.outColorName},</if>
            <if test="param.dlrCode !=null and param.dlrCode !=''">DLR_CODE=#{param.dlrCode},</if>
            <if test="param.dlrShortName !=null and param.dlrShortName !=''">DLR_SHORT_NAME=#{param.dlrShortName},</if>
            <if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">
                SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode},
            </if>
            <if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">
                SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName},
            </if>
            <if test="param.receiveTime !=null">RECEIVE_TIME=case when #{param.receiveTime}='' then null else
                #{param.receiveTime} end,
            </if>
            <if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">
                SOURCE_SERVER_ORDER=#{param.sourceServerOrder},
            </if>
            <if test="param.infoChanMCode !=null and param.infoChanMCode !=''">INFO_CHAN_M_CODE=case when
                INFO_CHAN_M_CODE is null then #{param.infoChanMCode} else INFO_CHAN_M_CODE end,
            </if>
            <if test="param.infoChanMName !=null and param.infoChanMName !=''">INFO_CHAN_M_NAME=case when
                INFO_CHAN_M_NAME is null then #{param.infoChanMName} else INFO_CHAN_M_NAME end,
            </if>
            <if test="param.infoChanDCode !=null and param.infoChanDCode !=''">INFO_CHAN_D_CODE=case when
                INFO_CHAN_D_CODE is null then #{param.infoChanDCode} else INFO_CHAN_D_CODE end,
            </if>
            <if test="param.infoChanDName !=null and param.infoChanDName !=''">INFO_CHAN_D_NAME=case when
                INFO_CHAN_D_NAME is null then #{param.infoChanDName} else INFO_CHAN_D_NAME end,
            </if>
            <if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">
                INFO_CHAN_DD_CODE=#{param.infoChanDdCode},
            </if>
            <if test="param.infoChanDdName !=null and param.infoChanDdName !=''">
                INFO_CHAN_DD_NAME=#{param.infoChanDdName},
            </if>
            <if test="param.channelCode !=null and param.channelCode !=''">CHANNEL_CODE=case when CHANNEL_CODE is null
                then #{param.channelCode} else CHANNEL_CODE end,
            </if>
            <if test="param.channelName !=null and param.channelName !=''">CHANNEL_NAME=case when CHANNEL_NAME is null
                then #{param.channelName} else CHANNEL_NAME end,
            </if>
            <if test="param.genderCode !=null and param.genderCode !=''">GENDER_CODE=#{param.genderCode},</if>
            <if test="param.genderName !=null and param.genderName !=''">GENDER_NAME=#{param.genderName},</if>
            <if test="param.statusCode !=null and param.statusCode !=''">STATUS_CODE=#{param.statusCode},</if>
            <if test="param.statusName !=null and param.statusName !=''">STATUS_NAME=#{param.statusName},</if>
            <if test="param.dealNodeCode !=null and param.dealNodeCode !=''">DEAL_NODE_CODE=#{param.dealNodeCode},</if>
            <if test="param.dealNodeName !=null and param.dealNodeName !=''">DEAL_NODE_NAME=#{param.dealNodeName},</if>
            <if test="param.reviewId !=null and param.reviewId !=''">REVIEW_ID=#{param.reviewId},</if>
            <if test="param.firstReviewTime !=null">FIRST_REVIEW_TIME=case when #{param.firstReviewTime}='' then null
                else #{param.firstReviewTime} end,
            </if>
            <if test="param.lastReviewTime !=null">LAST_REVIEW_TIME=case when #{param.lastReviewTime}='' then null else
                #{param.lastReviewTime} end,
            </if>
            <if test="param.extendsJson !=null and param.extendsJson !=''">EXTENDS_JSON=#{param.extendsJson},</if>
            <if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
            <if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
            <if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
            <if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
            <if test="param.createdDate !=null">CREATED_DATE=#{param.createdDate},</if>
            <if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
            <if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
            <if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
            <if test="param.assignTime !=null">ASSIGN_TIME=case when #{param.assignTime}='' then null else
                #{param.assignTime} end,
            </if>
            <if test="param.reviewPersonName !=null">REVIEW_PERSON_NAME=#{param.reviewPersonName},</if>
            <if test="param.reviewPersonId !=null">REVIEW_PERSON_ID=#{param.reviewPersonId},</if>
        </set>
        where
             ID=#{param.id}
        <if test="param.updateControlId!=null and param.updateControlId !=''">
             UPDATE_CONTROL_ID = #{param.updateControlId}
        </if>
    </update>

    <update id="updateClueInfoBatch" parameterType="com.smart.adp.domain.entity.clue.SacClueInfoDlr">
        <foreach collection="clueInfoDlrList" item="param" separator=";">
            update
                adp_leads.t_sac_clue_info_dlr
            set
                <if test="param.statusCode !=null and param.statusCode !=''">STATUS_CODE=#{param.statusCode},</if>
                <if test="param.statusName !=null and param.statusName !=''">STATUS_NAME=#{param.statusName},</if>
                <if test="param.manageLabelCode !=null and param.manageLabelCode !=''">MANAGE_LABEL_CODE=#{param.manageLabelCode},</if>
                <if test="param.manageLabelName !=null and param.manageLabelName !=''">MANAGE_LABEL_NAME=#{param.manageLabelName},</if>
                <if test="param.lastUpdatedDate !=null">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
                <if test="param.assignTime !=null">ASSIGN_TIME=#{param.assignTime},</if>
                <if test="param.allocateTime !=null">ALLOCATE_TIME=#{param.allocateTime},</if>
                <if test="param.reviewPersonName !=null">REVIEW_PERSON_NAME=#{param.reviewPersonName},</if>
                <if test="param.reviewPersonId !=null">REVIEW_PERSON_ID=#{param.reviewPersonId},</if>
                <if test="param.dlrCode !=null and param.dlrCode !=''">DLR_CODE=#{param.dlrCode},</if>
                <if test="param.dlrShortName !=null and param.dlrShortName !=''">DLR_SHORT_NAME=#{param.dlrShortName},</if>
                <if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
                <if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
                <if test="param.updateControlId!=null and param.updateControlId !=''">
                     UPDATE_CONTROL_ID = #{param.updateControlId}
                </if>
            where
                REVIEW_ID=#{param.reviewId}
        </foreach>
    </update>

</mapper>