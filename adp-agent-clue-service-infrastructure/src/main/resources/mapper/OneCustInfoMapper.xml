<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smart.adp.infrastructure.repository.clue.OneCustInfoMapper">

    <insert id="saveOnecustInfo">
        INSERT INTO
        csc.`t_sac_onecust_info` (
            `CUST_ID`,
            `SMART_ID`,
            `CUST_NAME`,
            `GENDER_CODE`,
            `GENDER_NAME`,
            `NICK_NAME`,
            `PHONE`,
            `PHONE_STANDBY`,
            `EMAIL`,
            `WECHAT`,
            `ID_CARD`,
            `DRIVER_CARD`,
            `DRIVING_LICENSE`,
            `PASSPORT`,
            `source5`,
            `source6`,
            `attr83`,
            `COMPLAINTS_LABEL`,
            `FREE_LABEL`,
            `USER_HOBBIES_CODE`,
            `USER_HOBBIES_NAME`,
            `CHARACTERISTICS_CODE`,
            `CHARACTERISTICS_NAME`,
            `ONE_CUST_SOURCE`,
            `COLUMN1`,
            `COLUMN2`,
            `COLUMN3`,
            `COLUMN4`,
            `COLUMN5`,
            `COLUMN6`,
            `COLUMN7`,
            `COLUMN8`,
            `COLUMN9`,
            `COLUMN10`,
            `OEM_ID`,
            `GROUP_ID`,
            `OEM_CODE`,
            `GROUP_CODE`,
            `CREATOR`,
            `CREATED_NAME`,
            `CREATED_DATE`,
            `MODIFIER`,
            `MODIFY_NAME`,
            `LAST_UPDATED_DATE`,
            `IS_ENABLE`,
            `SDP_USER_ID`,
            `SDP_ORG_ID`,
            `UPDATE_CONTROL_ID`,
            CLUE_SOURCE,
            LOCATION,
            LOCATION_CN,
            USER_MATURITY,
            USER_MATURITY_CN,
            ESTIMATE_TIME,
            ESTIMATE_TIME_CN,
            COMPETITOR_TYPE,
            COMPETITOR_TYPE_CN,
            REVIEW_RECORD,
            REVIEW_RECORD_CN
        ) VALUES
        (
            #{param.custId},
            #{param.smartId},
            #{param.custName},
            #{param.genderCode},
            #{param.genderName},
            #{param.nickName},
            #{param.phone},
            #{param.phoneStandby},
            #{param.email},
            #{param.wechat},
            #{param.idCard},
            #{param.driverCard},
            #{param.drivingLicense},
            #{param.passport},
            #{param.source5},
            #{param.source6},
            #{param.attr83},
            #{param.complaintsLabel},
            #{param.freeLabel},
            #{param.userHobbiesCode},
            #{param.userHobbiesName},
            #{param.characteristicsCode},
            #{param.characteristicsName},
            #{param.oneCustSource},
            #{param.column1},
            #{param.column2},
            #{param.column3},
            #{param.column4},
            #{param.column5},
            #{param.column6},
            #{param.column7},
            #{param.column8},
            #{param.column9},
            #{param.column10},
            #{param.oemId},
            #{param.groupId},
            #{param.oemCode},
            #{param.groupCode},
            #{param.creator},
            #{param.createdName},
            now(),
            #{param.modifier},
            #{param.modifyName},
            now(),
            '1',
            '1',
            '1',
            uuid(),
            #{param.clueSource},
            #{param.location},
            #{param.locationCn},
            #{param.userMaturity},
            #{param.userMaturityCn},
            #{param.estimateTime},
            #{param.estimateTimeCn},
            #{param.competitorType},
            #{param.competitorTypeCn},
            #{param.reviewRecord},
            #{param.reviewRecordCn}
        )
    </insert>

</mapper>