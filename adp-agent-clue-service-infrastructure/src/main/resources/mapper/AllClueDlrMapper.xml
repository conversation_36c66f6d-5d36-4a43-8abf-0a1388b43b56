<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smart.adp.infrastructure.repository.clue.AllClueDlrMapper">

    <delete id="deleteByPhone">
        DELETE FROM
            csc.t_sac_clue_info_dlr
        WHERE
            PHONE = #{phone}
    </delete>

    <update id="updateClueInfoDlrBatch" parameterType="com.smart.adp.domain.entity.clue.SacAllClueInfoDlr">
        <foreach collection="sacAllClueInfoDlrList" item="param" separator=";">
            update
                csc.t_sac_clue_info_dlr
            set
                <if test="param.manageLabelCode !=null and param.manageLabelCode !=''">MANAGE_LABEL_CODE=#{param.manageLabelCode},</if>
                <if test="param.manageLabelName !=null and param.manageLabelName !=''">MANAGE_LABEL_NAME=#{param.manageLabelName},</if>
                <if test="param.lastUpdatedDate !=null">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
                <if test="param.assignTime !=null">ASSIGN_TIME=#{param.assignTime},</if>
                <if test="param.allocateTime !=null">ALLOCATE_TIME=#{param.allocateTime},</if>
                <if test="param.reviewPersonName !=null">REVIEW_PERSON_NAME=#{param.reviewPersonName},</if>
                <if test="param.reviewPersonId !=null">REVIEW_PERSON_ID=#{param.reviewPersonId},</if>
                <if test="param.dlrCode !=null and param.dlrCode !=''">DLR_CODE=#{param.dlrCode},</if>
                <if test="param.dlrShortName !=null and param.dlrShortName !=''">DLR_SHORT_NAME=#{param.dlrShortName},</if>
                <if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
                <if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
                <if test="param.updateControlId!=null and param.updateControlId !=''">
                    UPDATE_CONTROL_ID = #{param.updateControlId}
                </if>
            where
                REVIEW_ID=#{param.reviewId}
        </foreach>
    </update>
</mapper>