<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smart.adp.infrastructure.repository.clue.ReviewMapper">

    <insert id="saveSacReview">
        insert into csc.t_sac_review(
            REVIEW_ID
            ,ORG_CODE
            ,ORG_NAME
            ,BILL_TYPE
            ,BILL_TYPE_NAME
            ,BUSINESS_TYPE
            ,BUSINESS_TYPE_NAME
            ,INFO_CHAN_M_CODE
            ,INFO_CHAN_M_NAME
            ,INFO_CHAN_D_CODE
            ,INFO_CHAN_D_NAME
            ,INFO_CHAN_DD_CODE
            ,INFO_CHAN_DD_NAME
            ,CHANNEL_CODE
            ,CHANNEL_NAME
            ,BILL_CODE
            ,PLAN_REVIEW_TIME
            ,PLAN_COME_TIME
            ,FACT_COME_TIME
            ,IS_COME
            ,REVIEW_TIME
            ,LAST_REVIEW_TIME
            ,OVER_REVIEW_TIME
            ,ASSIGN_STATUS
            ,ASSIGN_STATUS_NAME
            ,ASSIGN_TIME
            ,ASSIGN_PERSON_ID
            ,ASSIGN_PERSON_NAME
            ,REVIEW_PERSON_ID
            ,REVIEW_PERSON_NAME
            ,REVIEW_DESC
            ,REVIEW_STATUS
            ,REVIEW_STATUS_NAME
            ,CUST_ID
            ,CUST_NAME
            ,PHONE
            ,GENDER
            ,GENDER_NAME
            ,TOUCH_STATUS
            ,TOUCH_STATUS_NAME
            ,ERROR_REASON_CODE
            ,ERROR_REASON_NAME
            ,NODE_CODE
            ,NODE_NAME
            ,SEND_DLR_CODE
            ,SEND_DLR_SHORT_NAME
            ,SEND_TIME
            ,INTEN_LEVEL_CODE
            ,INTEN_LEVEL_NAME
            ,INTEN_BRAND_CODE
            ,INTEN_BRAND_NAME
            ,INTEN_SERIES_CODE
            ,INTEN_SERIES_NAME
            ,INTEN_CAR_TYPE_CODE
            ,INTEN_CAR_TYPE_NAME
            ,COLUMN1
            ,COLUMN2
            ,COLUMN3
            ,COLUMN4
            ,COLUMN5
            ,COLUMN6
            ,COLUMN7
            ,COLUMN8
            ,COLUMN9
            ,COLUMN10
            ,COLUMN11
            ,COLUMN12
            ,COLUMN13
            ,COLUMN14
            ,COLUMN15
            ,COLUMN16
            ,COLUMN17
            ,COLUMN18
            ,COLUMN19
            ,COLUMN20
            ,BIG_COLUMN1
            ,BIG_COLUMN2
            ,BIG_COLUMN3
            ,BIG_COLUMN4
            ,BIG_COLUMN5
            ,EXTENDS_JSON
            ,OEM_ID
            ,GROUP_ID
            ,CREATOR
            ,CREATED_NAME
            ,CREATED_DATE
            ,MODIFIER
            ,MODIFY_NAME
            ,LAST_UPDATED_DATE
            ,IS_ENABLE
            ,UPDATE_CONTROL_ID
            ,PROVINCE_CODE
            ,PROVINCE_NAME
            ,CITY_CODE
            ,CITY_NAME
            ,COUNTY_CODE
            ,COUNTY_NAME
        )
        values(
            #{param.reviewId}
            ,#{param.orgCode}
            ,#{param.orgName}
            ,#{param.billType}
            ,#{param.billTypeName}
            ,#{param.businessType}
            ,#{param.businessTypeName}
            ,#{param.infoChanMCode}
            ,#{param.infoChanMName}
            ,#{param.infoChanDCode}
            ,#{param.infoChanDName}
            ,#{param.infoChanDdCode}
            ,#{param.infoChanDdName}
            ,#{param.channelCode}
            ,#{param.channelName}
            ,#{param.billCode}
            ,#{param.planReviewTime}
            ,#{param.planComeTime}
            ,#{param.factComeTime}
            ,#{param.isCome}
            ,#{param.reviewTime}
            ,#{param.lastReviewTime}
            ,#{param.overReviewTime}
            ,#{param.assignStatus}
            ,#{param.assignStatusName}
            ,#{param.assignTime}
            ,#{param.assignPersonId}
            ,#{param.assignPersonName}
            ,#{param.reviewPersonId}
            ,#{param.reviewPersonName}
            ,#{param.reviewDesc}
            ,#{param.reviewStatus}
            ,#{param.reviewStatusName}
            ,#{param.custId}
            ,#{param.custName}
            ,#{param.phone}
            ,#{param.gender}
            ,#{param.genderName}
            ,#{param.touchStatus}
            ,#{param.touchStatusName}
            ,#{param.errorReasonCode}
            ,#{param.errorReasonName}
            ,#{param.nodeCode}
            ,#{param.nodeName}
            ,#{param.sendDlrCode}
            ,#{param.sendDlrShortName}
            ,#{param.sendTime}
            ,#{param.intenLevelCode}
            ,#{param.intenLevelName}
            ,#{param.intenBrandCode}
            ,#{param.intenBrandName}
            ,#{param.intenSeriesCode}
            ,#{param.intenSeriesName}
            ,#{param.intenCarTypeCode}
            ,#{param.intenCarTypeName}
            ,#{param.column1}
            ,#{param.column2}
            ,#{param.column3}
            ,#{param.column4}
            ,#{param.column5}
            ,#{param.column6}
            ,#{param.column7}
            ,#{param.column8}
            ,#{param.column9}
            ,#{param.column10}
            ,#{param.column11}
            ,#{param.column12}
            ,#{param.column13}
            ,#{param.column14}
            ,#{param.column15}
            ,#{param.column16}
            ,#{param.column17}
            ,#{param.column18}
            ,#{param.column19}
            ,#{param.column20}
            ,#{param.bigColumn1}
            ,#{param.bigColumn2}
            ,#{param.bigColumn3}
            ,#{param.bigColumn4}
            ,#{param.bigColumn5}
            ,#{param.extendsJson}
            ,#{param.oemId}
            ,#{param.groupId}
            ,#{param.creator}
            ,#{param.createdName}
            ,#{param.createdDate}
            ,#{param.modifier}
            ,#{param.modifyName}
            ,#{param.lastUpdatedDate}
            ,'1'
            ,#{param.updateControlId}
            ,#{param.provinceCode}
            ,#{param.provinceName}
            ,#{param.cityCode}
            ,#{param.cityName}
            ,#{param.countyCode}
            ,#{param.countyName}
        )
    </insert>

    <update id="updateReviewAssignBatch" parameterType="com.smart.adp.domain.entity.clue.SacReview">
        <foreach collection="sacReviews" item="param" separator=";">
            update
                csc.t_sac_review
            set
                <if test="param.assignPersonId !=null and param.assignPersonId !=''">ASSIGN_PERSON_ID=#{param.assignPersonId},</if>
                <if test="param.assignPersonName !=null and param.assignPersonName !=''">ASSIGN_PERSON_NAME=#{param.assignPersonName},</if>
                <if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
                <if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
                <if test="param.manageLabelCode !=null and param.manageLabelCode !=''">MANAGE_LABEL_CODE=#{param.manageLabelCode},</if>
                <if test="param.manageLabelName !=null and param.manageLabelName !=''">MANAGE_LABEL_NAME=#{param.manageLabelName},</if>
                <if test="param.column13 !=null and param.column13 !=''">COLUMN13=#{param.column13},</if>
                <if test="param.lastUpdatedDate !=null">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
                <if test="param.assignTime !=null">ASSIGN_TIME=#{param.assignTime},</if>
                <if test="param.assignStatus !=null">ASSIGN_STATUS=#{param.assignStatus},</if>
                <if test="param.assignStatusName !=null">ASSIGN_STATUS_NAME=#{param.assignStatusName},</if>
                <if test="param.reviewPersonName !=null">REVIEW_PERSON_NAME=#{param.reviewPersonName},</if>
                <if test="param.reviewPersonId !=null">REVIEW_PERSON_ID=#{param.reviewPersonId},</if>
                <if test="param.orgCode !=null and param.orgCode !=''">ORG_CODE=#{param.orgCode},</if>
                <if test="param.orgName !=null and param.orgName !=''">ORG_NAME=#{param.orgName},</if>
                <if test="param.planReviewTime !=null">PLAN_REVIEW_TIME=#{param.planReviewTime},</if>
                <if test="param.overReviewTime !=null">OVER_REVIEW_TIME=#{param.overReviewTime},</if>
                <if test="param.updateControlId!=null and param.updateControlId !=''">
                    UPDATE_CONTROL_ID = #{param.updateControlId}
                </if>
            where
                REVIEW_ID=#{param.reviewId}
        </foreach>
    </update>

</mapper>