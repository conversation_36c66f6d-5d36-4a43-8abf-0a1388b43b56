<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smart.adp.infrastructure.repository.base.SystemMapper">

    <select id="selectByConfigCode" resultType="com.smart.adp.domain.valueObject.base.SystemConfigVO">
        SELECT
            sv.CONFIG_VALUE_ID AS configValueId,
            sy.CONFIG_ID AS configId,
            sy.CONFIG_RANGE AS configRange,
            sv.ORG_CODE AS orgCode,
            sv.ORG_NAME AS orgName,
            sy.CONFIG_CODE AS configCode,
            sy.CONFIG_NAME AS configName,
            sv.VALUE_CODE AS valueCode,
            sv.VALUE_NAME AS valueName,
            sy.CONFIG_DESC AS configDesc
        FROM
            csc.t_sac_system_config sy
        INNER JOIN
            csc.t_sac_system_config_value sv
        ON
            sy.config_id = sv.config_id
        WHERE
            sy.CONFIG_CODE = #{configCode}
            AND sy.is_enable = #{isEnable}
            AND sv.is_enable = #{isEnable}
            AND (
            sy.CONFIG_RANGE = 'GLOBAL'
            OR sv.ORG_CODE = #{orgCode}
            )
    </select>

    <select id="findSystemConfigByCode" resultType="com.smart.adp.domain.entity.base.SystemConfig">
        select
            CONFIG_ID,
            CONFIG_CODE,
            CONFIG_NAME,
            CONFIG_RANGE,
            CONFIG_DESC
        from
            csc.t_sac_system_config
        where
         CONFIG_CODE = #{configCode}
    </select>

</mapper>