<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smart.adp.infrastructure.repository.clue.CustEventFlowMapper">

    <select id="selectEventFlowLast" resultType="com.smart.adp.domain.entity.clue.CustEventFlow">
        SELECT *
        FROM
        (SELECT *,ROW_NUMBER() OVER (PARTITION BY cust_id ORDER BY event_time DESC, id DESC) AS rn
        FROM csc.t_sac_cust_event_flow
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="param.listCustId !=null and param.listCustId.size() > 0">
                AND cust_id IN
                <foreach collection="param.listCustId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.custId !=null and param.custId != ''">
                AND cust_id = #{param.custId}
            </if>
        </trim>
        ) AS ranked
        WHERE rn = 1
    </select>

    <select id="selectEventFlowLastWithClueRemark" resultType="com.smart.adp.domain.bo.clue.CustEventFlowBO">
        SELECT *
        FROM
        (SELECT flow.*, remark.CLUE_LEVEL ,ROW_NUMBER() OVER (PARTITION BY flow.cust_id ORDER BY event_time DESC, id DESC) AS rn
        FROM csc.t_sac_cust_event_flow as flow
        INNER JOIN csc.t_sac_onecust_remark as remark on remark.cust_id = flow.cust_id
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="param.listCustId !=null and param.listCustId.size() > 0">
                AND flow.cust_id IN
                <foreach collection="param.listCustId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.custId !=null and param.custId != ''">
                AND flow.cust_id = #{param.custId}
            </if>
        </trim>
        ) AS ranked
        WHERE rn = 1
    </select>

</mapper>