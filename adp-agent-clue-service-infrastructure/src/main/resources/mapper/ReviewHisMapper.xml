<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smart.adp.infrastructure.repository.clue.ReviewHisMapper">

    <update id="updateSacReviewHisBatch" parameterType="com.smart.adp.domain.entity.clue.SacReviewHis">
        <foreach collection="sacReviewHis" item="param" separator=";">
            update
                csc.t_sac_review_his
            set
                <if test='param.orgCode!=null'>ORG_CODE = #{param.orgCode},</if>
                <if test='param.orgName!=null'>ORG_NAME = #{param.orgName},</if>
                <if test='param.billType!=null'>BILL_TYPE = #{param.billType},</if>
                <if test='param.billTypeName!=null'>BILL_TYPE_NAME = #{param.billTypeName},</if>
                <if test='param.businessType!=null'>BUSINESS_TYPE = #{param.businessType},</if>
                <if test='param.businessTypeName!=null'>BUSINESS_TYPE_NAME = #{param.businessTypeName},</if>
                <if test='param.infoChanMCode!=null'>INFO_CHAN_M_CODE = #{param.infoChanMCode},</if>
                <if test='param.infoChanMName!=null'>INFO_CHAN_M_NAME = #{param.infoChanMName},</if>
                <if test='param.infoChanDCode!=null'>INFO_CHAN_D_CODE = #{param.infoChanDCode},</if>
                <if test='param.infoChanDName!=null'>INFO_CHAN_D_NAME = #{param.infoChanDName},</if>
                <if test='param.infoChanDdCode!=null'>INFO_CHAN_DD_CODE = #{param.infoChanDdCode},</if>
                <if test='param.infoChanDdName!=null'>INFO_CHAN_DD_NAME = #{param.infoChanDdName},</if>
                <if test='param.channelCode!=null'>CHANNEL_CODE = #{param.channelCode},</if>
                <if test='param.channelName!=null'>CHANNEL_NAME = #{param.channelName},</if>
                <if test='param.billCode!=null'>BILL_CODE = #{param.billCode},</if>
                <if test='param.planReviewTime!=null'>PLAN_REVIEW_TIME = #{param.planReviewTime},</if>
                <if test='param.planComeTime!=null'>PLAN_COME_TIME = #{param.planComeTime},</if>
                <if test='param.factComeTime!=null'>FACT_COME_TIME = #{param.factComeTime},</if>
                <if test='param.isCome!=null'>IS_COME = #{param.isCome},</if>
                <if test='param.reviewTime!=null'>REVIEW_TIME = #{param.reviewTime},</if>
                <if test='param.lastReviewTime!=null'>LAST_REVIEW_TIME = #{param.lastReviewTime},</if>
                <if test='param.overReviewTime!=null'>OVER_REVIEW_TIME = #{param.overReviewTime},</if>
                <if test='param.assignStatus!=null'>ASSIGN_STATUS = #{param.assignStatus},</if>
                <if test='param.assignStatusName!=null'>ASSIGN_STATUS_NAME = #{param.assignStatusName},</if>
                <if test='param.assignTime!=null'>ASSIGN_TIME = #{param.assignTime},</if>
                <if test='param.assignPersonId!=null'>ASSIGN_PERSON_ID = #{param.assignPersonId},</if>
                <if test='param.assignPersonName!=null'>ASSIGN_PERSON_NAME = #{param.assignPersonName},</if>
                <if test='param.reviewPersonId!=null'>REVIEW_PERSON_ID = #{param.reviewPersonId},</if>
                <if test='param.reviewPersonName!=null'>REVIEW_PERSON_NAME = #{param.reviewPersonName},</if>
                <if test='param.reviewDesc!=null'>REVIEW_DESC = #{param.reviewDesc},</if>
                <if test='param.reviewStatus!=null'>REVIEW_STATUS = #{param.reviewStatus},</if>
                <if test='param.reviewStatusName!=null'>REVIEW_STATUS_NAME = #{param.reviewStatusName},</if>
                <if test='param.custId!=null'>CUST_ID = #{param.custId},</if>
                <if test='param.custName!=null'>CUST_NAME = #{param.custName},</if>
                <if test='param.phone!=null'>PHONE = #{param.phone},</if>
                <if test='param.gender!=null'>GENDER = #{param.gender},</if>
                <if test='param.genderName!=null'>GENDER_NAME = #{param.genderName},</if>
                <if test='param.touchStatus!=null'>TOUCH_STATUS = #{param.touchStatus},</if>
                <if test='param.touchStatusName!=null'>TOUCH_STATUS_NAME = #{param.touchStatusName},</if>
                <if test='param.errorReasonCode!=null'>ERROR_REASON_CODE = #{param.errorReasonCode},</if>
                <if test='param.errorReasonName!=null'>ERROR_REASON_NAME = #{param.errorReasonName},</if>
                <if test='param.nodeCode!=null'>NODE_CODE = #{param.nodeCode},</if>
                <if test='param.nodeName!=null'>NODE_NAME = #{param.nodeName},</if>
                <if test='param.sendDlrCode!=null'>SEND_DLR_CODE = #{param.sendDlrCode},</if>
                <if test='param.sendDlrShortName!=null'>SEND_DLR_SHORT_NAME = #{param.sendDlrShortName},</if>
                <if test='param.sendTime!=null'>SEND_TIME = #{param.sendTime},</if>
                <if test='param.intenLevelCode!=null'>INTEN_LEVEL_CODE = #{param.intenLevelCode},</if>
                <if test='param.intenLevelName!=null'>INTEN_LEVEL_NAME = #{param.intenLevelName},</if>
                <if test='param.intenBrandCode!=null'>INTEN_BRAND_CODE = #{param.intenBrandCode},</if>
                <if test='param.intenBrandName!=null'>INTEN_BRAND_NAME = #{param.intenBrandName},</if>
                <if test='param.intenSeriesCode!=null'>INTEN_SERIES_CODE = #{param.intenSeriesCode},</if>
                <if test='param.intenSeriesName!=null'>INTEN_SERIES_NAME = #{param.intenSeriesName},</if>
                <if test='param.intenCarTypeCode!=null'>INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode},</if>
                <if test='param.intenCarTypeName!=null'>INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName},</if>
                <if test='param.column1!=null'>COLUMN1 = #{param.column1},</if>
                <if test='param.column2!=null'>COLUMN2 = #{param.column2},</if>
                <if test='param.column3!=null'>COLUMN3 = #{param.column3},</if>
                <if test='param.column4!=null'>COLUMN4 = #{param.column4},</if>
                <if test='param.column5!=null'>COLUMN5 = #{param.column5},</if>
                <if test='param.column6!=null'>COLUMN6 = #{param.column6},</if>
                <if test='param.column7!=null'>COLUMN7 = #{param.column7},</if>
                <if test='param.column8!=null'>COLUMN8 = #{param.column8},</if>
                <if test='param.column9!=null'>COLUMN9 = #{param.column9},</if>
                <if test='param.column10!=null'>COLUMN10 = #{param.column10},</if>
                <if test='param.column11!=null'>COLUMN11 = #{param.column11},</if>
                <if test='param.column12!=null'>COLUMN12 = #{param.column12},</if>
                <if test='param.column13!=null'>COLUMN13 = #{param.column13},</if>
                <if test='param.column14!=null'>COLUMN14 = #{param.column14},</if>
                <if test='param.column15!=null'>COLUMN15 = #{param.column15},</if>
                <if test='param.column16!=null'>COLUMN16 = #{param.column16},</if>
                <if test='param.column17!=null'>COLUMN17 = #{param.column17},</if>
                <if test='param.column18!=null'>COLUMN18 = #{param.column18},</if>
                <if test='param.column19!=null'>COLUMN19 = #{param.column19},</if>
                <if test='param.column20!=null'>COLUMN20 = #{param.column20},</if>
                <if test='param.bigColumn1!=null'>BIG_COLUMN1 = #{param.bigColumn1},</if>
                <if test='param.bigColumn2!=null'>BIG_COLUMN2 = #{param.bigColumn2},</if>
                <if test='param.bigColumn3!=null'>BIG_COLUMN3 = #{param.bigColumn3},</if>
                <if test='param.bigColumn4!=null'>BIG_COLUMN4 = #{param.bigColumn4},</if>
                <if test='param.bigColumn5!=null'>BIG_COLUMN5 = #{param.bigColumn5},</if>
                <if test='param.extendsJson!=null'>EXTENDS_JSON = #{param.extendsJson},</if>
                <if test='param.oemId!=null'>OEM_ID = #{param.oemId},</if>
                <if test='param.groupId!=null'>GROUP_ID = #{param.groupId},</if>
                <if test='param.isEnable!=null'>IS_ENABLE = #{param.isEnable},</if>
                <if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
                <if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
                <if test="param.lastUpdatedDate !=null">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
                <if test="param.updateControlId!=null and param.updateControlId !=''">
                    UPDATE_CONTROL_ID = #{param.updateControlId}
                </if>
            where
                REVIEW_ID = #{param.reviewId}
        </foreach>
    </update>

</mapper>