package com.smart.adp.infrastructure.gateway.base;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.gateway.base.ProvinceGateway;
import com.smart.adp.domain.valueObject.base.ProvinceInfo;
import com.smart.adp.infrastructure.repository.base.ProvinceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.smart.adp.domain.valueObject.base.table.ProvinceInfoTableDef.PROVINCE_INFO;

/**
 * <AUTHOR>
 * date 2025/3/9 13:58
 * @description 省信息gateway实现
 **/
@Slf4j
@Component
public class ProvinceGatewayImpl implements ProvinceGateway {

    @Autowired
    private ProvinceMapper provinceMapper;

    /**
     * 通过省id获取省份配置信息
     *
     * @param provinceId
     * @return
     */
    @Override
    public ProvinceInfo findProvinceByProvinceId(String provinceId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(PROVINCE_INFO.PROVINCE_ID, PROVINCE_INFO.PROVINCE_CODE, PROVINCE_INFO.PROVINCE_NAME)
                .eq(ProvinceInfo::getProvinceId, provinceId)
                .limit(1)
                .orderBy(ProvinceInfo::getCreatedDate)
                .desc();
        return provinceMapper.selectOneByQuery(wrapper);
    }
}
