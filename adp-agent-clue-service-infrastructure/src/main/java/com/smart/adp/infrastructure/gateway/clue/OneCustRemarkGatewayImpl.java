package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;
import com.smart.adp.domain.gateway.clue.OneCustRemarkGateway;
import com.smart.adp.infrastructure.repository.clue.CustRemarkMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.smart.adp.domain.entity.clue.table.SacOneCustRemarkTableDef.SAC_ONE_CUST_REMARK;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Component
public class OneCustRemarkGatewayImpl implements OneCustRemarkGateway {

    @Autowired
    private CustRemarkMapper custRemarkMapper;

    @Override
    public SacOneCustRemark findByCustId(String custId, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{SAC_ONE_CUST_REMARK.CUST_ID, SAC_ONE_CUST_REMARK.LOCATION};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .eq(SacOneCustRemark::getCustId, custId)
                .orderBy(SacOneCustRemark::getCreateDate)
                .desc();
        return custRemarkMapper.selectOneByQuery(wrapper);
    }

    @Override
    public List<SacOneCustRemark> findByCustId(List<String> custIds, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{SAC_ONE_CUST_REMARK.CUST_ID, SAC_ONE_CUST_REMARK.LOCATION};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .in(SacOneCustRemark::getCustId, custIds);
        return custRemarkMapper.selectListByQuery(wrapper);
    }

    /**
     * 保存客户备注信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean saveOneCustRemark(SacOneCustRemark param) {
        return custRemarkMapper.insertSelective(param) > 0;
    }

    /**
     * 修改信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyOneCustRemark(SacOneCustRemark param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("CUST_ID"), SqlConsts.EQUALS, param.getCustId());
        return custRemarkMapper.updateByCondition(param, condition) > 0;
    }

}
