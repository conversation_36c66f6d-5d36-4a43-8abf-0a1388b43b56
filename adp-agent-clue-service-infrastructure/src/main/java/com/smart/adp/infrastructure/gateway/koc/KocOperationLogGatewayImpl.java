package com.smart.adp.infrastructure.gateway.koc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.koc.SacTagOperationLog;
import com.smart.adp.domain.gateway.koc.KocOperationLogGateway;
import com.smart.adp.infrastructure.repository.koc.SacTagOperationLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.smart.adp.domain.entity.koc.table.SacTagOperationLogTableDef.SAC_TAG_OPERATION_LOG;

/**
 * @Description: KOC操作日志网关实现
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Slf4j
@Service
public class KocOperationLogGatewayImpl implements KocOperationLogGateway {

    @Autowired
    private SacTagOperationLogMapper sacTagOperationLogMapper;

    @Override
    public SacTagOperationLog findById(String logId) {
        if (StrUtil.isBlank(logId)) {
            return null;
        }
        return sacTagOperationLogMapper.findByLogId(logId);
    }

    @Override
    public List<SacTagOperationLog> findByCondition(SacTagOperationLog condition) {
        if (ObjectUtil.isEmpty(condition)) {
            return new ArrayList<>();
        }
        return sacTagOperationLogMapper.selectListByQuery(condition);
    }

    @Override
    public DomainPage<SacTagOperationLog> findPage(SacTagOperationLog condition, Integer pageNum, Integer pageSize) {
        try {
            // 构建查询条件
            QueryWrapper queryWrapper = buildQueryWrapper(condition);
            
            // 分页查询
            Page<SacTagOperationLog> page = Page.of(
                pageNum != null ? pageNum : 1,
                pageSize != null ? pageSize : 10
            );
            
            Page<SacTagOperationLog> resultPage = sacTagOperationLogMapper.paginate(page, queryWrapper);
            
            // 构建分页结果
            DomainPage<SacTagOperationLog> domainPage = new DomainPage<>();
            domainPage.setRecords(resultPage.getRecords());
            domainPage.setTotal(resultPage.getTotalRow());
            domainPage.setCurrent((long) resultPage.getPageNumber());
            domainPage.setSize((long) resultPage.getPageSize());
            domainPage.setPages((long) resultPage.getTotalPage());
            
            return domainPage;
        } catch (Exception e) {
            log.error("分页查询操作日志失败", e);
            return new DomainPage<>();
        }
    }

    @Override
    public List<SacTagOperationLog> findBySmartId(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return new ArrayList<>();
        }
        return sacTagOperationLogMapper.findBySmartId(smartId);
    }

    @Override
    public List<SacTagOperationLog> findByOperator(String operator) {
        if (StrUtil.isBlank(operator)) {
            return new ArrayList<>();
        }
        return sacTagOperationLogMapper.findByOperator(operator);
    }

    @Override
    public List<SacTagOperationLog> findByOperationType(Integer operationType) {
        if (operationType == null) {
            return new ArrayList<>();
        }
        return sacTagOperationLogMapper.findByOperationType(operationType);
    }

    @Override
    public List<SacTagOperationLog> findByTargetId(String targetId) {
        if (StrUtil.isBlank(targetId)) {
            return new ArrayList<>();
        }
        return sacTagOperationLogMapper.findByTargetBusinessId(targetId);
    }

    @Override
    public List<SacTagOperationLog> findByBatchId(String batchId) {
        if (StrUtil.isBlank(batchId)) {
            return new ArrayList<>();
        }
        return sacTagOperationLogMapper.findByBatchId(batchId);
    }

    @Override
    public List<SacTagOperationLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return new ArrayList<>();
        }
        return sacTagOperationLogMapper.findByTimeRange(startTime, endTime);
    }

    @Override
    public Boolean save(SacTagOperationLog operationLog) {
        if (ObjectUtil.isEmpty(operationLog)) {
            return false;
        }
        try {
            int result = sacTagOperationLogMapper.insert(operationLog);
            return result > 0;
        } catch (Exception e) {
            log.error("保存操作日志失败", e);
            return false;
        }
    }

    @Override
    public Boolean batchSave(List<SacTagOperationLog> operationLogs) {
        if (CollectionUtil.isEmpty(operationLogs)) {
            return false;
        }
        try {
            int result = sacTagOperationLogMapper.insertBatch(operationLogs);
            return result > 0;
        } catch (Exception e) {
            log.error("批量保存操作日志失败", e);
            return false;
        }
    }

    @Override
    public Boolean deleteById(String logId) {
        if (StrUtil.isBlank(logId)) {
            return false;
        }
        try {
            SacTagOperationLog operationLog = findById(logId);
            if (ObjectUtil.isEmpty(operationLog)) {
                return false;
            }
            int result = sacTagOperationLogMapper.deleteById(operationLog.getId());
            return result > 0;
        } catch (Exception e) {
            log.error("删除操作日志失败", e);
            return false;
        }
    }

    @Override
    public Integer deleteExpiredLogs(LocalDateTime expireTime) {
        if (expireTime == null) {
            return 0;
        }
        try {
            return sacTagOperationLogMapper.deleteExpiredLogs(expireTime);
        } catch (Exception e) {
            log.error("删除过期日志失败", e);
            return 0;
        }
    }

    @Override
    public Integer countOperations(Integer operationType, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return sacTagOperationLogMapper.countOperations(operationType, startTime, endTime);
        } catch (Exception e) {
            log.error("统计操作次数失败", e);
            return 0;
        }
    }

    @Override
    public Integer countUserOperations(String smartId, LocalDateTime startTime, LocalDateTime endTime) {
        if (StrUtil.isBlank(smartId)) {
            return 0;
        }
        try {
            return sacTagOperationLogMapper.countUserOperations(smartId, startTime, endTime);
        } catch (Exception e) {
            log.error("统计用户操作次数失败", e);
            return 0;
        }
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildQueryWrapper(SacTagOperationLog condition) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        
        if (condition == null) {
            queryWrapper.orderBy(SAC_TAG_OPERATION_LOG.OPERATION_DATE.desc());
            return queryWrapper;
        }
        
        if (condition.getOperationType() != null) {
            queryWrapper.and(SAC_TAG_OPERATION_LOG.OPERATION_TYPE.eq(condition.getOperationType()));
        }
        
        if (StrUtil.isNotBlank(condition.getTargetBusinessId())) {
            queryWrapper.and(SAC_TAG_OPERATION_LOG.TARGET_BUSINESS_ID.eq(condition.getTargetBusinessId()));
        }
        
        if (StrUtil.isNotBlank(condition.getSmartId())) {
            queryWrapper.and(SAC_TAG_OPERATION_LOG.SMART_ID.eq(condition.getSmartId()));
        }
        
        if (StrUtil.isNotBlank(condition.getBatchId())) {
            queryWrapper.and(SAC_TAG_OPERATION_LOG.BATCH_ID.eq(condition.getBatchId()));
        }
        
        if (StrUtil.isNotBlank(condition.getOperator())) {
            queryWrapper.and(SAC_TAG_OPERATION_LOG.OPERATOR.eq(condition.getOperator()));
        }
        
        queryWrapper.orderBy(SAC_TAG_OPERATION_LOG.OPERATION_DATE.desc());
        
        return queryWrapper;
    }
}
