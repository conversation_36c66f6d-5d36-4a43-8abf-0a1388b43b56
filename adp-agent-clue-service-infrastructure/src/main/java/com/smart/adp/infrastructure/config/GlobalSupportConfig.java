package com.smart.adp.infrastructure.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * Created by andy on 2022/8/30 18:10.
 * 全局支持性配置
 * 通常在这里配置一些数据库的底层支持,常用基本配置,工具类, 第三方库类支持
 */
@Configuration
public class GlobalSupportConfig {


    /**
     * Spring环境工具类Bean
     */
    @Bean
    public SpringEnvHelper springEnvHelper() {
        return new SpringEnvHelper();
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * jackson LocalDateTime 序列化处理
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {

        return builder -> {
            DateTimeFormatter dateTimeFormatter =  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            JavaTimeModule javaTimeModule = new JavaTimeModule();
            javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
            javaTimeModule.addDeserializer(LocalDateTime.class, new JsonDeserializer<LocalDateTime>() {

                /**
                 * localDateTime deserializer
                 * <li/>日期时间字符串
                 * <li/>日期字符串
                 * <li/>毫秒时间戳
                 *
                 * @param p Parsed used for reading JSON content
                 * @param ctxt Context that can be used to access information about
                 *   this deserialization activity.
                 *
                 * @return localDateTime
                 * @throws IOException -
                 */
                @Override
                public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                    String text = p.getText();
                    if (text == null || text.isEmpty()) {
                        return null;
                    }

                    try {
                        return LocalDateTime.parse(text, dateTimeFormatter);
                    } catch (DateTimeParseException ignored) {
                        try {
                            return LocalDate.parse(text, dateFormatter).atStartOfDay();
                        } catch (DateTimeParseException e) {
                            long mills = Long.parseLong(text);
                            return LocalDateTime.ofInstant(Instant.ofEpochMilli(mills), ZoneId.systemDefault());
                        }
                    }
                }
            });

            builder.modules(javaTimeModule);
        };
    }
}
