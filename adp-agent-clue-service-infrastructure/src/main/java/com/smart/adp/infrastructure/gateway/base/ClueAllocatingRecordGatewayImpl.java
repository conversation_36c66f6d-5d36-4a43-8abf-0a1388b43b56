package com.smart.adp.infrastructure.gateway.base;

import com.smart.adp.domain.entity.clue.ClueAllocatingRecord;
import com.smart.adp.domain.gateway.base.ClueAllocatingRecordGateway;
import com.smart.adp.infrastructure.repository.base.ClueAllocatingRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/20 14:33
 * @description 线索分配日志
 **/
@Slf4j
@Component
public class ClueAllocatingRecordGatewayImpl implements ClueAllocatingRecordGateway {

    @Autowired
    private ClueAllocatingRecordMapper clueAllocatingRecordMapper;

    @Override
    public int batchSaveClueAllocatingRecord(List<ClueAllocatingRecord> clueAllocatingRecordList) {
        return clueAllocatingRecordMapper.insertBatch(clueAllocatingRecordList);
    }
}
