package com.smart.adp.infrastructure.utils;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName LoggingInterceptor
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/7/20 1:13
 * @Version 1.0
 */
@Slf4j
public class LoggingRequestInterceptor implements RequestInterceptor {

    /**
     * Called for every request. Add data using methods on the supplied {@link RequestTemplate}.
     *
     * @param template
     */
    @Override
    public void apply(RequestTemplate template) {
        // 在发出请求之前记录参数
        log.info("Request - Method: " + template.method() + ", URL: " + template.url() + ", Headers: " + template.headers() + ", Body: " + template.body());
    }
}
