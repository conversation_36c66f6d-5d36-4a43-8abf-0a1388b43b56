package com.smart.adp.infrastructure.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import com.smart.adp.domain.entity.base.AgentEmployee;
import com.smart.adp.domain.entity.base.ClueMsgRecord;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.message.ReviewDisTributeMessage;
import com.smart.adp.domain.enums.EmployeeStatusEnum;
import com.smart.adp.domain.enums.MessageTypeEnum;
import com.smart.adp.domain.gateway.clue.AgentEmployeeGateway;
import com.smart.adp.domain.gateway.clue.ClueDlrGateway;
import com.smart.adp.domain.gateway.clue.ClueMsgRecordGateway;
import com.smart.adp.domain.service.clue.IClueDlrService;
import com.smart.adp.infrastructure.feign.XApiFeignService;
import com.smart.adp.infrastructure.utils.ProvinceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

import static com.smart.adp.domain.common.constants.MQConstants.DISTRIBUTE_CLUE_QUEUE;

/**
 * <AUTHOR>
 * date 2025/3/10 14:58
 * @description 店长分配线索通知CDP 消费者
 **/
@Slf4j
@Component
public class ClueDistributeConsumer {

    private static final String CDP_EVENT_CODE = "ADP_INSERT_CDP_LEADS";
    private static final String CLUE_ASSIGN_MESSAGE_CONTENT = "有条线索分配，请及时跟进";
    private static final String ADP_SYSTEM = "ADP";
    private static final String STORE_SOURCE = "门店";
    private static final String REMARK_CLUE_ASSIGN = "线索分配";

    @Autowired
    private XApiFeignService xApiFeignService;

    @Autowired
    private AgentEmployeeGateway agentEmployeeGateway;

    @Autowired
    private IClueDlrService clueDlrService;

    @Autowired
    private ClueDlrGateway clueDlrGateway;

    @Autowired
    private ClueMsgRecordGateway clueMsgRecordGateway;

    @Autowired
    private ProvinceUtil provinceUtil;

    @RabbitListener(queues = DISTRIBUTE_CLUE_QUEUE)
    public void noticeCdp(@Payload ReviewDisTributeMessage message, Channel channel,
                          @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        try {
            log.info("店长分配线索通知CDP消费消息开始,message={}", JSONUtil.toJsonStr(message));
            if (Objects.nonNull(message)) {
                this.dealClueDlrSaveData(message);
            }
        } catch (Exception e) {
            log.error("店长分配线索通知CDP异常", e);
        } finally {
            channel.basicAck(tag, false);
        }
    }

    /**
     * 处理adp-cdp通知
     *
     * @param message
     */
    private void dealClueDlrSaveData(ReviewDisTributeMessage message) {
        String reviewId = message.getReviewId();
        String reviewPersonId = message.getReviewPersonId();
        String reviewPersonName = message.getReviewPersonName();
        Boolean isDefeatedDis = message.getIsDefeatedDis();
        String dlrCode = message.getDlrCode();
        String dlrShortName = message.getDlrShortName();

        // 1. 处理销售顾问信息查询
        AgentEmployee agentEmployee = getAgentEmployee(reviewPersonId);

        // 2. 处理店长分配消息
        processStoreManagerMessage(message, agentEmployee, dlrCode);

        // 3. 通知CDP系统
        notifyCdpSystem(message, reviewId, isDefeatedDis, dlrCode, dlrShortName, reviewPersonName, reviewPersonId, agentEmployee);
    }

    //获取雇员信息
    private AgentEmployee getAgentEmployee(String reviewPersonId) {
        if (StrUtil.isBlank(reviewPersonId)) {
            return null;
        }
        List<String> statusList = Collections.singletonList(EmployeeStatusEnum.WORKING.getCode());
        return agentEmployeeGateway.findOne(reviewPersonId, statusList);
    }

    //校验是否存在最新的一条消息
    private void processStoreManagerMessage(ReviewDisTributeMessage message, AgentEmployee agentEmployee, String dlrCode) {
        if (!shouldProcessStoreManagerMessage(message, agentEmployee)) {
            return;
        }

        ClueMsgRecord existingRecord = findExistingClueMsgRecord(message, agentEmployee, dlrCode);
        if (existingRecord != null) {
            updateClueMsgRecord(existingRecord, message.getAssignPersonId(), message.getAssignPersonName());
        } else {
            createNewClueMsgRecord(message, agentEmployee, dlrCode);
        }
    }

    private boolean shouldProcessStoreManagerMessage(ReviewDisTributeMessage message, AgentEmployee agentEmployee) {
        return StringUtils.isNotEmpty(message.getAssignPersonId())
                && !Objects.equals(message.getAssignPersonId(), message.getReviewPersonId())
                && MessageTypeEnum.CLUE_ASSIGNMENT.getCode().equals(message.getScenario())
                && agentEmployee != null;
    }

    private ClueMsgRecord findExistingClueMsgRecord(ReviewDisTributeMessage message, AgentEmployee agentEmployee, String dlrCode) {
        ClueMsgRecord query = new ClueMsgRecord();
        query.setDlrCode(dlrCode);
        query.setReceiveEmpId(agentEmployee.getEmpId());
        query.setIsRead("0");
        query.setMessageType(message.getMessageType());

        List<ClueMsgRecord> records = clueMsgRecordGateway.findClueMsgRecords(query);
        return CollUtil.isNotEmpty(records) ? records.get(0) : null;
    }

    //跟新站内信消息
    private void updateClueMsgRecord(ClueMsgRecord existingRecord, String assignPersonId, String assignPersonName) {
        String currentValue = existingRecord.getBusiKeyValue();
        int newValue = StrUtil.isNotBlank(currentValue) ? Integer.parseInt(currentValue) + 1 : 1;

        ClueMsgRecord updateRecord = new ClueMsgRecord();
        updateRecord.setMessageId(existingRecord.getMessageId());
        updateRecord.setBusiKeyValue(String.valueOf(newValue));
        updateRecord.setLastUpdatedDate(LocalDateTime.now());
        updateRecord.setUpdateControlId(UUID.randomUUID().toString());
        updateRecord.setModify(assignPersonId);
        updateRecord.setModifyName(assignPersonName);

        clueMsgRecordGateway.modifyClueMsgRecord(updateRecord);
    }

    //新建站内信消息
    private void createNewClueMsgRecord(ReviewDisTributeMessage message, AgentEmployee agentEmployee, String dlrCode) {
        ClueMsgRecord newRecord = new ClueMsgRecord();
        LocalDateTime time = LocalDateTime.now();
        newRecord.setMessageId(UUID.randomUUID().toString());
        newRecord.setMessageContent(CLUE_ASSIGN_MESSAGE_CONTENT);
        newRecord.setUpdateControlId(UUID.randomUUID().toString());
        newRecord.setModify(message.getAssignPersonId());
        newRecord.setModifyName(message.getAssignPersonName());
        newRecord.setCreatedDate(time);
        newRecord.setLastUpdatedDate(time);
        newRecord.setCreator(message.getAssignPersonId());
        newRecord.setCreatedName(message.getAssignPersonName());
        newRecord.setReceiveEmpId(agentEmployee.getEmpId());
        newRecord.setDlrCode(dlrCode);
        newRecord.setMessageType(message.getMessageType());
        newRecord.setIsRead("0");
        newRecord.setPhone(message.getReviewPersonPhone());
        newRecord.setBusiKeyValue("1");

        clueMsgRecordGateway.saveClueMsgRecord(newRecord);
    }

    private void notifyCdpSystem(ReviewDisTributeMessage message, String reviewId, Boolean isDefeatedDis,
                                 String dlrCode, String dlrShortName, String reviewPersonName,
                                 String reviewPersonId, AgentEmployee agentEmployee) {
        try {
            Object clueInfo = isDefeatedDis
                    ? clueDlrGateway.findDefeatedClueByReviewId(reviewId)
                    : clueDlrGateway.findActiveClueByReviewId(reviewId);

            if (clueInfo == null) {
                return;
            }

            Map<String, Object> noticeCdpMap = buildCdpMap(clueInfo, dlrShortName, dlrCode,
                    reviewPersonName, reviewPersonId, agentEmployee);
            sendCdpNotification(noticeCdpMap, getPhoneFromClueInfo(clueInfo));
        } catch (Exception e) {
            log.error("线索分配通知CDP异常 reviewId={}", reviewId, e);
        }
    }

    /**
     * 构建传递cdp入参模型
     *
     * @param clueInfo
     * @param dlrShortName
     * @param dlrCode
     * @param reviewPersonName
     * @param reviewPersonId
     * @param agentEmployee
     * @return
     */
    private Map<String, Object> buildCdpMap(Object clueInfo, String dlrShortName, String dlrCode,
                                            String reviewPersonName, String reviewPersonId,
                                            AgentEmployee agentEmployee) {
        Map<String, Object> map = new HashMap<>(33);

        // 公共字段
        map.put("c_lastupdate_system", ADP_SYSTEM);
        map.put("source", STORE_SOURCE);
        map.put("remark", REMARK_CLUE_ASSIGN);
        map.put("c_store", dlrShortName);
        map.put("c_store_code", dlrCode);
        map.put("c_store_name", dlrShortName);
        map.put("c_seller", reviewPersonName);
        map.put("c_seller_id", reviewPersonId);

        if (agentEmployee != null) {
            map.put("c_seller_phone", agentEmployee.getMobile());
            map.put("c_seller_url", agentEmployee.getHeadPortrait());
        }

        // 动态字段处理
        if (clueInfo instanceof SacAllClueInfoDlr) {
            populateCdpMapFromSacAll((SacAllClueInfoDlr) clueInfo, map);
        } else if (clueInfo instanceof SacClueInfoDlr) {
            populateCdpMapFromSac((SacClueInfoDlr) clueInfo, map);
        }

        return MapUtil.removeNullValue(map);
    }

    /**
     * 构建战败线索通知对象
     *
     * @param info
     * @param map
     */
    private void populateCdpMapFromSacAll(SacAllClueInfoDlr info, Map<String, Object> map) {
        map.put("bk", info.getPhone());
        map.put("mobile", info.getPhone());
        map.put("c_smartid", info.getColumn10());
        map.put("name", info.getCustName());
        // 2506 大区设置省份特殊处理
        map.put("c_province", provinceUtil.getMappingProvince(info.getProvinceName()));// 省份
        map.put("c_city", info.getCityName());// 城市
        map.put("c_county", info.getCountyName());// 地区
        map.put("c_gender", info.getGenderName());// 性别
        // "用户注册/留资最早来源渠道（=一级来源）"
        map.put("c_register_channel", info.getChannelName());
        if (StrUtil.isNotBlank(info.getChannelCode())) {
            map.put("c_second_channel", info.getChannelCode());// 二级来源
        }
        if (StrUtil.isNotBlank(info.getInfoChanDCode())) {
            map.put("c_third_channel", info.getInfoChanDCode());// 三级来源
        }
        map.put("c_cus_source", info.getInfoChanDName());// 渠道描述
        map.put("c_heat_name", info.getColumn5());// 热度名称
        map.put("c_heat_code", info.getColumn6());// 热度编码
        map.put("c_interested_car_model", info.getIntenCarTypeName());// 意向车型
        map.put("c_interested_car_colour", info.getInnerColorName());// 意向车型颜色
        map.put("c_interested_car_model", info.getIntenCarTypeName());// 意向车型
        map.put("c_interested_car_colour", info.getInnerColorName());// 意向车型颜色
    }

    /**
     * 构建活跃线索分配对象
     *
     * @param info
     * @param map
     */
    private void populateCdpMapFromSac(SacClueInfoDlr info, Map<String, Object> map) {
        map.put("bk", info.getPhone());
        map.put("mobile", info.getPhone());
        map.put("c_smartid", info.getColumn10());
        map.put("name", info.getCustName());
        // 2506 大区设置省份特殊处理
        map.put("c_province", provinceUtil.getMappingProvince(info.getProvinceName()));// 省份        map.put("c_city", info.getCityName())
        // ;// 城市
        map.put("c_county", info.getCountyName());// 地区
        map.put("c_gender", info.getGenderName());// 性别
        // "用户注册/留资最早来源渠道（=一级来源）"
        map.put("c_register_channel", info.getChannelName());
        if (StrUtil.isNotBlank(info.getChannelCode())) {
            map.put("c_second_channel", info.getChannelCode());// 二级来源
        }
        if (StrUtil.isNotBlank(info.getInfoChanDCode())) {
            map.put("c_third_channel", info.getInfoChanDCode());// 三级来源
        }
        map.put("c_cus_source", info.getInfoChanDName());// 渠道描述
        map.put("c_heat_name", info.getColumn5());// 热度名称
        map.put("c_heat_code", info.getColumn6());// 热度编码
        map.put("c_interested_car_model", info.getIntenCarTypeName());// 意向车型
        map.put("c_interested_car_colour", info.getInnerColorName());// 意向车型颜色
        map.put("c_interested_car_model", info.getIntenCarTypeName());// 意向车型
        map.put("c_interested_car_colour", info.getInnerColorName());// 意向车型颜色
    }

    private String getPhoneFromClueInfo(Object clueInfo) {
        if (clueInfo instanceof SacAllClueInfoDlr) {
            return ((SacAllClueInfoDlr) clueInfo).getPhone();
        } else if (clueInfo instanceof SacClueInfoDlr) {
            return ((SacClueInfoDlr) clueInfo).getPhone();
        }
        return "";
    }

    private void sendCdpNotification(Map<String, Object> noticeCdpMap, String phone) {
        Map<String, Object> param = Collections.singletonMap("mapParam", noticeCdpMap);
        log.info("线索分配通知CDP开始,phone={}", phone);
        try {
            Map map = xApiFeignService.sendCancleData(CDP_EVENT_CODE, param);
            log.info("线索分配通知CDP结束,phone={},map={}", phone, JSONUtil.toJsonStr(map));
        } catch (Exception e) {
            log.error("线索分配通知CDP异常", e);
        }

    }
}
