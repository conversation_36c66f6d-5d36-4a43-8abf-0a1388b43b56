package com.smart.adp.infrastructure.repository.clue;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.clue.SacReviewHis;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 回访mapper
 * @Author: rik.ren
 * @Date: 2025/3/5 20:42
 **/
public interface ReviewHisMapper extends BaseMapper<SacReviewHis> {


    int updateSacReviewHisBatch(@Param("sacReviewHis") List<SacReviewHis> sacReviewHis);

}
