package com.smart.adp.infrastructure.adapter;

import com.smart.adp.domain.adapter.IAdapterClueRequestEntity;
import com.smart.adp.infrastructure.feign.request.QueryActiveInfoReq;

import java.util.List;

/**
 * @Description: 适配器的抽象实现类，目的后续的转换类不用实现全部的接口
 * @Author: rik.ren
 * @Date: 2025/03/11 18:10
 **/
public abstract class AbstractIAdapterClueConvent implements IAdapterClueRequestEntity {
    @Override
    public QueryActiveInfoReq queryClueActiveInfo(List<String> param) {
        return null;
    }

}
