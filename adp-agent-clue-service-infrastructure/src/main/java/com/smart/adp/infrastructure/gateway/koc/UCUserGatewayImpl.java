package com.smart.adp.infrastructure.gateway.koc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.smart.adp.domain.bo.koc.KocUserInfoBO;
import com.smart.adp.domain.gateway.koc.UCUserGateway;
import com.smart.adp.infrastructure.feign.UCUserFeign;
import com.smart.adp.infrastructure.feign.dto.UCUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: UC用户网关实现
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Slf4j
@Service
public class UCUserGatewayImpl implements UCUserGateway {

    @Autowired
    private UCUserFeign ucUserFeign;

    @Override
    public KocUserInfoBO findBySmartId(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return null;
        }
        try {
            UCUserDTO ucUserDTO = ucUserFeign.findBySmartId(smartId);
            return convertToKocUserInfoBO(ucUserDTO);
        } catch (Exception e) {
            log.error("根据smartId查询用户信息失败, smartId={}", smartId, e);
            return null;
        }
    }

    @Override
    public KocUserInfoBO findByPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return null;
        }
        try {
            UCUserDTO ucUserDTO = ucUserFeign.findByPhone(phone);
            return convertToKocUserInfoBO(ucUserDTO);
        } catch (Exception e) {
            log.error("根据手机号查询用户信息失败, phone={}", phone, e);
            return null;
        }
    }

    @Override
    public List<KocUserInfoBO> findByNickNameLike(String nickName) {
        if (StrUtil.isBlank(nickName)) {
            return new ArrayList<>();
        }
        try {
            List<UCUserDTO> ucUserDTOs = ucUserFeign.findByNickNameLike(nickName);
            return convertToKocUserInfoBOs(ucUserDTOs);
        } catch (Exception e) {
            log.error("根据昵称模糊查询用户信息失败, nickName={}", nickName, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<KocUserInfoBO> searchUsers(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new ArrayList<>();
        }
        try {
            List<UCUserDTO> ucUserDTOs = ucUserFeign.searchUsers(keyword);
            return convertToKocUserInfoBOs(ucUserDTOs);
        } catch (Exception e) {
            log.error("根据关键字搜索用户失败, keyword={}", keyword, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<KocUserInfoBO> findBySmartIds(List<String> smartIds) {
        if (CollectionUtil.isEmpty(smartIds)) {
            return new ArrayList<>();
        }
        try {
            List<UCUserDTO> ucUserDTOs = ucUserFeign.findBySmartIds(smartIds);
            return convertToKocUserInfoBOs(ucUserDTOs);
        } catch (Exception e) {
            log.error("批量根据smartId查询用户信息失败, smartIds={}", smartIds, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<KocUserInfoBO> findByPhones(List<String> phones) {
        if (CollectionUtil.isEmpty(phones)) {
            return new ArrayList<>();
        }
        try {
            List<UCUserDTO> ucUserDTOs = ucUserFeign.findByPhones(phones);
            return convertToKocUserInfoBOs(ucUserDTOs);
        } catch (Exception e) {
            log.error("批量根据手机号查询用户信息失败, phones={}", phones, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Boolean existsBySmartId(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return false;
        }
        try {
            return ucUserFeign.existsBySmartId(smartId);
        } catch (Exception e) {
            log.error("检查用户是否存在失败, smartId={}", smartId, e);
            return false;
        }
    }

    @Override
    public Boolean existsByPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        try {
            return ucUserFeign.existsByPhone(phone);
        } catch (Exception e) {
            log.error("检查手机号是否存在失败, phone={}", phone, e);
            return false;
        }
    }

    @Override
    public List<KocUserInfoBO> findByCity(String city) {
        if (StrUtil.isBlank(city)) {
            return new ArrayList<>();
        }
        try {
            List<UCUserDTO> ucUserDTOs = ucUserFeign.findByCity(city);
            return convertToKocUserInfoBOs(ucUserDTOs);
        } catch (Exception e) {
            log.error("根据城市查询用户失败, city={}", city, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<KocUserInfoBO> findByProvince(String province) {
        if (StrUtil.isBlank(province)) {
            return new ArrayList<>();
        }
        try {
            List<UCUserDTO> ucUserDTOs = ucUserFeign.findByProvince(province);
            return convertToKocUserInfoBOs(ucUserDTOs);
        } catch (Exception e) {
            log.error("根据省份查询用户失败, province={}", province, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<KocUserInfoBO> findByUserLevel(String userLevel) {
        if (StrUtil.isBlank(userLevel)) {
            return new ArrayList<>();
        }
        try {
            List<UCUserDTO> ucUserDTOs = ucUserFeign.findByUserLevel(userLevel);
            return convertToKocUserInfoBOs(ucUserDTOs);
        } catch (Exception e) {
            log.error("根据用户等级查询用户失败, userLevel={}", userLevel, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<KocUserInfoBO> findVipUsers() {
        try {
            List<UCUserDTO> ucUserDTOs = ucUserFeign.findVipUsers();
            return convertToKocUserInfoBOs(ucUserDTOs);
        } catch (Exception e) {
            log.error("查询VIP用户失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换单个用户信息
     */
    private KocUserInfoBO convertToKocUserInfoBO(UCUserDTO ucUserDTO) {
        if (ObjectUtil.isEmpty(ucUserDTO)) {
            return null;
        }

        return KocUserInfoBO.builder()
                .smartId(ucUserDTO.getSmartId())
                .phone(ucUserDTO.getPhone())
                .nickName(ucUserDTO.getNickName())
                .avatar(ucUserDTO.getAvatar())
                .gender(ucUserDTO.getGender())
                .age(ucUserDTO.getAge())
                .city(ucUserDTO.getCity())
                .province(ucUserDTO.getProvince())
                .registerTime(ucUserDTO.getRegisterTime())
                .lastActiveTime(ucUserDTO.getLastActiveTime())
                .isVip(ucUserDTO.getIsVip())
                .userLevel(ucUserDTO.getUserLevel())
                .points(ucUserDTO.getPoints())
                .build();
    }

    /**
     * 批量转换用户信息
     */
    private List<KocUserInfoBO> convertToKocUserInfoBOs(List<UCUserDTO> ucUserDTOs) {
        if (CollectionUtil.isEmpty(ucUserDTOs)) {
            return new ArrayList<>();
        }

        return ucUserDTOs.stream()
                .map(this::convertToKocUserInfoBO)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());
    }
}
