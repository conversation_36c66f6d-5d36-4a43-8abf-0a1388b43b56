package com.smart.adp.infrastructure.handler;

import com.smart.adp.domain.entity.clue.CustEventFlow;
import com.smart.adp.domain.entity.message.CustEventMessage;
import com.smart.adp.domain.enums.ClueStageEnum;
import com.smart.adp.domain.gateway.clue.CustEventFlowGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/21
 */
@Component
public class DefeatEventHandler implements CustEventHandler {

    @Autowired
    private CustEventFlowGateway custEventFlowGateway;

    @Override
    public void handle(CustEventMessage message) {
        // 战败不体现在 线索阶段
        ClueStageEnum stage = ClueStageEnum.DEFEAT;
        CustEventFlow flow = message.buildFlow();
        flow.setStage(stage.getCode());
        custEventFlowGateway.insertSelective(flow);
    }
}
