package com.smart.adp.infrastructure.config.es;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.nio.conn.ssl.SSLIOSessionStrategy;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.config.AbstractElasticsearchConfiguration;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;

import javax.net.ssl.*;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;

/**
 * 华为云https无秘钥配置请求elasticsearch
 */
@Configuration
@Slf4j
public class ESSecuredClientConfig extends AbstractElasticsearchConfiguration {


    @Value("${spring.elasticsearch.rest.uris}")
    private String url;

    @Value("${spring.elasticsearch.rest.port}")
    private Integer port;

    @Value("${spring.elasticsearch.rest.username}")
    private String username;

    @Value("${spring.elasticsearch.rest.password}")
    private String password;

    private static final String SSL = "SSL";

    @Bean
    public RestHighLevelClient elasticsearchClient() {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));

        SSLContext sc = null;
        try {
            sc = SSLContext.getInstance(SSL);
            sc.init(null, trustAllCerts, new SecureRandom());
        } catch (KeyManagementException | NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        List<HttpHost> httpHostList = new ArrayList<>();
        HttpHost httpHost = null;
        if (StringUtils.contains(url, ",")) {
            //集群配置
            String[] ipCluster = StringUtils.split(url, ",");
            for (String ip : ipCluster) {
                httpHost = new HttpHost(ip, port, "http");
                httpHostList.add(httpHost);
            }
        } else {
            //单机
            httpHost = new HttpHost(url, port, "http");
            httpHostList.add(httpHost);
        }


        SSLIOSessionStrategy sessionStrategy = new SSLIOSessionStrategy(sc, new NullHostNameVerifier());
        SecuredHttpClientConfigCallback httpClientConfigCallback = new SecuredHttpClientConfigCallback(sessionStrategy, credentialsProvider) {
            @Override
            public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                // 先应用原有的安全配置
                HttpAsyncClientBuilder securedBuilder = super.customizeHttpClient(httpClientBuilder);

                // 添加日志拦截器
                securedBuilder.addInterceptorFirst(new LoggingInterceptor.RequestLoggingInterceptor());

                return securedBuilder;
            }
        };

        RestClientBuilder builder = RestClient.builder(httpHostList.toArray(new HttpHost[httpHostList.size()]))
                                              .setHttpClientConfigCallback(httpClientConfigCallback);
        return new RestHighLevelClient(builder);
    }

    @Bean(name = {"elasticsearchOperations", "elasticsearchRestTemplate"})
    public ElasticsearchRestTemplate elasticsearchTemplate() {
        return new ElasticsearchRestTemplate(elasticsearchClient());
    }

    static TrustManager[] trustAllCerts = new TrustManager[]{
            new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    //修复代码检查严重问题，没有做客户端的证书校验
                    if (chain == null) {
                        throw new IllegalArgumentException("checkServerTrusted:x509Certificate array isnull");
                    }
                }

                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    //修复代码检查严重问题，没有做服务端的证书校验
                    if (chain == null) {
                        throw new IllegalArgumentException("checkServerTrusted:x509Certificate array isnull");
                    }
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
            }
    };


    public static class NullHostNameVerifier implements HostnameVerifier {
        static final Boolean VERIFY_RETURN_VALUE = true;

        @Override
        public boolean verify(String arg0, SSLSession arg1) {
            return VERIFY_RETURN_VALUE;
        }
    }


}