package com.smart.adp.infrastructure.feign;

import com.smart.adp.infrastructure.feign.response.cdp.CdpClueVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * date 2025/6/16 14:13
 * @description cdp feign
 **/
@FeignClient(name = "cdpApiClient", url = "${refer.url.cdp.api}")
public interface CdpFeignService {

    /**
     * 查询CDP线索
     *
     * @param mobile
     * @return
     */
    @GetMapping("/v3/leads/leads/{mobile}")
    CdpClueVO getCdpClue(@PathVariable("mobile") String mobile);
}
