package com.smart.adp.infrastructure.helper;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.mybatisflex.core.row.Row;
import com.mybatisflex.core.util.CollectionUtil;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.enums.ClueStageEnum;
import com.smart.adp.domain.enums.ClueStatusEnum;
import com.smart.adp.domain.enums.CustEventEnum;
import com.smart.adp.domain.enums.EnableEnum;
import com.smart.adp.domain.valueObject.clue.SacOnecustResumeVO;
import com.smart.adp.infrastructure.handler.CreateEventHandler;
import com.smart.adp.infrastructure.handler.DriveEventHandler;
import com.smart.adp.infrastructure.handler.ReviewEventHandler;
import com.smart.adp.infrastructure.repository.clue.AllClueDlrMapper;
import com.smart.adp.infrastructure.repository.clue.ClueDlrMapper;
import com.smart.adp.infrastructure.repository.clue.CustEventFlowMapper;
import com.smart.adp.infrastructure.repository.clue.SacOnecustResumeMapper;
import com.smart.adp.infrastructure.repository.drive.DriveVehicleDataMapper;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveSheetMapper;
import com.smart.adp.infrastructure.repository.order.SaleOrderLogMapper;
import com.smart.adp.infrastructure.repository.order.SaleOrderMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.smart.adp.domain.common.constants.BizConstants.*;
import static com.smart.adp.domain.common.constants.StringConstant.*;
import static com.smart.adp.domain.entity.clue.table.CustEventFlowTableDef.CUST_EVENT_FLOW;
import static com.smart.adp.domain.entity.drive.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;
import static com.smart.adp.domain.entity.drive.table.TestDriveVehicleDataTableDef.TEST_DRIVE_VEHICLE_DATA;
import static com.smart.adp.domain.entity.order.table.VeBuSaleOrderLogTableDef.VE_BU_SALE_ORDER_LOG;
import static com.smart.adp.domain.entity.order.table.VeBuSaleOrderToCTableDef.VE_BU_SALE_ORDER_TO_C;
import static com.smart.adp.domain.valueObject.clue.table.SacOnecustResumeVOTableDef.SAC_ONECUST_RESUME_VO;

/**
 * <p>
 * 用户旅程 helper
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/26
 */
@Slf4j
@Component
public class UserJourneysHelper {

    @Autowired
    private SacOnecustResumeMapper onecustResumeMapper;

    @Autowired
    private SacTestDriveSheetMapper driveSheetMapper;

    @Autowired
    private DriveVehicleDataMapper driveVehicleDataMapper;

    @Autowired
    private SaleOrderMapper saleOrderMapper;

    @Autowired
    private SaleOrderLogMapper saleOrderLogMapper;

    @Autowired
    private CustEventFlowMapper custEventFlowMapper;

    @Autowired
    private ClueDlrMapper clueDlrMapper;

    @Autowired
    @Qualifier("taskExecutor")
    private Executor taskExecutor;

    @Autowired
    @Qualifier("calcExecutor")
    private Executor calcExecutor;

    private static final int CALC_TASK_SIZE = 200;

    public void stageFix(List<SacClueInfoDlr> clues) {
        if (CollectionUtil.isEmpty(clues)) {
            return;
        }

        CompletableFuture.runAsync(() -> {
            List<String> custIds = clues.stream()
                                        .map(SacClueInfoDlr::getCustId)
                                        .collect(Collectors.toList());

            QueryWrapper wrapper = QueryWrapper.create()
                                               .select(QueryMethods.max(CUST_EVENT_FLOW.STAGE).as(CUST_EVENT_FLOW.STAGE.getName()), CUST_EVENT_FLOW.CUST_ID)
                                               .and(CUST_EVENT_FLOW.CUST_ID.in(custIds))
                                               .and(CUST_EVENT_FLOW.STAGE.ne(ClueStageEnum.DEFEAT.getCode()))
                                               .groupBy(CUST_EVENT_FLOW.CUST_ID);
            List<Row> rows = Db.selectListByQuery(CUST_EVENT_FLOW.getSchema(), CUST_EVENT_FLOW.getTableName(), wrapper);
            Map<String, String> stageMap = rows.stream()
                                               .collect(Collectors.toMap(row -> row.getString(CUST_EVENT_FLOW.CUST_ID.getName()), row -> row.getString(CUST_EVENT_FLOW.STAGE.getName())));

            List<SacClueInfoDlr> activeList = new ArrayList<>();
            List<SacClueInfoDlr> defeatList = new ArrayList<>();
            for (SacClueInfoDlr clue : clues) {
                if (ClueStatusEnum.DEFEATED.getCode().equals(clue.getStatusCode())) {
                    defeatList.add(clue);
                } else {
                    activeList.add(clue);
                }
            }

            Db.executeBatch(activeList, ClueDlrMapper.class, (mapper, clue) -> {
                String stage = stageMap.get(clue.getCustId());
                if (Objects.nonNull(stage)) {
                    SacClueInfoDlr updateClue = new SacClueInfoDlr();
                    updateClue.setColumn18(stage);
                    updateClue.setId(clue.getId());
                    mapper.update(updateClue);
                }
            });

            Db.executeBatch(defeatList, AllClueDlrMapper.class, (mapper, clue) -> {
                String stage = stageMap.get(clue.getCustId());
                if (Objects.nonNull(stage)) {
                    SacAllClueInfoDlr updateClue = new SacAllClueInfoDlr();
                    updateClue.setColumn18(stage);
                    updateClue.setId(clue.getId());
                    mapper.update(updateClue);
                }
            });
            log.info("stage fixed {}", custIds);
        }, taskExecutor).exceptionally(ex -> {
            log.error("stage fix exception", ex);
            return null;
        });
    }

    /**
     * <p>根据周边数据 补充线索流转流水<p/>
     *
     * <li/>get all data by clues
     * <li/>calc flows
     * <li/>batch insert
     *
     * @param clues 线索列表
     */
    public void flowFix(List<SacClueInfoDlr> clues) {
        if (CollectionUtil.isEmpty(clues)) {
            return;
        }

        List<String> custIds = clues.stream()
                                    .map(SacClueInfoDlr::getCustId)
                                    .collect(Collectors.toList());

        CompletableFuture<Void> flowsFuture = CompletableFuture.runAsync(() -> deleteFlows(custIds), taskExecutor);
        CompletableFuture<Map<String, List<SacOnecustResumeVO>>> resumesFuture = CompletableFuture.supplyAsync(() -> getResumesMap(custIds), taskExecutor);
        CompletableFuture<Map<String, List<DriveDTO>>> drivesFuture = CompletableFuture.supplyAsync(() -> getDrivesMap(custIds), taskExecutor);
        CompletableFuture<Map<String, List<OrderDTO>>> ordersFuture = CompletableFuture.supplyAsync(() -> getOrdersMap(custIds), taskExecutor);
        CompletableFuture.allOf(flowsFuture, resumesFuture, drivesFuture, ordersFuture)
                         .thenAccept(v -> {
                             try {
                                 List<CompletableFuture<List<CustEventFlow>>> calcTasks = new ArrayList<>();

                                 // parallel calc
                                 Map<String, List<SacOnecustResumeVO>> resumesMap = resumesFuture.get();
                                 Map<String, List<DriveDTO>> drivesMap = drivesFuture.get();
                                 Map<String, List<OrderDTO>> ordersMap = ordersFuture.get();
                                 for (List<SacClueInfoDlr> list : Lists.partition(clues, CALC_TASK_SIZE)) {
                                     Supplier<List<CustEventFlow>> task = () -> calcFlows(list, resumesMap, drivesMap, ordersMap);
                                     calcTasks.add(CompletableFuture.supplyAsync(task, calcExecutor));
                                 }

                                 // join tasks
                                 List<CustEventFlow> insertFlows = CompletableFuture.allOf(calcTasks.toArray(new CompletableFuture[0]))
                                                                                    .thenApply(subV -> calcTasks.stream()
                                                                                                                .map(CompletableFuture::join)
                                                                                                                .flatMap(Collection::stream)
                                                                                                                .collect(Collectors.toList()))
                                                                                    .get();

                                 // insert flows
                                 custEventFlowMapper.insertBatchSelective(insertFlows);
                                 log.info("flows fixed {}", custIds);
                             } catch (InterruptedException | ExecutionException e) {
                                 throw new RuntimeException(e);
                             }
                         }).exceptionally(ex -> {
                             log.error("flows fix exception", ex);
                             return null;
                         });
    }

    private List<CustEventFlow> calcFlows(List<SacClueInfoDlr> clues, Map<String, List<SacOnecustResumeVO>> resumesMap,
                                          Map<String, List<DriveDTO>> drivesMap, Map<String, List<OrderDTO>> ordersMap) {
        List<CustEventFlow> res = new ArrayList<>();
        for (SacClueInfoDlr clue : clues) {
            boolean arriveFlag = false;
            String custId = clue.getCustId();
            if (StringUtil.noText(custId)) {
                continue;
            }

            // create
            JSONObject createJson = new JSONObject();
            createJson.put(CHANNEL_CODE, clue.getInfoChanDCode());
            CustEventFlow knowFlow = CustEventFlow.builder()
                                                  .custId(custId)
                                                  .type(CustEventEnum.CREATE.getCode())
                                                  .stage(ClueStageEnum.KNOW.getCode())
                                                  .businessId(clue.getServerOrder())
                                                  .eventTime(clue.getCreatedDate())
                                                  .extendJson(createJson)
                                                  .build();
            res.add(knowFlow);

            if (ARRIVE_CHANNEL_CODE.equals(clue.getInfoChanDCode())) {
                arriveFlag = true;
                JSONObject arriveJson = createJson.clone();
                arriveJson.put(SOURCE, CreateEventHandler.class.getSimpleName());
                CustEventFlow arriveFlow = CustEventFlow.builder()
                                                        .custId(custId)
                                                        .type(CustEventEnum.REVIEW.getCode())
                                                        .stage(ClueStageEnum.ARRIVE.getCode())
                                                        .businessId(clue.getServerOrder())
                                                        .eventTime(clue.getCreatedDate())
                                                        .extendJson(arriveJson)
                                                        .build();
                res.add(arriveFlow);
            }

            // review & defeat
            List<SacOnecustResumeVO> resumes = resumesMap.getOrDefault(custId, Collections.emptyList());
            for (SacOnecustResumeVO resume : resumes) {
                if (!arriveFlag && ARRIVE_REVIEW_TYPE_CODE_SET.contains(resume.getSenceCode())) {
                    arriveFlag = true;
                    JSONObject reviewJson = new JSONObject();
                    reviewJson.put(REVIEW_TYPE_CODE, resume.getSenceCode());
                    reviewJson.put(SOURCE, ReviewEventHandler.class.getSimpleName());
                    CustEventFlow arriveFlow = CustEventFlow.builder()
                                                            .custId(custId)
                                                            .type(CustEventEnum.REVIEW.getCode())
                                                            .stage(ClueStageEnum.ARRIVE.getCode())
                                                            .businessId(resume.getResumeId())
                                                            .eventTime(resume.getBussTime())
                                                            .extendJson(reviewJson)
                                                            .build();
                    res.add(arriveFlow);
                } else if (DEFEAT_SENCE_CODE.equals(resume.getSenceCode())) {
                    CustEventFlow defeatFlow = CustEventFlow.builder()
                                                            .custId(custId)
                                                            .type(CustEventEnum.DEFEAT.getCode())
                                                            .stage(ClueStageEnum.DEFEAT.getCode())
                                                            .businessId(resume.getResumeId())
                                                            .eventTime(resume.getBussTime())
                                                            .build();
                    res.add(defeatFlow);
                }
            }

            // drive
            List<DriveDTO> drives = drivesMap.getOrDefault(custId, Collections.emptyList());
            for (DriveDTO drive : drives) {
                CustEventFlow driveFlow = CustEventFlow.builder()
                                                       .custId(custId)
                                                       .type(CustEventEnum.DRIVE.getCode())
                                                       .stage(ClueStageEnum.DRIVE.getCode())
                                                       .businessId(drive.getTestDriveOrderNo())
                                                       .eventTime(drive.getStartTime())
                                                       .build();
                res.add(driveFlow);

                if (!arriveFlag) {
                    arriveFlag = true;
                    JSONObject reviewJson = new JSONObject();
                    reviewJson.put(SOURCE, DriveEventHandler.class.getSimpleName());
                    CustEventFlow arriveFlow = CustEventFlow.builder()
                                                            .custId(custId)
                                                            .type(CustEventEnum.REVIEW.getCode())
                                                            .stage(ClueStageEnum.ARRIVE.getCode())
                                                            .businessId(drive.getTestDriveOrderNo())
                                                            .eventTime(drive.getStartTime())
                                                            .extendJson(reviewJson)
                                                            .build();
                    res.add(arriveFlow);
                }
            }

            // order
            List<OrderDTO> orders = ordersMap.getOrDefault(custId, Collections.emptyList());
            // 根据订单号分组 排除退款
            Map<String, List<OrderDTO>> logsMap = orders.stream()
                                                        .collect(Collectors.groupingBy(OrderDTO::getSaleOrderCode));
            for (List<OrderDTO> orderLogs : logsMap.values()) {
                boolean orderFlag = false;
                boolean deliveryFlag = false;

                for (OrderDTO orderLog : orderLogs) {
                    if (!orderFlag && ORDER_STATE_ORDER.equals(orderLog.getLogState())) {
                        orderFlag = true;
                        CustEventFlow orderFlow = CustEventFlow.builder()
                                                               .custId(custId)
                                                               .type(CustEventEnum.ORDER.getCode())
                                                               .stage(ClueStageEnum.ORDER.getCode())
                                                               .businessId(orderLog.getSaleOrderCode())
                                                               .eventTime(orderLog.getLogDate())
                                                               .build();
                        res.add(orderFlow);
                    } else if (!deliveryFlag && ORDER_STATE_DELIVERY.equals(orderLog.getLogState())) {
                        deliveryFlag = true;
                        CustEventFlow deliveryFlow = CustEventFlow.builder()
                                                                  .custId(custId)
                                                                  .type(CustEventEnum.DELIVERY.getCode())
                                                                  .stage(ClueStageEnum.DELIVERY.getCode())
                                                                  .businessId(orderLog.getSaleOrderCode())
                                                                  .eventTime(orderLog.getLogDate())
                                                                  .build();
                        res.add(deliveryFlow);
                    }
                }
            }
        }
        return res;
    }

    /**
     * get flow map | cust_id -> flows
     *
     * @param custIds -
     * @return map
     */
    private Map<String, List<CustEventFlow>> getFlowsMap(List<String> custIds) {
        // 重复校验
        return Collections.emptyMap();
//        QueryWrapper wrapper = QueryWrapper.create()
//                                           .select(CUST_EVENT_FLOW.CUST_ID, CUST_EVENT_FLOW.TYPE, CUST_EVENT_FLOW.STAGE, CUST_EVENT_FLOW.BUSINESS_ID, CUST_EVENT_FLOW.EXTEND_JSON)
//                                           .and(CUST_EVENT_FLOW.CUST_ID.in(custIds));
//
//        return custEventFlowMapper.selectListByQuery(wrapper)
//                                  .stream()
//                                  .collect(Collectors.groupingBy(CustEventFlow::getCustId));

    }

    /**
     * delete flows
     *
     * @param custIds -
     */
    private void deleteFlows(List<String> custIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                                           .and(CUST_EVENT_FLOW.CUST_ID.in(custIds));

        custEventFlowMapper.deleteByQuery(wrapper);
    }

    /**
     * get resume map | cust_id -> resumes
     *
     * @param custIds -
     * @return map
     */
    private Map<String, List<SacOnecustResumeVO>> getResumesMap(List<String> custIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                                           .select(SAC_ONECUST_RESUME_VO.RESUME_ID, SAC_ONECUST_RESUME_VO.CUST_ID, SAC_ONECUST_RESUME_VO.SENCE_CODE, SAC_ONECUST_RESUME_VO.BUSS_TIME)
                                           .and(SAC_ONECUST_RESUME_VO.CUST_ID.in(custIds))
                                           .and(SAC_ONECUST_RESUME_VO.SENCE_CODE.in(FIX_RESUME_SENCE_CODE_LIST));

        return onecustResumeMapper.selectListByQuery(wrapper)
                                  .stream()
                                  .collect(Collectors.groupingBy(SacOnecustResumeVO::getCustId));
    }

    /**
     * get test drive map | cust_id -> drives
     *
     * @param custIds -
     * @return map
     */
    private Map<String, List<DriveDTO>> getDrivesMap(List<String> custIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                                           .select(SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID.as(DriveDTO::getCustId), SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_ORDER_NO, SAC_TEST_DRIVE_SHEET_ENTITY.START_TIME)
                                           .from(SAC_TEST_DRIVE_SHEET_ENTITY.as("d"))
                                           .leftJoin(TEST_DRIVE_VEHICLE_DATA.as("dv")).on(SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_ORDER_NO.eq(TEST_DRIVE_VEHICLE_DATA.TEST_DRIVE_ORDER_NO))
                                           .and(SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID.in(custIds))
                                           .and(TEST_DRIVE_VEHICLE_DATA.IS_VALID_TEST_DRIVE.eq(EnableEnum.ENABLE.isFlag()));

        return driveVehicleDataMapper.selectListByQueryAs(wrapper, DriveDTO.class)
                                     .stream()
                                     .collect(Collectors.groupingBy(DriveDTO::getCustId));
    }

    /**
     * get order map | cust_id -> orders
     *
     * @param custIds -
     * @return map
     */
    private Map<String, List<OrderDTO>> getOrdersMap(List<String> custIds) {
        QueryWrapper wrapper = QueryWrapper.create()
                                           .select(VE_BU_SALE_ORDER_TO_C.BUY_CUST_ID.as(OrderDTO::getCustId), VE_BU_SALE_ORDER_TO_C.SALE_ORDER_CODE, VE_BU_SALE_ORDER_TO_C.SALE_ORDER_STATE,
                                                   VE_BU_SALE_ORDER_LOG.SALE_ORDER_STATE.as(OrderDTO::getLogState), VE_BU_SALE_ORDER_LOG.LOG_DATE)
                                           .from(VE_BU_SALE_ORDER_TO_C.as("o"))
                                           .leftJoin(VE_BU_SALE_ORDER_LOG.as("ol")).on(VE_BU_SALE_ORDER_TO_C.SALE_ORDER_CODE.eq(VE_BU_SALE_ORDER_LOG.SALE_ORDER_CODE))
                                           .and(VE_BU_SALE_ORDER_TO_C.BUY_CUST_ID.in(custIds))
                                           .and(VE_BU_SALE_ORDER_TO_C.SALE_ORDER_STATE.ge(ORDER_STATE_ORDER))
                                           .and(VE_BU_SALE_ORDER_LOG.SALE_ORDER_STATE.in(FIX_ORDER_STATE_LIST));

        return saleOrderLogMapper.selectListByQueryAs(wrapper, OrderDTO.class)
                                 .stream()
                                 .filter(o -> !ORDER_STATE_RETURN.equals(o.getSaleOrderState()))
                                 .collect(Collectors.groupingBy(OrderDTO::getCustId));
    }

    @Getter
    @Setter
    @ToString
    @EqualsAndHashCode
    private static class DriveDTO {

        private String custId;

        private String testDriveOrderNo;

        private LocalDateTime startTime;
    }

    @Getter
    @Setter
    @ToString
    @EqualsAndHashCode
    private static class OrderDTO {

        private String custId;

        private String saleOrderCode;

        private String saleOrderState;

        private String LogState;

        private LocalDateTime logDate;
    }
}
