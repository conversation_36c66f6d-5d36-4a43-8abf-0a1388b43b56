package com.smart.adp.infrastructure.feign.response.koc;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: UC接口响应DTO
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Data
@ToString
public class UcRsp<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final String SUCCESS_CODE = "0";

    /**
     * 响应码： 0 成功
     */
    private String code;

    /**
     * 数据列表
     */
    private List<T> data;

    /**
     * 请求码响应码信息
     */
    private String msg;

    /**
     * 请求状态是否成功：'成功'：'SUCCEED','失败'：'FAILED',可用值:SUCCEED
     */
    private String status;

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return SUCCESS_CODE.equals(this.code);
    }
}
