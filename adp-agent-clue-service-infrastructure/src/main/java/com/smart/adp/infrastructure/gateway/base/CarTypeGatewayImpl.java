package com.smart.adp.infrastructure.gateway.base;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.gateway.base.CarTypeGateway;
import com.smart.adp.domain.valueObject.base.SmallCarTypeInfo;
import com.smart.adp.infrastructure.repository.base.CarTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.smart.adp.domain.valueObject.base.table.SmallCarTypeInfoTableDef.SMALL_CAR_TYPE_INFO;

/**
 * <AUTHOR>
 * date 2025/3/18 16:12
 * @description
 **/
@Slf4j
@Component
public class CarTypeGatewayImpl implements CarTypeGateway {

    @Autowired
    private CarTypeMapper carTypeMapper;

    @Override
    public List<SmallCarTypeInfo> queryCarTypeList() {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SMALL_CAR_TYPE_INFO.CAR_TYPE_CODE, SMALL_CAR_TYPE_INFO.CAR_TYPE_CODE_CN, SMALL_CAR_TYPE_INFO.CAR_TYPE_CODE_DESC)
                .eq(SmallCarTypeInfo::getIsEnable, "1")
                .orderBy(SmallCarTypeInfo::getCreatedDate)
                .asc();
        return carTypeMapper.selectListByQuery(wrapper);
    }
}
