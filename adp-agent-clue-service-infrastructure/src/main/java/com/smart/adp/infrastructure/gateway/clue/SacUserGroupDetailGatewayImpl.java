package com.smart.adp.infrastructure.gateway.clue;

import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.clue.SacUserGroupDetailBO;
import com.smart.adp.domain.common.annotation.SmartADPCache;
import com.smart.adp.domain.entity.clue.SacUserGroupDetailEntity;
import com.smart.adp.domain.gateway.clue.SacUserGroupDetailGateway;
import com.smart.adp.infrastructure.repository.clue.SacUserGroupDetailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.smart.adp.domain.entity.clue.table.SacUserGroupDetailEntityTableDef.SAC_USER_GROUP_DETAIL_ENTITY;

/**
 * @Description: 用户分组明细gateway实现
 * @Author: rik.ren
 * @Date: 2025/3/9 16:05
 **/
@Service
public class SacUserGroupDetailGatewayImpl implements SacUserGroupDetailGateway {
    @Autowired
    private SacUserGroupDetailMapper userGroupDetailMapper;

    /**
     * 根据实体条件查询集合
     *
     * @param param
     * @param needColumns
     * @return
     */
    @Override
    @SmartADPCache(value = "userGroupDetailList", key = "#param.custId", expire = 10, timeUnit = TimeUnit.MINUTES)
    public List<SacUserGroupDetailEntity> findListByCondition(SacUserGroupDetailEntity param, QueryColumn... needColumns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns);
        param.conditions(wrapper);
        return userGroupDetailMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns
     * @return
     */
    @Override
//    @SmartADPCache(value = "userGroupDetailObject", key = "#param.custId", expire = 10, timeUnit = TimeUnit.MINUTES)
    public SacUserGroupDetailEntity findByCondition(SacUserGroupDetailEntity param, QueryColumn... needColumns) {
        // 查询数据库
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(SAC_USER_GROUP_DETAIL_ENTITY.as("groupDetail"));
        param.conditions(wrapper);
        return userGroupDetailMapper.selectOneByQuery(wrapper);
    }

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns
     * @return
     */
    @Override
    public List<SacUserGroupDetailEntity> findByCondition(SacUserGroupDetailBO param, QueryColumn... needColumns) {
        // 查询数据库
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(SAC_USER_GROUP_DETAIL_ENTITY.as("groupDetail"));
        param.conditions(wrapper);
        return userGroupDetailMapper.selectListByQuery(wrapper);
    }

    /**
     * 更新线索对应的用户分组
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyUserGroupDetail(SacUserGroupDetailEntity param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("CUST_ID"), SqlConsts.EQUALS, param.getCustId());
        condition.and(QueryCondition.create(new QueryColumn("SERVER_ORDER"), SqlConsts.EQUALS, param.getServerOrder()));
        return userGroupDetailMapper.updateByCondition(param, condition) > 0;
    }
}
