package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.clue.SacOnecustInfoEventBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.gateway.clue.SacOnecustInfoEventGateway;
import com.smart.adp.domain.valueObject.clue.SacOnecustInfoEventVO;
import com.smart.adp.infrastructure.repository.clue.SacOnecustInfoEventMapper;
import com.smart.adp.infrastructure.utils.PageConverterFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.smart.adp.domain.valueObject.clue.table.SacOnecustInfoEventVOTableDef.SAC_ONECUST_INFO_EVENT_VO;

/**
 * @Description: 用户事件gateway实现
 * @Author: rik.ren
 * @Date: 2025/3/14 15:52
 **/
@Service
public class SacOnecustInfoEventGatewayImpl implements SacOnecustInfoEventGateway {
    @Autowired
    private SacOnecustInfoEventMapper sacOnecustInfoEventMapper;

    @Override
    public DomainPage<SacOnecustInfoEventVO> querycustEventDomainPage(SacOnecustInfoEventVO param,
                                                            SacOnecustInfoEventBO boParam,
                                                            QueryOrderBy orderBy, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_ONECUST_INFO_EVENT_VO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(SAC_ONECUST_INFO_EVENT_VO.as("event"));
        param.conditions(wrapper, boParam);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_ONECUST_INFO_EVENT_VO.EVENT_TIME, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        Page<SacOnecustInfoEventVO> paginate = sacOnecustInfoEventMapper.paginate(boParam.getPage().getPageNumber(),
                boParam.getPage().getPageSize()
                , wrapper);
        // 转换为领域分页对象
        DomainPage<SacOnecustInfoEventVO> domainPage = PageConverterFactory.toDomainPage(paginate);
        return domainPage;
    }
}
