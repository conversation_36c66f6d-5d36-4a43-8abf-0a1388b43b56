package com.smart.adp.infrastructure.feign.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "小结 VO")
public class SummaryVO {

    /**
     * 排名
     */
    @Schema(description = "排名")
    private Integer rank;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String userName;

    /**
     * 线索量
     */
    @Schema(description = "线索量")
    private Integer clueNum;

    /**
     * 线索量_总部
     */
    @Schema(description = "线索量_总部")
    private Integer hostClueNum;

    /**
     * 线索量_自建
     */
    @Schema(description = "线索量_自建")
    private Integer createClueNum;

    /**
     * 跟进线索量
     */
    @Schema(description = "跟进线索量")
    private Integer reviewClueNum;

    /**
     * 跟进线索量_首次
     */
    @Schema(description = "跟进线索量_首次")
    private Integer firstReviewClueNum;

    /**
     * 跟进线索量_及时
     */
    @Schema(description = "跟进线索量_及时")
    private Integer timelyReviewClueNum;

    /**
     * 跟进线索量_跟进量
     */
    @Schema(description = "跟进线索量_跟进量")
    private Integer reviewNum;

    /**
     * 外呼线索量
     */
    @Schema(description = "外呼线索量")
    private Integer outboundClueNum;

    /**
     * 外呼线索量_外呼
     */
    @Schema(description = "外呼线索量_外呼")
    private Integer outboundNum;

    /**
     * 外呼线索量_接通
     */
    @Schema(description = "外呼线索量_接通")
    private Integer connectedNum;

    /**
     * 外呼线索量_有效
     */
    @Schema(description = "外呼线索量_有效")
    private Integer validOutboundNum;

    /**
     * 到店线索量
     */
    @Schema(description = "到店线索量")
    private Integer arriveNum;

    /**
     * 到店线索量_首次
     */
    @Schema(description = "到店线索量_首次")
    private Integer firstArriveNum;

    /**
     * 到店线索量_二次
     */
    @Schema(description = "到店线索量_二次")
    private Integer secondArriveNum;

    /**
     * 到店线索量_多次
     */
    @Schema(description = "到店线索量_多次")
    private Integer multiArriveNum;

    /**
     * 试驾量
     */
    @Schema(description = "试驾量")
    private Integer driveNum;

    /**
     * 试驾量_匹配
     */
    @Schema(description = "试驾量_匹配")
    private Integer matchDriveNum;

    /**
     * 试驾量_有效
     */
    @Schema(description = "试驾量_有效")
    private Integer validDriveNum;

    /**
     * 订单量
     */
    @Schema(description = "订单量")
    private Integer orderNum;

    /**
     * 订单量_#1
     */
    @Schema(description = "订单量_#1")
    private Integer orderNum1;

    /**
     * 订单量_#3
     */
    @Schema(description = "订单量_#3")
    private Integer orderNum3;

    /**
     * 订单量_#5
     */
    @Schema(description = "订单量_#5")
    private Integer orderNum5;
}
