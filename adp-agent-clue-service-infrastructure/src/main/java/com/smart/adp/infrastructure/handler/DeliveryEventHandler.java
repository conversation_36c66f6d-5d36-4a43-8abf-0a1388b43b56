package com.smart.adp.infrastructure.handler;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.message.CustEventMessage;
import com.smart.adp.domain.enums.ClueStageEnum;
import com.smart.adp.domain.gateway.clue.CustEventFlowGateway;
import com.smart.adp.infrastructure.repository.clue.ClueDlrMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/10
 */
@Component
public class DeliveryEventHandler implements CustEventHandler {

    @Autowired
    private CustEventFlowGateway custEventFlowGateway;

    @Autowired
    private ClueDlrMapper clueDlrMapper;

    @Override
    public void handle(CustEventMessage message) {
        ClueStageEnum stage = ClueStageEnum.DELIVERY;
        CustEventFlow flow = message.buildFlow();
        flow.setStage(stage.getCode());
        custEventFlowGateway.insertSelective(flow);

        // update clue
        SacClueInfoDlr clue = new SacClueInfoDlr();
        clue.setColumn18(stage.getCodeStr());
        clueDlrMapper.updateByQuery(clue, QueryWrapper.create()
                                                      .and(SAC_CLUE_INFO_DLR.CUST_ID.eq(message.getCustId()))
                                                      .and(SAC_CLUE_INFO_DLR.COLUMN18.lt(stage.getCodeStr())
                                                                                     .or(SAC_CLUE_INFO_DLR.COLUMN18.isNull())));
    }
}
