package com.smart.adp.infrastructure.config;

import com.mybatisflex.core.mybatis.FlexConfiguration;
import com.mybatisflex.spring.boot.ConfigurationCustomizer;
import org.apache.ibatis.logging.slf4j.Slf4jImpl;
import org.springframework.context.annotation.Configuration;
/**
 * @Description: mybatis脚本打印
 * @Author: rik.ren
 * @Date: 2025/3/5 20:32
 **/
@Configuration
public class MyConfigurationCustomizer implements ConfigurationCustomizer {

    @Override
    public void customize(FlexConfiguration configuration) {
        configuration.setLogImpl(Slf4jImpl.class);
    }
}