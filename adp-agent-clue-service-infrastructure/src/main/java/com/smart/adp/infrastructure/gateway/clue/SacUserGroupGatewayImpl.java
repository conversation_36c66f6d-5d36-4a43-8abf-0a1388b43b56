package com.smart.adp.infrastructure.gateway.clue;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.common.annotation.SmartADPCache;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;
import com.smart.adp.domain.gateway.clue.SacUserGroupGateway;
import com.smart.adp.infrastructure.repository.clue.SacUserGroupMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.smart.adp.domain.entity.clue.table.SacUserGroupDetailEntityTableDef.SAC_USER_GROUP_DETAIL_ENTITY;

/**
 * @Description: 用户分组gateway实现
 * @Author: rik.ren
 * @Date: 2025/3/9 16:05
 **/
@Service
public class SacUserGroupGatewayImpl implements SacUserGroupGateway {
    @Autowired
    private SacUserGroupMapper userGroupMapper;

    /**
     * 根据实体条件查询集合
     *
     * @param param
     * @param needColumns
     * @return
     */
    @Override
//    @SmartADPCache(value = "userGroupCreatorList", key = "#param.creator", expire = 10, timeUnit = TimeUnit.MINUTES)
    public List<SacUserGroupEntity> findListByCreator(SacUserGroupEntity param, QueryColumn... needColumns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns);
        param.contidionsCreate(wrapper);
        return userGroupMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据实体条件查询集合
     *
     * @param param
     * @param needColumns
     * @return
     */
    @Override
//    @SmartADPCache(value = "userGroupList", key = "#param.userGroupId", expire = 20, timeUnit = TimeUnit.MINUTES)
    public List<SacUserGroupEntity> findListByUserGroupId(SacUserGroupEntity param, QueryColumn... needColumns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns);
        param.conditions(wrapper);
        return userGroupMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns
     * @return
     */
    @Override
    @SmartADPCache(value = "userGroupObject", key = "#param.custId", expire = 20, timeUnit = TimeUnit.MINUTES)
    public SacUserGroupEntity findByCondition(SacUserGroupEntity param, QueryColumn... needColumns) {
        // 查询数据库
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(SAC_USER_GROUP_DETAIL_ENTITY.as("groupDetail"));
        param.conditions(wrapper);
        return userGroupMapper.selectOneByQuery(wrapper);
    }
}
