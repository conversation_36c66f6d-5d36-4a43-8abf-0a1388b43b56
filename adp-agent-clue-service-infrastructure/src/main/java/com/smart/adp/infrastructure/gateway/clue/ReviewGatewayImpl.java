package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryOrderBy;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import com.smart.adp.domain.bo.clue.SacReviewHisBO;
import com.smart.adp.domain.entity.clue.SacReview;
import com.smart.adp.domain.entity.clue.SacReviewHis;
import com.smart.adp.domain.gateway.clue.ReviewGateway;
import com.smart.adp.infrastructure.repository.clue.ReviewHisMapper;
import com.smart.adp.infrastructure.repository.clue.ReviewMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.entity.clue.table.SacOneCustRemarkTableDef.SAC_ONE_CUST_REMARK;
import static com.smart.adp.domain.entity.clue.table.SacReviewHisTableDef.SAC_REVIEW_HIS;
import static com.smart.adp.domain.entity.clue.table.SacReviewTableDef.SAC_REVIEW;

/**
 * @Description: 回访gateway实现
 * @Author: rik.ren
 * @Date: 2025/3/5 20:42
 **/
@Service
public class ReviewGatewayImpl implements ReviewGateway {

    @Autowired
    private ReviewMapper reviewMapper;
    @Autowired
    private ReviewHisMapper reviewHisMapper;

    /**
     * 根据id查询
     *
     * @param reviewId
     * @param columns
     * @return
     */
    @Override
    public SacReview findById(String reviewId, QueryColumn... columns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .eq(SacReview::getReviewId, reviewId);
        return reviewMapper.selectOneByQuery(wrapper);
    }

    /**
     * 获取回访任务信息
     *
     * @param reviewIds
     * @param columns
     * @return
     */
    @Override
    public List<SacReview> findByIds(List<String> reviewIds, QueryColumn... columns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .in(SacReview::getReviewId, reviewIds);
        return reviewMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据实体条件查询集合
     *
     * @param param
     * @param needColumns
     * @return
     */
    @Override
    public List<SacReview> findListByCondition(SacReview param, QueryColumn... needColumns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns);
        return reviewMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns，尽量提供需要的字段
     * @return
     */
    @Override
//    @SmartADPCache(value = "review", key = "#param.custId", expire = 10, timeUnit = TimeUnit.MINUTES)
    public SacReviewBO findByCondition(SacReview param, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{SAC_REVIEW.ALL_COLUMNS};
        }
        // 查询数据库
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(SAC_REVIEW.as("review"))
                .leftJoin(SAC_ONE_CUST_REMARK.as("remark"))
                .on(SAC_REVIEW.CUST_ID.eq(SAC_ONE_CUST_REMARK.CUST_ID))
                .limit(1);
        param.conditions(wrapper);
        return reviewMapper.selectOneByQueryAs(wrapper, SacReviewBO.class);
    }

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns，尽量提供需要的字段
     * @return
     */
    @Override
//    @SmartADPCache(value = "review", key = "#param.custId", expire = 10, timeUnit = TimeUnit.MINUTES)
    public SacReviewHisBO findByCondition(SacReviewHis param, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{SAC_REVIEW_HIS.ALL_COLUMNS};
        }
        // 查询数据库
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(SAC_REVIEW_HIS.as("review"))
                .leftJoin(SAC_ONE_CUST_REMARK.as("remark"))
                .on(SAC_REVIEW_HIS.CUST_ID.eq(SAC_ONE_CUST_REMARK.CUST_ID))
                .limit(1);
        wrapper.orderBy(new QueryOrderBy(SAC_REVIEW_HIS.LAST_REVIEW_TIME, SqlConsts.DESC));
        param.conditions(wrapper);
        return reviewHisMapper.selectOneByQueryAs(wrapper, SacReviewHisBO.class);
    }

    /**
     * 手机号查询你回访记录表
     *
     * @param phone
     * @return
     */
    @Override
    public SacReview findOneByPhone(String phone) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SAC_REVIEW.REVIEW_PERSON_ID, SAC_REVIEW.BILL_TYPE, SAC_REVIEW.REVIEW_ID, SAC_REVIEW.REVIEW_PERSON_NAME)
                .eq(SacReview::getPhone, phone)
                .orderBy(SacReview::getCreatedDate)
                .desc();
        return reviewMapper.selectOneByQuery(wrapper);
    }

    /**
     * 更新review，param中有值得属性字段都会更新，不想更新的就设为空
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyReview(SacReview param) {
        // 更新review的条件
        QueryCondition condition = QueryCondition.create(new QueryColumn("CUST_ID"), SqlConsts.EQUALS, param.getCustId());
        condition.and(QueryCondition.create(new QueryColumn("BILL_CODE"), SqlConsts.EQUALS, param.getBillCode()));
        // 更新的字段就是param中的不为空的字段
        return reviewMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 更新review，param中有值得属性字段都会更新，不想更新的就设为空
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyReviewInfo(SacReview param) {
        // 更新review的条件
        QueryCondition condition = QueryCondition.create(new QueryColumn("PHONE"), SqlConsts.EQUALS, param.getPhone());
        // 更新的字段就是param中的不为空的字段
        return reviewMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 保存回访记录信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean saveReview(SacReview param) {
        return reviewMapper.saveSacReview(param) > 0;
    }

    /**
     * 店端线索分配批量更新回访任务
     *
     * @param sacReviews
     * @return
     */
    @Override
    public Boolean updateReviewAssignBatch(List<SacReview> sacReviews) {
        return reviewMapper.updateReviewAssignBatch(sacReviews) > 0;
    }
}
