package com.smart.adp.infrastructure.feign;

import com.smart.adp.infrastructure.feign.request.UpdateOrderPeInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * date 2025/4/20 15:52
 * @description 订单服务feign
 **/
@FeignClient(name = "${refer.url.adp.orc}", url = "${refer.url.adp.orc}")
public interface OrcFeignService {

    @PostMapping("/pri/adp/orc/updateOrderPeInfo")
    Object updateOrderPeInfo(@RequestBody UpdateOrderPeInfoDTO updateOrderPeInfoDTO);
}
