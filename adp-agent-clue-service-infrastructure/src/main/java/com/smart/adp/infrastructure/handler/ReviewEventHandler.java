package com.smart.adp.infrastructure.handler;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.common.constants.StringConstant;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.message.CustEventMessage;
import com.smart.adp.domain.enums.ClueStageEnum;
import com.smart.adp.domain.gateway.clue.CustEventFlowGateway;
import com.smart.adp.infrastructure.repository.clue.ClueDlrMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.smart.adp.domain.common.constants.BizConstants.ARRIVE_REVIEW_TYPE_CODE_SET;
import static com.smart.adp.domain.common.constants.StringConstant.REVIEW_TYPE_CODE;
import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/9
 */
@Component
public class ReviewEventHandler implements CustEventHandler {

    @Autowired
    private CustEventFlowGateway custEventFlowGateway;

    @Autowired
    private ClueDlrMapper clueDlrMapper;

    @Override
    public void handle(CustEventMessage message) {
        if (ARRIVE_REVIEW_TYPE_CODE_SET.contains(message.getExtendJson().getString(REVIEW_TYPE_CODE))) {
            ClueStageEnum stage = ClueStageEnum.ARRIVE;
            CustEventFlow flow = message.buildFlow();
            flow.setStage(stage.getCode());
            flow.getExtendJson().put(StringConstant.SOURCE, this.getClass()
                                                                .getSimpleName());
            custEventFlowGateway.insertSelective(flow);

            // update clue
            SacClueInfoDlr clue = new SacClueInfoDlr();
            clue.setColumn18(stage.getCodeStr());
            clueDlrMapper.updateByQuery(clue, QueryWrapper.create()
                                                          .and(SAC_CLUE_INFO_DLR.CUST_ID.eq(message.getCustId()))
                                                          .and(SAC_CLUE_INFO_DLR.COLUMN18.lt(stage.getCodeStr())
                                                                                         .or(SAC_CLUE_INFO_DLR.COLUMN18.isNull())));
        }
    }
}
