package com.smart.adp.infrastructure.gateway.base;


import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.gateway.base.CompetitiveCarModelGateway;
import com.smart.adp.domain.valueObject.base.CompetitiveCarModels;
import com.smart.adp.infrastructure.repository.base.CarModelsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.smart.adp.domain.valueObject.base.table.CompetitiveCarModelsTableDef.COMPETITIVE_CAR_MODELS;


/**
 * <AUTHOR>
 * date 2025/3/5 11:07
 * @description
 **/
@Slf4j
@Component
public class CompetitiveCarModelGatewayImpl implements CompetitiveCarModelGateway {

    @Autowired
    private CarModelsMapper carModelsMapper;

    @Override
    public List<CompetitiveCarModels> findCarModels() {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(COMPETITIVE_CAR_MODELS.CAR_MODEL_NAME, COMPETITIVE_CAR_MODELS.CAR_MODEL_TYPE)
                .orderBy(CompetitiveCarModels::getId)
                .asc();
        return carModelsMapper.selectListByQuery(wrapper);
    }

}
