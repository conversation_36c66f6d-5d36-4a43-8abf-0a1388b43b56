package com.smart.adp.infrastructure.gateway.task;

import com.mybatisflex.core.paginate.Page;
import com.smart.adp.domain.entity.task.OnetaskDetail;
import com.smart.adp.domain.entity.task.OnetaskInfo;
import com.smart.adp.domain.gateway.task.TaskGateway;
import com.smart.adp.domain.qry.TaskQry;
import com.smart.adp.domain.valueObject.task.TaskVO;
import com.smart.adp.infrastructure.repository.task.TaskDetailMapper;
import com.smart.adp.infrastructure.repository.task.TaskInfoMapper;
import com.smart.adp.infrastructure.repository.task.TaskUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/5
 */
@Component
public class TaskGatewayImpl implements TaskGateway {

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private TaskUserMapper taskUserMapper;

    @Autowired
    private TaskDetailMapper taskDetailMapper;


    @Override
    public Page<TaskVO> page(TaskQry qry) {
        return null;
    }

    @Override
    public List<OnetaskInfo> getInfosByTaskIds(List<String> taskIds) {
        return Collections.emptyList();
    }

    @Override
    public List<OnetaskDetail> getDetailsByTaskIds(List<String> taskIds) {
        return Collections.emptyList();
    }

    @Override
    public boolean existByQry(TaskQry qry) {
        return true;
    }

    @Override
    public long countByQry(TaskQry qry) {
        return 0;
    }

    @Override
    public TaskVO detail(Long id) {
        return null;
    }

    @Override
    public OnetaskInfo getInfoByTaskId(String taskId) {
        return null;
    }
}
