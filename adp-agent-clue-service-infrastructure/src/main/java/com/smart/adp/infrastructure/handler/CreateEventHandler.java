package com.smart.adp.infrastructure.handler;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.common.constants.StringConstant;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.message.CustEventMessage;
import com.smart.adp.domain.enums.ClueStageEnum;
import com.smart.adp.domain.enums.CustEventEnum;
import com.smart.adp.domain.gateway.clue.CustEventFlowGateway;
import com.smart.adp.infrastructure.repository.clue.ClueDlrMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

import static com.smart.adp.domain.common.constants.BizConstants.ARRIVE_CHANNEL_CODE;
import static com.smart.adp.domain.common.constants.StringConstant.CHANNEL_CODE;
import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/10
 */
@Component
public class CreateEventHandler implements CustEventHandler {

    @Autowired
    private CustEventFlowGateway custEventFlowGateway;

    @Autowired
    private ClueDlrMapper clueDlrMapper;

    @Override
    public void handle(CustEventMessage message) {
        String channelCode = message.getExtendJson()
                                    .getString(CHANNEL_CODE);

        ClueStageEnum stage = ClueStageEnum.KNOW;
        CustEventFlow flow = message.buildFlow();
        flow.setStage(stage.getCode());

        // insert flow
        if (ARRIVE_CHANNEL_CODE.equals(channelCode)) {
            stage = ClueStageEnum.ARRIVE;
            CustEventFlow arriveFlow = message.buildFlow();
            arriveFlow.setType(CustEventEnum.REVIEW.getCode());
            arriveFlow.setStage(ClueStageEnum.ARRIVE.getCode());
            arriveFlow.getExtendJson().put(StringConstant.SOURCE, this.getClass()
                                                                      .getSimpleName());
            custEventFlowGateway.insertBatchSelective(Arrays.asList(flow, arriveFlow));
        } else {
            custEventFlowGateway.insertSelective(flow);
        }

        // update clue
        SacClueInfoDlr clue = new SacClueInfoDlr();
        clue.setColumn18(stage.getCodeStr());
        clueDlrMapper.updateByQuery(clue, QueryWrapper.create()
                                                      .and(SAC_CLUE_INFO_DLR.CUST_ID.eq(message.getCustId()))
                                                      .and(SAC_CLUE_INFO_DLR.COLUMN18.lt(stage.getCodeStr())
                                                                                     .or(SAC_CLUE_INFO_DLR.COLUMN18.isNull())));
    }
}
