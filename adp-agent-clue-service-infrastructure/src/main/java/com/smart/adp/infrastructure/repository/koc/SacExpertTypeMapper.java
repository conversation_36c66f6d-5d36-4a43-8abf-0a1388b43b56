package com.smart.adp.infrastructure.repository.koc;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.koc.SacExpertType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 达人类型Mapper
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Mapper
public interface SacExpertTypeMapper extends BaseMapper<SacExpertType> {

    /**
     * 根据达人类型名称模糊查询
     *
     * @param typeName 达人类型名称
     * @return 达人类型列表
     */
    @Select("SELECT * FROM t_sac_expert_type WHERE type_name LIKE CONCAT('%', #{typeName}, '%') ORDER BY created_date DESC")
    List<SacExpertType> findByNameLike(@Param("typeName") String typeName);

    /**
     * 根据达人类型内容查询
     *
     * @param typeContent 达人类型内容
     * @return 达人类型信息
     */
    @Select("SELECT * FROM t_sac_expert_type WHERE type_content = #{typeContent}")
    SacExpertType findByContent(@Param("typeContent") String typeContent);

    /**
     * 根据业务ID查询达人类型
     *
     * @param typeId 达人类型业务ID
     * @return 达人类型信息
     */
    @Select("SELECT * FROM t_sac_expert_type WHERE type_id = #{typeId}")
    SacExpertType findByTypeId(@Param("typeId") String typeId);

    /**
     * 查询所有达人类型
     *
     * @return 达人类型列表
     */
    @Select("SELECT * FROM t_sac_expert_type ORDER BY created_date DESC")
    List<SacExpertType> findAll();

    /**
     * 检查达人类型内容是否存在
     *
     * @param typeContent 达人类型内容
     * @param excludeId 排除的主键ID
     * @return 数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM t_sac_expert_type WHERE type_content = #{typeContent}",
        "<if test='excludeId != null'>",
        "  AND id != #{excludeId}",
        "</if>",
        "</script>"
    })
    Integer countByContent(@Param("typeContent") String typeContent, @Param("excludeId") Long excludeId);

    /**
     * 根据业务ID检查达人类型内容是否存在
     *
     * @param typeContent 达人类型内容
     * @param excludeTypeId 排除的达人类型业务ID
     * @return 数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM t_sac_expert_type WHERE type_content = #{typeContent}",
        "<if test='excludeTypeId != null and excludeTypeId != \"\"'>",
        "  AND type_id != #{excludeTypeId}",
        "</if>",
        "</script>"
    })
    Integer countByContentAndTypeId(@Param("typeContent") String typeContent, @Param("excludeTypeId") String excludeTypeId);

    /**
     * 检查达人类型名称是否存在
     *
     * @param typeName 达人类型名称
     * @param excludeId 排除的主键ID
     * @return 数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM t_sac_expert_type WHERE type_name = #{typeName}",
        "<if test='excludeId != null'>",
        "  AND id != #{excludeId}",
        "</if>",
        "</script>"
    })
    Integer countByName(@Param("typeName") String typeName, @Param("excludeId") Long excludeId);

    /**
     * 根据业务ID检查达人类型名称是否存在
     *
     * @param typeName 达人类型名称
     * @param excludeTypeId 排除的达人类型业务ID
     * @return 数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM t_sac_expert_type WHERE type_name = #{typeName}",
        "<if test='excludeTypeId != null and excludeTypeId != \"\"'>",
        "  AND type_id != #{excludeTypeId}",
        "</if>",
        "</script>"
    })
    Integer countByNameAndTypeId(@Param("typeName") String typeName, @Param("excludeTypeId") String excludeTypeId);
}
