package com.smart.adp.infrastructure.gateway.clue;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.entity.clue.SacClueTag;
import com.smart.adp.domain.gateway.clue.ClueTagGateway;
import com.smart.adp.infrastructure.repository.clue.ClueTagMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/22
 */
@Component
public class ClueTagGatewayImpl implements ClueTagGateway {

    @Autowired
    private ClueTagMapper clueTagMapper;

    @Override
    public List<SacClueTag> queryList(QueryWrapper wrapper) {
        if (Objects.isNull(wrapper)) {
            return Collections.emptyList();
        }

        return clueTagMapper.selectListByQuery(wrapper);
    }
}
