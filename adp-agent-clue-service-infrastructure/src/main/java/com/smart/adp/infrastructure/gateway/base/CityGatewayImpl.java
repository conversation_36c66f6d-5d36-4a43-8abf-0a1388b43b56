package com.smart.adp.infrastructure.gateway.base;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.gateway.base.CityGateway;
import com.smart.adp.domain.valueObject.base.CityInfo;
import com.smart.adp.infrastructure.repository.base.CityMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.smart.adp.domain.valueObject.base.table.CityInfoTableDef.CITY_INFO;

/**
 * <AUTHOR>
 * date 2025/3/9 13:58
 * @description 市信息gateway实现
 **/
@Slf4j
@Component
public class CityGatewayImpl implements CityGateway {

    @Autowired
    private CityMapper cityMapper;

    @Override
    public CityInfo findCityByCityId(String cityId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(CITY_INFO.PROVINCE_ID, CITY_INFO.CITY_ID, CITY_INFO.CITY_CODE, CITY_INFO.CITY_NAME)
                .eq(CityInfo::getCityId, cityId)
                .limit(1)
                .orderBy(CityInfo::getCreatedDate)
                .desc();
        return cityMapper.selectOneByQuery(wrapper);
    }
}
