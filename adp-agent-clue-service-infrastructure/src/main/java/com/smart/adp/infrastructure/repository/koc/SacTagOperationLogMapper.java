package com.smart.adp.infrastructure.repository.koc;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.koc.SacTagOperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 标签操作日志Mapper
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Mapper
public interface SacTagOperationLogMapper extends BaseMapper<SacTagOperationLog> {

    /**
     * 根据用户smartId查询操作日志
     *
     * @param smartId 用户smartId
     * @return 操作日志列表
     */
    @Select("SELECT * FROM t_sac_tag_operation_log WHERE smart_id = #{smartId} ORDER BY operation_date DESC")
    List<SacTagOperationLog> findBySmartId(@Param("smartId") String smartId);

    /**
     * 根据操作人查询操作日志
     *
     * @param operator 操作人
     * @return 操作日志列表
     */
    @Select("SELECT * FROM t_sac_tag_operation_log WHERE operator = #{operator} ORDER BY operation_date DESC")
    List<SacTagOperationLog> findByOperator(@Param("operator") String operator);

    /**
     * 根据操作类型查询操作日志
     *
     * @param operationType 操作类型
     * @return 操作日志列表
     */
    @Select("SELECT * FROM t_sac_tag_operation_log WHERE operation_type = #{operationType} ORDER BY operation_date DESC")
    List<SacTagOperationLog> findByOperationType(@Param("operationType") Integer operationType);

    /**
     * 根据目标主键ID查询操作日志
     *
     * @param targetId 目标主键ID
     * @return 操作日志列表
     */
    @Select("SELECT * FROM t_sac_tag_operation_log WHERE target_id = #{targetId} ORDER BY operation_date DESC")
    List<SacTagOperationLog> findByTargetId(@Param("targetId") Long targetId);

    /**
     * 根据目标业务ID查询操作日志
     *
     * @param targetBusinessId 目标业务ID
     * @return 操作日志列表
     */
    @Select("SELECT * FROM t_sac_tag_operation_log WHERE target_business_id = #{targetBusinessId} ORDER BY operation_date DESC")
    List<SacTagOperationLog> findByTargetBusinessId(@Param("targetBusinessId") String targetBusinessId);

    /**
     * 根据批量操作ID查询操作日志
     *
     * @param batchId 批量操作ID
     * @return 操作日志列表
     */
    @Select("SELECT * FROM t_sac_tag_operation_log WHERE batch_id = #{batchId} ORDER BY operation_date DESC")
    List<SacTagOperationLog> findByBatchId(@Param("batchId") String batchId);

    /**
     * 根据日志业务ID查询操作日志
     *
     * @param logId 日志业务ID
     * @return 操作日志
     */
    @Select("SELECT * FROM t_sac_tag_operation_log WHERE log_id = #{logId}")
    SacTagOperationLog findByLogId(@Param("logId") String logId);

    /**
     * 根据时间范围查询操作日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作日志列表
     */
    @Select("SELECT * FROM t_sac_tag_operation_log WHERE operation_date >= #{startTime} AND operation_date <= #{endTime} ORDER BY operation_date DESC")
    List<SacTagOperationLog> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据时间删除过期日志
     *
     * @param expireTime 过期时间
     * @return 删除数量
     */
    @Select("DELETE FROM t_sac_tag_operation_log WHERE operation_date < #{expireTime}")
    Integer deleteExpiredLogs(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 统计操作次数
     *
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM t_sac_tag_operation_log WHERE 1=1",
        "<if test='operationType != null'>",
        "  AND operation_type = #{operationType}",
        "</if>",
        "<if test='startTime != null'>",
        "  AND operation_date >= #{startTime}",
        "</if>",
        "<if test='endTime != null'>",
        "  AND operation_date <= #{endTime}",
        "</if>",
        "</script>"
    })
    Integer countOperations(@Param("operationType") Integer operationType, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户操作次数
     *
     * @param smartId 用户smartId
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM t_sac_tag_operation_log WHERE smart_id = #{smartId}",
        "<if test='startTime != null'>",
        "  AND operation_date >= #{startTime}",
        "</if>",
        "<if test='endTime != null'>",
        "  AND operation_date <= #{endTime}",
        "</if>",
        "</script>"
    })
    Integer countUserOperations(@Param("smartId") String smartId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计操作人操作次数
     *
     * @param operator 操作人
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM t_sac_tag_operation_log WHERE operator = #{operator}",
        "<if test='startTime != null'>",
        "  AND operation_date >= #{startTime}",
        "</if>",
        "<if test='endTime != null'>",
        "  AND operation_date <= #{endTime}",
        "</if>",
        "</script>"
    })
    Integer countOperatorOperations(@Param("operator") String operator, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
