package com.smart.adp.infrastructure.gateway.koc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.smart.adp.domain.bo.koc.KocExpertTypeBO;
import com.smart.adp.domain.entity.koc.SacExpertType;
import com.smart.adp.domain.enums.KocRelTypeEnum;
import com.smart.adp.domain.gateway.koc.KocExpertTypeGateway;
import com.smart.adp.infrastructure.repository.koc.SacExpertTypeMapper;
import com.smart.adp.infrastructure.repository.koc.SacUserTagRelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: KOC达人类型网关实现
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Slf4j
@Service
public class KocExpertTypeGatewayImpl implements KocExpertTypeGateway {

    @Autowired
    private SacExpertTypeMapper sacExpertTypeMapper;

    @Autowired
    private SacUserTagRelMapper sacUserTagRelMapper;

    @Override
    public SacExpertType findById(String typeId) {
        if (StrUtil.isBlank(typeId)) {
            return null;
        }
        return sacExpertTypeMapper.findByTypeId(typeId);
    }

    @Override
    public List<SacExpertType> findByCondition(SacExpertType condition) {
        if (ObjectUtil.isEmpty(condition)) {
            return new ArrayList<>();
        }
        return sacExpertTypeMapper.selectListByQuery(condition);
    }

    @Override
    public List<SacExpertType> findAll() {
        return sacExpertTypeMapper.findAll();
    }

    @Override
    public List<SacExpertType> findByNameLike(String typeName) {
        if (StrUtil.isBlank(typeName)) {
            return new ArrayList<>();
        }
        return sacExpertTypeMapper.findByNameLike(typeName);
    }

    @Override
    public SacExpertType findByContent(String typeContent) {
        if (StrUtil.isBlank(typeContent)) {
            return null;
        }
        return sacExpertTypeMapper.findByContent(typeContent);
    }

    @Override
    public Boolean save(SacExpertType expertType) {
        if (ObjectUtil.isEmpty(expertType)) {
            return false;
        }
        try {
            int result = sacExpertTypeMapper.insert(expertType);
            return result > 0;
        } catch (Exception e) {
            log.error("保存达人类型信息失败", e);
            return false;
        }
    }

    @Override
    public Boolean update(SacExpertType expertType) {
        if (ObjectUtil.isEmpty(expertType) || expertType.getId() == null) {
            return false;
        }
        try {
            int result = sacExpertTypeMapper.update(expertType);
            return result > 0;
        } catch (Exception e) {
            log.error("更新达人类型信息失败", e);
            return false;
        }
    }

    @Override
    public Boolean deleteById(String typeId) {
        if (StrUtil.isBlank(typeId)) {
            return false;
        }
        try {
            SacExpertType expertType = findById(typeId);
            if (ObjectUtil.isEmpty(expertType)) {
                return false;
            }
            int result = sacExpertTypeMapper.deleteById(expertType.getId());
            return result > 0;
        } catch (Exception e) {
            log.error("删除达人类型信息失败", e);
            return false;
        }
    }

    @Override
    public Boolean batchDelete(List<String> typeIds) {
        if (CollectionUtil.isEmpty(typeIds)) {
            return false;
        }
        try {
            // 根据业务ID查询主键ID
            List<Long> ids = new ArrayList<>();
            for (String typeId : typeIds) {
                SacExpertType expertType = findById(typeId);
                if (ObjectUtil.isNotEmpty(expertType)) {
                    ids.add(expertType.getId());
                }
            }
            
            if (CollectionUtil.isEmpty(ids)) {
                return false;
            }
            
            int result = sacExpertTypeMapper.deleteBatchByIds(ids);
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除达人类型信息失败", e);
            return false;
        }
    }

    @Override
    public Boolean existsByContent(String typeContent, String excludeTypeId) {
        if (StrUtil.isBlank(typeContent)) {
            return false;
        }
        try {
            Integer count = sacExpertTypeMapper.countByContentAndTypeId(typeContent, excludeTypeId);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查达人类型内容是否存在失败", e);
            return false;
        }
    }

    @Override
    public Boolean existsByName(String typeName, String excludeTypeId) {
        if (StrUtil.isBlank(typeName)) {
            return false;
        }
        try {
            Integer count = sacExpertTypeMapper.countByNameAndTypeId(typeName, excludeTypeId);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查达人类型名称是否存在失败", e);
            return false;
        }
    }

    @Override
    public Integer countUsage(String typeId) {
        if (StrUtil.isBlank(typeId)) {
            return 0;
        }
        try {
            return sacUserTagRelMapper.countUsageByRefBusinessId(typeId, KocRelTypeEnum.EXPERT_TYPE.getCode());
        } catch (Exception e) {
            log.error("统计达人类型使用次数失败", e);
            return 0;
        }
    }

    @Override
    public List<KocExpertTypeBO> batchCountUsage(List<String> typeIds) {
        if (CollectionUtil.isEmpty(typeIds)) {
            return new ArrayList<>();
        }

        List<KocExpertTypeBO> result = new ArrayList<>();
        for (String typeId : typeIds) {
            SacExpertType expertType = findById(typeId);
            if (ObjectUtil.isNotEmpty(expertType)) {
                KocExpertTypeBO expertTypeBO = KocExpertTypeBO.buildFromEntity(expertType);
                Integer useCount = countUsage(typeId);
                expertTypeBO.setUseCount(useCount);
                result.add(expertTypeBO);
            }
        }
        return result;
    }
}
