package com.smart.adp.infrastructure.feign.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: UC用户信息DTO
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Data
public class UCUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户smartId
     */
    private String smartId;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 用户性别
     */
    private String gender;

    /**
     * 用户年龄
     */
    private Integer age;

    /**
     * 用户城市
     */
    private String city;

    /**
     * 用户省份
     */
    private String province;

    /**
     * 用户注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 用户最后活跃时间
     */
    private LocalDateTime lastActiveTime;

    /**
     * 是否为VIP用户
     */
    private Boolean isVip;

    /**
     * 用户等级
     */
    private String userLevel;

    /**
     * 用户积分
     */
    private Integer points;

    /**
     * 用户状态
     */
    private Integer status;

    /**
     * 用户来源
     */
    private String source;

    /**
     * 用户标签（逗号分隔）
     */
    private String tags;

    /**
     * 用户备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
