package com.smart.adp.infrastructure.repository.clue;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
public interface ClueDlrMapper extends BaseMapper<SacClueInfoDlr> {

    /**
     * 插入
     *
     * @param
     * @return
     */
    int insertClueInfo(@Param("param") SacClueInfoDlr param);

    /**
     * 更新线索信息
     *
     * @param param
     * @return
     */
    int updateClueInfo(@Param("param") SacClueInfoDlr param);

    /**
     * 批量更新活跃线索
     *
     * @param clueInfoDlrList
     * @return
     */
    int updateClueInfoBatch(@Param("clueInfoDlrList") List<SacClueInfoDlr> clueInfoDlrList);
}
