package com.smart.adp.infrastructure.feign.request;

import com.smart.adp.domain.enums.SummarySortEnum;
import com.smart.adp.domain.enums.SummaryTimeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "小结入参")
public class SummaryDTO {

    /**
     * 门店编码
     */
    @Schema(description = "门店编码")
    private String dlrCode;

    /**
     * 用户 ID 列表
     */
    @Schema(description = "用户 ID 列表")
    private List<String> userIdList;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    private SummarySortEnum sortField;

    /**
     * 时间区间
     */
    @Schema(description = "时间区间", required = true)
    private SummaryTimeEnum timePeriod;

    /**
     * 是否门店维度
     */
    @Schema(description = "是否门店维度")
    private Boolean dlr;
}
