package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryOrderBy;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.bo.clue.CustEventFlowBO;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import com.smart.adp.domain.gateway.clue.CustEventFlowGateway;
import com.smart.adp.infrastructure.repository.clue.CustEventFlowMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.smart.adp.domain.entity.clue.table.CustEventFlowTableDef.CUST_EVENT_FLOW;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
@Service
public class CustEventFlowFlowGatewayImpl implements CustEventFlowGateway {
    @Autowired
    private CustEventFlowMapper custEventFlowMapper;

    /**
     * 根据客户id查询旅程事件
     *
     * @param custId
     * @return
     */
    @Override
    public List<CustEventFlow> queryListEventByCustId(String custId) {
        if (StringUtils.isEmpty(custId)) {
            return null;
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(CUST_EVENT_FLOW.ALL_COLUMNS);
        wrapper.and(CUST_EVENT_FLOW.CUST_ID.eq(custId, StringUtil::hasText));
        wrapper.orderBy(new QueryOrderBy(CUST_EVENT_FLOW.EVENT_TIME, SqlConsts.ASC));
        return custEventFlowMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据客户id查询旅程事件
     *
     * @param custId
     * @return
     */
    @Override
    public CustEventFlow queryEventByCustId(String custId) {
        if (StringUtils.isEmpty(custId)) {
            return null;
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(CUST_EVENT_FLOW.ALL_COLUMNS);
        wrapper.and(CUST_EVENT_FLOW.CUST_ID.eq(custId, StringUtil::hasText));
        wrapper.orderBy(new QueryOrderBy(CUST_EVENT_FLOW.EVENT_TIME, SqlConsts.DESC));
        wrapper.limit(1);
        return custEventFlowMapper.selectOneByQuery(wrapper);
    }

    /**
     * 根据客户id查询旅程事件
     *
     * @param listCustId
     * @return
     */
    @Override
    public List<CustEventFlow> queryEventByCustId(List<String> listCustId) {
        if (CollectionUtil.isEmpty(listCustId)) {
            return null;
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(CUST_EVENT_FLOW.ALL_COLUMNS);

        wrapper.and(CUST_EVENT_FLOW.CUST_ID.in(listCustId, CollectionUtil::isNotEmpty));
        wrapper.orderBy(new QueryOrderBy(CUST_EVENT_FLOW.EVENT_TIME, SqlConsts.DESC));
        return custEventFlowMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据客户id查询旅程集合
     * 不能不带条件的查询
     *
     * @param boParam
     * @return
     */
    @Override
    public List<CustEventFlow> queryEventByCustId(CustEventFlowBO boParam) {
        if (ObjectUtil.isEmpty(boParam) || (StringUtils.isEmpty(boParam.getCustId()) && CollectionUtil.isEmpty(boParam.getListCustId()))) {
            return Collections.emptyList();
        }
        QueryWrapper wrapper = QueryWrapper.create().select(CUST_EVENT_FLOW.ALL_COLUMNS);
        boParam.conditions(wrapper);
        wrapper.orderBy(new QueryOrderBy(CUST_EVENT_FLOW.EVENT_TIME, SqlConsts.DESC));
        return custEventFlowMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据客户id查询旅程集合
     * 不能不带条件的查询
     * 根据客户id查询旅程集合，只查询每个custId的最新的一条数据
     *
     * @param boParam
     * @return
     */
    @Override
    public List<CustEventFlow> queryEventByCustIdLast(CustEventFlowBO boParam) {
        if (ObjectUtil.isEmpty(boParam) || (StringUtils.isEmpty(boParam.getCustId()) && CollectionUtil.isEmpty(boParam.getListCustId()))) {
            return Collections.emptyList();
        }
        return custEventFlowMapper.selectEventFlowLast(boParam);
    }

    @Override
    public boolean exists(String custId, Integer type) {
        return custEventFlowMapper.selectCountByQuery(QueryWrapper.create()
                .and(CUST_EVENT_FLOW.CUST_ID.eq(custId))
                .and(CUST_EVENT_FLOW.TYPE.eq(type))) > 0;
    }

    @Override
    public int insertSelective(CustEventFlow flow) {
        return custEventFlowMapper.insertSelective(flow);
    }

    @Override
    public int insertBatchSelective(List<CustEventFlow> flows) {
        return custEventFlowMapper.insertBatchSelective(flows);
    }

    @Override
    public CustEventFlow selectOne(QueryWrapper wrapper) {
        return custEventFlowMapper.selectOneByQuery(wrapper);
    }
}
