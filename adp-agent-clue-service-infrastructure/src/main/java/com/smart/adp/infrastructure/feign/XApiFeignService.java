package com.smart.adp.infrastructure.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * date 2025/3/10 20:50
 * @description 调用xApi的feign
 **/
@FeignClient(name = "${refer.url.adp.api:ly.adp.api}", url = "${refer.url.adp.api}")
public interface XApiFeignService {

    @PostMapping("/rest/ADP_INSERT/{ms}")
    Map sendCancleData(@PathVariable("ms") String ms, @RequestBody Map<String, Object> param);
}
