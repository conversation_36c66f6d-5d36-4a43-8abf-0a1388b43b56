package com.smart.adp.infrastructure.gateway.clue;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.entity.base.AgentDlrInfo;
import com.smart.adp.domain.gateway.clue.AgentDlrGateway;
import com.smart.adp.infrastructure.repository.base.AgentDlrMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.smart.adp.domain.entity.base.table.AgentDlrInfoTableDef.AGENT_DLR_INFO;

/**
 * <AUTHOR>
 * date 2025/3/5 11:07
 * @description
 **/
@Slf4j
@Component
public class AgentDlrGatewayImpl implements AgentDlrGateway {

    @Autowired
    private AgentDlrMapper agentDlrMapper;

    /**
     * 店铺code获取门店信息
     *
     * @param dlrCode
     * @return
     */
    @Override
    public AgentDlrInfo findDlrInfo(String dlrCode) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(AGENT_DLR_INFO.DLR_ID, AGENT_DLR_INFO.DLR_CODE, AGENT_DLR_INFO.DLR_SHORT_NAME,
                        AGENT_DLR_INFO.DLR_TYPE, AGENT_DLR_INFO.CREATOR,AGENT_DLR_INFO.PROVINCE_ID,
                        AGENT_DLR_INFO.CITY_ID,AGENT_DLR_INFO.COUNTY_ID)
                .eq(AgentDlrInfo::getDlrCode, dlrCode)
                .limit(1)
                .orderBy(AgentDlrInfo::getCreatedDate)
                .desc();
        return agentDlrMapper.selectOneByQuery(wrapper);
    }

}
