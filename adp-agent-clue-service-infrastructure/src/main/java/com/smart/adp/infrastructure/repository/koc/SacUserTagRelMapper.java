package com.smart.adp.infrastructure.repository.koc;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.koc.SacUserTagRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 用户标签关系Mapper
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Mapper
public interface SacUserTagRelMapper extends BaseMapper<SacUserTagRel> {

    /**
     * 根据用户smartId查询标签关系
     *
     * @param smartId 用户smartId
     * @return 用户标签关系列表
     */
    @Select("SELECT * FROM t_sac_user_tag_rel WHERE smart_id = #{smartId} ORDER BY created_date DESC")
    List<SacUserTagRel> findBySmartId(@Param("smartId") String smartId);

    /**
     * 根据手机号查询标签关系
     *
     * @param phone 手机号
     * @return 用户标签关系列表
     */
    @Select("SELECT * FROM t_sac_user_tag_rel WHERE phone = #{phone} ORDER BY created_date DESC")
    List<SacUserTagRel> findByPhone(@Param("phone") String phone);

    /**
     * 根据用户smartId和关系类型查询
     *
     * @param smartId 用户smartId
     * @param relType 关系类型
     * @return 用户标签关系列表
     */
    @Select("SELECT * FROM t_sac_user_tag_rel WHERE smart_id = #{smartId} AND rel_type = #{relType} ORDER BY created_date DESC")
    List<SacUserTagRel> findBySmartIdAndRelType(@Param("smartId") String smartId, @Param("relType") Integer relType);

    /**
     * 根据引用主键ID查询用户标签关系
     *
     * @param refId 引用主键ID
     * @return 用户标签关系列表
     */
    @Select("SELECT * FROM t_sac_user_tag_rel WHERE ref_id = #{refId} ORDER BY created_date DESC")
    List<SacUserTagRel> findByRefId(@Param("refId") Long refId);

    /**
     * 根据引用业务ID查询用户标签关系
     *
     * @param refBusinessId 引用业务ID
     * @return 用户标签关系列表
     */
    @Select("SELECT * FROM t_sac_user_tag_rel WHERE ref_business_id = #{refBusinessId} ORDER BY created_date DESC")
    List<SacUserTagRel> findByRefBusinessId(@Param("refBusinessId") String refBusinessId);

    /**
     * 根据关系业务ID查询
     *
     * @param relId 关系业务ID
     * @return 用户标签关系
     */
    @Select("SELECT * FROM t_sac_user_tag_rel WHERE rel_id = #{relId}")
    SacUserTagRel findByRelId(@Param("relId") String relId);

    /**
     * 检查用户标签关系是否存在
     *
     * @param smartId 用户smartId
     * @param refId 引用主键ID
     * @param relType 关系类型
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM t_sac_user_tag_rel WHERE smart_id = #{smartId} AND ref_id = #{refId} AND rel_type = #{relType}")
    Integer countBySmartIdAndRefIdAndRelType(@Param("smartId") String smartId, @Param("refId") Long refId, @Param("relType") Integer relType);

    /**
     * 根据业务ID检查用户标签关系是否存在
     *
     * @param smartId 用户smartId
     * @param refBusinessId 引用业务ID
     * @param relType 关系类型
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM t_sac_user_tag_rel WHERE smart_id = #{smartId} AND ref_business_id = #{refBusinessId} AND rel_type = #{relType}")
    Integer countBySmartIdAndRefBusinessIdAndRelType(@Param("smartId") String smartId, @Param("refBusinessId") String refBusinessId, @Param("relType") Integer relType);

    /**
     * 统计用户的标签数量
     *
     * @param smartId 用户smartId
     * @return 标签数量
     */
    @Select("SELECT COUNT(*) FROM t_sac_user_tag_rel WHERE smart_id = #{smartId} AND rel_type = 1")
    Integer countUserTags(@Param("smartId") String smartId);

    /**
     * 统计用户的达人类型数量
     *
     * @param smartId 用户smartId
     * @return 达人类型数量
     */
    @Select("SELECT COUNT(*) FROM t_sac_user_tag_rel WHERE smart_id = #{smartId} AND rel_type = 2")
    Integer countUserExpertTypes(@Param("smartId") String smartId);

    /**
     * 统计用户的备注数量
     *
     * @param smartId 用户smartId
     * @return 备注数量
     */
    @Select("SELECT COUNT(*) FROM t_sac_user_tag_rel WHERE smart_id = #{smartId} AND rel_type = 3")
    Integer countUserRemarks(@Param("smartId") String smartId);

    /**
     * 统计标签使用次数
     *
     * @param refId 引用主键ID
     * @param relType 关系类型
     * @return 使用次数
     */
    @Select("SELECT COUNT(*) FROM t_sac_user_tag_rel WHERE ref_id = #{refId} AND rel_type = #{relType}")
    Integer countUsageByRefId(@Param("refId") Long refId, @Param("relType") Integer relType);

    /**
     * 根据业务ID统计标签使用次数
     *
     * @param refBusinessId 引用业务ID
     * @param relType 关系类型
     * @return 使用次数
     */
    @Select("SELECT COUNT(*) FROM t_sac_user_tag_rel WHERE ref_business_id = #{refBusinessId} AND rel_type = #{relType}")
    Integer countUsageByRefBusinessId(@Param("refBusinessId") String refBusinessId, @Param("relType") Integer relType);

    /**
     * 根据条件删除用户标签关系
     *
     * @param smartId 用户smartId
     * @param refId 引用主键ID
     * @param relType 关系类型
     * @return 影响行数
     */
    @Select("DELETE FROM t_sac_user_tag_rel WHERE smart_id = #{smartId} AND ref_id = #{refId} AND rel_type = #{relType}")
    Integer deleteByCondition(@Param("smartId") String smartId, @Param("refId") Long refId, @Param("relType") Integer relType);

    /**
     * 根据业务ID条件删除用户标签关系
     *
     * @param smartId 用户smartId
     * @param refBusinessId 引用业务ID
     * @param relType 关系类型
     * @return 影响行数
     */
    @Select("DELETE FROM t_sac_user_tag_rel WHERE smart_id = #{smartId} AND ref_business_id = #{refBusinessId} AND rel_type = #{relType}")
    Integer deleteByBusinessCondition(@Param("smartId") String smartId, @Param("refBusinessId") String refBusinessId, @Param("relType") Integer relType);
}
