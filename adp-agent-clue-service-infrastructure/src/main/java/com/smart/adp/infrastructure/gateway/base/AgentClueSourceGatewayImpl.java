package com.smart.adp.infrastructure.gateway.base;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.entity.base.AgentClueSource;
import com.smart.adp.domain.gateway.clue.AgentClueSourceGateway;
import com.smart.adp.infrastructure.repository.clue.AgentClueSourceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.smart.adp.domain.entity.base.table.AgentClueSourceTableDef.AGENT_CLUE_SOURCE;

/**
 * <AUTHOR>
 * date 2025/3/12 16:08
 * @description
 **/

/**
 * <AUTHOR>
 * date 2025/3/5 11:07
 * @description
 **/
@Slf4j
@Component
public class AgentClueSourceGatewayImpl implements AgentClueSourceGateway {

    @Autowired
    private AgentClueSourceMapper agentClueSourceMapper;

    @Override
    public List<AgentClueSource> findAgentClueSources(String code) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(AGENT_CLUE_SOURCE.CLUE_SOURCE_NAME,AGENT_CLUE_SOURCE.CLUE_SOURCE_TYPE,AGENT_CLUE_SOURCE.CODE)
                .eq(AgentClueSource::getCode,code)
                .orderBy(AgentClueSource::getId)
                .asc();
        return agentClueSourceMapper.selectListByQuery(wrapper);
    }


}
