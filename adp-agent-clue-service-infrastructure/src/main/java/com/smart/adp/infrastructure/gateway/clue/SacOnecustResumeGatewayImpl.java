package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.domain.bo.clue.SacOnecustResumeBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.gateway.clue.SacOnecustResumeGateway;
import com.smart.adp.domain.valueObject.clue.SacOnecustResumeVO;
import com.smart.adp.infrastructure.repository.clue.SacOnecustResumeMapper;
import com.smart.adp.infrastructure.utils.PageConverterFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.smart.adp.domain.valueObject.clue.table.SacOnecustResumeVOTableDef.SAC_ONECUST_RESUME_VO;

/**
 * @Description: 线索回访gateway实现
 * @Author: rik.ren
 * @Date: 2025/3/13 17:40
 **/
@Service
public class SacOnecustResumeGatewayImpl implements SacOnecustResumeGateway {
    @Autowired
    private SacOnecustResumeMapper onecustResumeMapper;

    /**
     * 根据客户id查询跟进记录
     *
     * @param custId
     * @return
     */
    @Override
    public List<SacOnecustResumeVO> queryOnecustResumeByCustId(String custId, QueryOrderBy orderBy, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_ONECUST_RESUME_VO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .eq(SacOnecustResumeVO::getCustId, custId)
                .from(SAC_ONECUST_RESUME_VO.as("resume"));
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_ONECUST_RESUME_VO.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        return onecustResumeMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据实体对象查询跟进记录，无分页
     *
     * @param entityParam
     * @param orderBy
     * @param columns
     * @return
     */
    @Override
    public List<SacOnecustResumeVO> queryOnecustResumeAll(SacOnecustResumeVO entityParam,
                                                          SacOnecustResumeBO resumeBOParam, QueryOrderBy orderBy,
                                                          QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_ONECUST_RESUME_VO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(SAC_ONECUST_RESUME_VO.as("resume"));
        entityParam.conditions(wrapper, resumeBOParam);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_ONECUST_RESUME_VO.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        return onecustResumeMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据实体对象查询跟进记录，有分页
     *
     * @param entityParam
     * @param orderBy
     * @param columns
     * @return
     */
    @Override
    public DomainPage<SacOnecustResumeVO> queryOnecustResume(SacOnecustResumeVO entityParam, SacOnecustResumeBO resumeBOParam,
                                                             QueryOrderBy orderBy,
                                                             QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_ONECUST_RESUME_VO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(SAC_ONECUST_RESUME_VO.as("resume"));
        entityParam.conditions(wrapper, resumeBOParam);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_ONECUST_RESUME_VO.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        Page<SacOnecustResumeVO> paginate = onecustResumeMapper.paginate(resumeBOParam.getPageObj().getPageNumber(),
                resumeBOParam.getPageObj().getPageSize()
                , wrapper);
        DomainPage<SacOnecustResumeVO> domainPage = PageConverterFactory.toDomainPage(paginate);
        return domainPage;
    }

    @Override
    public List<SacOnecustResumeVO> queryResumeListByCustId(List<String> custIds, QueryColumn... columns) {
        if (CollectionUtil.isEmpty(custIds)) {
            return Collections.emptyList();
        }

        QueryWrapper wrapper = QueryWrapper.create();
        wrapper.select(columns)
               .and(SAC_ONECUST_RESUME_VO.CUST_ID.in(custIds));
        return onecustResumeMapper.selectListByQuery(wrapper);
    }
}
