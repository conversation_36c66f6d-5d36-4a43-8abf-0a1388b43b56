package com.smart.adp.infrastructure.helper.smartBinLogMonitor;

import com.github.shyiko.mysql.binlog.BinaryLogClient;
import com.github.shyiko.mysql.binlog.event.*;
import com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer;
import com.smart.adp.domain.enums.DelFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: 监听binlogListener
 * 监听数据库表的增删改操作，将操作记录到数据库表中
 * @Author: rik.ren
 * @Date: 2025/3/13 13:26
 **/

@Slf4j
@Component
@ConditionalOnProperty(prefix = "smartListening", name = "enable", havingValue = "true")
public class BinlogListener {

    //region 配置和注入
    private BinaryLogClient client;

    private Thread listenerThread;

    @Value("${smartListening.dbConfig.ip}")
    private String host;

    @Value("${smartListening.dbConfig.port}")
    private int port;

    @Value("${smartListening.dbConfig.username}")
    private String user;

    @Value("${smartListening.dbConfig.password}")
    private String password;

    // 是否启用监听（配置项）
    @Value("${smartListening.enable}")
    private Boolean listeningEnable;

    // 是否启用结构监听（配置项）
    @Value("${smartListening.structureListen.enable}")
    private Boolean structureEnable;

    // 是否启用数据监听（配置项）
    @Value("${smartListening.dataListen.enable}")
    private Boolean dataListeningEnable;

    // 需要监听的数据库列表（配置项）
    @Value("${smartListening.dbConfig.include-databases}")
    private String[] includeDatabases;

    @Autowired
    private MigrationScriptsMapper migrationScriptsMapper;

    // 允许的数据库集合（小写，方便匹配）
    private Set<String> allowedDatabases = new HashSet<>();

    // 表映射缓存（用于将 tableId 映射到表名）
    private final Map<Long, TableMapEventData> tableMapCache = new ConcurrentHashMap<>();
    //endregion

    @PostConstruct
    public void init() {
        // 初始化允许的数据库集合
        allowedDatabases = Arrays.stream(includeDatabases)
                .map(String::trim)
                .map(String::toLowerCase)
                .collect(Collectors.toSet());
    }

    /**
     * 停机关闭监听
     */
    @PreDestroy
    public void shutdown() {
        if (client != null) {
            try {
                client.disconnect();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        if (listenerThread != null) {
            try {
                // 等待线程结束，最多等待 5 秒
                if (listenerThread.isAlive()) {
                    listenerThread.interrupt();
                    listenerThread.join(5000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 注册监听
     */
    @PostConstruct
    public void startListening() {
        listenerThread = new Thread(() -> {

            client = new BinaryLogClient(host, port, user, password);
            client.setServerId(1);

            EventDeserializer eventDeserializer = new EventDeserializer();
            eventDeserializer.setCompatibilityMode(
                    EventDeserializer.CompatibilityMode.DATE_AND_TIME_AS_LONG,
                    EventDeserializer.CompatibilityMode.CHAR_AND_BINARY_AS_BYTE_ARRAY
            );
            client.setEventDeserializer(eventDeserializer);

            // 注册监听器（处理具体的事件）
            registerListener();

            // 注册生命周期监听器（处理断线重连）
            registerLifycycleListener();

            try {
                client.connect();
                log.info("监听连接数据库成功");
            } catch (IOException e) {
                log.error("监听连接数据库失败", e);
            }
        });

        listenerThread.start();
    }

    private void registerListener() {
        client.registerEventListener(event -> {
            try {
                EventType eventType = event.getHeader().getEventType();
                if (dataListeningEnable) {
                    switch (eventType) {
                        case TABLE_MAP: // 表映射事件
                            handleTableMapEvent(event.getData());
                            break;
                        case WRITE_ROWS: // 插入操作
                            handleWriteRows(event.getData());
                            break;
                        case UPDATE_ROWS: // 更新操作
                            handleUpdateRows(event.getData());
                            break;
                        case DELETE_ROWS: // 删除操作
                            handleDeleteRows(event.getData());
                            break;
                        default:
                            // 忽略其他类型的操作
                            break;
                    }
                } else if (structureEnable) {
                    if (Objects.requireNonNull(eventType) == EventType.QUERY) {
                        handleDdl(event);// DDL操作
                    }
                }
            } catch (Exception e) {
                log.error("监听处理事件时发生错误", e);
            }
        });
    }

    private void registerLifycycleListener() {
        client.registerLifecycleListener(new BinaryLogClient.LifecycleListener() {
            @Override
            public void onConnect(BinaryLogClient client) {
                log.info("已成功连接数据库");
            }

            @Override
            public void onCommunicationFailure(BinaryLogClient client, Exception ex) {
                log.info("监听连接丢失，开始重连: " + ex.getMessage());
                handleReconnect(client);
            }

            @Override
            public void onEventDeserializationFailure(BinaryLogClient client, Exception ex) {
                log.error("监听解析事件错误: " + ex.getMessage());
            }

            @Override
            public void onDisconnect(BinaryLogClient client) {
                log.error("监听断线了");
            }
        });
    }

    /**
     * 处理断线重连逻辑
     *
     * @param client BinaryLogClient 实例
     */
    private void handleReconnect(BinaryLogClient client) {
        int retryCount = 0;
        final int maxRetries = 20; // 最大重试次数
        final long retryIntervalMillis = 5000; // 每次重试间隔（毫秒）

        while (retryCount < maxRetries) {
            try {
                retryCount++;
                log.info("监听尝试重连数据库 " + retryCount);
                TimeUnit.MILLISECONDS.sleep(retryIntervalMillis); // 等待一段时间后重试
                client.connect();
                log.info("监听尝试重连成功");
                return;
            } catch (Exception e) {
                log.error("监听重连时异常", e);
            }
        }
        log.error("监听重连已达最大次数，重连失败");
    }

    /**
     * 提取 DDL 语句
     *
     * @param event QueryEventData
     * @return
     */
    private void handleDdl(Event event) {
        QueryEventData data = (QueryEventData) event.getData();
        // 获取数据库和 SQL 语句
        String database = data.getDatabase();
        // 1. 检查数据库是否在允许的列表中
        if (database == null || !allowedDatabases.contains(database.toLowerCase())) {
            // 跳过不在配置中的数据库
            return;
        }

        // 2. 排除事务控制语句
        String sql = data.getSql().trim().toLowerCase();
        if (sql.startsWith("begin") || sql.startsWith("commit") || sql.startsWith("rollback")) {
            return;
        }

        // 3. 判断是否为 DDL 语句
        if (!isDDL(sql)) {
            return;
        }
        // 进一步处理
        log.info("有效 DDL: {}", data.getSql());
        MigrationScripts migrationScripts = MigrationScripts.builder()
                .schema(database)
                .script(data.getSql())
                .eventDate(LocalDateTime.now())
                .version(new SimpleDateFormat("yyyyMMdd").format(new Date()))
                .delFlag(DelFlagEnum.NORMAL.getCode())
                .build();
        migrationScriptsMapper.insert(migrationScripts);
    }

    /**
     * 判断是否满足ddl
     *
     * @param sql
     * @return
     */
    private boolean isDDL(String sql) {
        return Pattern.compile(
                "^(create|alter|drop|truncate|rename|revoke|grant|lock|unlock)\\s+",
                Pattern.CASE_INSENSITIVE
        ).matcher(sql).find();
    }

    /**
     * 处理表映射事件
     *
     * @param eventData 表映射事件数据
     */
    private void handleTableMapEvent(TableMapEventData eventData) {
        if (!allowedDatabases.contains(eventData.getDatabase().toLowerCase())) {
            return;
        }
        tableMapCache.put(eventData.getTableId(), eventData); // 缓存 tableId 和表名及列信息
    }

    /**
     * 处理插入事件
     *
     * @param eventData 插入事件数据
     */
    private void handleWriteRows(WriteRowsEventData eventData) {
        TableMapEventData tableMapEventData = tableMapCache.get(eventData.getTableId());
        String tableName = tableMapEventData.getTable();
        if (tableName == null) {
            log.error("插入操作没有找到表名" + eventData.getTableId());
            return;
        }
        log.info("插入操作，表名: " + tableName);
        for (Serializable[] row : eventData.getRows()) {
            log.info("插入操作数据: " + formatRow(row, tableMapEventData));
        }
    }

    /**
     * 处理更新事件
     *
     * @param eventData 更新事件数据
     */
    private void handleUpdateRows(UpdateRowsEventData eventData) {
        TableMapEventData tableMapEventData = tableMapCache.get(eventData.getTableId());
        String tableName = tableMapEventData.getTable();
        if (tableName == null) {
            log.error("更新操作没有找到表名" + eventData.getTableId());
            return;
        }
        log.info("更新操作，表名: " + tableName);
        // 获取更新前后的行数据
        List<Map.Entry<Serializable[], Serializable[]>> rows = eventData.getRows();
        for (Map.Entry<Serializable[], Serializable[]> entry : rows) {
            log.info("更新操作原数据: " + formatRow(entry.getKey(), tableMapEventData));
            log.info("更新操作新数据: " + formatRow(entry.getValue(), tableMapEventData));
        }
    }

    /**
     * 处理删除事件
     *
     * @param eventData 删除事件数据
     */
    private void handleDeleteRows(DeleteRowsEventData eventData) {
        TableMapEventData tableMapEventData = tableMapCache.get(eventData.getTableId());
        String tableName = tableMapEventData.getTable();
        if (tableName == null) {
            log.error("删除操作没有找到表名" + eventData.getTableId());
            return;
        }
        log.info("删除操作，表名: " + tableName);
        for (Serializable[] row : eventData.getRows()) {
            log.info("删除操作数据: " + formatRow(row, tableMapEventData));
        }
    }

    /**
     * 格式化行数据为字符串，并尝试解析二进制数据
     *
     * @param row               行数据
     * @param tableMapEventData 表映射事件数据（包含列信息）
     * @return 格式化后的字符串
     */
    private String formatRow(Serializable[] row, TableMapEventData tableMapEventData) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < row.length; i++) {
            Object value = row[i];
            if (value instanceof byte[]) {
                // 尝试将字节数组转换为字符串
                value = new String((byte[]) value, StandardCharsets.UTF_8);
            }
            sb.append(value);
            if (i < row.length - 1) {
                sb.append(", ");
            }
        }
        sb.append("]");
        return sb.toString();
    }
}