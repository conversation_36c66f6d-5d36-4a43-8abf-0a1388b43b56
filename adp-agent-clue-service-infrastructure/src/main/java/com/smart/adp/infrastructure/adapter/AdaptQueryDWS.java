package com.smart.adp.infrastructure.adapter;

import com.smart.adp.infrastructure.feign.request.QueryActiveInfoReq;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 查询DWS服务入参适配器的真正实现类
 * @Author: rik.ren
 * @Date: 2025/03/11 20:16
 **/
@Component
public class AdaptQueryDWS extends AbstractIAdapterClueConvent {
    /**
     * 调用DWS服务，针对DWS的查询线索活跃信息接口的入参进行适配
     *
     * @param param
     * @return
     */
    @Override
    public QueryActiveInfoReq queryClueActiveInfo(List<String> param) {
        return QueryActiveInfoReq.builder().listPhone(param).build();
    }
}
