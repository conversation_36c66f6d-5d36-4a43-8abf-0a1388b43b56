package com.smart.adp.infrastructure.gateway.clue;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.entity.clue.ActivityInfo;
import com.smart.adp.domain.gateway.clue.ActivityGateway;

import com.smart.adp.infrastructure.repository.clue.ActivityMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.smart.adp.domain.entity.clue.table.ActivityInfoTableDef.ACTIVITY_INFO;


/**
 * <AUTHOR>
 * date 2025/3/5 11:07
 * @description
 **/
@Slf4j
@Component
public class ActivityGatewayImpl implements ActivityGateway {

    @Autowired
    private ActivityMapper activityMapper;

    @Override
    public ActivityInfo findActivityInfo(String activityId, String isEnable) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(ACTIVITY_INFO.ACTIVITY_ID, ACTIVITY_INFO.CREATE_TYPE_CODE, ACTIVITY_INFO.ACTIVITY_NAME,
                        ACTIVITY_INFO.INFO_CHAN_DCODE, ACTIVITY_INFO.INFO_CHAN_DNAME, ACTIVITY_INFO.INFO_CHAN_DDESC)
                .eq(ActivityInfo::getActivityId, activityId)
                .eq(ActivityInfo::getIsEnable, isEnable)
                .limit(1)
                .orderBy(ActivityInfo::getCreatedDate)
                .desc();
        return activityMapper.selectOneByQuery(wrapper);
    }

    /**
     * 获取指定活动信息
     *
     * @param activityId
     * @param date
     * @param createTypeCode
     * @param isEnable
     * @param statusCode
     * @param releaseStatusCode
     * @return
     */
    @Override
    public ActivityInfo findActivityData(String activityId, Date date, String createTypeCode, String isEnable, String statusCode, String releaseStatusCode) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(ACTIVITY_INFO.ACTIVITY_ID, ACTIVITY_INFO.CREATE_TYPE_CODE, ACTIVITY_INFO.ACTIVITY_NAME,
                        ACTIVITY_INFO.INFO_CHAN_DCODE, ACTIVITY_INFO.INFO_CHAN_DNAME, ACTIVITY_INFO.INFO_CHAN_DDESC)
                .eq(ActivityInfo::getActivityId, activityId)
                .eq(ActivityInfo::getCreateTypeCode, createTypeCode)
                .eq(ActivityInfo::getStatusCode, statusCode)
                .eq(ActivityInfo::getReleaseStatusCode, releaseStatusCode)
                .eq(ActivityInfo::getIsEnable, isEnable)
                .and(ACTIVITY_INFO.END_TIME.gt(date))
                .groupBy(ActivityInfo::getActivityId)
                .orderBy(ActivityInfo::getLastUpdatedDate)
                .desc();
        return activityMapper.selectOneByQuery(wrapper);
    }
}
