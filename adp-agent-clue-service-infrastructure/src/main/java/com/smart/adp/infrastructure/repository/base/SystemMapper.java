package com.smart.adp.infrastructure.repository.base;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.base.SystemConfig;
import com.smart.adp.domain.valueObject.base.SystemConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface SystemMapper extends BaseMapper<SystemConfig> {

    List<SystemConfigVO> selectByConfigCode(@Param("configCode")String configCode, @Param("orgCode")String orgCode, @Param("isEnable")String isEnable);


    List<SystemConfig> findSystemConfigByCode(@Param("configCode")String configCode);
}
