package com.smart.adp.infrastructure.repository.clue;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.bo.clue.CustEventFlowBO;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
public interface CustEventFlowMapper extends BaseMapper<CustEventFlow> {

    List<CustEventFlow> selectEventFlowLast(@Param("param") CustEventFlowBO boParam);

    List<CustEventFlowBO> selectEventFlowLastWithClueRemark(@Param("param") CustEventFlowBO boParam);
}
