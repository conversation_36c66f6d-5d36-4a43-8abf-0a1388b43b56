package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.base.AgentEmployeeBO;
import com.smart.adp.domain.entity.base.AgentEmployee;
import com.smart.adp.domain.gateway.clue.AgentEmployeeGateway;
import com.smart.adp.infrastructure.repository.base.AgentEmployeeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.smart.adp.domain.entity.base.table.AgentDlrInfoTableDef.AGENT_DLR_INFO;
import static com.smart.adp.domain.entity.base.table.AgentEmployeeTableDef.AGENT_EMPLOYEE;

/**
 * <AUTHOR>
 * date 2025/3/5 11:07
 * @description
 **/
@Slf4j
@Component
public class AgentEmployeeGatewayImpl implements AgentEmployeeGateway {

    @Autowired
    AgentEmployeeMapper agentEmployeeMapper;

    @Override
    public AgentEmployee findOne(String userId, List<String> userStatusList) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(AGENT_EMPLOYEE.EMP_ID, AGENT_EMPLOYEE.EMP_CODE, AGENT_EMPLOYEE.EMP_NAME, AGENT_EMPLOYEE.USER_ID,
                        AGENT_EMPLOYEE.USER_NAME, AGENT_EMPLOYEE.MOBILE, AGENT_EMPLOYEE.DLR_CODE, AGENT_EMPLOYEE.USER_STATUS,
                        AGENT_EMPLOYEE.HEAD_PORTRAIT)
                .eq(AgentEmployee::getUserId, userId)
                .in(AgentEmployee::getUserStatus, userStatusList)
                .limit(1)
                .orderBy(AgentEmployee::getCreatedDate)
                .desc();
        return agentEmployeeMapper.selectOneByQuery(wrapper);
    }

    @Override
    public AgentEmployee findEmployee(String empCode) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(AGENT_EMPLOYEE.EMP_ID, AGENT_EMPLOYEE.EMP_CODE, AGENT_EMPLOYEE.EMP_NAME, AGENT_EMPLOYEE.USER_ID,
                        AGENT_EMPLOYEE.USER_NAME, AGENT_EMPLOYEE.MOBILE, AGENT_EMPLOYEE.DLR_CODE, AGENT_EMPLOYEE.USER_STATUS)
                .eq(AgentEmployee::getEmpCode, empCode)
                .limit(1)
                .orderBy(AgentEmployee::getCreatedDate)
                .desc();
        return agentEmployeeMapper.selectOneByQuery(wrapper);
    }

    @Override
    public List<AgentEmployee> findEmployee(AgentEmployee entityParam, AgentEmployeeBO boParam, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{AGENT_EMPLOYEE.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(AGENT_EMPLOYEE.as("emp"));
        entityParam.conditions(wrapper, boParam);
        return agentEmployeeMapper.selectListByQuery(wrapper);
    }

    @Override
    public List<AgentEmployee> findEmployeeByDlrCode(AgentEmployee entityParam, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{AGENT_EMPLOYEE.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(AGENT_EMPLOYEE.as("emp"))
                .eq(AgentEmployee::getDlrCode, entityParam.getDlrCode())
                .eq(AgentEmployee::getUserStatus, entityParam.getUserStatus())
                .orderBy(AgentEmployee::getCreatedDate)
                .desc();
        return agentEmployeeMapper.selectListByQuery(wrapper);
    }

    @Override
    public List<AgentEmployeeBO> findEmployeeDlr(AgentEmployee entityParam, AgentEmployeeBO boParam, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{AGENT_EMPLOYEE.ALL_COLUMNS, AGENT_DLR_INFO.DLR_SHORT_NAME,
                    AGENT_DLR_INFO.DLR_FULL_NAME, AGENT_DLR_INFO.CITY_ID, AGENT_DLR_INFO.DLR_TYPE, AGENT_DLR_INFO.COUNTY_ID,
                    AGENT_DLR_INFO.PROVINCE_ID};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(AGENT_EMPLOYEE.as("emp"))
                .leftJoin(AGENT_DLR_INFO.as("dlr")).on(AGENT_EMPLOYEE.DLR_CODE.eq(AGENT_DLR_INFO.DLR_CODE));
        entityParam.conditions(wrapper, boParam);
        return agentEmployeeMapper.selectListByQueryAs(wrapper, AgentEmployeeBO.class);
    }
}
