package com.smart.adp.infrastructure.gateway.clue;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.entity.clue.CityClueSwitch;
import com.smart.adp.domain.gateway.clue.CityClueSwitchGateway;
import com.smart.adp.infrastructure.repository.clue.CityClueSwitchMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.smart.adp.domain.entity.clue.table.CityClueSwitchTableDef.CITY_CLUE_SWITCH;

/**
 * <AUTHOR>
 * date 2025/3/9 16:11
 * @description
 **/
@Slf4j
@Component
public class CityClueSwitchGatewayImpl implements CityClueSwitchGateway {

    @Autowired
    private CityClueSwitchMapper cityClueSwitchMapper;

    @Override
    public CityClueSwitch findOne(String dlrCode) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(CITY_CLUE_SWITCH.SWITCH_VALUE, CITY_CLUE_SWITCH.DLR_CODE)
                .eq(CityClueSwitch::getDlrCode, dlrCode)
                .limit(1)
                .orderBy(CityClueSwitch::getCreatedDate)
                .desc();
        return cityClueSwitchMapper.selectOneByQuery(wrapper);
    }
}
