package com.smart.adp.infrastructure.gateway.koc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.smart.adp.domain.bo.koc.KocTagBO;
import com.smart.adp.domain.entity.koc.SacTagInfo;
import com.smart.adp.domain.enums.KocRelTypeEnum;
import com.smart.adp.domain.gateway.koc.KocTagGateway;
import com.smart.adp.infrastructure.repository.koc.SacTagInfoMapper;
import com.smart.adp.infrastructure.repository.koc.SacUserTagRelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: KOC标签网关实现
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Slf4j
@Service
public class KocTagGatewayImpl implements KocTagGateway {

    @Autowired
    private SacTagInfoMapper sacTagInfoMapper;

    @Autowired
    private SacUserTagRelMapper sacUserTagRelMapper;

    @Override
    public SacTagInfo findById(String tagId) {
        if (StrUtil.isBlank(tagId)) {
            return null;
        }
        return sacTagInfoMapper.findByTagId(tagId);
    }

    @Override
    public List<SacTagInfo> findByCondition(SacTagInfo condition) {
        if (ObjectUtil.isEmpty(condition)) {
            return new ArrayList<>();
        }
        return sacTagInfoMapper.selectListByQuery(condition);
    }

    @Override
    public List<SacTagInfo> findAllEnabled() {
        return sacTagInfoMapper.findAllEnabled();
    }

    @Override
    public List<SacTagInfo> findByParentId(String parentTagId) {
        if (StrUtil.isBlank(parentTagId)) {
            // 查询一级标签（parent_tag_id为空）
            return sacTagInfoMapper.findByParentTagId(null);
        }
        return sacTagInfoMapper.findByParentTagId(parentTagId);
    }

    @Override
    public List<SacTagInfo> findByLevel(Integer tagLevel) {
        if (tagLevel == null) {
            return new ArrayList<>();
        }
        return sacTagInfoMapper.findByLevel(tagLevel);
    }

    @Override
    public List<KocTagBO> findTagTree() {
        // 查询所有启用的标签
        List<SacTagInfo> allTags = findAllEnabled();
        if (CollectionUtil.isEmpty(allTags)) {
            return new ArrayList<>();
        }

        // 转换为BO对象
        List<KocTagBO> tagBOs = allTags.stream()
                .map(KocTagBO::buildFromEntity)
                .collect(Collectors.toList());

        // 构建树形结构
        return buildTagTree(tagBOs);
    }

    @Override
    public List<SacTagInfo> findByNameLike(String tagName) {
        if (StrUtil.isBlank(tagName)) {
            return new ArrayList<>();
        }
        return sacTagInfoMapper.findByNameLike(tagName);
    }

    @Override
    public Boolean save(SacTagInfo tagInfo) {
        if (ObjectUtil.isEmpty(tagInfo)) {
            return false;
        }
        try {
            int result = sacTagInfoMapper.insert(tagInfo);
            return result > 0;
        } catch (Exception e) {
            log.error("保存标签信息失败", e);
            return false;
        }
    }

    @Override
    public Boolean update(SacTagInfo tagInfo) {
        if (ObjectUtil.isEmpty(tagInfo) || tagInfo.getId() == null) {
            return false;
        }
        try {
            int result = sacTagInfoMapper.update(tagInfo);
            return result > 0;
        } catch (Exception e) {
            log.error("更新标签信息失败", e);
            return false;
        }
    }

    @Override
    public Boolean deleteById(String tagId) {
        if (StrUtil.isBlank(tagId)) {
            return false;
        }
        try {
            SacTagInfo tagInfo = findById(tagId);
            if (ObjectUtil.isEmpty(tagInfo)) {
                return false;
            }
            int result = sacTagInfoMapper.deleteById(tagInfo.getId());
            return result > 0;
        } catch (Exception e) {
            log.error("删除标签信息失败", e);
            return false;
        }
    }

    @Override
    public Boolean batchDelete(List<String> tagIds) {
        if (CollectionUtil.isEmpty(tagIds)) {
            return false;
        }
        try {
            // 根据业务ID查询主键ID
            List<Long> ids = new ArrayList<>();
            for (String tagId : tagIds) {
                SacTagInfo tagInfo = findById(tagId);
                if (ObjectUtil.isNotEmpty(tagInfo)) {
                    ids.add(tagInfo.getId());
                }
            }
            
            if (CollectionUtil.isEmpty(ids)) {
                return false;
            }
            
            int result = sacTagInfoMapper.deleteBatchByIds(ids);
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除标签信息失败", e);
            return false;
        }
    }

    @Override
    public Boolean updateStatus(String tagId, Integer status) {
        if (StrUtil.isBlank(tagId) || status == null) {
            return false;
        }
        try {
            int result = sacTagInfoMapper.updateStatusByTagId(tagId, status);
            return result > 0;
        } catch (Exception e) {
            log.error("更新标签状态失败", e);
            return false;
        }
    }

    @Override
    public Boolean existsByName(String tagName, String parentTagId, String excludeTagId) {
        if (StrUtil.isBlank(tagName)) {
            return false;
        }
        try {
            Integer count = sacTagInfoMapper.countByNameAndParentTagId(tagName, parentTagId, excludeTagId);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查标签名称是否存在失败", e);
            return false;
        }
    }

    @Override
    public Integer countUsage(String tagId) {
        if (StrUtil.isBlank(tagId)) {
            return 0;
        }
        try {
            return sacUserTagRelMapper.countUsageByRefBusinessId(tagId, KocRelTypeEnum.TAG.getCode());
        } catch (Exception e) {
            log.error("统计标签使用次数失败", e);
            return 0;
        }
    }

    @Override
    public List<KocTagBO> batchCountUsage(List<String> tagIds) {
        if (CollectionUtil.isEmpty(tagIds)) {
            return new ArrayList<>();
        }

        List<KocTagBO> result = new ArrayList<>();
        for (String tagId : tagIds) {
            SacTagInfo tagInfo = findById(tagId);
            if (ObjectUtil.isNotEmpty(tagInfo)) {
                KocTagBO tagBO = KocTagBO.buildFromEntity(tagInfo);
                Integer useCount = countUsage(tagId);
                tagBO.setUseCount(useCount);
                result.add(tagBO);
            }
        }
        return result;
    }

    /**
     * 构建标签树形结构
     */
    private List<KocTagBO> buildTagTree(List<KocTagBO> allTags) {
        if (CollectionUtil.isEmpty(allTags)) {
            return new ArrayList<>();
        }

        // 按层级分组
        Map<Integer, List<KocTagBO>> levelMap = allTags.stream()
                .collect(Collectors.groupingBy(KocTagBO::getTagLevel));

        // 构建父子关系映射
        Map<String, List<KocTagBO>> parentChildMap = new HashMap<>();
        for (KocTagBO tag : allTags) {
            String parentTagId = tag.getParentTagId();
            if (StrUtil.isBlank(parentTagId)) {
                parentTagId = "ROOT";
            }
            parentChildMap.computeIfAbsent(parentTagId, k -> new ArrayList<>()).add(tag);
        }

        // 设置子标签
        for (KocTagBO tag : allTags) {
            List<KocTagBO> children = parentChildMap.get(tag.getTagId());
            if (CollectionUtil.isNotEmpty(children)) {
                tag.setChildren(children);
                tag.setHasChildren(true);
            } else {
                tag.setChildren(new ArrayList<>());
                tag.setHasChildren(false);
            }
        }

        // 返回根节点（一级标签）
        return parentChildMap.getOrDefault("ROOT", new ArrayList<>());
    }
}
