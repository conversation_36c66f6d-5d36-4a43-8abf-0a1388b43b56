package com.smart.adp.infrastructure.feign.response;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.domain.valueObject.clue.ClueActiveInfoVO;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 线索活跃信息
 * @Author: rik.ren
 * @Date: 2025/3/11 20:25
 **/
@Data
@ToString
public class ClueActiveInfoRsp implements Serializable {
    /**
     * 时间
     */
    private String dealDate;
    /**
     * 手机号
     */
    private String leadsId;
    /**
     * smartId
     */
    private String smartId;
    /**
     * 7天内活跃的天数
     */
    private Long activeDays7d;
    /**
     * 30天内活跃的天数
     */
    private Long activeDays30d;
    /**
     * 用户成熟度
     */
    private String matureDegree;
    private Long touchTimes;
    private Long activeTimesIn1h;
    private Long activeTimesIn2h;
    private Long activeTimesIn3h;
    private Long activeTimesIn4h;
    private Long activeTimesIn5h;
    private Long activeTimesIn6h;
    private Long activeTimesIn7h;
    private Long activeTimesIn8h;
    private Long activeTimesIn9h;
    private Long activeTimesIn10h;
    private Long activeTimesIn11h;
    private Long activeTimesIn12h;
    private Long activeTimesIn13h;
    private Long activeTimesIn14h;
    private Long activeTimesIn15h;
    private Long activeTimesIn16h;
    private Long activeTimesIn17h;
    private Long activeTimesIn18h;
    private Long activeTimesIn19h;
    private Long activeTimesIn20h;
    private Long activeTimesIn21h;
    private Long activeTimesIn22h;
    private Long activeTimesIn23h;
    private Long activeTimesIn24h;
    /**
     * custId
     */
    private String adpCustId;
    /**
     *
     */
    private String keyEvent;

    public static List<ClueActiveInfoVO> convertToClueActiveInfoVO(List<ClueActiveInfoRsp> listResponse) {
        if (CollectionUtil.isEmpty(listResponse)) {
            return Collections.emptyList();
        }
        List<ClueActiveInfoVO> listResult = new ArrayList<>();
        listResponse.forEach(response -> {
            listResult.add(ClueActiveInfoVO.builder()
                    .phone(response.getLeadsId())  // leadsId 对应 phone
                    .smartId(response.getSmartId())
                    .activeDays7d(response.getActiveDays7d() != null ? response.getActiveDays7d().intValue() : null)
                    .activeDays30d(response.getActiveDays30d() != null ? response.getActiveDays30d().intValue() : null)
                    .matureDegree(response.getMatureDegree())
                    .touchTimes(response.getTouchTimes())
                    .activeTimesIn1h(response.getActiveTimesIn1h())
                    .activeTimesIn2h(response.getActiveTimesIn2h())
                    .activeTimesIn3h(response.getActiveTimesIn3h())
                    .activeTimesIn4h(response.getActiveTimesIn4h())
                    .activeTimesIn5h(response.getActiveTimesIn5h())
                    .activeTimesIn6h(response.getActiveTimesIn6h())
                    .activeTimesIn7h(response.getActiveTimesIn7h())
                    .activeTimesIn8h(response.getActiveTimesIn8h())
                    .activeTimesIn9h(response.getActiveTimesIn9h())
                    .activeTimesIn10h(response.getActiveTimesIn10h())
                    .activeTimesIn11h(response.getActiveTimesIn11h())
                    .activeTimesIn12h(response.getActiveTimesIn12h())
                    .activeTimesIn13h(response.getActiveTimesIn13h())
                    .activeTimesIn14h(response.getActiveTimesIn14h())
                    .activeTimesIn15h(response.getActiveTimesIn15h())
                    .activeTimesIn16h(response.getActiveTimesIn16h())
                    .activeTimesIn17h(response.getActiveTimesIn17h())
                    .activeTimesIn18h(response.getActiveTimesIn18h())
                    .activeTimesIn19h(response.getActiveTimesIn19h())
                    .activeTimesIn20h(response.getActiveTimesIn20h())
                    .activeTimesIn21h(response.getActiveTimesIn21h())
                    .activeTimesIn22h(response.getActiveTimesIn22h())
                    .activeTimesIn23h(response.getActiveTimesIn23h())
                    .activeTimesIn24h(response.getActiveTimesIn24h())
                    .custId(response.getAdpCustId())  // adpCustId 对应 custId
                    .build());
        });
        return listResult;
    }
}
