package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.MapperUtil;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.adapter.IAdapterClueRequestEntity;
import com.smart.adp.domain.bo.clue.SacClueInfoDlrBO;
import com.smart.adp.domain.common.annotation.SmartADPCache;
import com.smart.adp.domain.common.constants.StringConstant;
import com.smart.adp.domain.context.TimeContext;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlrLog;
import com.smart.adp.domain.entity.clue.SacHistoryClueInfoDlr;
import com.smart.adp.domain.enums.*;
import com.smart.adp.domain.gateway.clue.ClueDlrGateway;
import com.smart.adp.domain.qry.ClueDlrQry;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.domain.valueObject.clue.CalenderCountVO;
import com.smart.adp.domain.valueObject.clue.ClueActiveInfoVO;
import com.smart.adp.domain.valueObject.clue.ClueDlrListVO;
import com.smart.adp.domain.valueObject.clue.ClueDlrStatisticsVO;
import com.smart.adp.infrastructure.feign.DWSFeign;
import com.smart.adp.infrastructure.feign.response.ClueActiveInfoRsp;
import com.smart.adp.infrastructure.repository.clue.*;
import com.smart.adp.infrastructure.utils.CacheUtil;
import com.smart.tools.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mybatisflex.core.query.QueryMethods.selectOne;
import static com.smart.adp.domain.common.constants.BizConstants.LONG_DEFAULT_VALUE;
import static com.smart.adp.domain.common.constants.BizConstants.TRANSFER_DLR_APPLY_TYPE_CODE;
import static com.smart.adp.domain.common.constants.StringConstant.CLUE_LOG_PREFIX;
import static com.smart.adp.domain.entity.clue.table.SacAllClueInfoDlrTableDef.SAC_ALL_CLUE_INFO_DLR;
import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;
import static com.smart.adp.domain.entity.clue.table.SacOneCustRemarkTableDef.SAC_ONE_CUST_REMARK;
import static com.smart.adp.domain.entity.clue.table.SacReviewAuditTableDef.SAC_REVIEW_AUDIT;
import static com.smart.adp.domain.entity.clue.table.SacReviewTableDef.SAC_REVIEW;
import static com.smart.adp.domain.entity.clue.table.SacTransferApplyTableDef.SAC_TRANSFER_APPLY;
import static com.smart.adp.domain.entity.clue.table.SacTransferAuditTableDef.SAC_TRANSFER_AUDIT;
import static com.smart.adp.domain.entity.task.table.OnetaskInfoTableDef.ONETASK_INFO;
import static com.smart.adp.domain.valueObject.clue.table.SacClueMsgRecordVOTableDef.SAC_CLUE_MSG_RECORD_VO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
@Slf4j
@Component
public class ClueDlrGatewayImpl implements ClueDlrGateway {

    @Autowired
    private ClueDlrMapper clueDlrMapper;

    @Autowired
    private AllClueDlrMapper allClueDlrMapper;

    @Autowired
    private ClueHistoryDlrMapper clueHistoryDlrMapper;

    @Autowired
    private SacClueInfoDlrLogMapper sacClueInfoDlrLogMapper;

    @Autowired
    private SacTransferApplyMapper transferApplyMapper;

    @Autowired
    private SacClueMsgRecordMapper msgRecordMapper;

    @Resource
    private IAdapterClueRequestEntity adapterClueRequest;

    @Autowired
    private DWSFeign dwsFeign;

    @Autowired
    private CacheUtil cacheUtil;

    private static final String ACTIVE_CACHE_PREFIX = "CLUE:ACTIVE:";
    public static final Duration ACTIVE_CACHE_EXPIRE = Duration.ofHours(10);

    @Override
    public SacClueInfoDlr findLastOne(String statusCode, QueryColumn... columns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .eq(SacClueInfoDlr::getStatusCode, statusCode)
                .limit(1)
                .orderBy(SacClueInfoDlr::getCreatedDate)
                .desc();
        return clueDlrMapper.selectOneByQuery(wrapper);
    }

    @Override
    public SacClueInfoDlr findClueByPhone(String phone) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.CUST_NAME, SAC_CLUE_INFO_DLR.PHONE,
                        SAC_CLUE_INFO_DLR.INFO_CHAN_MNAME, SAC_CLUE_INFO_DLR.INFO_CHAN_DNAME, SAC_CLUE_INFO_DLR.CREATED_DATE,
                        SAC_CLUE_INFO_DLR.INFO_CHAN_DCODE, SAC_CLUE_INFO_DLR.INFO_CHAN_DNAME, SAC_CLUE_INFO_DLR.DLR_CODE,
                        SAC_CLUE_INFO_DLR.UPDATE_CONTROL_ID, SAC_CLUE_INFO_DLR.REVIEW_ID, SAC_CLUE_INFO_DLR.SERVER_ORDER,
                        SAC_CLUE_INFO_DLR.REVIEW_PERSON_NAME, SAC_CLUE_INFO_DLR.COLUMN10, SAC_CLUE_INFO_DLR.INTEN_LEVEL_CODE)
                .eq(SacClueInfoDlr::getPhone, phone)
                //原本customString配置信息 非战败状态 not in 10
                .notIn(SacClueInfoDlr::getStatusCode, ClueStatusEnum.DEFEATED.getCode())
                .limit(1)
                .orderBy(SacClueInfoDlr::getCreatedDate)
                .desc();
        return clueDlrMapper.selectOneByQuery(wrapper);
    }

    private static QueryWrapper qry2Wrapper(ClueDlrQry qry) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SAC_CLUE_INFO_DLR.ID, SAC_REVIEW.CUST_ID, SAC_REVIEW.CUST_NAME, SAC_REVIEW.PHONE,
                        SAC_CLUE_INFO_DLR.INFO_CHAN_MNAME.as(ClueDlrListVO::getChannelMName), SAC_CLUE_INFO_DLR.MANAGE_LABEL_CODE,
                        SAC_CLUE_INFO_DLR.INFO_CHAN_DNAME.as(ClueDlrListVO::getChannelDName), SAC_CLUE_INFO_DLR.COLUMN6.as(ClueDlrListVO::getHeat),
                        SAC_CLUE_INFO_DLR.COLUMN18.as(ClueDlrListVO::getStage), SAC_REVIEW.CREATED_DATE,
                        SAC_REVIEW.PLAN_REVIEW_TIME.as(ClueDlrListVO::getOverdueTime), SAC_CLUE_INFO_DLR.ALLOCATE_TIME,
                        SAC_REVIEW.COLUMN11.as(ClueDlrListVO::getReviewType), SAC_ONE_CUST_REMARK.REMARK.as(ClueDlrListVO::getCustDesc),
                        SAC_REVIEW.REVIEW_DESC, SAC_CLUE_INFO_DLR.INTEN_CAR_TYPE_NAME.as(ClueDlrListVO::getIntentionCarType),
                        SAC_ONE_CUST_REMARK.CLUE_LEVEL.as(ClueDlrListVO::getLevel), SAC_CLUE_INFO_DLR.SERVER_ORDER,
                        SAC_ONE_CUST_REMARK.LAST_ACTIVE_TIME,
                        SAC_REVIEW.REVIEW_PERSON_NAME, SAC_CLUE_INFO_DLR.STATUS_CODE, SAC_REVIEW.LAST_REVIEW_TIME,
                        SAC_CLUE_INFO_DLR.REVIEW_ID);

        qry.from(wrapper)
                .conditions(wrapper);
        return wrapper;
    }

    private static QueryWrapper qry2DefeatWrapper(ClueDlrQry qry) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SAC_ALL_CLUE_INFO_DLR.ID, SAC_ALL_CLUE_INFO_DLR.CUST_ID, SAC_ALL_CLUE_INFO_DLR.CUST_NAME,
                        SAC_ALL_CLUE_INFO_DLR.PHONE,
                        SAC_ALL_CLUE_INFO_DLR.INFO_CHAN_MNAME.as(ClueDlrListVO::getChannelMName),
                        SAC_ALL_CLUE_INFO_DLR.INFO_CHAN_DNAME.as(ClueDlrListVO::getChannelDName),
                        SAC_ALL_CLUE_INFO_DLR.COLUMN18.as(ClueDlrListVO::getStage), SAC_ALL_CLUE_INFO_DLR.CREATED_DATE,
                        SAC_CLUE_INFO_DLR.ALLOCATE_TIME,
                        SAC_ONE_CUST_REMARK.REMARK.as(ClueDlrListVO::getCustDesc),
                        SAC_ALL_CLUE_INFO_DLR.INTEN_CAR_TYPE_NAME.as(ClueDlrListVO::getIntentionCarType),
                        SAC_ONE_CUST_REMARK.CLUE_LEVEL.as(ClueDlrListVO::getLevel), SAC_ALL_CLUE_INFO_DLR.SERVER_ORDER,
                        SAC_ONE_CUST_REMARK.LAST_ACTIVE_TIME,
                        SAC_ALL_CLUE_INFO_DLR.REVIEW_PERSON_NAME, SAC_ALL_CLUE_INFO_DLR.STATUS_CODE, SAC_ALL_CLUE_INFO_DLR.COLUMN6.as(ClueDlrListVO::getHeat),
                        SAC_ALL_CLUE_INFO_DLR.LAST_REVIEW_TIME, SAC_ALL_CLUE_INFO_DLR.REVIEW_ID, SAC_ALL_CLUE_INFO_DLR.MANAGE_LABEL_CODE);

        qry.defeatFrom(wrapper)
                .defeatConditions(wrapper);
        return wrapper;
    }

    @Override
    public List<ClueDlrListVO> list(ClueDlrQry qry) {
        QueryWrapper wrapper = qry2Wrapper(qry);

        return clueDlrMapper.selectListByQueryAs(wrapper, ClueDlrListVO.class);
    }


    @Override
    public List<ClueDlrListVO> defeatList(ClueDlrQry qry) {
        QueryWrapper wrapper = qry2DefeatWrapper(qry);

        return clueDlrMapper.selectListByQueryAs(wrapper, ClueDlrListVO.class);
    }

    @Override
    public Page<ClueDlrListVO> page(ClueDlrQry qry, Page<ClueDlrListVO> page) {
        QueryWrapper wrapper = qry2Wrapper(qry);
        qry.orderBy(wrapper);

        return clueDlrMapper.paginateAs(page, wrapper, ClueDlrListVO.class);
    }

    @Override
    public Page<ClueDlrListVO> defeatPage(ClueDlrQry qry, Page<ClueDlrListVO> page) {
        QueryWrapper wrapper = qry2DefeatWrapper(qry);
        wrapper.orderBy(SAC_ALL_CLUE_INFO_DLR.LAST_REVIEW_TIME, false);

        return clueDlrMapper.paginateAs(page, wrapper, ClueDlrListVO.class);
    }

    @Override
    public SacClueInfoDlr findById(String id, QueryColumn... columns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .eq(SacClueInfoDlr::getId, id);
        SacClueInfoDlr sacClueInfoDlr = clueDlrMapper.selectOneByQuery(wrapper);
        return sacClueInfoDlr;
    }

    @Override
//    @SmartADPCache(value = "ClueInfoDlr", key = "#param.custId", expire = 10, timeUnit = TimeUnit.MINUTES)
    public SacClueInfoDlr findByCondition(SacClueInfoDlr param, QueryColumn... columns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .limit(1);
        param.conditions(wrapper);
        return clueDlrMapper.selectOneByQuery(wrapper);
    }

    @Override
    public SacClueInfoDlr findByCondition(SacClueInfoDlrBO param, QueryColumn... columns) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(param.getDbName().concat(".t_sac_clue_info_dlr"))
                .select(columns)
                .limit(1);
        param.conditions(wrapper);
        return clueDlrMapper.selectOneByQuery(wrapper);
    }

    @Override
    public int saveClueInfo(SacClueInfoDlr clueInfoDlr) {
        return clueDlrMapper.insertClueInfo(clueInfoDlr);
    }

    @Override
    public int updateClueInfo(SacClueInfoDlr clueInfoDlr) {
        return clueDlrMapper.updateClueInfo(clueInfoDlr);
    }

    /**
     * 批量更新活跃线索
     *
     * @param clueInfoDlrList
     * @return
     */
    @Override
    public Boolean updateClueInfoBatch(List<SacClueInfoDlr> clueInfoDlrList) {
        return clueDlrMapper.updateClueInfoBatch(clueInfoDlrList) > 0;
    }

    /**
     * 批量更新线索
     *
     * @param sacAllClueInfoDlrList
     * @return
     */
    @Override
    public Boolean updateClueInfoDlrBatch(List<SacAllClueInfoDlr> sacAllClueInfoDlrList) {
        return allClueDlrMapper.updateClueInfoDlrBatch(sacAllClueInfoDlrList) > 0;
    }

    @Override
    public int saveClueLog(SacClueInfoDlrLog sacClueInfoDlrLog) {
        return sacClueInfoDlrLogMapper.insert(sacClueInfoDlrLog);
    }

    @Override
    public int saveHistoryClueInfo(SacAllClueInfoDlr sacAllClueInfoDlr) {
        SacHistoryClueInfoDlr sacHistoryClueInfoDlr = BeanUtils.copyProperties(sacAllClueInfoDlr, SacHistoryClueInfoDlr.class);
        return clueHistoryDlrMapper.insert(sacHistoryClueInfoDlr);
    }

    @Override
    public List<CalenderCountVO> overdueCount(LocalDateTime startTime, LocalDateTime endTime, UserBusiEntity userInfo) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(QueryMethods.dateFormat(SAC_REVIEW.PLAN_REVIEW_TIME, StringConstant.SQL_DATE_FORMAT).as(CalenderCountVO::getDate),
                        QueryMethods.count(SAC_REVIEW.REVIEW_ID).as(CalenderCountVO::getCount))
                .from(SAC_REVIEW)
                .and(SAC_REVIEW.ORG_CODE.eq(userInfo.getDlrCode(), StringUtil::hasText))
                .and(SAC_REVIEW.REVIEW_PERSON_ID.eq(userInfo.getUserID(), UserUtil.productExpertValid()))
                .and(SAC_REVIEW.PLAN_REVIEW_TIME.ge(startTime, Objects::nonNull))
                .and(SAC_REVIEW.PLAN_REVIEW_TIME.le(endTime, Objects::nonNull))
                // dcc
                .and(SAC_REVIEW.COLUMN19.isNull())
                .groupBy("`date`");

        return clueDlrMapper.selectListByQueryAs(wrapper, CalenderCountVO.class);
    }

    @Override
    public List<SacAllClueInfoDlr> findDefeatedClueByPhone(String phone, String statusCode) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SAC_ALL_CLUE_INFO_DLR.ALL_COLUMNS)
                .eq(SacAllClueInfoDlr::getPhone, phone)
                .eq(SacAllClueInfoDlr::getStatusCode, statusCode);
        return allClueDlrMapper.selectListByQuery(wrapper);
    }

    @Override
    public SacAllClueInfoDlr findDefeatedClueByReviewId(String reviewId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SAC_ALL_CLUE_INFO_DLR.ALL_COLUMNS)
                .eq(SacAllClueInfoDlr::getReviewId, reviewId)
                .limit(1)
                .orderBy(SacAllClueInfoDlr::getCreatedDate)
                .desc();
        return allClueDlrMapper.selectOneByQuery(wrapper);
    }

    @Override
    public List<SacAllClueInfoDlr> findDefeatedClueByReviewIds(QueryWrapper wrapper) {
        return allClueDlrMapper.selectListByQuery(wrapper);
    }

    @Override
    public SacClueInfoDlr findActiveClueByReviewId(String reviewId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SAC_CLUE_INFO_DLR.ALL_COLUMNS)
                .eq(SacClueInfoDlr::getReviewId, reviewId)
                .limit(1)
                .orderBy(SacClueInfoDlr::getCreatedDate)
                .desc();
        return clueDlrMapper.selectOneByQuery(wrapper);
    }

    @Override
    public List<SacClueInfoDlr> findActiveClueByReviewIds(QueryWrapper wrapper) {
        return clueDlrMapper.selectListByQuery(wrapper);
    }

    /**
     * 通过手机号删除战败线索
     *
     * @param phone
     * @return
     */
    @Override
    public int delDefeatedClueByPhone(String phone) {
        return allClueDlrMapper.deleteByPhone(phone);
    }

    @Override
    public ClueDlrStatisticsVO statisticsByType(ClueQueryTypeEnum type, UserBusiEntity userInfo) {
        ClueDlrQry qry = new ClueDlrQry();
        qry.setQryType(type);
        qry.setDlrCode(userInfo.getDlrCode());
        if (UserUtil.isProductExpert(userInfo.getStationId())) {
            qry.setReviewPersonId(userInfo.getUserID());
        }

        long count = ((ClueDlrGatewayImpl) AopContext.currentProxy()).countByQry(qry);
        return new ClueDlrStatisticsVO(type, count);
    }


    @SmartADPCache(value = "clueCount", key = "#qry.countCacheKey()", expire = 30)
    public long countByQry(ClueDlrQry qry) {
        QueryWrapper wrapper = QueryWrapper.create();
        if (qry.getQryType().includeDefeat()) {
            qry.defeatFrom(wrapper)
               .defeatConditions(wrapper);
        } else {
            qry.from(wrapper)
               .conditions(wrapper);
        }

        return clueDlrMapper.selectCountByQuery(MapperUtil.optimizeCountQueryWrapper(wrapper));
    }

    /**
     * 更新线索中的信息，param中属性有值得都会更新，不更新的字段可以设为空
     * adp_leads库线索表更新失败，会更新csc库的线索表
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyClueInfo(SacClueInfoDlr param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("CUST_ID"), SqlConsts.EQUALS, param.getCustId());
        if (Objects.nonNull(param.getServerOrder())) {
            condition.and(QueryCondition.create(new QueryColumn("SERVER_ORDER"), SqlConsts.EQUALS, param.getServerOrder()));
        }
        Boolean updateResult = clueDlrMapper.updateByCondition(param, condition) > 0;
        if (!updateResult) {
            SacAllClueInfoDlr allClueInfoDlr = BeanUtil.copyProperties(param, SacAllClueInfoDlr.class);
            updateResult = modifyAllClueInfo(allClueInfoDlr);
        }
        return updateResult;
    }

    /**
     * 更新csc库中的线索中的信息，param中属性有值得都会更新，不更新的字段可以设为空
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyAllClueInfo(SacAllClueInfoDlr param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("CUST_ID"), SqlConsts.EQUALS, param.getCustId());
        condition.and(QueryCondition.create(new QueryColumn("SERVER_ORDER"), SqlConsts.EQUALS, param.getServerOrder()));
        return allClueDlrMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 更新线索中的信息，param中属性有值得都会更新，不更新的字段可以设为空
     * adp_leads库线索
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyClue(SacClueInfoDlr param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("PHONE"), SqlConsts.EQUALS, param.getPhone());
        Boolean updateResult = clueDlrMapper.updateByCondition(param, condition) > 0;
        return updateResult;
    }

    /**
     * 更新csc库中的线索中的信息，param中属性有值得都会更新，不更新的字段可以设为空
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyAllClue(SacAllClueInfoDlr param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("PHONE"), SqlConsts.EQUALS, param.getPhone());
        return allClueDlrMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 根据手机号集合查询线索活跃信息 <br/>
     * 弱依赖 DWS
     *
     * @param listPhone
     * @return
     */
    @Override
    public List<ClueActiveInfoVO> queryClueActiveInfo(List<String> listPhone) {
        log.info("{} 批量查询活跃度入参 {}", CLUE_LOG_PREFIX, listPhone);
        if (CollectionUtil.isEmpty(listPhone)) {
            return Collections.emptyList();
        }

        // cache
        Map<String, String> cacheMap = cacheUtil.getAll(listPhone, this::activeCacheKey);

        // dws query
        List<String> dwsParam = listPhone.stream()
                .filter(phone -> Objects.isNull(cacheMap.get(phone)))
                .collect(Collectors.toList());
        Map<String, ClueActiveInfoRsp> dwsMap;
        if (CollectionUtil.isNotEmpty(dwsParam)) {
            dwsMap = dwsFeign.queryActiveInfo(adapterClueRequest.queryClueActiveInfo(dwsParam))
                    .getRows()
                    .stream()
                    .collect(Collectors.toMap(ClueActiveInfoRsp::getLeadsId, Function.identity(), (v1, v2) -> v2));
        } else {
            dwsMap = new HashMap<>();
        }

        // build
        List<ClueActiveInfoRsp> list = listPhone.stream()
                .map(phone -> {
                    String cache = cacheMap.get(phone);
                    if (Objects.nonNull(cache)) {
                        return JSONObject.parseObject(cache, ClueActiveInfoRsp.class);
                    } else {
                        ClueActiveInfoRsp rsp = dwsMap.get(phone);
                        if (Objects.isNull(rsp)) {
                            // TODO 空结果 短期缓存？
                            return null;
                        }
                        cacheUtil.put(activeCacheKey(phone), JSONObject.toJSONString(rsp), ACTIVE_CACHE_EXPIRE);
                        return rsp;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        log.info("{} 批量查询活跃度结果 {}", CLUE_LOG_PREFIX, list);
        return ClueActiveInfoRsp.convertToClueActiveInfoVO(list);
    }

    private String activeCacheKey(String phone) {
        return ACTIVE_CACHE_PREFIX + phone;
    }

    @Override
    public List<SacClueInfoDlr> listByWrapper(QueryWrapper wrapper) {
        if (Objects.isNull(wrapper)) {
            return Collections.emptyList();
        }

        return clueDlrMapper.selectListByQuery(wrapper);
    }

    @Override
    public long countByWrapper(QueryWrapper wrapper) {
        if (Objects.isNull(wrapper)) {
            return LONG_DEFAULT_VALUE;
        }

        return clueDlrMapper.selectCountByQuery(wrapper);
    }

    @Override
    public List<SacClueInfoDlr> listAllByWrapper(QueryWrapper wrapper) {
        if (Objects.isNull(wrapper)) {
            return Collections.emptyList();
        }

        return allClueDlrMapper.selectListByQueryAs(wrapper, SacClueInfoDlr.class);
    }

    @Override
    public long countAllByWrapper(QueryWrapper wrapper) {
        if (Objects.isNull(wrapper)) {
            return LONG_DEFAULT_VALUE;
        }

        return allClueDlrMapper.selectCountByQuery(wrapper);
    }

    @Override
    public long activatesDefeatedNum(UserBusiEntity user) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(SAC_REVIEW_AUDIT.as("ra"))
                .innerJoin(SAC_ALL_CLUE_INFO_DLR.as("c")).on(SAC_REVIEW_AUDIT.REVIEW_ID.eq(SAC_ALL_CLUE_INFO_DLR.REVIEW_ID))
                .and(SAC_ALL_CLUE_INFO_DLR.DLR_CODE.eq(user.getDlrCode(), StringUtil::hasText))
                .and(SAC_ALL_CLUE_INFO_DLR.REVIEW_PERSON_ID.eq(user.getUserID(),
                        UserUtil.isNormalProductExpert(user.getStationId()) && StringUtil.hasText(user.getUserID())))
                .and(SAC_ALL_CLUE_INFO_DLR.STATUS_CODE.eq(ClueStatusEnum.DEFEATED.getCode()))
                .and(SAC_ALL_CLUE_INFO_DLR.COLUMN20.isNull())
                .and(SAC_REVIEW_AUDIT.SH_STATUS.eq(AuditStatusEnum.AUDIT_PASS.getCode()))
                .and(SAC_REVIEW_AUDIT.IS_ENABLE.eq(EnableEnum.ENABLE.getCodeStr()));
        return clueDlrMapper.selectCountByQuery(wrapper);
    }

    @Override
    public long transferAuditNum(UserBusiEntity user) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(SAC_TRANSFER_APPLY.as("tap"))
                .leftJoin(SAC_TRANSFER_AUDIT.as("tau")).on(SAC_TRANSFER_APPLY.APPLY_ID.eq(SAC_TRANSFER_AUDIT.APPLY_ID))
                .and(SAC_TRANSFER_APPLY.OUT_DLR_CODE.eq(user.getDlrCode(), StringUtil::hasText))
                .and(SAC_TRANSFER_AUDIT.SH_STATUS.eq(AuditStatusEnum.WAIT_FOR_AUDIT.getCode()))
                .and(SAC_TRANSFER_AUDIT.APPLY_TYPE_CODE.eq(TRANSFER_DLR_APPLY_TYPE_CODE))
                .and(SAC_TRANSFER_AUDIT.IS_ENABLE.eq(EnableEnum.ENABLE.getCodeStr()));
        return transferApplyMapper.selectCountByQuery(wrapper);
    }

    @Override
    public long defeatedAuditNum(UserBusiEntity user) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(SAC_REVIEW_AUDIT.as("ra"))
                .innerJoin(SAC_REVIEW.as("r")).on(SAC_REVIEW_AUDIT.REVIEW_ID.eq(SAC_REVIEW.REVIEW_ID))
                .and(SAC_REVIEW.ORG_CODE.eq(user.getDlrCode(), StringUtil::hasText))
                .and(SAC_REVIEW_AUDIT.SH_PERSON_ID.isNull()
                        .or(SAC_REVIEW_AUDIT.SH_PERSON_ID.eq(user.getUserID(), StringUtil::hasText)))
                .and(SAC_REVIEW_AUDIT.SH_STATUS.eq(AuditStatusEnum.WAIT_FOR_AUDIT.getCode()))
                .and(SAC_REVIEW_AUDIT.IS_ENABLE.eq(EnableEnum.ENABLE.getCodeStr()));
        return clueDlrMapper.selectCountByQuery(wrapper);
    }

    @Override
    public long reviewTaskNum(UserBusiEntity user) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(SAC_CLUE_MSG_RECORD_VO.as("mr"))
                .innerJoin(ONETASK_INFO.as("ti")).on(SAC_CLUE_MSG_RECORD_VO.RELATION_BILL_ID.eq(ONETASK_INFO.TASK_ID))
                .and(SAC_CLUE_MSG_RECORD_VO.DLR_CODE.eq(user.getDlrCode(), StringUtil::hasText))
                .and(SAC_CLUE_MSG_RECORD_VO.RECEIVE_EMP_ID.eq(user.getEmpID(), StringUtil.hasText(user.getEmpID())))
                .and(SAC_CLUE_MSG_RECORD_VO.MESSAGE_TYPE.eq("4"))
                .and(SAC_CLUE_MSG_RECORD_VO.IS_READ.eq(EnableEnum.DISABLE.getCodeStr()))
                // 未逾期
                .and(ONETASK_INFO.BUSS_END_TIME.gt(TimeContext.now()))
                .and(ONETASK_INFO.TASK_STATE_CODE.eq(TaskStateCodeEnum.PUBLISHED.getCode()));
        return msgRecordMapper.selectCountByQuery(wrapper);
    }

    @Override
    public boolean existByQry(ClueDlrQry qry) {
        QueryWrapper wrapper = selectOne().limit(1);
        qry.from(wrapper)
                .conditions(wrapper);

        List<Integer> list = clueDlrMapper.selectListByQueryAs(wrapper, Integer.class);
        return !list.isEmpty();
    }
}
