package com.smart.adp.infrastructure.helper;

import com.smart.adp.infrastructure.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
public class CacheHelper {
    private static CacheHelper cacheHelper;
    @Resource
    private RedisUtil redisUtil;

    public static Object get(String cacheName, String key) {
        return cacheHelper.redisUtil.get(cacheName + "-" + key);
    }

    public static Object get(String key) {
        return cacheHelper.redisUtil.get(key);
    }

    public static boolean put(String cacheName, String key, String value, int timeLength) {
        return cacheHelper.redisUtil.set(cacheName + "-" + key, value, timeLength);
    }

    public static boolean put(String cacheName, String key, String value) {
        return cacheHelper.redisUtil.set(cacheName + "-" + key, value);
    }

    public static boolean put(String cacheName, String key, int value) {
        return cacheHelper.redisUtil.set(cacheName + "-" + key, String.valueOf(value));
    }

    public static boolean put(String cacheName, String key, Object value) {
        return cacheHelper.redisUtil.set(cacheName + "-" + key, value);
    }

    public static boolean put(String cacheName, String key, Object value, int timeLength) {
        return cacheHelper.redisUtil.set(cacheName + "-" + key, value, timeLength);
    }

    public static boolean put(String cacheName, String key, Object value, int timeLength, TimeUnit timeUnit) {
        return cacheHelper.redisUtil.set(cacheName + "-" + key, value, timeLength, timeUnit);
    }
    public static boolean putHash(String cacheName, String key, Object value, int timeLength, TimeUnit timeUnit) {
        return cacheHelper.redisUtil.hset(cacheName, key, value, timeLength, timeUnit);
    }

    public static void remove(String cacheName, String key) {
        cacheHelper.redisUtil.del(cacheName + "-" + key);
    }

    @PostConstruct
    public void init() {
        cacheHelper = this;
        cacheHelper.redisUtil = this.redisUtil;
    }
}
