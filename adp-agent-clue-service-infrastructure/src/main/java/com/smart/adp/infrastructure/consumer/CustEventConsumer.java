package com.smart.adp.infrastructure.consumer;

import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.util.StringUtil;
import com.rabbitmq.client.Channel;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.entity.message.CustEventMessage;
import com.smart.adp.domain.enums.CustEventEnum;
import com.smart.adp.infrastructure.handler.CustEventHandler;
import com.smart.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

import static com.smart.adp.domain.common.constants.MQConstants.CUST_EVENT_QUEUE;
import static com.smart.adp.domain.common.constants.StringConstant.CLUE_LOG_PREFIX;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
@Slf4j
@Component
public class CustEventConsumer {

    @Autowired
    private Map<String, CustEventHandler> handlerMap;

    @RabbitListener(queues = CUST_EVENT_QUEUE, autoStartup = "${rabbitmq.enable:false}")
    @Transactional(rollbackFor = Exception.class)
    public void eventConsumer(@Payload CustEventMessage eventMessage, Channel channel,
                              @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        try {
            log.info("{} 客户事件 MQ 消费消息 {}", CLUE_LOG_PREFIX, JSONObject.toJSONString(eventMessage));
            // valid & get handler
            if (StringUtil.noText(eventMessage.getCustId())) {
                throw new BusinessException(RespCode.FAIL.getCode(),"cust id can`t be empty.");
            }

            CustEventEnum type = CustEventEnum.getByCode(eventMessage.getType());

            CustEventHandler handler = handlerMap.get(type.getHandlerName());
            if (Objects.isNull(handler)) {
                throw new BusinessException(RespCode.FAIL.getCode(),"no handler for this type.");
            }

            handler.handle(eventMessage);

            channel.basicAck(tag, false);
        } catch (Exception e) {
            log.warn("{} 客户事件 MQ 消费异常", CLUE_LOG_PREFIX, e);
            channel.basicNack(tag, false, false);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }
}
