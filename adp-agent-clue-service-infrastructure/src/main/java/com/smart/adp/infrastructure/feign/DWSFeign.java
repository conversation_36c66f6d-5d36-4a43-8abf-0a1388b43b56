package com.smart.adp.infrastructure.feign;

import com.smart.adp.domain.common.EntityResult;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.infrastructure.feign.request.SummaryDTO;
import com.smart.adp.infrastructure.feign.response.ClueActiveInfoRsp;
import com.smart.adp.infrastructure.feign.response.SummaryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Description: 调用dws服务的feign
 * @Author: rik.ren
 * @Date: 2025/3/11 20:08
 **/
@FeignClient(name = "${refer.url.dws.api}", url = "${refer.url.dws.api}")
public interface DWSFeign {

    /**
     * 获取大数据计算的活跃信息数据
     *
     * @param param
     * @return
     */
    @PostMapping("/api/sacClueInfoDlr/queryActiveInfo")
    EntityResult<List<ClueActiveInfoRsp>> queryActiveInfo(@RequestBody Object param);

    /**
     * 获取大数据的个人总结数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/agent/summary")
    RespBody<List<SummaryVO>> summary(@RequestBody SummaryDTO dto);
}
