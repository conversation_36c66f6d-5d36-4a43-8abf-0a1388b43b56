package com.smart.adp.infrastructure.gateway.base;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.gateway.base.CountyGateway;
import com.smart.adp.domain.valueObject.base.CountyInfo;
import com.smart.adp.infrastructure.repository.base.CountyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.smart.adp.domain.valueObject.base.table.CountyInfoTableDef.COUNTY_INFO;

/**
 * <AUTHOR>
 * date 2025/3/9 13:58
 * @description 县区信息gateway实现
 **/
@Slf4j
@Component
public class CountyGatewayImpl implements CountyGateway {

    @Autowired
    private CountyMapper countyMapper;

    @Override
    public CountyInfo findCountyByCountyId(String countyId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(COUNTY_INFO.CITY_ID, COUNTY_INFO.COUNTY_ID, COUNTY_INFO.COUNTY_CODE, COUNTY_INFO.COUNTY_NAME)
                .eq(CountyInfo::getCountyId, countyId)
                .limit(1)
                .orderBy(CountyInfo::getCreatedDate)
                .desc();
        return countyMapper.selectOneByQuery(wrapper);
    }
}
