package com.smart.adp.infrastructure.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/12
 */
@Component
public class CacheUtil {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public Map<String, String> getAll(List<String> indexes, Function<String, String> keyFunc) {
        Map<String, String> map = new HashMap<>();
        List<String> caches = stringRedisTemplate.opsForValue()
                                                 .multiGet(indexes.stream()
                                                                  .map(keyFunc)
                                                                  .collect(Collectors.toList()));
        if (Objects.isNull(caches)) {
            throw new NullPointerException("redis mGet null");
        }

        for (int i = 0; i < indexes.size(); i++) {
            map.put(indexes.get(i), caches.get(i));
        }
        return map;
    }

    public void put(String key, String value, Duration duration) {
        stringRedisTemplate.opsForValue()
                           .set(key, value, duration);
    }
}
