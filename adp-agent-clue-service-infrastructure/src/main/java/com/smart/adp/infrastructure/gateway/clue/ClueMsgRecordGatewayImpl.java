package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.bo.clue.SacClueMsgRecordBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.base.ClueMsgRecord;
import com.smart.adp.domain.enums.ClueMsgReadEnum;
import com.smart.adp.domain.gateway.clue.ClueMsgRecordGateway;
import com.smart.adp.domain.valueObject.clue.SacClueMsgRecordVO;
import com.smart.adp.infrastructure.repository.base.ClueMsgRecordMapper;
import com.smart.adp.infrastructure.repository.clue.SacClueMsgRecordMapper;
import com.smart.adp.infrastructure.utils.PageConverterFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.smart.adp.domain.entity.base.table.ClueMsgRecordTableDef.CLUE_MSG_RECORD;
import static com.smart.adp.domain.valueObject.clue.table.SacClueMsgRecordVOTableDef.SAC_CLUE_MSG_RECORD_VO;

/**
 * @Description: 线索消息gateway实现
 * @Author: rik.ren
 * @Date: 2025/4/15 15:27
 **/
@Component
public class ClueMsgRecordGatewayImpl implements ClueMsgRecordGateway {
    @Autowired
    private SacClueMsgRecordMapper msgRecordMapper;

    @Autowired
    private ClueMsgRecordMapper clueMsgRecordMapper;

    /**
     * 根据实体条件查询集合
     *
     * @param boParam
     * @param needColumns
     * @return
     */
    @Override
    public List<SacClueMsgRecordVO> findListByCondition(SacClueMsgRecordBO boParam, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{SAC_CLUE_MSG_RECORD_VO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(SAC_CLUE_MSG_RECORD_VO.as("msg"))
                .orderBy(SacClueMsgRecordVO::getCreatedDate).desc();
        boParam.conditions(wrapper);
        return msgRecordMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据实体条件查询分页
     *
     * @param boParam
     * @param needColumns
     * @return
     */
    @Override
    public DomainPage<SacClueMsgRecordVO> findPageByCondition(SacClueMsgRecordBO boParam, QueryColumn... needColumns) {
        if (ObjectUtil.isEmpty(needColumns)) {
            needColumns = new QueryColumn[]{SAC_CLUE_MSG_RECORD_VO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(SAC_CLUE_MSG_RECORD_VO.as("msg"))
                .orderBy(SacClueMsgRecordVO::getCreatedDate).desc();
        boParam.conditions(wrapper);
        Page<SacClueMsgRecordVO> paginate = msgRecordMapper.paginate(boParam.getPage().getPageNumber(), boParam.getPage().getPageSize(),
                wrapper);
        // 转换为领域分页对象
        return PageConverterFactory.toDomainPage(paginate);
    }

    /**
     * 查询符合条件的个数
     *
     * @param boParam
     * @return
     */
    @Override
    public Long findCountByCondition(SacClueMsgRecordBO boParam) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(SAC_CLUE_MSG_RECORD_VO.as("msg"));
        boParam.conditions(wrapper);
        return msgRecordMapper.selectCountByQuery(wrapper);
    }

    @Override
    public Boolean modifyMsgRecord(SacClueMsgRecordBO boParam) {
        // 创建只包含更新字段的新对象
        SacClueMsgRecordBO updateFields = new SacClueMsgRecordBO();
        updateFields.setIsRead(boParam.getIsRead());
        updateFields.setLastUpdatedDate(boParam.getLastUpdatedDate() != null
                ? boParam.getLastUpdatedDate()
                : LocalDateTime.now());
        // 更新的条件
        QueryCondition condition = null;
        if (ObjectUtil.isNotEmpty(boParam.getMessageId())) {
            condition = QueryCondition.create(new QueryColumn("MESSAGE_ID"), SqlConsts.EQUALS, boParam.getMessageId())
                    .and(SAC_CLUE_MSG_RECORD_VO.IS_READ.eq(ClueMsgReadEnum.UN_READ.getCode(), StringUtil::hasText));
        } else if (CollectionUtil.isNotEmpty(boParam.getListMessageId())) {
            condition = QueryCondition.create(new QueryColumn("MESSAGE_ID"), SqlConsts.IN, boParam.getListMessageId())
                    .and(SAC_CLUE_MSG_RECORD_VO.IS_READ.eq(ClueMsgReadEnum.UN_READ.getCode(), StringUtil::hasText));
        } else {
            condition = QueryCondition.create(new QueryColumn("RECEIVE_EMP_ID"), SqlConsts.EQUALS, boParam.getReceiveEmpId())
                    .and(SAC_CLUE_MSG_RECORD_VO.IS_READ.eq(ClueMsgReadEnum.UN_READ.getCode(), StringUtil::hasText))
                    .and(SAC_CLUE_MSG_RECORD_VO.MESSAGE_TYPE.in(boParam.getListMessageType(), CollectionUtil::isNotEmpty));
        }
        // 更新的字段就是param中的不为空的字段
        return msgRecordMapper.updateByCondition(updateFields, condition) > 0;
    }

    @Override
    public List<ClueMsgRecord> findClueMsgRecords(ClueMsgRecord clueMsgRecord) {
        if (Objects.isNull(clueMsgRecord)) {
            return new ArrayList<>();
        }

        QueryWrapper wrapper = QueryWrapper.create()
                .select(CLUE_MSG_RECORD.DLR_CODE, CLUE_MSG_RECORD.BUSI_KEY_VALUE, CLUE_MSG_RECORD.MESSAGE_ID, CLUE_MSG_RECORD.PHONE)
                .eq(ClueMsgRecord::getDlrCode, clueMsgRecord.getDlrCode())
                .eq(ClueMsgRecord::getReceiveEmpId, clueMsgRecord.getReceiveEmpId())
                .eq(ClueMsgRecord::getMessageType, clueMsgRecord.getMessageType())
                .eq(ClueMsgRecord::getIsRead, clueMsgRecord.getIsRead())
                .orderBy(ClueMsgRecord::getCreatedDate)
                .desc();
        ;
        return clueMsgRecordMapper.selectListByQuery(wrapper);
    }

    /**
     * 更新消息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyClueMsgRecord(ClueMsgRecord param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("MESSAGE_ID"), SqlConsts.EQUALS, param.getMessageId());
        Boolean updateResult = clueMsgRecordMapper.updateByCondition(param, condition) > 0;
        return updateResult;
    }

    /**
     * 保存消息
     *
     * @param clueMsgRecord
     * @return
     */
    @Override
    public int saveClueMsgRecord(ClueMsgRecord clueMsgRecord) {
        return clueMsgRecordMapper.insert(clueMsgRecord);
    }
}
