package com.smart.adp.infrastructure.repository.clue;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 潜客mapper
 * @Author: rik.ren
 * @Date: 2025/3/5 20:42
 **/
public interface OneCustInfoMapper extends BaseMapper<SacOnecustInfoEntity> {


    /**
     * 插入
     *
     * @param
     * @return
     */
    int saveOnecustInfo(@Param("param") SacOnecustInfoEntity param);
}
