package com.smart.adp.infrastructure.publisher;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.smart.adp.domain.common.constants.MQConstants.*;

/**
 * <AUTHOR>
 * date 2025/3/12 20:59
 * @description
 **/
@Slf4j
@Service
public class AgentDlrCreateCluePublisher {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendClueCreateMessage(String msg) {
        log.info("店端线索创建通知CDP消息发送开始，msg={}", msg);
        String uuid = rabbitTemplate.getUUID();
        Message message = MessageBuilder.withBody(msg.getBytes()).setMessageId(uuid).setContentType("application/json").build();
        CorrelationData correlationData = new CorrelationData(uuid);
        rabbitTemplate.convertAndSend(CREATE_CLUE_EXCHANGE, "", message, correlationData);
    }

    public void sendClueDisTributeMessage(String msg) {
        log.info("店端线索店长分配通知CDP消息发送开始，msg={}", msg);
        String uuid = rabbitTemplate.getUUID();
        Message message = MessageBuilder.withBody(msg.getBytes()).setMessageId(uuid).setContentType("application/json").build();
        CorrelationData correlationData = new CorrelationData(uuid);
        rabbitTemplate.convertAndSend(DISTRIBUTE_CLUE_EXCHANGE, "", message, correlationData);
    }

    public void sendClueWECOMessage(String msg) {
        String uuid = rabbitTemplate.getUUID();
        Message message = MessageBuilder.withBody(msg.getBytes()).setMessageId(uuid).setContentType("application/json").build();
        CorrelationData correlationData = new CorrelationData(uuid);
        rabbitTemplate.convertAndSend(WECOM_MSG_EXCHANGE, WECOM_MSG_ROUTING_KEY, message, correlationData);
    }
}
