package com.smart.adp.infrastructure.gateway.clue;

import com.smart.adp.domain.entity.clue.SacReviewHis;
import com.smart.adp.domain.gateway.clue.ReviewHisGateway;
import com.smart.adp.infrastructure.repository.clue.ReviewHisMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/15 21:15
 * @description 回访任务历史gateway实现
 **/
@Service
public class ReviewHisGatewayImpl implements ReviewHisGateway {

    @Autowired
    private ReviewHisMapper reviewHisMapper;

    @Override
    public int updateReviewAssignBatch(List<SacReviewHis> sacReviewHis) {
        return reviewHisMapper.updateSacReviewHisBatch(sacReviewHis);
    }
}
