package com.smart.adp.infrastructure.gateway.clue;

import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import com.smart.adp.domain.gateway.clue.OneCustInfoGateway;
import com.smart.adp.infrastructure.repository.clue.OneCustInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.smart.adp.domain.entity.clue.table.SacOnecustInfoEntityTableDef.SAC_ONECUST_INFO_ENTITY;

/**
 * @Description: 潜客gateway
 * @Author: rik.ren
 * @Date: 2025/3/5 20:42
 **/
@Service
public class OneCustInfoGatewayImpl implements OneCustInfoGateway {

    @Autowired
    private OneCustInfoMapper oneCustInfoMapper;

    /**
     * 根据id查询
     *
     * @param id
     * @param columns
     * @return
     */
    @Override
    public SacOnecustInfoEntity findById(String id, QueryColumn... columns) {
        return null;
    }

    @Override
    public List<SacOnecustInfoEntity> findByIds(List<String> ids, QueryColumn... columns) {
        if (CollectionUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .and(SAC_ONECUST_INFO_ENTITY.CUST_ID.in(ids));
        return oneCustInfoMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据实体条件查询集合
     *
     * @param param
     * @param needColumns
     * @return
     */
    @Override
    public List<SacOnecustInfoEntity> findListByCondition(SacOnecustInfoEntity param, QueryColumn... needColumns) {
        return null;
    }

    /**
     * 根据实体条件查询一条
     *
     * @param param
     * @param needColumns
     * @return
     */
    @Override
//    @SmartADPCache(value = "oneCust", key = "#param.custId", expire = 10, timeUnit = TimeUnit.MINUTES)
    public SacOnecustInfoEntity findByCondition(SacOnecustInfoEntity param, QueryColumn... needColumns) {
        // 查询数据库
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumns)
                .from(SAC_ONECUST_INFO_ENTITY.as("onecust"))
                .limit(1);
        param.conditions(wrapper);
        return oneCustInfoMapper.selectOneByQuery(wrapper);
    }

    /**
     * 根据smartId获取用户信息
     *
     * @param smartId
     * @return
     */
    @Override
    public List<SacOnecustInfoEntity> findListBySmartId(String smartId) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SAC_ONECUST_INFO_ENTITY.CUST_ID, SAC_ONECUST_INFO_ENTITY.PHONE, SAC_ONECUST_INFO_ENTITY.SMART_ID)
                .eq(SacOnecustInfoEntity::getSmartId, smartId);
        return oneCustInfoMapper.selectListByQuery(wrapper);
    }

    @Override
    public SacOnecustInfoEntity findOneByPhone(String phone) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(SAC_ONECUST_INFO_ENTITY.CUST_ID, SAC_ONECUST_INFO_ENTITY.PHONE, SAC_ONECUST_INFO_ENTITY.SMART_ID)
                .eq(SacOnecustInfoEntity::getPhone, phone)
                .limit(1)
                .orderBy(SacOnecustInfoEntity::getCreatedDate)
                .desc();
        return oneCustInfoMapper.selectOneByQuery(wrapper);
    }

    /**
     * 更新潜客表
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyOnecust(SacOnecustInfoEntity param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("CUST_ID"), SqlConsts.EQUALS, param.getCustId());
        return oneCustInfoMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 更新潜客表信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyOnecustInfo(SacOnecustInfoEntity param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("PHONE"), SqlConsts.EQUALS, param.getPhone());
        return oneCustInfoMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 保存潜客表信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean saveOnecust(SacOnecustInfoEntity param) {
        return oneCustInfoMapper.saveOnecustInfo(param) > 0;
    }
}
