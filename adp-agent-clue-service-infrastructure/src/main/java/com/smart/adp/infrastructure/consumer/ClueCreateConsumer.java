package com.smart.adp.infrastructure.consumer;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import com.smart.adp.domain.common.constants.CommonConstant;
import com.smart.adp.domain.entity.base.AgentEmployee;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacReview;
import com.smart.adp.domain.entity.message.CreateClueMessage;
import com.smart.adp.domain.enums.ClueStatusEnum;
import com.smart.adp.domain.enums.EmployeeStatusEnum;
import com.smart.adp.domain.gateway.base.LookUpGateway;
import com.smart.adp.domain.gateway.clue.AgentEmployeeGateway;
import com.smart.adp.domain.gateway.clue.ClueDlrGateway;
import com.smart.adp.domain.gateway.clue.ReviewGateway;
import com.smart.adp.domain.valueObject.base.LookUpInfo;
import com.smart.adp.infrastructure.feign.CdpFeignService;
import com.smart.adp.infrastructure.feign.XApiFeignService;
import com.smart.adp.infrastructure.feign.response.cdp.CdpClueVO;
import com.smart.adp.infrastructure.utils.ProvinceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.smart.adp.domain.common.constants.MQConstants.CREATE_CLUE_QUEUE;

/**
 * <AUTHOR>
 * date 2025/3/10 14:58
 * @description 自建线索通知CDP 消费者
 **/
@Slf4j
@Component
public class ClueCreateConsumer {
    private static final String ADP_INDICATOR = "ADP";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private ReviewGateway reviewGateway;

    @Autowired
    private XApiFeignService xApiFeignService;

    @Autowired
    private AgentEmployeeGateway agentEmployeeGateway;

    @Autowired
    private LookUpGateway lookUpGateway;

    @Autowired
    private CdpFeignService cdpFeignService;

    @Autowired
    private ClueDlrGateway clueDlrGateway;

    @Autowired
    private ProvinceUtil provinceUtil;

    @RabbitListener(queues = CREATE_CLUE_QUEUE)
    public void delaClueCreateMessage(@Payload CreateClueMessage message, Channel channel,
                                      @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        try {
            // 1. 获取配置信息
            LookUpInfo lookUpInfo = lookUpGateway.findLookUpInfoWithCache(
                    CommonConstant.TYPE_CODE_SWITCH,
                    CommonConstant.VALUE_CODE_SWITCH
            );

            log.info("线索创建消息消费开始, message={}, lookUpInfo={}",
                    JSONUtil.toJsonStr(message),
                    JSONUtil.toJsonStr(lookUpInfo));

            // 2. 是否查询cdp更新线索判定条件【战败激活线索不查询cdp更新线索渠道】
            boolean shouldProcessCdp = lookUpInfo != null
                    && StrUtil.isNotBlank(lookUpInfo.getAttribute1())
                    && !CommonConstant.DEFAULT_VALUE.equals(lookUpInfo.getAttribute1())
                    && StrUtil.isNotBlank(message.getSystemSource())
                    && message.getSystemSource().contains(ADP_INDICATOR)
                    && !message.getExistDefeatClue();
            if (shouldProcessCdp) {
                //处理线索渠道更新
                processCdpLogic(message, lookUpInfo);
            }

            // 3. 是否通知cdp的通用判断逻辑
            boolean shouldSaveData = message != null
                    && StrUtil.isNotBlank(message.getSystemSource())
                    && message.getSystemSource().contains(ADP_INDICATOR);

            // 新增开关配置控制新逻辑：配置存在时的保存条件
            boolean saveConditionWithConfig = lookUpInfo != null
                    && CommonConstant.COMMON_VALUE.equals(lookUpInfo.getAttribute2());

            // 新增开关配置控制新逻辑：配置不存在时的保存条件
            boolean saveConditionWithoutConfig = lookUpInfo == null;
            //通知cdp逻辑
            if (shouldSaveData && (saveConditionWithConfig || saveConditionWithoutConfig)) {
                this.dealClueDlrSaveData(message);
            }
        } catch (Exception e) {
            log.error("自建线索消息处理异常", e);
        } finally {
            channel.basicAck(tag, false);
        }
    }

    /**
     * 获取cdp线索更新adp的渠道信息
     *
     * @param message
     * @param lookUpInfo
     */
    private void processCdpLogic(CreateClueMessage message, LookUpInfo lookUpInfo) {
        try {
            CdpClueVO cdpClueRsp = cdpFeignService.getCdpClue(message.getPhone());
            if (cdpClueRsp == null || cdpClueRsp.getCreateTime() == null) {
                log.info("未获取cdp线索更新adp的渠道信息得线索,phone={}", message.getPhone());
                return;
            }

            log.info("线索创建更新cdp渠道信息开始,phone={},cdpClueRsp={}", message.getPhone(), JSONUtil.toJsonStr(cdpClueRsp));
            // 时间有效性校验
            long validDays = Long.parseLong(lookUpInfo.getAttribute1());
            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime minusTime = currentTime.minusDays(validDays);

            LocalDateTime createTime = Instant.parse(cdpClueRsp.getCreateTime())
                    .atZone(ZoneId.systemDefault()) // 转换为系统默认时区
                    .toLocalDateTime();

            if (createTime.isAfter(minusTime)) {
                updateChannelInformation(message, cdpClueRsp);
            }
        } catch (NumberFormatException e) {
            log.error("获取cdp线索更新adp的渠道信息配置格式错误: {}", lookUpInfo.getAttribute1(), e);
        } catch (Exception e) {
            log.error("线索创建线索更新adp的渠道信息异常, phone={}", message.getPhone(), e);
        }
    }

    private void updateChannelInformation(CreateClueMessage message, CdpClueVO cdpClueRsp) {
        // 获取渠道信息
        LookUpInfo look49 = getLookUpInfo(CommonConstant.ADP_CLUE_049, cdpClueRsp.getC_fixed_second_channel());
        LookUpInfo look72 = getLookUpInfo(CommonConstant.ADP_CLUE_072, cdpClueRsp.getC_fixed_third_channel());

        // 构建更新对象
        if (Objects.nonNull(look49) && Objects.nonNull(look72)
                && StrUtil.isNotBlank(look49.getLookUpValueName())
                && StrUtil.isNotBlank(look72.getLookUpValueName())) {
            SacClueInfoDlr clueUpdate = buildClueUpdate(message, cdpClueRsp, look49, look72);
            SacReview reviewUpdate = buildReviewUpdate(clueUpdate);

            // 执行更新
            clueDlrGateway.modifyClue(clueUpdate);
            reviewGateway.modifyReviewInfo(reviewUpdate);
        } else {
            log.info("获取cdp线索更新,adp未配置当前渠道,phone={},cdpSecondChannel={},cdpThirdChannel", message.getPhone(), cdpClueRsp.getC_fixed_second_channel(), cdpClueRsp.getC_fixed_third_channel());
        }
    }

    private LookUpInfo getLookUpInfo(String type, String value) {
        return lookUpGateway.findLookUpInfoWithCache(type, value);
    }

    private SacClueInfoDlr buildClueUpdate(CreateClueMessage message, CdpClueVO cdpClueRsp,
                                           LookUpInfo look49, LookUpInfo look72) {
        SacClueInfoDlr update = new SacClueInfoDlr();
        update.setPhone(message.getPhone());
        update.setInfoChanMCode(cdpClueRsp.getC_fixed_second_channel());
        update.setInfoChanMName(look49 != null ? look49.getLookUpValueName() : "");
        update.setInfoChanDCode(cdpClueRsp.getC_fixed_third_channel());
        update.setInfoChanDName(look72 != null ? look72.getLookUpValueName() : "");

        // 设置渠道信息
        String channelName = StrUtil.isBlank(update.getInfoChanMName()) ? "" : update.getInfoChanMName();
        String channelCode = StrUtil.isBlank(update.getInfoChanMCode()) ? "" : update.getInfoChanMCode();

        update.setChannelName(channelName);
        update.setChannelCode(channelCode);

        message.setChannelName(channelName);
        message.setChannelCode(channelCode);
        message.setInfoChanDCode(cdpClueRsp.getC_fixed_third_channel());
        message.setInfoChanDDesc(look72 != null ? look72.getLookUpValueName() : "");
        message.setFirstChannel(look49.getAttribute1());

        return update;
    }

    private SacReview buildReviewUpdate(SacClueInfoDlr clueUpdate) {
        SacReview review = new SacReview();
        review.setPhone(clueUpdate.getPhone());
        review.setInfoChanMCode(clueUpdate.getInfoChanMCode());
        review.setInfoChanMName(clueUpdate.getInfoChanMName());
        review.setInfoChanDCode(clueUpdate.getInfoChanDdCode());
        review.setInfoChanDName(clueUpdate.getInfoChanDName());
        review.setChannelCode(clueUpdate.getChannelCode());
        review.setChannelName(clueUpdate.getChannelName());

        return review;
    }

    /**
     * 处理adp-cdp通知
     *
     * @param message
     */
    private void dealClueDlrSaveData(CreateClueMessage message) {
        if (!ClueStatusEnum.DUPLICATE_LEADS.getCode().equals(message.getStatusCode())) {
            String reviewPersonId = message.getReviewPersonId();
            if (StrUtil.isBlank(reviewPersonId)) {
                SacReview sacReview = reviewGateway.findOneByPhone(message.getPhone());
                if (Objects.nonNull(sacReview)) {
                    reviewPersonId = sacReview.getReviewPersonId();
                }
            }

            AgentEmployee agentEmployee = null;
            if (StrUtil.isNotBlank(reviewPersonId)) {
                //根据销售顾问ID调用Base服务查询查相应的销售顾问信息，填充到指定的Map
                List<String> statusList = Collections.singletonList(EmployeeStatusEnum.WORKING.getCode());
                agentEmployee = agentEmployeeGateway.findOne(reviewPersonId, statusList);
            }

            //处理通知CDP 该部分为必填参数
            Map<String, Object> noticeCdpMap = new HashMap<>(33);
            noticeCdpMap.put("bk", message.getPhone());
            noticeCdpMap.put("mobile", message.getPhone());
            noticeCdpMap.put("c_lastupdate_system", "ADP-DRIVE".equals(message.getSystemSource()) ? "" : "ADP");

            //非必填项
            noticeCdpMap.put("c_smartid", "");// smartid
            noticeCdpMap.put("name", message.getCustName());// 姓名
            // 2506 大区设置省份特殊处理
            noticeCdpMap.put("c_province", provinceUtil.getMappingProvince(message.getProvinceName()));// 省份
            noticeCdpMap.put("c_city", message.getCityName());// 城市
            noticeCdpMap.put("c_county", message.getCountyName());// 地区
            noticeCdpMap.put("c_gender", message.getGenderName());// 性别
            noticeCdpMap.put("source", "门店");// "用户来源（=二级来源）" 线上平台、线下活动、门店、合作品牌/机构（金融机构等）
            noticeCdpMap.put("c_register_channel", message.getChannelName());// "用户注册/留资最早来源渠道（=一级来源）"
            noticeCdpMap.put("c_first_channel", message.getFirstChannel());// 一级来源
            if (StrUtil.isNotBlank(message.getChannelCode())) {
                noticeCdpMap.put("c_second_channel", message.getChannelCode());// 二级来源
            }
            if (StrUtil.isNotBlank(message.getInfoChanDCode())) {
                noticeCdpMap.put("c_third_channel", message.getInfoChanDCode());// 三级来源
            }
            noticeCdpMap.put("c_cus_source", message.getInfoChanDDesc());// 渠道描述
            noticeCdpMap.put("c_store", message.getDlrShortName());// 所属销售门店
            noticeCdpMap.put("c_store_code", message.getDlrCode());// 门店编码
            noticeCdpMap.put("c_store_name", message.getDlrShortName());// 门店名称
            noticeCdpMap.put("c_heat_name", message.getBusinessHeatName());// 热度名称
            noticeCdpMap.put("c_heat_code", message.getBusinessHeatCode());// 热度编码
            noticeCdpMap.put("c_considering", message.getFocusOfCarPurchase());// 购车因素
            noticeCdpMap.put("c_interested_car_model", message.getIntenCarTypeName());// 意向车型
            noticeCdpMap.put("c_interested_car_colour", message.getInnerColorName());// 意向车型颜色
            noticeCdpMap.put("c_planned_purchase_time", message.getPlanBuyDateName());// 计划购车时间
            noticeCdpMap.put("c_intersted_car_body_colour", message.getOutColorName());// 意向车身配色
            noticeCdpMap.put("c_is_additional_purchase", message.getIsAddBuy());// 是否增换购
            noticeCdpMap.put("c_seller", message.getReviewPersonName());// 销售顾问
            noticeCdpMap.put("c_seller_id", reviewPersonId);// 销售顾问id
            if (Objects.nonNull(agentEmployee)) {
                noticeCdpMap.put("c_seller_phone", agentEmployee.getMobile());// 销售顾问电话
                noticeCdpMap.put("c_seller_url", agentEmployee.getHeadPortrait());// 头像URL
            }
            noticeCdpMap.put("remark", Objects.isNull(message.getRemark()) ? "新建线索" : message.getRemark());
            Map<String, Object> param = new HashMap<>(1);
            param.put("mapParam", MapUtil.removeNullValue(noticeCdpMap));
            log.info("线索创建通知CDP开始,phone={},param={}", message.getPhone(), JSONUtil.toJsonStr(param));
            xApiFeignService.sendCancleData("ADP_INSERT_CDP_LEADS", param);

            //事件
            String dateTZ = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(new Date());
            noticeCdpMap.put("c_first_channel", message.getFirstChannel());
            noticeCdpMap.put("event", "c_created_by_ADP");
            noticeCdpMap.put("c_create_time", dateTZ);
            noticeCdpMap.put("date", dateTZ);
            noticeCdpMap.put("logs_id", UUID.randomUUID().toString());
            param.put("mapParam", noticeCdpMap);
            xApiFeignService.sendCancleData("ADP_INSERT_CDP_LEADS_EVENT", param);
        }
    }
}
