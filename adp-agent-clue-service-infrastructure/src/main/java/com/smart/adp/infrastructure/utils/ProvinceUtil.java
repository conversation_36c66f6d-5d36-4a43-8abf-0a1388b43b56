package com.smart.adp.infrastructure.utils;

import com.smart.adp.domain.common.constants.CommonConstant;
import com.smart.adp.domain.enums.ProvinceConfigType;
import com.smart.adp.domain.gateway.base.LookUpGateway;
import com.smart.adp.domain.valueObject.base.LookUpInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.smart.adp.domain.valueObject.base.table.LookUpInfoTableDef.LOOK_UP_INFO;

/**
 * 省份配置工具
 */
@Component
public class ProvinceUtil {

    @Autowired
    private LookUpGateway lookupGateway;

    /**
     * 获取省份配置
     * 属性1（1-城市编码映射特殊省份编码 2-特殊省份编码映射原省份编码）属性2（映射省份名）属性3（当前特殊省份名）
     */
    public List<LookUpInfo> getProvinceConfig() {
        List<LookUpInfo> provinceConfig = lookupGateway.findLookUpByTypeCode(CommonConstant.TYPE_CODE_PROVINCE_CONFIG
                , LOOK_UP_INFO.LOOK_UP_TYPE_CODE, LOOK_UP_INFO.LOOK_UP_VALUE_CODE
                , LOOK_UP_INFO.LOOK_UP_VALUE_NAME, LOOK_UP_INFO.ATTRIBUTE1, LOOK_UP_INFO.ATTRIBUTE2, LOOK_UP_INFO.ATTRIBUTE3);
        return Optional.ofNullable(provinceConfig).orElseGet(Collections::emptyList);
    }

    /**
     * 获取特殊省份名映射的省份名
     * @param provinceName 省份名
     * @return 映射后的省份名
     */
    public String getMappingProvince(String provinceName) {
        if (StringUtils.isBlank(provinceName)) {
            return null;
        }
        return getProvinceConfig().stream()
                .filter(e -> ProvinceConfigType.SPECIAL_TO_ORIGINAL.getType().equals(e.getAttribute1()))
                .filter(e -> provinceName.equals(e.getAttribute3()))
                .map(LookUpInfo::getAttribute2)
                .findFirst()
                .orElse(provinceName);
    }
}
