package com.smart.adp.infrastructure.interceptor;

import com.smart.adp.domain.context.TimeContext;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.infrastructure.annotation.NoAuth;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * interceptor
 *
 * <AUTHOR>
 * @since 2025/3/3
 */
@Slf4j
@RequiredArgsConstructor
public class RequestInterceptor implements HandlerInterceptor {

    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean preHandle(final HttpServletRequest request, final HttpServletResponse response, Object handler) throws Exception {
        // 检查是否存在@NoAuth注解
        if (shouldSkipAuth(handler, request)) {
            return true; // 跳过鉴权
        }
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);

        try {
            UserInfoContext.init(stringRedisTemplate, token);
        } catch (Exception e) {
            log.warn("user info init failed.", e);
        }

        return Boolean.TRUE;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserInfoContext.remove();
        TimeContext.remove();
    }

    private boolean shouldSkipAuth(Object handler, HttpServletRequest request) {
        String path = request.getRequestURI();
        if (path.startsWith("/doc.html") || path.startsWith("/v3/api-docs") || path.startsWith("/druid")) {
            return true;
        }
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            // 检查方法或类上的注解
            return handlerMethod.getMethod().isAnnotationPresent(NoAuth.class)
                    || handlerMethod.getBeanType().isAnnotationPresent(NoAuth.class);
        }
        return false;
    }
}
