package com.smart.adp.infrastructure.repository.clue;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacHistoryClueInfoDlr;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
public interface ClueHistoryDlrMapper extends BaseMapper<SacHistoryClueInfoDlr> {

}
