package com.smart.adp.infrastructure.feign.config;

import feign.Request;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * @Description: UC Feign客户端配置
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Configuration
public class UcFeignConfig {

    /**
     * 配置请求超时时间
     * 连接超时和读取超时都设置为3秒
     */
    @Bean
    public Request.Options requestOptions() {
        return new Request.Options(3000, TimeUnit.MILLISECONDS, 3000, TimeUnit.MILLISECONDS, true);
    }

    /**
     * 配置重试策略
     * 不进行重试，避免重复请求
     */
    @Bean
    public Retryer retryer() {
        return Retryer.NEVER_RETRY;
    }
}
