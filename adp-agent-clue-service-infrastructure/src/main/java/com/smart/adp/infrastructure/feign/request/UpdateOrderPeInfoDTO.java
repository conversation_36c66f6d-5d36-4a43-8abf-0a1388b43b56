package com.smart.adp.infrastructure.feign.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/20 16:10
 * @description 更新产品专家集合
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateOrderPeInfoDTO implements Serializable {

    /**
     * 产品专家id（userId）
     */
    private String peId;

    /**
     * 更新人id
     */
    private String modifier;

    /**
     * 更新人
     */
    private String modifyName;

    /**
     * 订单客户信息List,list传入时尽可能小于200
     */
    private List<String> custIdList;
}
