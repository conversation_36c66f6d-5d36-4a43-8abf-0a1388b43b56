package com.smart.adp.infrastructure.config.es;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.*;
import org.apache.http.entity.BufferedHttpEntity;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

@Slf4j
public class LoggingInterceptor {

    public static class RequestLoggingInterceptor implements HttpRequestInterceptor {
        @Override
        public void process(HttpRequest request, HttpContext context)
                throws HttpException, IOException {
            try {
                String uri = request.getRequestLine().getUri();
                if ("/_cluster/health/".equals(uri)) {
                    return;
                }
                log.debug("ES Request: {} {}", request.getRequestLine().getMethod(), uri);

                if (request instanceof HttpEntityEnclosingRequest) {
                    HttpEntityEnclosingRequest entityRequest = (HttpEntityEnclosingRequest) request;
                    HttpEntity entity = entityRequest.getEntity();
                    if (entity != null) {
                        if (!entity.isRepeatable()) {
                            entity = new BufferedHttpEntity(entity);
                            entityRequest.setEntity(entity);
                        }
                        log.debug("ES Request Body: {}", EntityUtils.toString(entity));
                    }
                }
            } catch (Exception e) {
                log.warn("es request log ex", e);
            }
        }
    }

    public static class ResponseLoggingInterceptor implements HttpResponseInterceptor {
        @Override
        public void process(HttpResponse response, HttpContext context)
                throws HttpException, IOException {
            try {
                log.debug("ES Response Status: {}", response.getStatusLine());
                HttpEntity entity = response.getEntity();
                if (entity != null && entity.getContentLength() > 0L) {
                    // TODO 感知响应完成
                    entity = new BufferedHttpEntity(entity);
                    response.setEntity(entity);
                    log.debug("ES Response Body: {}", EntityUtils.toString(entity));
                }
            } catch (Exception e) {
                log.warn("es response log ex", e);
            }
        }
    }
}