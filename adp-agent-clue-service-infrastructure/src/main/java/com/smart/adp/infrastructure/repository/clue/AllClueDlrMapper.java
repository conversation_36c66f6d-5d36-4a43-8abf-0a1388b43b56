package com.smart.adp.infrastructure.repository.clue;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
public interface AllClueDlrMapper extends BaseMapper<SacAllClueInfoDlr> {

    int deleteByPhone(@Param("phone") String phone);

    /**
     * 批量更新线索
     *
     * @param sacAllClueInfoDlrList
     * @return
     */
    int updateClueInfoDlrBatch(@Param("sacAllClueInfoDlrList") List<SacAllClueInfoDlr> sacAllClueInfoDlrList);
}
