package com.smart.adp.infrastructure.feign.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/4/20 16:13
 * @description 订单产品专家信息
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPeInfoDTO implements Serializable {

    /**
     * 客户id
     */
    private String custId;
}
