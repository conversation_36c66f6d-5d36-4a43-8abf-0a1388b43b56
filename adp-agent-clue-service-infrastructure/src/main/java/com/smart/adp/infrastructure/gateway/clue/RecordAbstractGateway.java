package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.gateway.clue.IRecordAbstractGateway;
import com.smart.adp.domain.valueObject.clue.SacTestRecordAbstractVO;
import com.smart.adp.domain.valueObject.clue.SacVirtualRecordAbstractVO;
import com.smart.adp.infrastructure.repository.clue.SacTestRecordAbstractMapper;
import com.smart.adp.infrastructure.repository.clue.SacVirtualRecordAbstractMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.valueObject.clue.table.SacVirtualRecordAbstractVOTableDef.SAC_VIRTUAL_RECORD_ABSTRACT_VO;

/**
 * @Description: 录音摘要gateway实现
 * @Author: rik.ren
 * @Date: 2025/3/13 15:33
 **/
@Service
public class RecordAbstractGateway implements IRecordAbstractGateway {
    @Autowired
    private SacVirtualRecordAbstractMapper virtualRecordAbstractMapper;
    @Autowired
    private SacTestRecordAbstractMapper testRecordAbstractMapper;

    /**
     * 报错虚拟外呼摘要内容
     *
     * @param entity
     * @return
     */
    @Override
    public Boolean saveVirtualAbstractContent(SacVirtualRecordAbstractVO entity) {
        return virtualRecordAbstractMapper.insert(entity) > 0;
    }

    /**
     * 保存试驾录音摘要内容
     *
     * @param entity
     * @return
     */
    @Override
    public Boolean saveTestAbstractContent(SacTestRecordAbstractVO entity) {
        return testRecordAbstractMapper.insert(entity) > 0;
    }

    /**
     * 查询虚拟外呼摘要内容
     *
     * @return
     */
    @Override
    public Page<SacVirtualRecordAbstractVO> queryVirtualRecordAbs(SacVirtualRecordAbstractVO param,
                                                                  Page<SacVirtualRecordAbstractVO> pageParam,
                                                                  QueryOrderBy orderBy,
                                                                  QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_VIRTUAL_RECORD_ABSTRACT_VO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(SAC_VIRTUAL_RECORD_ABSTRACT_VO.as("vRecord"));
        wrapper.and(SAC_VIRTUAL_RECORD_ABSTRACT_VO.CUST_ID.eq(param.getCustId(), StringUtil::hasText))
                .and(SAC_VIRTUAL_RECORD_ABSTRACT_VO.RECORD_ID.eq(param.getRecordId(), StringUtil::hasText));
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_VIRTUAL_RECORD_ABSTRACT_VO.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        Page<SacVirtualRecordAbstractVO> paginate = virtualRecordAbstractMapper.paginate(pageParam.getPageNumber(),
                pageParam.getPageSize(), wrapper);
        return paginate;
    }

    /**
     * 查询试驾录音摘要内容
     *
     * @return
     */
    @Override
    public List<SacTestRecordAbstractVO> queryTestRecordAbs() {
        return null;
    }
}
