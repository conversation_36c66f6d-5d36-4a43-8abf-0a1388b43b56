package com.smart.adp.infrastructure.publisher;

import com.smart.adp.domain.common.constants.StringConstant;
import com.smart.adp.domain.entity.message.CustEventMessage;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

import static com.smart.adp.domain.common.constants.MQConstants.CUST_EVENT_EXCHANGE;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/17
 */
@Component
public class CustEventPublisher {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendCustEventMessage(CustEventMessage msg) {
        // 生成唯一关联ID（用于追踪）
        CorrelationData correlation = new CorrelationData(UUID.randomUUID().toString());

        // 发送消息
        rabbitTemplate.convertAndSend(
                CUST_EVENT_EXCHANGE,
                StringConstant.EMPTY,
                msg,
                message -> {
                    message.getMessageProperties().setContentType(MessageProperties.CONTENT_TYPE_JSON);
                    return message;
                },
                correlation
        );
    }
}
