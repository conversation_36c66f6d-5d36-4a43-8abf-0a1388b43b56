package com.smart.adp.infrastructure.gateway.base;


import com.smart.adp.domain.entity.base.SystemConfig;
import com.smart.adp.domain.gateway.base.SystemConfigGateway;
import com.smart.adp.domain.valueObject.base.SystemConfigVO;
import com.smart.adp.infrastructure.repository.base.SystemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * date 2025/3/6 20:40
 * @description 关联系统配置信息
 **/
@Slf4j
@Component
public class SystemConfigGatewayImpl implements SystemConfigGateway {

    @Autowired
    SystemMapper systemMapper;

    @Override
    public List<SystemConfigVO> findSystemConfigInfo(String configCode, String orgCode, String isEnable) {

        return systemMapper.selectByConfigCode(configCode, orgCode, isEnable);
    }

    @Override
    public List<SystemConfig> findSysTemConfigByCode(String configCode) {
        return systemMapper.findSystemConfigByCode(configCode);
    }
}
