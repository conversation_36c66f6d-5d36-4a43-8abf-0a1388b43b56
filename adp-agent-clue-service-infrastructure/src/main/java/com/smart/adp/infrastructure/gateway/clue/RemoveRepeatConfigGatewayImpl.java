package com.smart.adp.infrastructure.gateway.clue;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.entity.clue.RemoveRepeatConfig;
import com.smart.adp.domain.gateway.clue.RemoveRepeatConfigGateway;
import com.smart.adp.infrastructure.repository.clue.RemoveRepeatConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.smart.adp.domain.entity.clue.table.RemoveRepeatConfigTableDef.REMOVE_REPEAT_CONFIG;


/**
 * <AUTHOR>
 * date 2025/3/6 20:40
 * @description 关联系统配置信息
 **/
@Slf4j
@Component
public class RemoveRepeatConfigGatewayImpl implements RemoveRepeatConfigGateway {

    @Autowired
    private RemoveRepeatConfigMapper removeRepeatConfigMapper;

    @Override
    public List<RemoveRepeatConfig> findRemoveRepeatConfig(String orgCode) {
        QueryWrapper wrapper = QueryWrapper.create()
                .select(REMOVE_REPEAT_CONFIG.CONFIG_ID, REMOVE_REPEAT_CONFIG.CUSTOM, REMOVE_REPEAT_CONFIG.CHECK_PHONE,
                        REMOVE_REPEAT_CONFIG.CHECK_TIME,
                        REMOVE_REPEAT_CONFIG.CHECK_TIME_HORIZON, REMOVE_REPEAT_CONFIG.CREATE_DATE,
                        REMOVE_REPEAT_CONFIG.CREATE_NAME, REMOVE_REPEAT_CONFIG.CREATOR,
                        REMOVE_REPEAT_CONFIG.GROUP_ID, REMOVE_REPEAT_CONFIG.IS_ENABLE, REMOVE_REPEAT_CONFIG.ORG_CODE,
                        REMOVE_REPEAT_CONFIG.ORG_NAME,
                        REMOVE_REPEAT_CONFIG.UPDATE_CONTROL_ID, REMOVE_REPEAT_CONFIG.LAST_UPDATED_DATE,
                        REMOVE_REPEAT_CONFIG.MODIFIER, REMOVE_REPEAT_CONFIG.MODIFIER_NAME)
                .eq(RemoveRepeatConfig::getOrgCode, orgCode)
                .orderBy(RemoveRepeatConfig::getLastUpdatedDate)
                .desc();
        ;
        return removeRepeatConfigMapper.selectListByQuery(wrapper);
    }
}
