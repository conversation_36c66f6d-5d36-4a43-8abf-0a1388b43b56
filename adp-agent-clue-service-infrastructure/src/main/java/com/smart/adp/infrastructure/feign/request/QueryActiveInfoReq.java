package com.smart.adp.infrastructure.feign.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 查询线索的活跃信息
 * @Author: rik.ren
 * @Date: 2025/3/11 20:15
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryActiveInfoReq implements Serializable {
    /**
     * 手机号集合
     */
    List<String> listPhone;
}
