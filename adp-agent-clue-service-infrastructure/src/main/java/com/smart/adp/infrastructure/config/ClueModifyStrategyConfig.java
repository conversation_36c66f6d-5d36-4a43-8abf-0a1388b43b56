package com.smart.adp.infrastructure.config;

import com.smart.adp.domain.enums.ClueModifyTypeEnum;
import com.smart.adp.domain.strategy.modifyClue.*;
import com.smart.adp.domain.utils.ApplicationContextHolder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: spring启动时加载策略到map中
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Configuration
public class ClueModifyStrategyConfig {

    @Bean
    @DependsOn("applicationContextHolder")
    public Map<Integer, ClueModifyStrategy> clueModifyStrategyMap() {
        Map<Integer, ClueModifyStrategy> map = new HashMap<>();
        map.put(ClueModifyTypeEnum.UserDescription.getCode(),
                ApplicationContextHolder.getBean(UserDescriptionModifyStrategy.class));
        map.put(ClueModifyTypeEnum.UserLable.getCode(), ApplicationContextHolder.getBean(UserLabelModifyStrategy.class));
        map.put(ClueModifyTypeEnum.UserLevel.getCode(), ApplicationContextHolder.getBean(UserLevelModifyStrategy.class));
        map.put(ClueModifyTypeEnum.IntendedVehicleModel.getCode(),
                ApplicationContextHolder.getBean(IntendedVehicleModelModifyStrategy.class));
        map.put(ClueModifyTypeEnum.Location.getCode(), ApplicationContextHolder.getBean(LocationModifyStrategy.class));
        map.put(ClueModifyTypeEnum.SourceOfClues.getCode(), ApplicationContextHolder.getBean(SourceOfCluesModifyStrategy.class));
        map.put(ClueModifyTypeEnum.SourceOfAgentClues.getCode(),
                ApplicationContextHolder.getBean(SourceOfAgentCluesModifyStrategy.class));
        map.put(ClueModifyTypeEnum.CompetitorModels.getCode(),
                ApplicationContextHolder.getBean(CompetitorModelsModifyStrategy.class));
        map.put(ClueModifyTypeEnum.Special.getCode(),
                ApplicationContextHolder.getBean(ClueSpecialModifyStrategy.class));
        map.put(ClueModifyTypeEnum.StartDriving.getCode(),
                ApplicationContextHolder.getBean(StartDrivingModifyStrategy.class));
        return map;
    }
}