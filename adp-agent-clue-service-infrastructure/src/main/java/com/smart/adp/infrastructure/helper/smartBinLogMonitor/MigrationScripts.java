package com.smart.adp.infrastructure.helper.smartBinLogMonitor;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description: 数据库监听变动脚本实体
 * @Author: rik.ren
 * @Date: 2025/4/22 15:13
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(value = "db_migration_scripts", schema = "interfacecenter")
public class MigrationScripts {
    @Id(keyType = KeyType.Auto)
    private Long id;
    @Column("version")
    private String version;
    @Column("schema")
    private String schema;
    @Column("script")
    private String script;
    @Column("event_date")
    private LocalDateTime eventDate;
    @Column(value = "del_flag", isLogicDelete = true)
    private Integer delFlag;
}
