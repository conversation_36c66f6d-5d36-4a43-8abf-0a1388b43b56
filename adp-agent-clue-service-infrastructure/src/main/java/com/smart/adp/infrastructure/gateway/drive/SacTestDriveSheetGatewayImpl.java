package com.smart.adp.infrastructure.gateway.drive;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryOrderBy;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.bo.drive.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.entity.drive.SacTestDriveSheetEntity;
import com.smart.adp.domain.enums.DriveTaskStateCodeEnum;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.gateway.drive.SacTestDriveSheetGateway;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveSheetMapper;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveTaskMapper;
import com.smart.adp.infrastructure.utils.PageConverterFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.entity.drive.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;
import static com.smart.adp.domain.entity.drive.table.SacTestDriveTaskTableDef.SAC_TEST_DRIVE_TASK;

/**
 * @Description: 试驾gateway实现
 * @Author: rik.ren
 * @Date: 2025/3/15 15:27
 **/
@Service
public class SacTestDriveSheetGatewayImpl implements SacTestDriveSheetGateway {

    @Autowired
    private SacTestDriveSheetMapper testDriveSheetMapper;

    @Autowired
    private SacTestDriveTaskMapper testDriveTaskMapper;

    /**
     * 查询试驾单
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns 尽量指定字段查询
     * @return
     */
    @Override
    public DomainPage<SacTestDriveSheetEntity> queryTestDriveSheet(SacTestDriveSheetEntity param, SacTestDriveSheetBO boParam,
                                                                   QueryOrderBy orderBy, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_TEST_DRIVE_SHEET_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet"));
        param.conditions(wrapper, boParam);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        Page<SacTestDriveSheetEntity> paginate = testDriveSheetMapper.paginate(boParam.getPageObj().getPageNumber(),
                boParam.getPageObj().getPageSize(), wrapper);
        DomainPage<SacTestDriveSheetEntity> domainPage = PageConverterFactory.toDomainPage(paginate);
        return domainPage;
    }

    /**
     * 查询试驾单
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns
     * @return
     */
    @Override
    public List<SacTestDriveSheetEntity> queryTestDriveSheetList(SacTestDriveSheetEntity param, SacTestDriveSheetBO boParam,
                                                                 QueryOrderBy orderBy, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_TEST_DRIVE_SHEET_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet"));
        param.conditions(wrapper, boParam);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        return testDriveSheetMapper.selectListByQuery(wrapper);
    }

    /**
     * 更新试驾单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean updateTestDriveSheet(SacTestDriveSheetEntity param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("TEST_DRIVE_SHEET_ID"), SqlConsts.EQUALS,
                param.getTestDriveSheetId());
        return testDriveSheetMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 插入试驾单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean insertTestDriveSheet(SacTestDriveSheetEntity param) {
        return null;
    }

    @Override
    public long toDoTestDriveTaskNum(UserBusiEntity user) {
        QueryWrapper wrapper = QueryWrapper.create()
                                           .and(SAC_TEST_DRIVE_TASK.TASK_PERSON_DLR_CODE.eq(user.getDlrCode(), StringUtil::hasText))
                                           .and(SAC_TEST_DRIVE_TASK.TASK_STATE_CODE.eq(DriveTaskStateCodeEnum.TO_DO.getCode()))
                                           .and(SAC_TEST_DRIVE_TASK.TASK_PERSON_ID.eq(user.getUserID(), UserUtil::productExpertValid));

        return testDriveTaskMapper.selectCountByQuery(wrapper);
    }

    @Override
    public long toDoTestDriveNum(UserBusiEntity user) {
        QueryWrapper wrapper = QueryWrapper.create()
                                           .and(SAC_TEST_DRIVE_SHEET_ENTITY.DLR_CODE.eq(user.getDlrCode(), StringUtil::hasText))
                                           .and(SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_ID.eq(user.getUserID(), UserUtil::productExpertValid))
                                           .and(SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS.in(TestDriveStatusEnum.NOT_STARTED.getCode(), TestDriveStatusEnum.IN_PROGRESS.getCode()));

        return testDriveSheetMapper.selectCountByQuery(wrapper);
    }
}
