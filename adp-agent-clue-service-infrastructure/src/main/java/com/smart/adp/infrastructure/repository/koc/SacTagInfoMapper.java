package com.smart.adp.infrastructure.repository.koc;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.koc.SacTagInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 标签信息Mapper
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Mapper
public interface SacTagInfoMapper extends BaseMapper<SacTagInfo> {

    /**
     * 根据父标签主键ID查询子标签
     *
     * @param parentId 父标签主键ID
     * @return 子标签列表
     */
    @Select("SELECT * FROM t_sac_tag_info WHERE parent_id = #{parentId} AND tag_status = 1 ORDER BY sort_order ASC")
    List<SacTagInfo> findByParentId(@Param("parentId") Long parentId);

    /**
     * 根据父标签业务ID查询子标签
     *
     * @param parentTagId 父标签业务ID
     * @return 子标签列表
     */
    @Select("SELECT * FROM t_sac_tag_info WHERE parent_tag_id = #{parentTagId} AND tag_status = 1 ORDER BY sort_order ASC")
    List<SacTagInfo> findByParentTagId(@Param("parentTagId") String parentTagId);

    /**
     * 根据标签层级查询标签
     *
     * @param tagLevel 标签层级
     * @return 标签列表
     */
    @Select("SELECT * FROM t_sac_tag_info WHERE tag_level = #{tagLevel} AND tag_status = 1 ORDER BY sort_order ASC")
    List<SacTagInfo> findByLevel(@Param("tagLevel") Integer tagLevel);

    /**
     * 查询所有启用的标签
     *
     * @return 标签列表
     */
    @Select("SELECT * FROM t_sac_tag_info WHERE tag_status = 1 ORDER BY tag_level ASC, sort_order ASC")
    List<SacTagInfo> findAllEnabled();

    /**
     * 根据标签名称模糊查询
     *
     * @param tagName 标签名称
     * @return 标签列表
     */
    @Select("SELECT * FROM t_sac_tag_info WHERE tag_name LIKE CONCAT('%', #{tagName}, '%') AND tag_status = 1 ORDER BY tag_level ASC, sort_order ASC")
    List<SacTagInfo> findByNameLike(@Param("tagName") String tagName);

    /**
     * 根据业务ID查询标签
     *
     * @param tagId 标签业务ID
     * @return 标签信息
     */
    @Select("SELECT * FROM t_sac_tag_info WHERE tag_id = #{tagId}")
    SacTagInfo findByTagId(@Param("tagId") String tagId);

    /**
     * 检查标签名称是否存在（同一父标签下）
     *
     * @param tagName 标签名称
     * @param parentId 父标签主键ID
     * @param excludeId 排除的主键ID
     * @return 数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM t_sac_tag_info WHERE tag_name = #{tagName}",
        "<if test='parentId != null'>",
        "  AND parent_id = #{parentId}",
        "</if>",
        "<if test='parentId == null'>",
        "  AND parent_id IS NULL",
        "</if>",
        "<if test='excludeId != null'>",
        "  AND id != #{excludeId}",
        "</if>",
        "</script>"
    })
    Integer countByName(@Param("tagName") String tagName, @Param("parentId") Long parentId, @Param("excludeId") Long excludeId);

    /**
     * 根据业务ID检查标签名称是否存在
     *
     * @param tagName 标签名称
     * @param parentTagId 父标签业务ID
     * @param excludeTagId 排除的标签业务ID
     * @return 数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM t_sac_tag_info WHERE tag_name = #{tagName}",
        "<if test='parentTagId != null and parentTagId != \"\"'>",
        "  AND parent_tag_id = #{parentTagId}",
        "</if>",
        "<if test='parentTagId == null or parentTagId == \"\"'>",
        "  AND parent_tag_id IS NULL",
        "</if>",
        "<if test='excludeTagId != null and excludeTagId != \"\"'>",
        "  AND tag_id != #{excludeTagId}",
        "</if>",
        "</script>"
    })
    Integer countByNameAndParentTagId(@Param("tagName") String tagName, @Param("parentTagId") String parentTagId, @Param("excludeTagId") String excludeTagId);

    /**
     * 更新标签状态
     *
     * @param id 主键ID
     * @param status 状态
     * @return 影响行数
     */
    @Select("UPDATE t_sac_tag_info SET tag_status = #{status} WHERE id = #{id}")
    Integer updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 根据业务ID更新标签状态
     *
     * @param tagId 标签业务ID
     * @param status 状态
     * @return 影响行数
     */
    @Select("UPDATE t_sac_tag_info SET tag_status = #{status} WHERE tag_id = #{tagId}")
    Integer updateStatusByTagId(@Param("tagId") String tagId, @Param("status") Integer status);
}
