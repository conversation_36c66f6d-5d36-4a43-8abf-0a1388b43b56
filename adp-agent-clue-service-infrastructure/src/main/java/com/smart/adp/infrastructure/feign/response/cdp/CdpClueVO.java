package com.smart.adp.infrastructure.feign.response.cdp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/6/16 15:01
 * @description 查询Cdp线索返回响应
 **/
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "Cdp线索信息")
public class CdpClueVO implements Serializable {

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String bk;

    /**
     * 手用户头像地址URL
     */
    @Schema(description = "用户头像地址URL")
    private String name;

    /**
     * 中国手机号 1开头 11位
     */
    @Schema(description = "手机号")
    private String mobile;


    /**
     * 省
     */
    @Schema(description = "省（参照smart提供区域标准编码）")
    private String c_province;

    /**
     * 省code
     */
    @Schema(description = "省code（参照smart提供区域标准编码）")
    private String c_province_code;

    /**
     * 城市
     */
    @Schema(description = "城市（参照smart提供区域标准编码）")
    private String c_city;

    /**
     * 城市code
     */
    @Schema(description = "城市code（参照smart提供区域标准编码）")
    private String c_city_code;

    /**
     * 地区
     */
    @Schema(description = "地区（参照smart提供区域标准编码）")
    private String c_county;

    /**
     * 区县code
     */
    @Schema(description = "区县code（参照smart提供区域标准编码）")
    private String c_district_code;


    /**
     * 手机号、bk和name是否加密，1加密，0不加密
     */
    @Schema(description = "手机号、bk和name是否加密，1加密，0不加密 ")
    private String c_mobile_encrypted;


    /**
     * 一级渠道编码，根据业务提供具体数据传值
     */
    @Schema(description = "一级渠道编码，根据业务提供具体数据传值")
    private String c_first_channel;

    /**
     * 二级渠道编码，根据业务提供具体数据传值
     */
    @Schema(description = "二级渠道编码，根据业务提供具体数据传值  ")
    private String c_second_channel;

    /**
     * 首次二级渠道编码，根据业务提供具体数据传值
     */
    @Schema(description = "首次二级渠道编码，根据业务提供具体数据传值  ")
    private String c_fixed_second_channel;

    /**
     * 三级渠道编码，根据业务提供具体数据传值
     */
    @Schema(description = "三级渠道编码，根据业务提供具体数据传值  ")
    private String c_third_channel;

    /**
     * 首次三级渠道编码，根据业务提供具体数据传值
     */
    @Schema(description = "首次三级渠道编码，根据业务提供具体数据传值  ")
    private String c_fixed_third_channel;

    /**
     * 细分渠道，根据内部投放分配填写
     */
    @Schema(description = "细分渠道，根据内部投放分配填写")
    private String c_cus_source;

    /**
     * 首次细分渠道，根据内部投放分配填写
     */
    @Schema(description = "首次细分渠道，根据内部投放分配填写")
    private String c_fixed_cus_source;

    /**
     * 意向车型，赋值当前投放车型
     */
    @Schema(description = "意向车型，赋值当前投放车型")
    private String c_interested_car_model;

    /**
     * 意向版型
     */
    @Schema(description = "意向版型")
    private String c_intended_version;

    /**
     * 门店编码
     */
    @Schema(description = "门店编码")
    private String c_store_code;


    /**
     * 门店名称
     */
    @Schema(description = "门店名称")
    private String c_store;

    /**
     * 系统来源
     */
    @Schema(description = "系统来源")
    private String c_lastupdate_system;

    /**
     * 线索创建时间
     */
    @Schema(description = "线索创建时间")
    private String createTime;

    /**
     * 线索更新时间
     */
    @Schema(description = "线索更新时间")
    private String updateTime;
}
