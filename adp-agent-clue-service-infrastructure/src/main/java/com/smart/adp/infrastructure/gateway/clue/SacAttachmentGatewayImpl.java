package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.clue.SacAttachmentBO;
import com.smart.adp.domain.gateway.clue.SacAttachmentGateway;
import com.smart.adp.domain.valueObject.clue.SacAttachmentVO;
import com.smart.adp.infrastructure.repository.clue.SacAttachmentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.valueObject.clue.table.SacAttachmentVOTableDef.SAC_ATTACHMENT_VO;

/**
 * @Description: 附件gateway
 * @Author: rik.ren
 * @Date: 2025/3/21 18:03
 **/
@Service
public class SacAttachmentGatewayImpl implements SacAttachmentGateway {

    @Autowired
    private SacAttachmentMapper attachmentMapper;

    /**
     * 根据对象条件查询附件
     *
     * @param param
     * @return
     */
    @Override
    public List<SacAttachmentVO> queryAttachment(SacAttachmentBO param, QueryColumn[] columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_ATTACHMENT_VO.ATTACHMENT_ID, SAC_ATTACHMENT_VO.BILL_TYPE, SAC_ATTACHMENT_VO.BILL_ID,
                    SAC_ATTACHMENT_VO.FILE_NAME, SAC_ATTACHMENT_VO.FILE_PATH, SAC_ATTACHMENT_VO.FILE_EXTENSION};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(columns);
        param.conditions(wrapper, param);
        return attachmentMapper.selectListByQuery(wrapper);
    }
}
