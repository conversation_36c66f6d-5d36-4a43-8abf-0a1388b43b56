package com.smart.adp.infrastructure.gateway.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.common.annotation.SmartADPCache;
import com.smart.adp.domain.common.constants.CommonConstant;
import com.smart.adp.domain.gateway.base.LookUpGateway;
import com.smart.adp.domain.valueObject.base.LookUpInfo;
import com.smart.adp.infrastructure.repository.base.LookUpMapper;
import com.smart.adp.infrastructure.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.smart.adp.domain.valueObject.base.table.LookUpInfoTableDef.LOOK_UP_INFO;


/**
 * <AUTHOR>
 * date 2025/3/5 11:07
 * @description
 **/
@Slf4j
@Component
public class LookUpGatewayImpl implements LookUpGateway {

    @Autowired
    private LookUpMapper lookUpMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public LookUpInfo findLookUpInfo(String lookUpTypeCode, String lookUpValueCode, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{LOOK_UP_INFO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumn)
                .eq(LookUpInfo::getLookUpTypeCode, lookUpTypeCode)
                .eq(LookUpInfo::getLookUpValueCode, lookUpValueCode)
                .orderBy(LookUpInfo::getCreatedDate)
                .desc();
        return lookUpMapper.selectOneByQuery(wrapper);
    }

    @Override
    public LookUpInfo findLookUpInfoWithCache(String lookUpTypeCode, String lookUpValueCode, QueryColumn... needColumn) {
        String cacheKey = CommonConstant.CACHE_PREFIX_LOOK + lookUpTypeCode + ":" + lookUpValueCode;
        String cachedValue = (String) redisUtil.get(cacheKey);
        // 缓存命中
        if (com.smart.tools.utils.StringUtils.isNotEmpty(cachedValue)) {
            return JSONUtil.toBean(cachedValue, LookUpInfo.class);
        }

        LookUpInfo lookUpInfo = this.findLookUpInfo(lookUpTypeCode, lookUpValueCode);
        if (Objects.isNull(lookUpInfo)) {
            return new LookUpInfo();
        }
        // 缓存有效数据
        redisUtil.set(cacheKey, JSONUtil.toJsonStr(lookUpInfo), 24 * 60 * 60, TimeUnit.SECONDS);
        return lookUpInfo;
    }

    @Override
    public LookUpInfo findLookUpConfig(String lookUpTypeCode, QueryColumn... needColumn) {
        if (StringUtils.isEmpty(lookUpTypeCode)) {
            return null;
        }
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{LOOK_UP_INFO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumn)
                .eq(LookUpInfo::getLookUpTypeCode, lookUpTypeCode)
                .orderBy(LookUpInfo::getCreatedDate)
                .desc();
        return lookUpMapper.selectOneByQuery(wrapper);
    }

    @Override
    @SmartADPCache(value = "lookUpTypeCode", key = "#lookUpTypeCode", expire = 10, timeUnit = TimeUnit.HOURS)
    public List<LookUpInfo> findLookUpByTypeCode(String lookUpTypeCode, QueryColumn... needColumn) {
        if (StringUtils.isEmpty(lookUpTypeCode)) {
            return null;
        }
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{LOOK_UP_INFO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumn)
                .eq(LookUpInfo::getLookUpTypeCode, lookUpTypeCode)
                .from(LOOK_UP_INFO.as("lookup"));
        return lookUpMapper.selectListByQuery(wrapper);
    }

    @Override
    public List<LookUpInfo> findLookUp(String lookUpTypeCode, List<String> lookUpValueCode, QueryColumn... needColumn) {
        if (CollectionUtil.isEmpty(lookUpValueCode)) {
            return null;
        }
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{LOOK_UP_INFO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumn)
                .eq(LookUpInfo::getLookUpTypeCode, lookUpTypeCode)
                .in(LookUpInfo::getLookUpValueCode, lookUpValueCode)
                .from(LOOK_UP_INFO.as("lookup"));
        return lookUpMapper.selectListByQuery(wrapper);
    }

    @Override
    public List<LookUpInfo> findLookUp(LookUpInfo entity, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{LOOK_UP_INFO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(needColumn)
                .from(LOOK_UP_INFO.as("lookup"));
        entity.conditions(wrapper);
        return lookUpMapper.selectListByQuery(wrapper);
    }

    @Override
    public List<LookUpInfo> findByTypeCode(String lookUpTypeCode) {
        if (StringUtils.isEmpty(lookUpTypeCode)) {
            return null;
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(LOOK_UP_INFO.ALL_COLUMNS)
                .eq(LookUpInfo::getLookUpTypeCode, lookUpTypeCode)
                .from(LOOK_UP_INFO.as("lookup"))
                .orderBy(LookUpInfo::getLookUpTypeCode)
                .asc()
                .orderBy(LookUpInfo::getOrderNo)
                .asc()
                .orderBy(LookUpInfo::getLookUpValueCode)
                .asc();
        return lookUpMapper.selectListByQuery(wrapper);
    }

    @Override
    public List<LookUpInfo> findByType(String type, QueryColumn... needColumn) {
        if (StringUtils.isEmpty(type)) {
            return Collections.emptyList();
        }
        QueryWrapper wrapper = QueryWrapper.create()
                                           .select(needColumn)
                                           .and(LOOK_UP_INFO.LOOK_UP_TYPE_CODE.eq(type))
                                           .from(LOOK_UP_INFO.as("lookup"));
        return lookUpMapper.selectListByQuery(wrapper);
    }
}
