package com.smart.adp.infrastructure.feign;

import com.smart.adp.infrastructure.feign.config.UcFeignConfig;
import com.smart.adp.infrastructure.feign.response.koc.UcRsp;
import com.smart.adp.infrastructure.feign.response.koc.UcUserInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Description: UC用户服务Feign客户端
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@FeignClient(name = "uc-user-service"
        , url = "${feign.uc.user.url:http://localhost:8080}"
        , configuration = UcFeignConfig.class)
public interface UCUserFeign {
    /**
     * 根据用户ID批量查询用户信息
     * @param smartIdList smartId列表
     * @return UC响应
     */
    @PostMapping("/tob/userBase/v1/by-smartIds")
    UcRsp<UcUserInfoVO> getUsersBySmartIds(@RequestBody List<String> smartIdList);

    /**
     * 根据手机号批量查询用户信息
     * @param mobiles 手机号列表
     * @return UC响应
     */
    @PostMapping("/tob/userBase/v1/by-mobiles")
    UcRsp<UcUserInfoVO> getUsersByMobiles(@RequestBody List<String> mobiles);
}
