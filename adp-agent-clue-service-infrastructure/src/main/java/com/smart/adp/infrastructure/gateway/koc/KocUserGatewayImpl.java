package com.smart.adp.infrastructure.gateway.koc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.koc.KocUserInfoBO;
import com.smart.adp.domain.bo.koc.KocUserQueryBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.koc.SacUserTagRel;
import com.smart.adp.domain.gateway.koc.KocUserGateway;
import com.smart.adp.domain.gateway.koc.UCUserGateway;
import com.smart.adp.infrastructure.repository.koc.SacUserTagRelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.smart.adp.domain.entity.koc.table.SacUserTagRelTableDef.SAC_USER_TAG_REL;

/**
 * @Description: KOC用户网关实现
 * @Author: e-xin.zhou
 * @Date: 2025/8/4
 **/
@Slf4j
@Service
public class KocUserGatewayImpl implements KocUserGateway {

    @Autowired
    private SacUserTagRelMapper sacUserTagRelMapper;

    @Autowired
    private UCUserGateway ucUserGateway;

    @Override
    public List<SacUserTagRel> findBySmartId(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return new ArrayList<>();
        }
        return sacUserTagRelMapper.findBySmartId(smartId);
    }

    @Override
    public List<SacUserTagRel> findByPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return new ArrayList<>();
        }
        return sacUserTagRelMapper.findByPhone(phone);
    }

    @Override
    public List<SacUserTagRel> findByCondition(SacUserTagRel condition) {
        if (ObjectUtil.isEmpty(condition)) {
            return new ArrayList<>();
        }
        return sacUserTagRelMapper.selectListByQuery(condition);
    }

    @Override
    public DomainPage<KocUserInfoBO> findUserPage(KocUserQueryBO queryBO) {
        if (ObjectUtil.isEmpty(queryBO)) {
            return new DomainPage<>();
        }

        try {
            // 构建查询条件
            QueryWrapper queryWrapper = buildQueryWrapper(queryBO);
            
            // 分页查询
            Page<SacUserTagRel> page = Page.of(
                queryBO.getPageNum() != null ? queryBO.getPageNum() : 1,
                queryBO.getPageSize() != null ? queryBO.getPageSize() : 10
            );
            
            Page<SacUserTagRel> resultPage = sacUserTagRelMapper.paginate(page, queryWrapper);
            
            // 转换结果
            List<KocUserInfoBO> userInfos = convertToUserInfos(resultPage.getRecords());
            
            // 构建分页结果
            DomainPage<KocUserInfoBO> domainPage = new DomainPage<>();
            domainPage.setRecords(userInfos);
            domainPage.setTotal(resultPage.getTotalRow());
            domainPage.setCurrent((long) resultPage.getPageNumber());
            domainPage.setSize((long) resultPage.getPageSize());
            domainPage.setPages((long) resultPage.getTotalPage());
            
            return domainPage;
        } catch (Exception e) {
            log.error("分页查询用户信息失败", e);
            return new DomainPage<>();
        }
    }

    @Override
    public List<KocUserInfoBO> searchUsers(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new ArrayList<>();
        }
        return ucUserGateway.searchUsers(keyword);
    }

    @Override
    public List<KocUserInfoBO> findUsersByTagId(String tagId) {
        if (StrUtil.isBlank(tagId)) {
            return new ArrayList<>();
        }
        
        List<SacUserTagRel> tagRels = sacUserTagRelMapper.findByRefBusinessId(tagId);
        if (CollectionUtil.isEmpty(tagRels)) {
            return new ArrayList<>();
        }
        
        List<String> smartIds = tagRels.stream()
                .map(SacUserTagRel::getSmartId)
                .distinct()
                .collect(Collectors.toList());
        
        return ucUserGateway.findBySmartIds(smartIds);
    }

    @Override
    public List<KocUserInfoBO> findUsersByExpertTypeId(String expertTypeId) {
        if (StrUtil.isBlank(expertTypeId)) {
            return new ArrayList<>();
        }
        
        List<SacUserTagRel> expertTypeRels = sacUserTagRelMapper.findByRefBusinessId(expertTypeId);
        if (CollectionUtil.isEmpty(expertTypeRels)) {
            return new ArrayList<>();
        }
        
        List<String> smartIds = expertTypeRels.stream()
                .map(SacUserTagRel::getSmartId)
                .distinct()
                .collect(Collectors.toList());
        
        return ucUserGateway.findBySmartIds(smartIds);
    }

    @Override
    public Boolean saveUserTagRel(SacUserTagRel userTagRel) {
        if (ObjectUtil.isEmpty(userTagRel)) {
            return false;
        }
        try {
            int result = sacUserTagRelMapper.insert(userTagRel);
            return result > 0;
        } catch (Exception e) {
            log.error("保存用户标签关系失败", e);
            return false;
        }
    }

    @Override
    public Boolean batchSaveUserTagRel(List<SacUserTagRel> userTagRels) {
        if (CollectionUtil.isEmpty(userTagRels)) {
            return false;
        }
        try {
            int result = sacUserTagRelMapper.insertBatch(userTagRels);
            return result > 0;
        } catch (Exception e) {
            log.error("批量保存用户标签关系失败", e);
            return false;
        }
    }

    @Override
    public Boolean deleteUserTagRel(String relId) {
        if (StrUtil.isBlank(relId)) {
            return false;
        }
        try {
            SacUserTagRel userTagRel = sacUserTagRelMapper.findByRelId(relId);
            if (ObjectUtil.isEmpty(userTagRel)) {
                return false;
            }
            int result = sacUserTagRelMapper.deleteById(userTagRel.getId());
            return result > 0;
        } catch (Exception e) {
            log.error("删除用户标签关系失败", e);
            return false;
        }
    }

    @Override
    public Boolean deleteByCondition(SacUserTagRel condition) {
        if (ObjectUtil.isEmpty(condition)) {
            return false;
        }
        try {
            int result = sacUserTagRelMapper.deleteByBusinessCondition(
                condition.getSmartId(), 
                condition.getRefBusinessId(), 
                condition.getRelType()
            );
            return result > 0;
        } catch (Exception e) {
            log.error("根据条件删除用户标签关系失败", e);
            return false;
        }
    }

    @Override
    public Boolean batchDeleteUserTagRel(List<String> relIds) {
        if (CollectionUtil.isEmpty(relIds)) {
            return false;
        }
        try {
            // 根据业务ID查询主键ID
            List<Long> ids = new ArrayList<>();
            for (String relId : relIds) {
                SacUserTagRel userTagRel = sacUserTagRelMapper.findByRelId(relId);
                if (ObjectUtil.isNotEmpty(userTagRel)) {
                    ids.add(userTagRel.getId());
                }
            }
            
            if (CollectionUtil.isEmpty(ids)) {
                return false;
            }
            
            int result = sacUserTagRelMapper.deleteBatchByIds(ids);
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除用户标签关系失败", e);
            return false;
        }
    }

    @Override
    public Boolean updateUserTagRel(SacUserTagRel userTagRel) {
        if (ObjectUtil.isEmpty(userTagRel)) {
            return false;
        }
        try {
            int result = sacUserTagRelMapper.update(userTagRel);
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户标签关系失败", e);
            return false;
        }
    }

    @Override
    public Boolean existsUserTagRel(String smartId, String refId, Integer relType) {
        if (StrUtil.isBlank(smartId) || relType == null) {
            return false;
        }
        try {
            Integer count = sacUserTagRelMapper.countBySmartIdAndRefBusinessIdAndRelType(smartId, refId, relType);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查用户标签关系是否存在失败", e);
            return false;
        }
    }

    @Override
    public List<SacUserTagRel> findBySmartIdAndRelType(String smartId, Integer relType) {
        if (StrUtil.isBlank(smartId) || relType == null) {
            return new ArrayList<>();
        }
        return sacUserTagRelMapper.findBySmartIdAndRelType(smartId, relType);
    }

    @Override
    public Integer countUserTags(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return 0;
        }
        return sacUserTagRelMapper.countUserTags(smartId);
    }

    @Override
    public Integer countUserExpertTypes(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return 0;
        }
        return sacUserTagRelMapper.countUserExpertTypes(smartId);
    }

    @Override
    public Integer countUserRemarks(String smartId) {
        if (StrUtil.isBlank(smartId)) {
            return 0;
        }
        return sacUserTagRelMapper.countUserRemarks(smartId);
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildQueryWrapper(KocUserQueryBO queryBO) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        
        if (StrUtil.isNotBlank(queryBO.getSmartId())) {
            queryWrapper.and(SAC_USER_TAG_REL.SMART_ID.eq(queryBO.getSmartId()));
        }
        
        if (StrUtil.isNotBlank(queryBO.getPhone())) {
            queryWrapper.and(SAC_USER_TAG_REL.PHONE.eq(queryBO.getPhone()));
        }
        
        if (StrUtil.isNotBlank(queryBO.getNickName())) {
            queryWrapper.and(SAC_USER_TAG_REL.NICK_NAME.like(queryBO.getNickName()));
        }
        
        if (CollectionUtil.isNotEmpty(queryBO.getTagIds())) {
            queryWrapper.and(SAC_USER_TAG_REL.REF_BUSINESS_ID.in(queryBO.getTagIds()))
                        .and(SAC_USER_TAG_REL.REL_TYPE.eq(1));
        }
        
        if (CollectionUtil.isNotEmpty(queryBO.getExpertTypeIds())) {
            queryWrapper.and(SAC_USER_TAG_REL.REF_BUSINESS_ID.in(queryBO.getExpertTypeIds()))
                        .and(SAC_USER_TAG_REL.REL_TYPE.eq(2));
        }
        
        if (queryBO.getHasRemark() != null && queryBO.getHasRemark()) {
            queryWrapper.and(SAC_USER_TAG_REL.REL_TYPE.eq(3));
        }
        
        queryWrapper.orderBy(SAC_USER_TAG_REL.CREATED_DATE.desc());
        
        return queryWrapper;
    }

    /**
     * 转换为用户信息BO
     */
    private List<KocUserInfoBO> convertToUserInfos(List<SacUserTagRel> userTagRels) {
        if (CollectionUtil.isEmpty(userTagRels)) {
            return new ArrayList<>();
        }
        
        // 获取唯一的smartId列表
        List<String> smartIds = userTagRels.stream()
                .map(SacUserTagRel::getSmartId)
                .distinct()
                .collect(Collectors.toList());
        
        // 从UC系统获取用户信息
        return ucUserGateway.findBySmartIds(smartIds);
    }
}
