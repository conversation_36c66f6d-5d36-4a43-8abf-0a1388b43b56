package com.smart.adp.infrastructure.handler;

import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.entity.clue.CustEventFlow;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.message.CustEventMessage;
import com.smart.adp.domain.enums.ClueStageEnum;
import com.smart.adp.domain.enums.CustEventEnum;
import com.smart.adp.domain.gateway.clue.CustEventFlowGateway;
import com.smart.adp.infrastructure.repository.clue.ClueDlrMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.smart.adp.domain.entity.clue.table.CustEventFlowTableDef.CUST_EVENT_FLOW;
import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/9
 */
@Component
public class ReturnEventHandler implements CustEventHandler {

    @Autowired
    private CustEventFlowGateway custEventFlowGateway;

    @Autowired
    private ClueDlrMapper clueDlrMapper;

    @Override
    public void handle(CustEventMessage message) {
        // 不是下定的最新一条流水
        CustEventFlow lastFlow = custEventFlowGateway.selectOne(QueryWrapper.create()
                                                                            .and(CUST_EVENT_FLOW.CUST_ID.eq(message.getCustId()))
                                                                            .and(CUST_EVENT_FLOW.TYPE.ne(CustEventEnum.ORDER.getCode()))
                                                                            .orderBy(CUST_EVENT_FLOW.EVENT_TIME, false)
                                                                            .limit(1));
        ClueStageEnum stage = ClueStageEnum.getByCode(lastFlow.getStage());
        CustEventFlow flow = message.buildFlow();
        flow.setStage(stage.getCode());
        custEventFlowGateway.insertSelective(flow);

        // update clue
        SacClueInfoDlr clue = new SacClueInfoDlr();
        clue.setColumn18(stage.getCodeStr());
        clueDlrMapper.updateByQuery(clue, QueryWrapper.create()
                                                      .and(SAC_CLUE_INFO_DLR.CUST_ID.eq(message.getCustId()))
                                                      .and(SAC_CLUE_INFO_DLR.COLUMN18.lt(stage.getCodeStr())
                                                                                     .or(SAC_CLUE_INFO_DLR.COLUMN18.isNull())));
    }
}
