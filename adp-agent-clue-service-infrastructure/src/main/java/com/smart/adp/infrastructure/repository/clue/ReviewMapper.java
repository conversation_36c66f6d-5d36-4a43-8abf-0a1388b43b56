package com.smart.adp.infrastructure.repository.clue;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import com.smart.adp.domain.entity.clue.SacReview;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 回访mapper
 * @Author: rik.ren
 * @Date: 2025/3/5 20:42
 **/
public interface ReviewMapper extends BaseMapper<SacReview> {


    /**
     * 插入回访信息
     *
     * @param
     * @return
     */
    int saveSacReview(@Param("param") SacReview param);

    /**
     * 批量更新回访任务
     * @param sacReviews
     * @return
     */
    int updateReviewAssignBatch(@Param("sacReviews") List<SacReview> sacReviews);
}
