package com.smart.adp.infrastructure.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.smart.adp.domain.common.constants.MQConstants.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
@Slf4j
@Configuration
public class RabbitMQConfig {

    /**
     * message converter
     */
    @Bean
    public MessageConverter jsonMessageConverter(ObjectMapper objectMapper) {
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    @Bean
    public RabbitTemplate rabbitTemplate(
            ConnectionFactory connectionFactory,
            MessageConverter converter) {

        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(converter);

        // 发送确认回调
        template.setConfirmCallback((correlation, ack, reason) -> {
            if (ack) {
//                log.info("消息到达Broker: {}", correlation.getId());
            } else {
                log.error("消息未到达 Broker: {}", reason);
            }
        });

        // 路由失败回调
        template.setReturnsCallback(returned -> {
            log.error("消息无法路由到队列: {}", returned.getReplyText());
        });

        return template;
    }

    @Bean
    public CachingConnectionFactory connectionFactory(RabbitProperties properties) {
        CachingConnectionFactory factory = new CachingConnectionFactory();
        factory.setHost(properties.getHost());
        factory.setPort(properties.getPort());
        factory.setUsername(properties.getUsername());
        factory.setPassword(properties.getPassword());
        factory.setVirtualHost(properties.getVirtualHost());
        // recovery
        factory.getRabbitConnectionFactory().setAutomaticRecoveryEnabled(true);
        factory.getRabbitConnectionFactory().setNetworkRecoveryInterval(5000);
        factory.getRabbitConnectionFactory().setTopologyRecoveryEnabled(true);
        factory.setRequestedHeartBeat(60);
        return factory;
    }

    /**
     * 客户事件 DLQ ex
     */
    @Bean
    public DirectExchange custEventDlqExchange() {
        return new DirectExchange(CUST_EVENT_DLQ_EXCHANGE);
    }

    /**
     * 客户事件 DLQ queue
     */
    @Bean
    public Queue custEventDlqQueue() {
        return QueueBuilder.durable(CUST_EVENT_DLQ_QUEUE)
                .build();
    }

    @Bean
    public Binding custEventDlqBinding() {
        return BindingBuilder.bind(custEventDlqQueue())
                .to(custEventDlqExchange())
                .with("");
    }

    /**
     * 客户事件 ex
     */
    @Bean
    public FanoutExchange custEventExchange() {
        return new FanoutExchange(CUST_EVENT_EXCHANGE);
    }

    /**
     * 客户事件 queue
     */
    @Bean
    public Queue custEventQueue() {
        return QueueBuilder.durable(CUST_EVENT_QUEUE)
                .maxLength(CUST_EVENT_MAX_LENGTH)
                .deadLetterExchange(CUST_EVENT_DLQ_EXCHANGE)
                .deadLetterRoutingKey("")
                .build();
    }

    @Bean
    public Binding custEventBinding() {
        return BindingBuilder.bind(custEventQueue())
                .to(custEventExchange());
    }


    /**
     * 线索创建
     */
    @Bean
    public DirectExchange createClueExchange() {
        return new DirectExchange(CREATE_CLUE_EXCHANGE);
    }

    /**
     * 线索创建 queue
     */
    @Bean
    public Queue createClueQueue() {
        return QueueBuilder.durable(CREATE_CLUE_QUEUE)
                .build();
    }

    @Bean
    public Binding createClueBinding() {
        return BindingBuilder.bind(createClueQueue())
                .to(createClueExchange())
                .with("");
    }


    /**
     * 线索分配交换机
     */
    @Bean
    public DirectExchange clueDistributeExchange() {
        return new DirectExchange(DISTRIBUTE_CLUE_EXCHANGE);
    }

    /**
     * 线索分配 queue
     */
    @Bean
    public Queue clueDistributeQueue() {
        return QueueBuilder.durable(DISTRIBUTE_CLUE_QUEUE)
                .build();
    }

    /**
     * 线索分配绑定
     *
     * @return
     */
    @Bean
    public Binding clueDistributeBinding() {
        return BindingBuilder.bind(clueDistributeQueue())
                .to(clueDistributeExchange())
                .with("");
    }

    /**
     * 线索发送企微提醒交换机
     */
    @Bean
    public DirectExchange clueSendWECOMExchange() {
        return new DirectExchange(WECOM_MSG_EXCHANGE);
    }

    /**
     * 线索发送企微提醒 queue
     */
    @Bean
    public Queue clueSendWECOMQueue() {
        return QueueBuilder.durable(WECOM_MSG_QUEUE)
                .build();
    }

    /**
     * 线索发送企微提醒绑定
     *
     * @return
     */
    @Bean
    public Binding clueSendWECOMBinding() {
        return BindingBuilder.bind(clueSendWECOMQueue())
                .to(clueSendWECOMExchange())
                .with(WECOM_MSG_ROUTING_KEY);
    }
}
