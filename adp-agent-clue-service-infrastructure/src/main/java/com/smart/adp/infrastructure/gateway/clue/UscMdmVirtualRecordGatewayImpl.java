package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryOrderBy;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.bo.clue.UscMdmVirtualRecordBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.entity.clue.UscMdmVirtualRecordEntity;
import com.smart.adp.domain.gateway.clue.UscMdmVirtualRecordGateway;
import com.smart.adp.infrastructure.repository.clue.UscMdmVirtualRecordMapper;
import com.smart.adp.infrastructure.utils.PageConverterFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.entity.clue.table.UscMdmVirtualRecordEntityTableDef.USC_MDM_VIRTUAL_RECORD_ENTITY;

/**
 * @Description: 虚拟外呼gateway实现
 * @Author: rik.ren
 * @Date: 2025/3/15 16:17
 **/
@Service
public class UscMdmVirtualRecordGatewayImpl implements UscMdmVirtualRecordGateway {
    @Autowired
    private UscMdmVirtualRecordMapper virtualRecordMapper;

    /**
     * 更新虚拟外呼内容
     *
     * @param param
     * @return
     */
    @Override
    public Boolean updateVirtualRecord(UscMdmVirtualRecordEntity param) {
        // 更新的条件
        QueryCondition condition = QueryCondition.create(new QueryColumn("RECORD_ID"), SqlConsts.EQUALS, param.getRecordId());
        // 更新的字段就是param中的不为空的字段
        return virtualRecordMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 查询虚拟外呼
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns 尽量提供需要的字段
     * @return
     */
    @Override
    public DomainPage<UscMdmVirtualRecordEntity> queryVirtualRecord(UscMdmVirtualRecordEntity param,
                                                                    UscMdmVirtualRecordBO boParam,
                                                                    QueryOrderBy orderBy, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{USC_MDM_VIRTUAL_RECORD_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(USC_MDM_VIRTUAL_RECORD_ENTITY.as("record"));
        param.conditions(wrapper, boParam);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(USC_MDM_VIRTUAL_RECORD_ENTITY.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        Page<UscMdmVirtualRecordEntity> paginate = virtualRecordMapper.paginate(boParam.getPageObj().getPageNumber(),
                boParam.getPageObj().getPageSize(), wrapper);
        DomainPage<UscMdmVirtualRecordEntity> domainPage = PageConverterFactory.toDomainPage(paginate);
        return domainPage;
    }

    /**
     * 查询虚拟外呼
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns 尽量提供需要的字段
     * @return
     */
    @Override
    public List<UscMdmVirtualRecordEntity> queryVirtualRecordList(UscMdmVirtualRecordEntity param,
                                                                  UscMdmVirtualRecordBO boParam, QueryOrderBy orderBy,
                                                                  QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{USC_MDM_VIRTUAL_RECORD_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(USC_MDM_VIRTUAL_RECORD_ENTITY.as("record"));
        param.conditions(wrapper, boParam);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(USC_MDM_VIRTUAL_RECORD_ENTITY.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        return virtualRecordMapper.selectListByQuery(wrapper);
    }
}
