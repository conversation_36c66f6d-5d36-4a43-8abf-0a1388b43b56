package com.smart.adp.interfaces.facade;

import com.smart.adp.application.service.base.WorkbenchApplicationService;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.domain.enums.StatisticsTypeEnum;
import com.smart.adp.domain.valueObject.base.StatisticsVO;
import com.smart.adp.infrastructure.feign.request.SummaryDTO;
import com.smart.adp.infrastructure.feign.response.SummaryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/11
 */
@Slf4j
@RestController
@Validated
@CrossOrigin(origins = "*")
@Tag(name = "工作台", description = "工作台")
@RequestMapping("/api/agent/base")
public class WorkbenchFacade {

    @Autowired
    private WorkbenchApplicationService workbenchApplicationService;

    @Operation(summary = "工作台指标", description = "工作台指标")
    @GetMapping(value = "/statistics")
    public RespBody<List<StatisticsVO>> agentDlrClueStatistics(@RequestParam(value = "types", required = false) Set<StatisticsTypeEnum> types) {
        return RespBody.ok(workbenchApplicationService.statistics(types));
    }

    @Operation(summary = "首页小结", description = "首页小结")
    @ApiResponse(responseCode = "200", description = "成功获取小结")
    @ApiResponse(responseCode = "429", description = "系统繁忙")
    @PostMapping(value = "/summary")
    public RespBody<SummaryVO> summary(@RequestBody SummaryDTO dto) {
        return workbenchApplicationService.summary(dto);
    }

    @Operation(summary = "小结列表", description = "小结列表")
    @ApiResponse(responseCode = "200", description = "成功获取小结")
    @ApiResponse(responseCode = "429", description = "系统繁忙")
    @PostMapping(value = "/summaryList")
    public RespBody<List<SummaryVO>> summaryList(@RequestBody SummaryDTO dto) {
        return workbenchApplicationService.summaryList(dto);
    }
}
