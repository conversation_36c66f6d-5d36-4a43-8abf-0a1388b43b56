package com.smart.adp.interfaces.rpcFacade;

import com.smart.adp.application.dto.clue.ClueDlrInfoQueryDTO;
import com.smart.adp.application.dto.clue.ClueEventFlowDTO;
import com.smart.adp.application.dto.clue.ClueRemarkDTO;
import com.smart.adp.application.dto.clue.ModifyIntentionCarDTO;
import com.smart.adp.application.service.clue.AgentClueApplicationService;
import com.smart.adp.application.service.clue.AgentClueProviderRpcApplicationService;
import com.smart.adp.application.vo.clue.ClueDlrRspVO;
import com.smart.adp.application.vo.clue.ClueEventFlowRspVO;
import com.smart.adp.application.vo.clue.SacOneCustRemarkRspVO;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.infrastructure.annotation.NoAuth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * date 2025/2/27 20:57
 * @description 代理商门店线索实现，提供给RPC服务使用
 **/
@Slf4j
@RestController
@Validated
@CrossOrigin(origins = "*")
@Tag(name = "经销商店端线索", description = "经销商店端线索")
@RequestMapping("/api/rpc/agent/clue")
public class AgentDlrClueProviderFacade {

    @Resource
    AgentClueProviderRpcApplicationService providerRpcApplicationService;

    @Resource
    AgentClueApplicationService agentClueApplicationService;

    @NoAuth
    @Operation(summary = "详情页用户旅程集合", description = "详情页用户旅程集合")
    @PostMapping(value = "/queryListUserEventFlow")
    public RespBody<List<ClueEventFlowRspVO>> queryListUserEventFlow(@RequestBody ClueEventFlowDTO dto) {
        return RespBody.ok(providerRpcApplicationService.queryListUserEventFlow(dto));
    }

    @Operation(summary = "获取线索扩展信息", description = "获取线索扩展信息")
    @PostMapping(value = "/queryClueRemark")
    public RespBody<List<SacOneCustRemarkRspVO>> queryClueRemark(@RequestBody ClueRemarkDTO dto) {
        return RespBody.ok(providerRpcApplicationService.queryClueRemark(dto));
    }

    @Operation(summary = "更新意向车信息", description = "更新意向车信息")
    @PostMapping(value = "/modifyIntentionCar")
    public RespBody<String> modifyIntentionCar(@RequestBody ModifyIntentionCarDTO dto) {
        return RespBody.ok(providerRpcApplicationService.modifyIntentionCarForStartDriving(dto));
    }

    @NoAuth
    @Operation(summary = "根据手机号码精确查询一条线索", description = "根据手机号码精确查询一条线索")
    @PostMapping(value = "/queryClueInfoDlr")
    public RespBody<ClueDlrRspVO> queryClueInfoDlr(@RequestBody @Validated ClueDlrInfoQueryDTO dto) {
        return RespBody.ok(agentClueApplicationService.queryClueInfoDlr(dto));
    }
}
