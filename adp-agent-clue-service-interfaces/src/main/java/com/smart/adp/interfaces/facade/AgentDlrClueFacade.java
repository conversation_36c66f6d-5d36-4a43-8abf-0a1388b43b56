package com.smart.adp.interfaces.facade;

import com.smart.adp.application.dto.clue.*;
import com.smart.adp.application.service.clue.AgentClueApplicationService;
import com.smart.adp.application.validate.ValidationAnnot;
import com.smart.adp.application.vo.clue.*;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.domain.context.TimeContext;
import com.smart.adp.domain.enums.ClueQueryTypeEnum;
import com.smart.adp.domain.valueObject.base.CompetitiveCarModelVO;
import com.smart.adp.domain.valueObject.clue.*;
import com.smart.adp.infrastructure.annotation.NoAuth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2025/2/27 20:57
 * @description 代理商门店线索实现
 **/
@Slf4j
@RestController
@Validated
@CrossOrigin(origins = "*")
@Tag(name = "经销商店端线索", description = "经销商店端线索")
@RequestMapping("/api/agent/clue")
public class AgentDlrClueFacade {

    @Resource
    AgentClueApplicationService agentClueApplicationService;

    @Operation(summary = "经销商线索保存", description = "经销商线索保存")
    @PostMapping(value = "/agentDlrClueSave")
    public RespBody<ClueDlrSaveVO> agentDlrClueSave(@RequestBody @Validated AgentClueDTO param) {
        try {
            ClueDlrSaveVO clueDlrSaveVO = agentClueApplicationService.agentDlrClueSave(param);
            return RespBody.ok(clueDlrSaveVO);
        } catch (Exception e) {
            log.info("经销商线索保存异常", e);
            return RespBody.fail(e.getMessage());
        }
    }

    @Operation(summary = "经销商线索保存前置校验", description = "经销商线索保存前置校验")
    @PostMapping(value = "/agentDlrClueCheck")
    public RespBody<ClueDlrCheckVO> agentDlrClueCheck(@RequestBody @Validated AgentClueCheckDTO param) {
        try {
            ClueDlrCheckVO clueDlrCheckVO = agentClueApplicationService.agentDlrClueCheck(param);
            return RespBody.ok(clueDlrCheckVO);
        } catch (Exception e) {
            log.error("经销商线索保存前置校验异常", e);
            return RespBody.fail("线索创建前置校验失败，请稍后重试！");
        }
    }

    @Operation(summary = "店端线索列表", description = "店端线索列表")
    @PostMapping(value = "/list")
    public RespBody<PageVO<ClueDlrListVO>> agentDlrClueList(@RequestBody @Validated ClueDlrListDTO dto) {
        PageVO<ClueDlrListVO> page = agentClueApplicationService.page(dto);
        return RespBody.ok(page);
    }

    @Operation(summary = "单个线索查询", description = "单个线索查询")
    @PostMapping(value = "/get/{clueId}")
    public RespBody<ClueDlrListVO> get(@PathVariable("clueId")
                                       @NotEmpty(message = "无效 ID")
                                       @Parameter(description = "线索 ID") String clueId,
                                       @RequestBody @Validated ClueDlrListDTO dto) {
        return RespBody.ok(agentClueApplicationService.get(clueId, dto));
    }

    @Operation(summary = "店端线索搜索", description = "店端线索搜索")
    @PostMapping(value = "/search")
    public RespBody<PageVO<ClueDlrSearchVO>> agentDlrClueSearch(@RequestBody @Validated ClueDlrSearchDTO dto) {
        PageVO<ClueDlrSearchVO> pageVO = agentClueApplicationService.search(dto);
        return RespBody.ok(pageVO);
    }

    @Operation(summary = "店端线索统计", description = "店端线索统计")
    @GetMapping(value = "/statistics")
    public RespBody<List<ClueDlrStatisticsVO>> agentDlrClueStatistics(@RequestParam(value = "types", required = false) Set<ClueQueryTypeEnum> types) {
        List<ClueDlrStatisticsVO> vos = agentClueApplicationService.statistics(types);
        return RespBody.ok(vos);
    }

    @Operation(summary = "逾期数量日历", description = "逾期数量日历")
    @GetMapping(value = "/overdueCountCalender")
    public RespBody<List<CalenderCountVO>> overdueCountCalender() {
        LocalDateTime startTime = TimeContext.now()
                .toLocalDate()
                .atStartOfDay();
        List<CalenderCountVO> vos = agentClueApplicationService.overdueCountCalender(startTime, startTime.plusMonths(2));
        return RespBody.ok(vos);
    }

    @Operation(summary = "店端线索详情", description = "店端线索详情")
    @PostMapping(value = "/detail")
    public RespBody<ClueDlrDetailRspVO> agentDlrClueDetail(@RequestBody @Validated ClueDlrDetailDTO dto) throws Exception {
        ClueDlrDetailRspVO detail = agentClueApplicationService.detailExecutor(dto);
        return RespBody.ok(detail);
    }

    @Operation(summary = "更新线索扩展信息", description = "更新线索扩展信息")
    @PostMapping(value = "/modifyDlrInfo")
    public RespBody<Boolean> modifyDlrInfo(@RequestBody @Validated @ValidationAnnot(message = "更新错误", checkMarkName =
            "modifyDlrInfoCheck") ClueDlrModifyDTO dto) {
        Boolean detail = agentClueApplicationService.modifyDlrInfo(dto);
        return RespBody.ok(detail);
    }

    @Operation(summary = "活跃的信息", description = "活跃的信息")
    @PostMapping(value = "/queryActiveInfo")
    public RespBody<ClueActiveInfoRspVO> queryActiveInfo(@RequestBody @Validated ClueActiveInfoDTO dto) {
        ClueActiveInfoRspVO result = agentClueApplicationService.queryActiveInfo(dto);
        return RespBody.ok(result);
    }

    @Operation(summary = "获取竞品车型列表", description = "获取竞品车型列表")
    @GetMapping(value = "/carList")
    public RespBody<List<CompetitiveCarModelVO>> competitiveCarList() {
        return RespBody.ok(agentClueApplicationService.competitiveCarList());
    }

    @Operation(summary = "代理商线索来源列表", description = "代理商线索来源列表")
    @GetMapping(value = "/agentSourceList")
    public RespBody<List<AgentClueSourceVO>> agentClueSourceList() {
        return RespBody.ok(agentClueApplicationService.agentClueSourceList());
    }

    @Operation(summary = "marketing推送语音摘要信息", description = "marketing推送语音摘要信息")
    @PostMapping(value = "/saveAbstractContent")
    @NoAuth
    public RespBody<Boolean> saveAbstractContent(@RequestBody AbstractContentDTO dto) {
        return RespBody.ok(agentClueApplicationService.saveAbstractContent(dto));
    }

    @Operation(summary = "查询互动数据", description = "查询互动数据")
    @PostMapping(value = "/queryInteractData")
    public RespBody<DomainPage<ClueInteractRspVO>> queryInteractData(@RequestBody @Validated InteractDataDTO dto) throws Exception {
        return RespBody.ok(agentClueApplicationService.queryInteractDataMain(dto));
    }

    @Operation(summary = "详情页用户旅程", description = "详情页用户旅程")
    @PostMapping(value = "/queryUserEventFlow")
    public RespBody<List<ClueEventFlowRspVO>> queryUserEventFlow(@RequestBody @Validated ClueEventFlowDTO dto) {
        return RespBody.ok(agentClueApplicationService.queryUserEventFlow(dto));
    }

    @Operation(summary = "跟进页面中获取最后一次跟进记录内容", description = "跟进页面中获取最后一次跟进记录内容")
    @PostMapping(value = "/queryReviewInfo")
    public RespBody<SacReviewRspVO> queryReviewInfo(@RequestBody @Validated ClueReviewDTO dto) {
        return RespBody.ok(agentClueApplicationService.queryReviewInfo(dto));
    }

    @Operation(summary = "根据手机号码精确查询一条线索", description = "根据手机号码精确查询一条线索")
    @PostMapping(value = "/queryClueInfoDlr")
    public RespBody<ClueDlrRspVO> queryClueInfoDlr(@RequestBody @Validated ClueDlrInfoQueryDTO dto) {
        return RespBody.ok(agentClueApplicationService.queryClueInfoDlr(dto));
    }

    @Operation(summary = "旅程流水修复", description = "旅程流水修复")
    @PostMapping("/flowFix")
    public RespBody<Integer> flowFix(@RequestBody UserJourneysFixDTO dto) {
        return RespBody.ok(agentClueApplicationService.userJourneysFlowFix(dto));
    }

    @Operation(summary = "线索阶段修复", description = "线索阶段修复")
    @PostMapping("/stageFix")
    public RespBody<Integer> stageFix(@RequestBody UserJourneysFixDTO dto) {
        return RespBody.ok(agentClueApplicationService.userJourneysStageFix(dto));
    }

    @Operation(summary = "ES 数据修复", description = "ES 数据修复")
    @PostMapping("/esFix")
    public RespBody<Integer> esFix(@RequestBody UserJourneysFixDTO dto) {
        return RespBody.ok(agentClueApplicationService.esFix(dto));
    }

    @Operation(summary = "线索 ES 初始化", description = "线索 ES 初始化")
    @PostMapping("/initClueES")
    public RespBody<Void> initClueES(@RequestBody UserJourneysFixDTO dto) {
        agentClueApplicationService.initClueES(dto);
        return RespBody.ok();
    }

    @Operation(summary = "履历 ES 初始化", description = "履历 ES 初始化")
    @PostMapping("/initResumeES")
    public RespBody<Void> initResumeES(@RequestBody UserJourneysFixDTO dto) {
        agentClueApplicationService.initResumeES(dto);
        return RespBody.ok();
    }

    @Operation(summary = "查询专家分组和线索所在分组", description = "查询专家分组和线索所在分组")
    @PostMapping("/queryClueGroup")
    public RespBody<List<ClueUserGroupVO>> queryClueGroup(@RequestBody QueryClueGroupDTO dto) {
        return RespBody.ok(agentClueApplicationService.queryUserGroupByCreate(dto));
    }

    @Operation(summary = "经销商线索校验潜客信息是否存在", description = "经销商线索校验潜客信息是否存在")
    @PostMapping(value = "/agentDlrCustCheck")
    public RespBody<ClueDlrCheckVO> agentDlrCustCheck(@RequestBody @Validated AgentClueCheckDTO param) {
        try {
            ClueDlrCheckVO clueDlrCheckVO = agentClueApplicationService.agentDlrCustCheck(param);
            return RespBody.ok(clueDlrCheckVO);
        } catch (Exception e) {
            log.error("经销商线索校验潜客信息是否存在异常", e);
            return RespBody.fail("线索创建前置校验失败，请稍后重试！");
        }
    }

    @Operation(summary = "店端线索分配", description = "店端线索分配【店长分配】")
    @PostMapping(value = "/agentDlrDistribute")
    public RespBody<List<ClueDistributeVO>> agentDlrDistribute(@RequestBody @Validated AgentClueDisTributeDTO param) {
        try {
            List<ClueDistributeVO> clueDistributeVOList = agentClueApplicationService.agentDlrDistribute(param);
            return RespBody.ok(clueDistributeVOList);
        } catch (Exception e) {
            log.error("店端线索分配【店长分配】异常", e);
            return RespBody.fail("线索分配失败，请稍后重试！");
        }
    }

    @Operation(summary = "修改线索客户信息", description = "修改线索客户信息")
    @PostMapping(value = "/modifyClueCustInfo")
    public RespBody<Boolean> modifyClueCustInfo(@RequestBody @Validated ModifyClueCustDTO param) {
        try {
            Boolean modifyFlag = agentClueApplicationService.modifyClueCustInfo(param);
            return RespBody.ok(modifyFlag);
        } catch (Exception e) {
            log.error("修改线索客户信息异常", e);
            return RespBody.fail("客户信息修改失败，请稍后重试！");
        }
    }

    @Operation(summary = "线索提示", description = "线索提示")
    @GetMapping(value = "/tips")
    public RespBody<ClueTipsVO> agentDlrClueTips(@RequestParam(value = "type") ClueQueryTypeEnum type) {
        return RespBody.ok(agentClueApplicationService.getTips(type));
    }
}
