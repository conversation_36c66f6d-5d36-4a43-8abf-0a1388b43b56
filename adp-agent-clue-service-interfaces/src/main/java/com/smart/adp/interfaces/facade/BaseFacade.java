package com.smart.adp.interfaces.facade;

import com.smart.adp.application.dto.base.DlrEmpDTO;
import com.smart.adp.application.dto.base.QueryLookUpInfoDTO;
import com.smart.adp.application.dto.base.QueryUserGroupDTO;
import com.smart.adp.application.dto.base.RemoveDTO;
import com.smart.adp.application.service.base.BaseApplicationService;
import com.smart.adp.application.vo.base.UserGroupVO;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.domain.valueObject.base.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 用户
 * @Author: rik.ren
 * @Date: 2025/3/9 17:19
 **/
@Slf4j
@RestController
@Validated
@CrossOrigin(origins = "*")
@Tag(name = "基础", description = "基础")
@RequestMapping("/api/agent/base")
public class BaseFacade {

    @Resource
    BaseApplicationService baseApplicationService;

    @Operation(summary = "查询用户分组信息-用户标签等", description = "查询用户分组信息")
    @PostMapping(value = "/queryUserGroup")
    public RespBody<List<UserGroupVO>> queryUserGroup(@RequestBody @Validated QueryUserGroupDTO param) {
        List<UserGroupVO> result = baseApplicationService.queryUserGroupByCreate(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "findLookUpInfo", description = "查询数据字典")
    @PostMapping(value = "/findLookUpInfo")
    public RespBody<List<LookUpInfo>> findLookUpInfo(@RequestBody @Validated QueryLookUpInfoDTO param) {
        List<LookUpInfo> result = baseApplicationService.findLookUpInfo(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "获取车型信息配置信息如#1，#3，#5", description = "获取车型信息配置信息如#1，#3，#5")
    @GetMapping(value = "/queryCarTypeList")
    public RespBody<List<CarTypeInfoVO>> queryCarTypeList() {
        List<CarTypeInfoVO> result = baseApplicationService.queryCarTypeList();
        return RespBody.ok(result);
    }

    @Operation(summary = "二三级渠道信息", description = "二三级渠道信息")
    @GetMapping("/channelInfo")
    public RespBody<List<ChannelInfoVO>> channelInfo() {
        return RespBody.ok(baseApplicationService.channelInfo());
    }

    @Operation(summary = "获取当前门店下的产品专家", description = "(0508)获取当前门店下的产品专家")
    @PostMapping(value = "/queryDlrEmpList")
    public RespBody<List<AgentDlrEmpVO>> queryDlrEmpList(@RequestBody @Validated DlrEmpDTO dlrEmpDTO) {
        List<AgentDlrEmpVO> agentDlrEmpVOS = baseApplicationService.queryDlrEmpList(dlrEmpDTO);
        return RespBody.ok(agentDlrEmpVOS);
    }

    @Operation(summary = "findCacheLookUpInfo", description = "(0508)查询缓存中数据字典")
    @PostMapping(value = "/findCacheLookUpInfo")
    public RespBody<List<LookUpInfoVO>> findCacheLookUpInfo(@RequestBody @Validated QueryLookUpInfoDTO param) {
        List<LookUpInfoVO> result = baseApplicationService.findCacheLookUpInfo(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "dealCacheInfo", description = "处理缓存信息")
    @PostMapping(value = "/dealCacheInfo")
    public RespBody<Boolean> dealCacheInfo(@RequestBody @Validated RemoveDTO param) {
        Boolean result = baseApplicationService.dealCacheInfo(param);
        return RespBody.ok(result);
    }
}
