package com.smart.adp.interfaces.facade;

import com.smart.adp.application.dto.task.TaskPageDTO;
import com.smart.adp.application.service.task.TaskApplicationService;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.domain.valueObject.task.TaskTipsVO;
import com.smart.adp.domain.valueObject.task.TaskVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/5
 */
@Slf4j
@RestController
@Validated
@CrossOrigin(origins = "*")
@Tag(name = "任务", description = "任务")
@RequestMapping("/api/agent/task")
public class TaskFacade {

    @Autowired
    private TaskApplicationService taskApplicationService;

    @Operation(summary = "任务列表", description = "任务列表")
    @PostMapping(value = "")
    public RespBody<PageVO<TaskVO>> page(@RequestBody @Validated TaskPageDTO dto) {
        return RespBody.ok(taskApplicationService.page(dto));
    }

    @Operation(summary = "任务提示", description = "任务提示")
    @GetMapping(value = "/tips")
    public RespBody<TaskTipsVO> tips(@RequestParam("userIds") List<String> userIds) {
        return RespBody.ok(taskApplicationService.tips(userIds));
    }

    @Operation(summary = "任务详情", description = "任务详情")
    @GetMapping(value = "/{id}")
    public RespBody<TaskVO> detail(@PathVariable("id") @Min(value = 1, message = "id 最小为 1") Long id) {
        return RespBody.ok(taskApplicationService.detail(id));
    }
}
