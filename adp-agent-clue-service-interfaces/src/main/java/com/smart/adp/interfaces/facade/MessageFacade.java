package com.smart.adp.interfaces.facade;

import com.smart.adp.application.dto.clue.MessageUnReadCountDTO;
import com.smart.adp.application.dto.clue.MessageQueryDTO;
import com.smart.adp.application.dto.clue.MessageReadFlagDTO;
import com.smart.adp.application.service.clue.AgentMsgApplicationService;
import com.smart.adp.application.vo.clue.ClueMsgRecordRspVO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.resp.RespBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Description: 用户
 * @Author: rik.ren
 * @Date: 2025/3/9 17:19
 **/
@Slf4j
@RestController
@Validated
@CrossOrigin(origins = "*")
@Tag(name = "消息", description = "消息")
@RequestMapping("/api/agent/msg")
public class MessageFacade {

    @Resource
    AgentMsgApplicationService agentClueApplicationService;

    @Operation(summary = "查询用户的消息", description = "查询用户的消息")
    @PostMapping(value = "/queryEmpMsg")
    public RespBody<DomainPage<ClueMsgRecordRspVO>> queryEmpMsg(@RequestBody @Validated MessageQueryDTO param) {
        DomainPage<ClueMsgRecordRspVO> result = agentClueApplicationService.queryMsg(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "查询用户未读消息总数", description = "查询用户未读消息总数")
    @PostMapping(value = "/queryEmpMsgUnReadCount")
    public RespBody<Long> queryEmpMsgUnReadCount(@RequestBody @Validated MessageUnReadCountDTO param) {
        Long result = agentClueApplicationService.queryCountMsg(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "标记消息已读", description = "标记消息已读")
    @PostMapping(value = "/markEmpMsgRead")
    public RespBody<Boolean> markEmpMsgRead(@RequestBody @Validated MessageReadFlagDTO param) {
        Boolean result = agentClueApplicationService.markEmpMsgRead(param);
        return RespBody.ok("已标记全部已读", result);
    }
}
