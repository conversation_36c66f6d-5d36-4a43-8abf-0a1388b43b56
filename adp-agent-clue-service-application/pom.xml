<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.smart.adp</groupId>
        <artifactId>adp-agent-clue-service-parent</artifactId>
        <version>1.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>adp-agent-clue-service-application</artifactId>
    <packaging>jar</packaging>
    <name>adp-agent-clue-service-application</name>
    <properties>
        <maven.install.skip>true</maven.install.skip>
    </properties>
    <dependencies>

        <dependency>
            <groupId>com.smart.adp</groupId>
            <artifactId>adp-agent-clue-service-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.smart.adp</groupId>
            <artifactId>adp-agent-clue-service-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.smart</groupId>
            <artifactId>smart-elf-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
    </dependencies>

</project>
