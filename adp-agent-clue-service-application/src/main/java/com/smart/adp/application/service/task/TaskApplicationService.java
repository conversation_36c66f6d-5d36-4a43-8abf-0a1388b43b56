package com.smart.adp.application.service.task;

import com.smart.adp.application.dto.task.TaskPageDTO;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.valueObject.task.TaskTipsVO;
import com.smart.adp.domain.valueObject.task.TaskVO;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/6
 */
public interface TaskApplicationService {

    PageVO<TaskVO> page(TaskPageDTO dto);

    TaskTipsVO tips(List<String> userIds);

    TaskVO detail(Long id);
}
