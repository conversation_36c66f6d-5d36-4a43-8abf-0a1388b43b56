package com.smart.adp.application.dto.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/14 17:27
 * @description 线索分配DTO
 **/
@Data
@Schema(description = "店端线索店长线索分配DTO")
public class AgentClueDisTributeDTO implements Serializable {

    @Schema(description = "回访人员id，产品专家userId")
    @NotBlank(message = "回访人员Id不能为空")
    private String reviewPersonId;

    @Schema(description = "回访人员名称")
    private String reviewPersonName;

    @Schema(description = "回访人员手机号")
    private String reviewPersonPhone;

    @Schema(description = "回访任务集合")
    private List<AgentReviewDisTributeDTO> reviewDisTributeDTOList;

    @Schema(description = "门店code")
    private String dlrCode;

    @Schema(description = "复合场景编码为必填，0:标记已读，1：试乘试驾 2：线索分配 3:预约试驾 4:任务 5：通知 6：异常关单 8：战败驳回 13：预约取消")
    private String scenario = "2";

    @Schema(description = "复合场景编码为必填，1:试乘试驾，2：线索待跟进 3：dlrCode 【专营店编码为必填】")
    private String messageType = "2";
}
