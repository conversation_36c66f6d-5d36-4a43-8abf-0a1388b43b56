package com.smart.adp.application.service.clue;

import com.smart.adp.application.dto.clue.*;
import com.smart.adp.application.vo.clue.*;
import com.smart.adp.domain.common.DomainPage;

/**
 * <AUTHOR>
 * date 2025/3/3 10:16
 * @description 店端线索application 服务接口
 **/
public interface AgentMsgApplicationService {

    /**
     * 查询符合条件的消息列表
     *
     * @param param
     * @return
     */
    DomainPage<ClueMsgRecordRspVO> queryMsg(MessageQueryDTO param);

    /**
     * 查询符合条件的消息个数
     *
     * @param param
     * @return
     */
    Long queryCountMsg(MessageUnReadCountDTO param);

    /**
     * 标记消息已读
     *
     * @param param
     * @return
     */
    Boolean markEmpMsgRead(MessageReadFlagDTO param);
}
