package com.smart.adp.application.dto.clue;

import com.smart.adp.application.common.PageDTO;
import com.smart.adp.domain.enums.ClueSearchTypeEnum;
import com.smart.adp.domain.qry.ClueSearchQry;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/10
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
@Schema(description = "线索搜索入参")
public class ClueDlrSearchDTO extends PageDTO {

    /**
     * 搜索类型
     */
    @NotNull(message = "搜索类型不能为空")
    @Schema(description = "搜索类型", required = true)
    private ClueSearchTypeEnum qryType;

    /**
     * 线索类型
     */
    @Schema(description = "线索类型")
    private ClueSearchTypeEnum.ClueStatus clueStatus;

    /**
     * 搜索内容
     */
    @NotBlank(message = "搜索内容不能为空")
    @Schema(description = "搜索内容", required = true)
    private String searchContent;

    /**
     * search after
     */
    @Schema(description = "分页参数")
    private Object[] searchAfter;

    public ClueSearchQry buildQry() {
        ClueSearchQry qry = new ClueSearchQry();
        qry.setPageIndex(getPageIndex());
        qry.setPageSize(getPageSize());
        qry.setQryType(getQryType());
        qry.setClueStatus(getClueStatus());
        qry.setSearchContent(getSearchContent());
        qry.setSearchAfter(getSearchAfter());
        return qry;
    }
}
