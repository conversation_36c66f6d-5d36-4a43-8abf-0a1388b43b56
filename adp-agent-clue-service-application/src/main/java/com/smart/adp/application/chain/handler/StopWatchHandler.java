package com.smart.adp.application.chain.handler;

import com.smart.adp.application.chain.context.BizContext;
import org.springframework.util.StopWatch;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/30
 */
public abstract class StopWatchHandler<C extends BizContext> extends AbstractHandler<C> {

    @Override
    public void handle(C ctx) {
        StopWatch stopWatch = ctx.getStopWatch();
        String simpleName = this.getClass().getSimpleName();
        stopWatch.start(simpleName);

        try {
            doHandle(ctx);
        } finally {
            stopWatch.stop();
        }

        if (Objects.nonNull(next)) {
            next.handle(ctx);
        }
    }
}
