package com.smart.adp.application.dto.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/14 17:27
 * @description 线索分配DTO
 **/
@Data
@Schema(description = "店端线索店长线索分配DTO")
public class AgentReviewDisTributeDTO implements Serializable {

    @Schema(description = "回访任务id")
    private String reviewId;

    @Schema(description = "线索移交label的code")
    private String manageLabelCode;

    @Schema(description = "是否战败分配，默认为非战败分配，战败分配传ture(老版本isFlag=2 企微端战败分配，isFlag=3 pc端战败分配，非战败不传)")
    private Boolean isDefeatedDis = Boolean.FALSE;
}
