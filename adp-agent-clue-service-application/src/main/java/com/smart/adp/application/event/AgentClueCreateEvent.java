package com.smart.adp.application.event;

import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * date 2025/3/11 15:49
 * @description 门店线索自建event
 **/
@Getter
@ToString
public class AgentClueCreateEvent extends ApplicationEvent {

    private AgentClueSaveDTO agentClueSaveDTO;

    public AgentClueCreateEvent(Object source, AgentClueSaveDTO param) {
        super(source);
        this.agentClueSaveDTO = param;
    }
}
