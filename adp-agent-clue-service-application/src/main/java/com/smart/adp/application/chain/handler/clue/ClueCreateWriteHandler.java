package com.smart.adp.application.chain.handler.clue;

import com.smart.adp.application.chain.context.ClueCreateContext;
import com.smart.adp.application.chain.handler.StopWatchHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/1
 */
@Slf4j
@Component
public class ClueCreateWriteHandler extends StopWatchHandler<ClueCreateContext> {

    /**
     * override #handle use aop
     *
     * @param ctx context
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handle(ClueCreateContext ctx) {
        super.handle(ctx);
    }

    @Override
    protected void doHandle(ClueCreateContext ctx) {

    }
}
