package com.smart.adp.application.dto.clue;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.domain.bo.clue.SacClueMsgRecordBO;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.enums.ClueMsgReadEnum;
import com.smart.adp.domain.enums.MessageTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 消息查询总数入参
 * @Author: rik.ren
 * @Date: 2025/4/15 13:26
 **/
@Data
@Schema(description = "消息查询总数入参")
public class MessageUnReadCountDTO {

    @Schema(description = "门店", required = false)
    private String dlrCode;

    @Schema(description = "消息类型", required = false)
    private List<String> listMessageType;

    @Schema(description = "开始时间", required = false)
    private LocalDateTime startTime;

    @Schema(description = "截止时间", required = false)
    private LocalDateTime endTime;

    @Schema(description = "员工empId", required = false, hidden = true)
    private String empId;

    @Schema(description = "已读未读标记，0未读，1已读")
    private String readFlag;

    // 定义静态常量，JVM加载类时初始化
    private static final List<String> DEFAULT_QUERY_MESSAGE_TYPE = MessageTypeEnum.getAllCodes();

    public SacClueMsgRecordBO buildClueMsgUnReadRecordBO() {
        SacClueMsgRecordBO bo = new SacClueMsgRecordBO();
        bo.setDlrCode(getDlrCode());
        bo.setCreatedDateBegin(getStartTime());
        bo.setCreatedDateEnd(getEndTime());
        bo.setListMessageType(getListMessageType());
        bo.setIsRead(ClueMsgReadEnum.UN_READ.getCode());
        bo.setReceiveEmpId(getEmpId());
        return bo;
    }

    public String getEmpId() {
        if (StringUtils.isEmpty(this.empId)) {
            this.empId = UserInfoContext.get().getEmpID();
        }
        return empId;
    }

    public void setEmpId(String empId) {
        if (StringUtils.isEmpty(this.empId)) {
            UserBusiEntity userInfo = UserInfoContext.get();
            this.empId = userInfo.getEmpID();
        }
    }


    public List<String> getListMessageType() {
        if (CollectionUtil.isEmpty(listMessageType)) {
            listMessageType = DEFAULT_QUERY_MESSAGE_TYPE;
        }
        return listMessageType;
    }
}
