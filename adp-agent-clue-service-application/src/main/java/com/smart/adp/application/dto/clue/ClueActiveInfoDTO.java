package com.smart.adp.application.dto.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * @Description: 线索活跃时间信息
 * @Author: rik.ren
 * @Date: 2025/3/11 14:05
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "线索活跃参数")
public class ClueActiveInfoDTO {
    /**
     * 线索 ID
     */
    @Schema(description = "线索 ID", required = true)
    @NotEmpty(message = "线索 ID 不能为空")
    private String custId;
    /**
     * 线索手机号
     */
    @Schema(description = "线索手机号", required = true)
    @NotEmpty(message = "线索手机号不能为空")
    private String phone;
}
