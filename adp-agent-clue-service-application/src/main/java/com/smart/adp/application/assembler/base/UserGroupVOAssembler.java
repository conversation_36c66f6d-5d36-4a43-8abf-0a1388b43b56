package com.smart.adp.application.assembler.base;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.vo.base.UserGroupVO;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;

import java.util.ArrayList;
import java.util.List;

public class UserGroupVOAssembler implements Assembler<List<SacUserGroupEntity>, List<UserGroupVO>> {

    /**
     * 装配 请实现装配过程
     *
     * @param source 源
     * @param target 目标
     */
    @Override
    public List<UserGroupVO> assemble(List<SacUserGroupEntity> source, Class<List<UserGroupVO>> target) {
        if (CollectionUtil.isEmpty(source)) {
            return null;
        }
        List<UserGroupVO> resultList = new ArrayList<>();
        source.forEach(item -> {
            UserGroupVO userGroupVO = new UserGroupVO();
            userGroupVO.setUserGroupId(item.getUserGroupId());
            userGroupVO.setUserGroupName(item.getUserGroupName());
            resultList.add(userGroupVO);
        });
        return resultList;
    }
}