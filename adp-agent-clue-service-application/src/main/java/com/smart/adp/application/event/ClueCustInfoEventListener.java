package com.smart.adp.application.event;

import cn.hutool.core.util.StrUtil;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import com.smart.adp.domain.gateway.clue.OneCustInfoGateway;
import com.smart.adp.domain.gateway.clue.OneCustRemarkGateway;
import com.smart.tools.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;


/**
 * <AUTHOR>
 * date 2025/3/10 22:41
 * @description 线索创建潜客信息维护
 **/
@Slf4j
@Component
public class ClueCustInfoEventListener implements ApplicationListener<ClueCustInfoEvent> {

    @Autowired
    private OneCustInfoGateway oneCustInfoGateway;

    @Autowired
    private OneCustRemarkGateway oneCustRemarkGateway;

    @Override
    @Async("clueExecutor")
    public void onApplicationEvent(ClueCustInfoEvent event) {
        if (event == null) {
            log.warn("线索创建潜客信息维护异步事件监听，event is null");
            return;
        }

        AgentClueSaveDTO dto = event.getAgentClueSaveDTO();
        if (dto == null) {
            log.warn("线索创建潜客信息维护异步事件监听，agentClueSaveDTO is null in event.");
            return;
        }
        //保存潜客信息
        if (!"false".equals(dto.getGooFlag())) {
            String custId = null;
            SacOnecustInfoEntity sacOnecustInfo = BeanUtils.copyProperties(dto, SacOnecustInfoEntity.class);
            SacOnecustInfoEntity sacOnecustInfoEntity = oneCustInfoGateway.findOneByPhone(dto.getPhone());
            if (Objects.isNull(sacOnecustInfoEntity)) {
                if (StrUtil.isNotBlank(dto.getCustId())) {
                    custId = dto.getCustId();
                } else {
                    custId = UUID.randomUUID().toString();
                }
                log.info("维护客户信息开始,phone={},custId={}", dto.getPhone(), custId);
                sacOnecustInfo.setCustId(custId);
                sacOnecustInfo.setColumn1(dto.getActivityId());
                oneCustInfoGateway.saveOnecust(sacOnecustInfo);
            } else {
                custId = sacOnecustInfoEntity.getCustId();
                SacOnecustInfoEntity param = new SacOnecustInfoEntity();
                param.setCustName(sacOnecustInfo.getCustName());
                param.setCustId(custId);
                param.setGenderCode(sacOnecustInfo.getGenderCode());
                param.setGenderName(sacOnecustInfo.getGenderName());
                param.setOemId(sacOnecustInfo.getOemId());
                param.setGroupCode(sacOnecustInfo.getGroupCode());
                param.setGroupId(sacOnecustInfo.getGroupId());
                param.setLastUpdatedDate(LocalDateTime.now());
                param.setModifier(sacOnecustInfo.getModifier());
                param.setModifyName(sacOnecustInfo.getModifyName());
                param.setColumn1(dto.getActivityId());
                oneCustInfoGateway.modifyOnecust(param);
            }

            //客户扩展信息维护
            if (StrUtil.isNotBlank(custId)) {
                log.info("维护客户扩展信息开始,phone={},custId={},clueLevel={},agentResource={},competitorModels={}", dto.getPhone(), custId, dto.getClueLevel(), dto.getAgentResource(), dto.getCompetitorModels());
                //维护备注信息
                SacOneCustRemark sacOneCustRemark = new SacOneCustRemark();
                sacOneCustRemark.setCustId(custId);
                sacOneCustRemark.setClueLevel(dto.getClueLevel());
                sacOneCustRemark.setLocation(dto.getLocation());
                sacOneCustRemark.setCompetitiveVehicleCode(dto.getCompetitorModels());
                if (StrUtil.isNotBlank(dto.getIntenCarTypeCode())) {
                    sacOneCustRemark.setIntentVehicleCode(dto.getIntenCarTypeCode().substring(0, 2));
                }
                sacOneCustRemark.setClueSource(dto.getAgentResource());
                if (Objects.nonNull(oneCustRemarkGateway.findByCustId(custId))) {
                    sacOneCustRemark.setUpdateDate(LocalDateTime.now());
                    oneCustRemarkGateway.modifyOneCustRemark(sacOneCustRemark);
                } else {
                    sacOneCustRemark.setCreateDate(LocalDateTime.now());
                    sacOneCustRemark.setUpdateDate(LocalDateTime.now());
                    oneCustRemarkGateway.saveOneCustRemark(sacOneCustRemark);
                }
            }
        } else {
            //当前部分 排查发现历史代码量逻辑存在问题

        }
    }
}
