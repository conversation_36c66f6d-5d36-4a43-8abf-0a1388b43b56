package com.smart.adp.application.dto.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 查询字典数据
 * @Author: rik.ren
 * @Date: 2025/3/9 17:28
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryLookUpInfoDTO implements Serializable {
    /**
     * 类型值编码
     */
    @Schema(description = "类型值编码")
    private String lookUpTypeCode;
    /**
     * 类型值
     */
    @Schema(description = "类型值")
    private String lookUpValueCode;

}
