package com.smart.adp.application.event;

import com.smart.adp.application.dto.clue.ReviewDisTributeDTO;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/20 11:21
 * @description 分配日志时间
 **/
@Getter
@ToString
public class ClueDisTributeLogEvent extends ApplicationEvent {

    private List<ReviewDisTributeDTO> disTributeDTOList;

    List<SacClueInfoDlr> querySacClueInfoDlrList;

    List<SacAllClueInfoDlr> querySacAllClueInfoDlrList;

    public ClueDisTributeLogEvent(Object source, List<ReviewDisTributeDTO> disTributeDTOList, List<SacClueInfoDlr> querySacClueInfoDlrList, List<SacAllClueInfoDlr> querySacAllClueInfoDlrList) {
        super(source);
        this.disTributeDTOList = disTributeDTOList;
        this.querySacClueInfoDlrList = querySacClueInfoDlrList;
        this.querySacAllClueInfoDlrList = querySacAllClueInfoDlrList;
    }
}
