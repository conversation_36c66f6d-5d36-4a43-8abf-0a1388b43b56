package com.smart.adp.application.dto.clue;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.application.common.PageDTO;
import com.smart.adp.domain.bo.clue.SacClueMsgRecordBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 消息查询入参
 * @Author: rik.ren
 * @Date: 2025/4/15 13:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "消息查询入参")
public class MessageQueryDTO extends PageDTO {

    @Schema(description = "门店", required = false)
    private String dlrCode;

    @Schema(description = "消息类型", required = false)
    private List<String> listMessageType;

    @Schema(description = "开始时间", required = false)
    private LocalDateTime startTime;

    @Schema(description = "截止时间", required = false)
    private LocalDateTime endTime;

    @Schema(description = "员工empId", required = false, hidden = true)
    private String empId;

    @Schema(description = "已读未读标记，0未读，1已读")
    private String readFlag;

    // 定义静态常量，JVM加载类时初始化
    private static final String[] DEFAULT_QUERY_MESSAGE_TYPE = {
            "1", "2", "3", "7", "8", "9", "13", "14", "15", "16", "17", "18"
    };

    public SacClueMsgRecordBO buildSacClueMsgRecordBO() {
        SacClueMsgRecordBO bo = new SacClueMsgRecordBO();
        bo.setDlrCode(getDlrCode());
        bo.setCreatedDateBegin(startTime);
        bo.setCreatedDateEnd(endTime);
        bo.setListMessageType(getListMessageType());
        bo.setReceiveEmpId(getEmpId());
        bo.setIsRead(getIsRead());
        bo.setPage(new DomainPage<>(getPageIndex(), getPageSize()));
        return bo;
    }

    public String getEmpId() {
        if (StringUtils.isEmpty(this.empId)) {
            //专家只能看自己的消息
            this.empId = UserInfoContext.get().getEmpID();
        }
        return empId;
    }

    public void setEmpId(String empId) {
        if (StringUtils.isEmpty(this.empId)) {
            UserBusiEntity userInfo = UserInfoContext.get();
            this.empId = userInfo.getEmpID();
        }
    }

    public String getIsRead() {
        if (StringUtils.isEmpty(this.readFlag)) {
            //不传默认查全部消息
            return null;
        }
        return readFlag;
    }

    public List<String> getListMessageType() {
        if (CollectionUtil.isEmpty(listMessageType)) {
            listMessageType = Arrays.asList(DEFAULT_QUERY_MESSAGE_TYPE);
        }
        return listMessageType;
    }
}
