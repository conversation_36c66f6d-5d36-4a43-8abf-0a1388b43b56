package com.smart.adp.application.dto.clue;

import com.smart.adp.domain.bo.clue.UscMdmVirtualRecordBO;
import com.smart.adp.domain.bo.drive.SacTestDriveSheetBO;
import com.smart.adp.domain.enums.AbstractType;
import com.smart.adp.domain.valueObject.clue.SacTestRecordAbstractVO;
import com.smart.adp.domain.valueObject.clue.SacVirtualRecordAbstractVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 保存虚拟外呼内容
 * @Author: rik.ren
 * @Date: 2025/3/13 14:43
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "保存保存虚拟外呼内容")
public class AbstractContentDTO implements Serializable {
    /**
     * 线索 custId
     */
    @Schema(description = "线索 custId", required = true)
    @NotEmpty(message = "线索custId不能为空")
    private String custId;
    /**
     * 线索手机号
     */
    @Schema(description = "线索手机号", required = false)
    private String phone;
    /**
     * 线索 ID
     */
    @Schema(description = "虚拟外呼内容", required = true)
    @NotEmpty(message = "虚拟外呼内容不能为空")
    private String content;
    /**
     * 员工id
     */
    @Schema(description = "员工id", required = true)
    @NotEmpty(message = "员工id不能为空")
    private String empId;
    /**
     * 业务单号
     *
     * @see AbstractType
     */
    @Schema(description = "业务单号，试驾时传的是test_drive_sheet_id，虚拟外呼传的是record_id", required = true)
    @NotEmpty(message = "业务单号不能为空")
    private String bizNo;
    /**
     * 摘要类型
     *
     * @see AbstractType
     */
    @Schema(description = "摘要类型 1虚拟外呼，2试驾录音", required = true)
    private Integer businessType;

    /**
     * 构建虚拟外呼的实体
     *
     * @return
     */
    public SacVirtualRecordAbstractVO buildVirtualRecordEntity() {
        return SacVirtualRecordAbstractVO.builder().custId(custId)
                .empId(empId)
                .recordId(bizNo)
                .abstractContent(content)
                .createdDate(LocalDateTime.now()).build();
    }

    /**
     * 构建试驾录音的实体
     *
     * @return
     */
    public SacTestRecordAbstractVO buildTestRecordEntity() {
        return SacTestRecordAbstractVO.builder().custId(custId)
                .empId(empId)
                .testDriveSheetId(bizNo)
                .abstractContent(content)
                .createdDate(LocalDateTime.now()).build();
    }

    /**
     * 构建虚拟外呼的BO
     *
     * @return
     */
    public UscMdmVirtualRecordBO buildVirtualRecordBO() {
        UscMdmVirtualRecordBO abstractBO = new UscMdmVirtualRecordBO();
        abstractBO.setRecordId(this.bizNo);
        abstractBO.setAbstractContent(this.content);
        abstractBO.setLastUpdatedDate(LocalDateTime.now());
        return abstractBO;
    }

    /**
     * 构建试驾录音的BO
     *
     * @return
     */
    public SacTestDriveSheetBO buildTestDriveSheetBO() {
        SacTestDriveSheetBO sheetBO = new SacTestDriveSheetBO();
        sheetBO.setTestDriveSheetId(this.bizNo);
        sheetBO.setLastUpdatedDate(LocalDateTime.now());
        sheetBO.setColumn10(this.content);
        return sheetBO;
    }
}
