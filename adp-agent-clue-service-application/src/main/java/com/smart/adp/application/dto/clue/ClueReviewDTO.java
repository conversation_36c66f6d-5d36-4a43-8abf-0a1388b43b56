package com.smart.adp.application.dto.clue;

import com.smart.adp.application.common.PageDTO;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * @Description: 查询线索回访记录
 * @Author: rik.ren
 * @Date: 2025/3/13 13:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "查询线索回访记录")
public class ClueReviewDTO extends PageDTO {

    /**
     * 线索 ID
     */
    @Schema(description = "线索id", required = false)
    private String reviewId;
    /**
     * 客户 ID
     */
    @Schema(description = "客户ID", required = true)
    @NotEmpty(message = "客户 ID 不能为空")
    private String custId;
    /**
     * 战败标签
     */
    @Schema(description = "战败标签，0正常，1战败", required = true)
    private Integer defeatFlag = 0;

    public SacReviewBO buildQuery() {
        SacReviewBO reviewBO = new SacReviewBO();
        reviewBO.setReviewId(reviewId);
        reviewBO.setCustId(custId);
        reviewBO.setDefeatFlag(defeatFlag);
        return reviewBO;
    }
}
