package com.smart.adp.application.constant;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/7
 */
public class TimeConstant {

    public static final int MS_IN_MINUTE = 60 * 1000;
    public static final long MS_IN_DAY = 24 * 60 * 60 * 1000;
    public static final int DAYS_IN_MONTH = 30;

    public static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter TIME_D_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter YEAR_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");
    public static final DateTimeFormatter TIME_PLAIN_D_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter TIME_PLAIN_M_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");
    public static final DateTimeFormatter STR_TIME_D_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter STR_TIME_6_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");
    public static final DateTimeFormatter STR_TIME_10_FORMATTER = DateTimeFormatter.ofPattern("yyMMddHHmm");
    public static final DateTimeFormatter H_M_S_STR_FORMATTER = DateTimeFormatter.ofPattern("HHmmss");
    public static final DateTimeFormatter H_M_S_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    public static final DateTimeFormatter H_M_S_PLAIN_FORMATTER = DateTimeFormatter.ofPattern("HHmmss");
    public static final DateTimeFormatter DATE_WITH_POINT_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM.dd");
    public static final DateTimeFormatter MSG_D_FORMATTER = DateTimeFormatter.ofPattern("yyyy/M/d HH:mm:ss");
    public static final DateTimeFormatter MILLISECOND_TIME_D_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    public static final ZoneId GMT_ZONE = ZoneId.of("GMT");

    public static final DateTimeFormatter DEFAULT_FORMATTER_ADD_S = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
}
