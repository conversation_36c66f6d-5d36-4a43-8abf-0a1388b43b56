package com.smart.adp.application.assembler.clue;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.vo.clue.ClueActiveInfoRspVO;
import com.smart.adp.domain.valueObject.clue.ClueActiveInfoVO;
import lombok.SneakyThrows;

public class ClueActiveVOAssembler implements Assembler<ClueActiveInfoVO, ClueActiveInfoRspVO> {

    /**
     * 装配 请实现装配过程
     *
     * @param source 源
     * @param target 目标
     */
    @SneakyThrows
    @Override
    public ClueActiveInfoRspVO assemble(ClueActiveInfoVO source, Class<ClueActiveInfoRspVO> target) {
        if (ObjectUtil.isEmpty(source)) {
            return null;
        }
        ClueActiveInfoRspVO resultVO = ClueActiveInfoRspVO.builder()
                .custId(source.getCustId())
                .phone(source.getPhone())
                .sevenDays(ObjectUtil.isEmpty(source.getActiveDays7d()) ? 0 : source.getActiveDays7d())
                .thirthDays(ObjectUtil.isEmpty(source.getActiveDays30d()) ? 0 : source.getActiveDays30d())
                .activeTimePeriod(source.getActiveTimePeriod())
                .build();
        return resultVO;
    }
}