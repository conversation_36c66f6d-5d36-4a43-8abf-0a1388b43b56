package com.smart.adp.application.assembler.clue;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.vo.clue.SacReviewRspVO;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import com.smart.adp.domain.context.Area;

/**
 * <AUTHOR>
 * date 2025/3/17 11:40
 * @description
 **/
public class SacReviewVOAssembler implements Assembler<SacReviewBO, SacReviewRspVO> {

    /**
     * 装配 请实现装配过程
     *
     * @param source 源
     * @param target 目标
     */
    @Override
    public SacReviewRspVO assemble(SacReviewBO source, Class<SacReviewRspVO> target) {
        return convert(source);
    }

    private SacReviewRspVO convert(SacReviewBO par) {
        if(ObjectUtil.isEmpty(par)) {
            return null;
        }
        SacReviewRspVO sacReviewRspVO = new SacReviewRspVO();
        sacReviewRspVO.setReviewId(par.getReviewId());
        sacReviewRspVO.setOrgCode(par.getOrgCode());
        sacReviewRspVO.setOrgName(par.getOrgName());
        sacReviewRspVO.setBillType(par.getBillType());
        sacReviewRspVO.setBillTypeName(par.getBillTypeName());
        sacReviewRspVO.setBusinessType(par.getBusinessType());
        sacReviewRspVO.setBusinessTypeName(par.getBusinessTypeName());
        sacReviewRspVO.setInfoChanMCode(par.getInfoChanMCode());
        sacReviewRspVO.setInfoChanMName(par.getInfoChanMName());
        sacReviewRspVO.setInfoChanDCode(par.getInfoChanDCode());
        sacReviewRspVO.setInfoChanDName(par.getInfoChanDName());
        sacReviewRspVO.setInfoChanDdCode(par.getInfoChanDdCode());
        sacReviewRspVO.setInfoChanDdName(par.getInfoChanDdName());
        sacReviewRspVO.setChannelCode(par.getChannelCode());
        sacReviewRspVO.setChannelName(par.getChannelName());
        sacReviewRspVO.setBillCode(par.getBillCode());
        sacReviewRspVO.setPlanReviewTime(par.getPlanReviewTime());
        sacReviewRspVO.setReviewTime(par.getReviewTime());
        sacReviewRspVO.setLastReviewTime(par.getLastReviewTime());
        sacReviewRspVO.setOverReviewTime(par.getOverReviewTime());
        sacReviewRspVO.setPlanComeTime(par.getPlanComeTime());
        sacReviewRspVO.setFactComeTime(par.getFactComeTime());
        sacReviewRspVO.setIsCome(par.getIsCome());
        sacReviewRspVO.setAssignStatus(par.getAssignStatus());
        sacReviewRspVO.setAssignStatusName(par.getAssignStatusName());
        sacReviewRspVO.setAssignTime(par.getAssignTime());
        sacReviewRspVO.setAssignPersonId(par.getAssignPersonId());
        sacReviewRspVO.setAssignPersonName(par.getAssignPersonName());
        sacReviewRspVO.setReviewPersonId(par.getReviewPersonId());
        sacReviewRspVO.setReviewPersonName(par.getReviewPersonName());
        sacReviewRspVO.setReviewDesc(par.getReviewDesc());
        sacReviewRspVO.setReviewStatus(par.getReviewStatus());
        sacReviewRspVO.setReviewStatusName(par.getReviewStatusName());
        sacReviewRspVO.setCustId(par.getCustId());
        sacReviewRspVO.setCustName(par.getCustName());
        sacReviewRspVO.setPhone(par.getPhone());
        sacReviewRspVO.setGender(par.getGender());
        sacReviewRspVO.setGenderName(par.getGenderName());
        sacReviewRspVO.setTouchStatus(par.getTouchStatus());
        sacReviewRspVO.setTouchStatusName(par.getTouchStatusName());
        sacReviewRspVO.setErrorReasonCode(par.getErrorReasonCode());
        sacReviewRspVO.setErrorReasonName(par.getErrorReasonName());
        sacReviewRspVO.setNodeCode(par.getNodeCode());
        sacReviewRspVO.setNodeName(par.getNodeName());
        sacReviewRspVO.setSendDlrCode(par.getSendDlrCode());
        sacReviewRspVO.setSendDlrShortName(par.getSendDlrShortName());
        sacReviewRspVO.setSendTime(par.getSendTime());
        sacReviewRspVO.setIntenLevelCode(par.getIntenLevelCode());
        sacReviewRspVO.setIntenLevelName(par.getIntenLevelName());
        sacReviewRspVO.setIntenBrandCode(par.getIntenBrandCode());
        sacReviewRspVO.setIntenBrandName(par.getIntenBrandName());
        sacReviewRspVO.setIntenSeriesCode(par.getIntenSeriesCode());
        sacReviewRspVO.setIntenSeriesName(par.getIntenSeriesName());
        sacReviewRspVO.setIntenCarTypeCode(par.getIntenCarTypeCode());
        sacReviewRspVO.setIntenCarTypeName(par.getIntenCarTypeName());
        sacReviewRspVO.setColumn1(par.getColumn1());
        sacReviewRspVO.setColumn2(par.getColumn2());
        sacReviewRspVO.setColumn3(par.getColumn3());
        sacReviewRspVO.setColumn4(par.getColumn4());
        sacReviewRspVO.setColumn5(par.getColumn5());
        sacReviewRspVO.setColumn6(par.getColumn6());
        sacReviewRspVO.setColumn7(par.getColumn7());
        sacReviewRspVO.setColumn8(par.getColumn8());
        sacReviewRspVO.setColumn9(par.getColumn9());
        sacReviewRspVO.setColumn10(par.getColumn10());
        sacReviewRspVO.setColumn11(par.getColumn11());
        sacReviewRspVO.setColumn12(par.getColumn12());
        sacReviewRspVO.setColumn13(par.getColumn13());
        sacReviewRspVO.setColumn14(par.getColumn14());
        sacReviewRspVO.setColumn15(par.getColumn15());
        sacReviewRspVO.setColumn16(par.getColumn16());
        sacReviewRspVO.setColumn17(par.getColumn17());
        sacReviewRspVO.setColumn18(par.getColumn18());
        sacReviewRspVO.setColumn19(par.getColumn19());
        sacReviewRspVO.setColumn20(par.getColumn20());
        sacReviewRspVO.setBigColumn1(par.getBigColumn1());
        sacReviewRspVO.setBigColumn2(par.getBigColumn2());
        sacReviewRspVO.setBigColumn3(par.getBigColumn3());
        sacReviewRspVO.setBigColumn4(par.getBigColumn4());
        sacReviewRspVO.setBigColumn5(par.getBigColumn5());
        sacReviewRspVO.setExtendsJson(par.getExtendsJson());
        sacReviewRspVO.setOemId(par.getOemId());
        sacReviewRspVO.setGroupId(par.getGroupId());
        sacReviewRspVO.setCreator(par.getCreator());
        sacReviewRspVO.setCreatedName(par.getCreatedName());
        sacReviewRspVO.setCreatedDate(par.getCreatedDate());
        sacReviewRspVO.setModifier(par.getModifier());
        sacReviewRspVO.setModifyName(par.getModifyName());
        sacReviewRspVO.setLastUpdatedDate(par.getLastUpdatedDate());
        sacReviewRspVO.setIsEnable(par.getIsEnable());
        sacReviewRspVO.setUpdateControlId(par.getUpdateControlId());
        sacReviewRspVO.setProvinceCode(par.getProvinceCode());
        sacReviewRspVO.setProvinceName(par.getProvinceName());
        sacReviewRspVO.setCityCode(par.getCityCode());
        sacReviewRspVO.setCityName(par.getCityName());
        sacReviewRspVO.setCountyCode(par.getCountyCode());
        sacReviewRspVO.setCountyName(par.getCountyName());
        sacReviewRspVO.setCityFirmCode(par.getCityFirmCode());
        sacReviewRspVO.setCityFirmName(par.getCityFirmName());
        sacReviewRspVO.setManageLabelCode(par.getManageLabelCode());
        sacReviewRspVO.setManageLabelName(par.getManageLabelName());
        sacReviewRspVO.setFirstReasonCode(par.getFirstReasonCode());
        sacReviewRspVO.setFirstReasonName(par.getFirstReasonName());
        sacReviewRspVO.setRemark(par.getRemark());
        sacReviewRspVO.setUserStage(par.getUserStage());
        sacReviewRspVO.setPreviousUserStage(par.getPreviousUserStage());
        sacReviewRspVO.setClueLevel(par.getClueLevel());
        sacReviewRspVO.setIntentVehicleCode(par.getIntentVehicleCode());
        sacReviewRspVO.setCompetitiveVehicleCode(par.getCompetitiveVehicleCode());
        sacReviewRspVO.setLocation(par.getLocation());
        sacReviewRspVO.setLocation(Area.fullPath(par.getLocation(), null));//位置
        sacReviewRspVO.setClueSource(par.getClueSource());
        sacReviewRspVO.setKnowDate(par.getKnowDate());
        sacReviewRspVO.setToStoreDate(par.getToStoreDate());
        sacReviewRspVO.setTestDriveDate(par.getTestDriveDate());
        sacReviewRspVO.setPlaceOrderDate(par.getPlaceOrderDate());
        return sacReviewRspVO;

    }
}
