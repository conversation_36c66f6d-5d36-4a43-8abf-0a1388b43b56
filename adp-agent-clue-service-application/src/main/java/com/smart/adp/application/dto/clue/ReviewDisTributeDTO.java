package com.smart.adp.application.dto.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * date 2025/4/14 17:27
 * @description 线索分配DTO
 **/
@Data
@Schema(description = "店端线索店长线索分配DTO")
public class ReviewDisTributeDTO implements Serializable {

    @Schema(description = "回访任务id")
    private String reviewId;

    @Schema(description = "线索门店")
    private String dlrCode;

    @Schema(description = "线索门店ID")
    private String dlrId;

    @Schema(description = "线索门店名称")
    private String dlrShortName;

    @Schema(description = "线索分配时间")
    private Date allocateTime;

    @Schema(description = "分配时间")
    private LocalDateTime assignTime;

    @Schema(description = "分配人userId，一般为店长")
    private String assignPersonId;

    @Schema(description = "分配人userId，一般为店长")
    private String assignPersonName;

    @Schema(description = "回访人员id")
    private String reviewPersonId;

    @Schema(description = "回访人员名称")
    private String reviewPersonName;

    @Schema(description = "回访人员手机号")
    private String reviewPersonPhone;

    @Schema(description = "复合场景编码为必填，0:标记已读，1：试乘试驾 2：线索分配 3:预约试驾 4:任务 5：通知 6：异常关单 8：战败驳回 13：预约取消")
    private String scenario;

    @Schema(description = "复合场景编码为必填，1:试乘试驾，2：线索待跟进 3：dlrCode 【专营店编码为必填】")
    private String messageType;

    @Schema(description = "是否战败分配，默认为非战败分配，战败分配传ture(老版本isFlag=2 企微端战败分配，isFlag=3 pc端战败分配，非战败不传)")
    private Boolean isDefeatedDis;
}
