package com.smart.adp.application.dto.clue;

import com.smart.adp.application.assembler.AssemblerFactory;
import com.smart.adp.application.assembler.clue.ClueInfoDlrAssembler;
import com.smart.adp.application.assembler.clue.SacReviewAssembler;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacReview;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/3 10:57
 * @description 保存线索DTO
 **/
@Data
public class AgentClueSaveDTO implements Serializable {

    private static final long serialVersionUID = -1900677801183495134L;

    /**
     * 客户名称
     */
    @Schema(description = "客户姓名")
    @NotBlank(message = "客户姓名不能为空")
    private String custName;

    /**
     * 系统客户姓名
     */
    @Schema(description = "系统客户姓名")
    private String systemCustName;

    /**
     * 线索等级
     */
    @Schema(description = "线索等级")
    private String clueLevel;

    /**
     * custId
     */
    @Schema(description = "custId")
    private String custId;

    /**
     * 代理商来源
     */
    @Schema(description = "代理商来源，代理商项目新增")
    private String agentResource;

    /**
     * 市或区编码
     */
    @Schema(description = "客户所在地(市或区编码)，代理商项目新增")
    private String location;

    /**
     * 竞品车型
     */
    @Schema(description = "竞品车型,代理商项目新增")
    private String competitorModels;


    /**
     * 手机号
     */
    @Schema(description = "手机号")
    @NotBlank(message = "联系电话不能为空")
    private String phone;

    /**
     * 备用手机号
     */
    @Schema(description = "备用手机号")
    private String phoneBackUp;

    /**
     * 性别编码
     */
    @Schema(description = "性别编码")
    private String genderCode;

    /**
     * 性别
     */
    @Schema(description = "性别")
    @NotBlank(message = "性别不能为空")
    private String genderName;

    /**
     * 一级信息来源编码
     */
    @Schema(description = "一级信息来源编码")
    private String infoChanMCode;

    /**
     * 一级信息来源名称
     */
    @Schema(description = "一级信息来源名称")
    private String infoChanMName;

    /**
     * 二级信息来源编码
     */
    @Schema(description = "二级信息来源编码")
    private String infoChanDCode;

    /**
     * 二级信息来源名称
     */
    @Schema(description = "二级信息来源名称")
    private String infoChanDName;

    @Schema(description = "二级信息描述")
    private String infoChanDDesc;

    /**
     * 最低一级的信息来源编码
     */
    @Schema(description = "最低一级的信息来源编码")
    private String channelCode;

    /**
     * 最低一级的信息来源名称
     */
    @Schema(description = "最低一级的信息来源名称")
    private String channelName;

    /**
     * 意向车型编码
     */
    @Schema(description = "意向车型编码")
    @NotBlank(message = "意向车型不能为空")
    private String intenCarTypeCode;

    /**
     * 意向车型名称
     */
    @Schema(description = "意向车型名称")
    private String intenCarTypeName;

    /**
     * 意向级别编码
     */
    @Schema(description = "'意向级别编码")
    private String intenLevelCode;

    /**
     * 意向级别名称
     */
    @Schema(description = "意向级别名称")
    private String intenLevelName;

    /**
     * 外观色编码
     */
    @Schema(description = "外观色编码")
    private String outColorCode;

    /**
     * 外观色名称
     */
    @Schema(description = "外观色名称")
    private String outColorName;

    /**
     * 内饰色编码
     */
    @Schema(description = "内饰色编码")
    private String innerColorCode;

    /**
     * 内饰色编码
     */
    @Schema(description = "内饰色编码")
    private String innerColorName;

    /**
     * planBuyDate
     * 如："column2": "1"
     */
    @Schema(description = "planBuyDate")
    private String planBuyDate;

    /**
     * 如："column1": "2周以内"
     */
    @Schema(description = "planBuyDateName")
    private String planBuyDateName;


    /**
     * 扩展字段5
     * 如："column5": "Hot"
     * 业务含义字段 businessHeatName
     */
    @Schema(description = "businessHeatName")
    private String businessHeatName;

    /**
     * 扩展字段6
     * 如："column6": "Hot"
     * 业务含义字段 businessHeatCode
     */
    @Schema(description = "businessHeatCode")
    @NotBlank(message = "热度不能为空")
    private String businessHeatCode;

    /**
     * 回访人员用户ID
     */
    @Schema(description = "回访人员用户ID")
    private String reviewPersonId;

    /**
     * 回访人员名称
     */
    @Schema(description = "回访人员名称")
    private String reviewPersonName;

    /**
     * 系统来源
     */
    @Schema(description = "一级信息来源编码")
    private String systemSource = "ADP-DLR";

    /**
     * smartId
     */
    @Schema(description = "smartId")
    private String smartId;

    /**
     * clueType 总部线索塞值处理orgCode
     */
    @Schema(description = "clueType")
    private String clueType = "dlrClue";

    /**
     * dlrCode
     */
    @Schema(description = "dlrCode")
    private String dlrCode;

    /**
     * dlrShortName
     */
    @Schema(description = "dlrShortName")
    private String dlrShortName;

    /**
     * dlrId
     */
    @Schema(description = "dlrId")
    private String dlrId;

    /**
     * 源DlrCode
     */
    private String orgCode;

    /**
     * 源DlrName
     */
    private String orgName;

    /**
     * 状态code
     */
    private String statusCode;

    /**
     * 状态描述
     */
    private String statusName;

    /**
     * 是否存在线索
     */
    @Schema(description = "是否存在线索", hidden = true)
    private int count;

    /**
     * 专营店名称补全标识
     */
    @Schema(description = "是否存在线索")
    private String gooFlag = "true";

    /**
     * 是否存在战败线索
     */
    @Schema(description = "是否存在战败线索,默认不存在")
    private Boolean existDefeatClue = Boolean.FALSE;

    /**
     * 系统记录
     */
    @Schema(description = "系统记录", hidden = true)
    private String systemRecord;

    /**
     * 二维码市场留资校验标识
     */
    @Schema(description = "二维码市场留资校验标识")
    private String flag;

    /**
     * 线索主键ID
     */
    @Schema(description = "线索主键ID", hidden = true)
    private String id;

    @Schema(description = "isEnable", hidden = true)
    private String isEnable;

    /**
     * 下次回访时间
     */
    @Schema(description = "下次回访时间")
    private String planReviewTime;

    /**
     * 手机号存在战败线索时填字段
     */
    @Schema(description = "线索战败线索的createdDate")
    private LocalDateTime createdDateT;

    @Schema(description = "战败线索的firstReviewTime")
    private LocalDateTime firstReviewTime;

    @Schema(description = "存在战败线索时当前token解析的userId")
    private String creatorT;

    @Schema(description = "存在战败线索时当前token解析的empName")
    private String createdNameT;

    /**
     * 存在战败线索时,t_prc_mds_lookup_value表的attribute
     */
    @Schema(description = "存在战败线索时,t_prc_mds_lookup_value表的attribute1")
    private String firstChannel;

    /**
     * 战败激活保留阶段
     */
    private String stage;

    /**
     * 活动id
     */
    @Schema(description = "活动id")
    private String activityId;

    /**
     * 活动运营
     */
    private String cActivityVariety;

    /**
     * 活动名称
     */
    private String resumeDesc;

    /**
     * CDP下发线索是参数
     */
    @Schema(description = "CDP下发线索是参数")
    private String fixedSecondChannel;

    /**
     * CDP下发线索是参数
     */
    @Schema(description = "CDP下发线索是参数")
    private String fixedThirdChannel;

    /**
     * morkPlanReviewTime
     */
    @Schema(description = "morkPlanReviewTime")
    private String morkPlanReviewTime;

    /**
     * 是否cdp标识 1
     */
    @Schema(description = "是否cdp标识")
    private String isCdp;

    private String oemId;

    private String oemCode;

    private String groupId;

    private String groupCode;

    private String createdName;

    private String modifyName;

    private String modifier;

    private String creator;

    private String updateControlId;

    private LocalDateTime createdDate;

    private LocalDateTime lastUpdatedDate;

    private String serverOrder;

    /**
     * 店端线索是否存在标识
     */
    private String clueExist;
    /**
     * 线索是否存在描述
     */
    private String clueExistDesc;


    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "区编码")
    private String countyCode;

    @Schema(description = "区名称")
    private String countyName;

    /**
     * 对应员工信息表的EMP_CODE
     */
    private String sellerCode;

    /**
     * 根据创建线索的门店code查询门店的线索开关会设置当前字段值
     */
    private String notAutoDlr;

    /**
     * 城市线索池开启开关
     */
    private Boolean cityOpenFlag = Boolean.FALSE;

    /**
     * 是否更新线索
     */
    private Boolean updateClue = Boolean.FALSE;

    //回访任务校验参数
    private String billType = "DLRCLUE";
    private String billTypeName = "线索";
    /**
     * 对应serverOrder
     */
    private String billCode;
    private String businessType;
    private String businessTypeName;

    private String factComeTime;

    /**
     * 超期回访时间
     */
    private String overReviewTime;

    /**
     * 分配时间，产品专家指定时候会取当前时间
     */
    private LocalDateTime assignTime;

    public static SacClueInfoDlr convert(AgentClueSaveDTO source) {
        return AssemblerFactory.getInstance().convert
                (ClueInfoDlrAssembler.class, source, SacClueInfoDlr.class);
    }


    public static SacReview convertReview(AgentClueSaveDTO source) {
        return AssemblerFactory.getInstance().convert
                (SacReviewAssembler.class, source, SacReview.class);
    }
}
