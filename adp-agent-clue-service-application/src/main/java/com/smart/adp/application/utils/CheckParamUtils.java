package com.smart.adp.application.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.smart.adp.application.dto.clue.AgentClueCheckDTO;
import com.smart.adp.application.dto.clue.AgentClueDTO;
import com.smart.adp.application.dto.clue.AgentClueDisTributeDTO;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * date 2025/3/5 19:43
 * @description 参数校验Utils
 **/
@Slf4j
public class CheckParamUtils {

    // 常量定义（建议统一管理）
    private static final int MAX_CUST_NAME_LENGTH = 50;
    private static final String PHONE_REGEX_INVALID_MSG = "请输入正确的手机号！";
    private static final String CUST_NAME_TOO_LONG_MSG = "客户名称不能超过" + MAX_CUST_NAME_LENGTH + "个字符";
    private static final String INTEN_CAR_TYPE_TOO_LONG_MSG = "意向车型编码不能超过" + MAX_CUST_NAME_LENGTH + "个字符";
    private static final int MAX_T_REVIEW_LENGTH = 36;
    private static final String MAX_T_REVIEW_LONG_MSG = "回访人员id不能超过" + MAX_T_REVIEW_LENGTH + "个字符";
    private static final String MAX_T_REVIEW_NAME_LONG_MSG = "回访人员姓名不能超过" + MAX_CUST_NAME_LENGTH + "个字符";
    private static final int MAX_DISTRIBUTE_SIZE = 200;

    /**
     * 门店自建线索入参校验
     *
     * @param param
     */
    public static void checkSaveClueParam(AgentClueDTO param) {
        // 去空格并回写
        String trimmedPhone = StrUtil.trim(param.getPhone());
        param.setPhone(trimmedPhone);

        if (!Validator.isMobile(trimmedPhone)) {
            log.error("手机号校验失败: {}", trimmedPhone);
            throw new BusinessException(RespCode.FAIL.getCode(), PHONE_REGEX_INVALID_MSG);
        }
        if (StrUtil.isNotBlank(param.getCustName()) && param.getCustName().length() > MAX_CUST_NAME_LENGTH) {
            log.error("客户名称超长: {}", param.getCustName());
            throw new BusinessException(RespCode.FAIL.getCode(), CUST_NAME_TOO_LONG_MSG);
        }

        if (StrUtil.isNotBlank(param.getIntenCarTypeCode()) && param.getIntenCarTypeCode().length() > MAX_CUST_NAME_LENGTH) {
            log.error("客户名称超长: {}", param.getCustName());
            throw new BusinessException(RespCode.FAIL.getCode(), INTEN_CAR_TYPE_TOO_LONG_MSG);
        }

        //初始化校验Heat信息
        initHeat(param);

        //线索下发时针对意向车型进行处理
        handleIntenCarType(param);

        if (StrUtil.isNotBlank(param.getPlanReviewTime())) {
            try {
                LocalDateTime planReviewTime = LocalDateTime.parse(param.getPlanReviewTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                log.error("线索创建时间格式转换异常", e);
                param.setPlanReviewTime(null);
            }
        }
    }

    /**
     * 手机号校验
     *
     * @param param
     */
    public static void checkPhone(AgentClueCheckDTO param) {
        // 去空格并回写
        String trimmedPhone = StrUtil.trim(param.getPhone());
        param.setPhone(trimmedPhone);
        if (!Validator.isMobile(trimmedPhone)) {
            log.error("手机号校验失败: {}", trimmedPhone);
            throw new BusinessException(RespCode.FAIL.getCode(), PHONE_REGEX_INVALID_MSG);
        }
    }

    /**
     * 线索下发时针对意向车型进行处理
     *
     * @param param 参数Map
     */
    private static void handleIntenCarType(AgentClueDTO param) {
        String code = param.getIntenCarTypeCode();
        String name = param.getIntenCarTypeName();

        param.setIntenCarTypeCode(StringUtils.defaultIfBlank(code, name));
        param.setIntenCarTypeName(StringUtils.defaultIfBlank(name, code));
    }

    /**
     * 初始化校验Heat信息
     *
     * @param param
     */
    public static void initHeat(AgentClueDTO param) {
        List<String> list_cdp = Arrays.asList("Cold", "Warm", "Hot");
        Boolean hitCode = list_cdp.stream().anyMatch(o -> o.equals(param.getBusinessHeatCode()));
        Boolean hitName = list_cdp.stream().anyMatch(o -> o.equals(param.getBusinessHeatName()));
        if (!hitCode) {
            param.setBusinessHeatCode("");
        }
        if (!hitName) {
            param.setBusinessHeatName("");
        }
    }

    /**
     * 校验adp自建线索三级渠道不传
     *
     * @param param
     */
    public static void checkADPCreateClue(AgentClueSaveDTO param) {
        String systemSource = param.getSystemSource();
        if (StrUtil.isNotBlank(systemSource) && systemSource.contains("ADP") && StrUtil.isBlank(param.getInfoChanDCode())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "自建线索不可不传三级渠道参数");
        }
    }


    /**
     * 回访信息校验
     *
     * @param param
     */
    public static void checkSaveReviewParam(AgentClueSaveDTO param) {
        if (StrUtil.isNotBlank(param.getReviewPersonId()) && param.getReviewPersonId().length() > MAX_T_REVIEW_LENGTH) {
            log.error("回访人员id超长: {}", param.getReviewPersonId());
            throw new BusinessException(RespCode.FAIL.getCode(), MAX_T_REVIEW_LONG_MSG);
        }

        if (StrUtil.isNotBlank(param.getReviewPersonName()) && param.getReviewPersonName().length() > MAX_CUST_NAME_LENGTH) {
            log.error("回访人员名称超长: {}", param.getReviewPersonName());
            throw new BusinessException(RespCode.FAIL.getCode(), MAX_T_REVIEW_NAME_LONG_MSG);
        }

        if (StrUtil.isBlank(param.getOrgCode())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "所属组织code不能为空");
        }

        if (param.getOrgCode().length() > 10) {
            throw new BusinessException(RespCode.FAIL.getCode(), "所属组织code超长");
        }

        if (StrUtil.isBlank(param.getOrgName())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "所属组织名称不能为空");
        }

        if (param.getOrgCode().length() > MAX_CUST_NAME_LENGTH) {
            throw new BusinessException(RespCode.FAIL.getCode(), "所属组织名称超长");
        }

        if (StrUtil.isBlank(param.getBillType())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "回访单据类型不能为空");
        }
        if (param.getBillType().length() > 10) {
            throw new BusinessException(RespCode.FAIL.getCode(), "回访单据类型超长");
        }

        if (StrUtil.isBlank(param.getBillTypeName())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "回访单据类型名称不能为空");
        }
        if (param.getBillTypeName().length() > MAX_CUST_NAME_LENGTH) {
            throw new BusinessException(RespCode.FAIL.getCode(), "回访单据类型名称超长");
        }
        if (StrUtil.isBlank(param.getBillCode())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "业务关联单号不能为空");
        }
        if (param.getBillCode().length() > MAX_CUST_NAME_LENGTH) {
            throw new BusinessException(RespCode.FAIL.getCode(), "业务关联单号超长");
        }

        if (StrUtil.isBlank(param.getCustId())) {
            param.setCustId(UUID.randomUUID().toString().replace("-", ""));
        }
        if (param.getCustId().length() > MAX_T_REVIEW_LENGTH) {
            throw new BusinessException(RespCode.FAIL.getCode(), "custId超长");
        }
    }

    /**
     * 校验门店店长分配
     *
     * @param param
     */
    public static void checkDlrDistribute(AgentClueDisTributeDTO param, UserBusiEntity userInfo) {
        if (CollUtil.isEmpty(param.getReviewDisTributeDTOList())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "回访任务不存在，请稍后重试！");
        }
        if (param.getReviewDisTributeDTOList().size() > MAX_DISTRIBUTE_SIZE) {
            throw new BusinessException(RespCode.FAIL.getCode(), "批量分配任务过多，请减少任务稍后重试！");
        }
        if (StrUtil.isBlank(param.getDlrCode())) {
            param.setDlrCode(userInfo.getDlrCode());
        }
    }

}
