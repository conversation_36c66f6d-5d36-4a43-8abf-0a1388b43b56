package com.smart.adp.application.service.base;

import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.domain.enums.StatisticsTypeEnum;
import com.smart.adp.domain.valueObject.base.StatisticsVO;
import com.smart.adp.infrastructure.feign.request.SummaryDTO;
import com.smart.adp.infrastructure.feign.response.SummaryVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/15
 */
public interface WorkbenchApplicationService {

    /**
     * 工作台统计
     *
     * @param types 指标类型集合
     * @return list
     */
    List<StatisticsVO> statistics(Set<StatisticsTypeEnum> types);

    /**
     * 当前用户小结
     *
     * @param dto 入参
     * @return res
     */
    RespBody<SummaryVO> summary(SummaryDTO dto);

    /**
     * 指定用户小结
     *
     * @param dto 入参
     * @return res
     */
    RespBody<List<SummaryVO>> summaryList(SummaryDTO dto);
}
