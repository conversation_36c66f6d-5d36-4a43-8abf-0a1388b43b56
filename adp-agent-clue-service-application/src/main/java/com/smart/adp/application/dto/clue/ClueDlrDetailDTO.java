package com.smart.adp.application.dto.clue;

import com.smart.adp.domain.bo.clue.SacClueInfoDlrBO;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import com.smart.adp.domain.entity.clue.SacUserGroupDetailEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "线索详情入参")
public class ClueDlrDetailDTO {

//    /**
//     * 线索 ID
//     */
//    @Schema(description = "线索 ID", required = true)
//    @NotEmpty(message = "线索 ID 不能为空")
//    private String id;
    /**
     * 线索 ID
     */
    @Schema(description = "客户ID", required = true)
    @NotEmpty(message = "客户 ID 不能为空")
    private String custId;

    /**
     * 线索标签
     *
     * @see com.smart.adp.domain.enums.DefeatFlagEnum
     */
    @Schema(description = "战败标签，0正常，1战败", required = true)
    private Integer defeatFlag = 0;

    public SacClueInfoDlr buildClueDlrEntity() {
        return SacClueInfoDlr.builder().custId(custId).build();
    }

    public SacClueInfoDlrBO buildClueDlrBO() {
        SacClueInfoDlrBO bo = new SacClueInfoDlrBO();
        bo.setCustId(custId);
        bo.setDefeatFlag(defeatFlag);
        return bo;
    }

    public SacOnecustInfoEntity buildOneCustInfoEntity() {
        return SacOnecustInfoEntity.builder().custId(custId).build();
    }

    public SacReviewBO buildReviewEntity() {
        SacReviewBO bo = new SacReviewBO();
        bo.setCustId(custId);
        bo.setDefeatFlag(defeatFlag);
        return bo;
    }

    public SacUserGroupDetailEntity buildSacUserGroupDetailEntity() {
        return SacUserGroupDetailEntity.builder().custId(custId).build();
    }

    public SacOneCustRemark buildSacClueRemarkEntity() {
        return SacOneCustRemark.builder().custId(custId).build();
    }
}
