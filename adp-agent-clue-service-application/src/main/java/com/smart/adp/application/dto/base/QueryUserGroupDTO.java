package com.smart.adp.application.dto.base;

import com.smart.adp.domain.entity.clue.SacUserGroupEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description: 查询用户分组
 * @Author: rik.ren
 * @Date: 2025/3/9 17:28
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryUserGroupDTO implements Serializable {
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @NotBlank(message = "创建人不能为空")
    private String creator;
    /**
     * 客户ID
     */
    @Schema(description = "客户 ID")
    private String custId;

    public SacUserGroupEntity buildEntity() {
        return SacUserGroupEntity.builder().creator(this.creator).build();
    }
}
