package com.smart.adp.application.dto.clue;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.application.vo.base.UserGroupVO;
import com.smart.adp.domain.bo.clue.SacUserGroupDetailBO;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 查询用户分组和线索所在分组
 * @Author: rik.ren
 * @Date: 2025/3/9 17:28
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryClueGroupDTO implements Serializable {
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @NotBlank(message = "创建人不能为空")
    private String creator;
    /**
     * 客户ID
     */
    @Schema(description = "客户 ID")
    private String custId;

    public SacUserGroupDetailBO buildSacUserGroupDetailBo(List<UserGroupVO> paramVO) {
        if (CollectionUtil.isEmpty(paramVO)) {
            return null;
        }
        List<String> listUserGroupId = paramVO.stream().map(UserGroupVO::getUserGroupId).collect(Collectors.toList());
        SacUserGroupDetailBO detailBO = new SacUserGroupDetailBO();
        detailBO.setListUserGroupId(listUserGroupId);
        detailBO.setCustId(custId);
        return detailBO;
    }

    public SacUserGroupEntity buildEntity() {
        return SacUserGroupEntity.builder().creator(this.creator).build();
    }
}
