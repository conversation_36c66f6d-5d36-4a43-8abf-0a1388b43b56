package com.smart.adp.application.dto.clue;

import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.application.common.PageDTO;
import com.smart.adp.application.utils.LocalDateTimeUtil;
import com.smart.adp.domain.context.Area;
import com.smart.adp.domain.enums.*;
import com.smart.adp.domain.qry.ClueDlrQry;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/3
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
@Schema(description = "线索列表入参")
public class ClueDlrListDTO extends PageDTO {

    /**
     * 查询类型
     */
    @NotNull(message = "查询类型不能为空")
    @Schema(description = "查询类型", required = true)
    private ClueQueryTypeEnum qryType;

    /**
     * 线索状态
     */
    @Schema(description = "线索状态")
    private String statusCode;

    /**
     * 线索等级
     */
    @Schema(description = "线索等级")
    private String level;

    /**
     * 跟进状态
     */
    @Schema(description = "跟进状态")
    private Integer reviewStatus;

    /**
     * 搜索内容
     */
    @Schema(description = "搜索内容")
    private String searchStr;

    /**
     * 阶段集合
     */
    @Schema(description = "阶段集合")
    private List<Integer> stages;

    /**
     * 活跃时间
     */
    @Schema(description = "活跃时间")
    private Integer activeTime;

    /**
     * 意向车型
     */
    @Schema(description = "意向车型")
    private String intentionCarType;

    /**
     * 地区编码列表
     */
    @Schema(description = "地区编码列表")
    private List<String> areaCodeList;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    /**
     * 跟进时间开始
     */
    @Schema(description = "跟进时间开始")
    private LocalDateTime reviewTimeStart;

    /**
     * 跟进时间结束
     */
    @Schema(description = "跟进时间结束")
    private LocalDateTime reviewTimeEnd;

    /**
     * 逾期时间开始
     */
    @Schema(description = "逾期时间开始")
    private LocalDateTime overdueTimeStart;

    /**
     * 逾期时间结束
     */
    @Schema(description = "逾期时间结束")
    private LocalDateTime overdueTimeEnd;

    /**
     * 分配时间开始
     */
    @Schema(description = "分配时间开始")
    private LocalDateTime allocateTimeStart;

    /**
     * 分配时间结束
     */
    @Schema(description = "分配时间结束")
    private LocalDateTime allocateTimeEnd;

    /**
     * 战败时间开始
     */
    @Schema(description = "战败时间开始")
    private LocalDateTime defeatTimeStart;

    /**
     * 战败时间结束
     */
    @Schema(description = "战败时间结束")
    private LocalDateTime defeatTimeEnd;

    /**
     * 订单状态列表
     */
    @Schema(description = "订单状态列表")
    private List<String> orderStatusList;

    /**
     * 二级渠道编码
     */
    @Schema(description = "二级渠道编码")
    private String channelMCode;

    /**
     * 三级渠道编码列表
     */
    @Schema(description = "三级渠道编码列表")
    private List<String> channelCodeList;

    /**
     * 热度
     */
    @Schema(description = "热度")
    private String heat;

    /**
     * 产品专家 ID 列表
     */
    @Schema(description = "产品专家 ID 列表")
    private List<String> reviewPersonIds;

    /**
     * 排序方式
     *
     * @see com.smart.adp.domain.enums.ClueSortEnum
     */
    @Schema(description = "排序方式")
    private Integer sort;

    private static final int AREA_LIST_MAX_LENGTH = 300;

    /**
     * dto -> qry
     *
     * @param dto -
     * @return com.smart.adp.domain.qry.ClueDlrQry
     */
    public static ClueDlrQry buildQry(ClueDlrListDTO dto) {
        ClueDlrQry qry = new ClueDlrQry();
        qry.setQryType(dto.getQryType());
        qry.setPageIndex(dto.getPageIndex());
        qry.setPageSize(dto.getPageSize());
        qry.setStatusCode(dto.getStatusCode());
        qry.setReviewStatus(ReviewStatusEnum.getByCode(dto.getReviewStatus()));
        qry.setLevel(dto.getLevel());
        qry.setSearchStr(dto.getSearchStr());
        qry.setStages(ClueStageEnum.getByCodes(dto.getStages()));
        qry.setActive(ClueActiveEnum.getByCode(dto.getActiveTime()));
        qry.setIntentionCarType(dto.getIntentionCarType());
        qry.setAreaCodeList(convertAreaList(dto.getAreaCodeList()));
        qry.setCreateTimeStart(dto.getCreateTimeStart());
        qry.setCreateTimeEnd(LocalDateTimeUtil.handleEnd(dto.getCreateTimeEnd()));
        qry.setReviewTimeStart(dto.getReviewTimeStart());
        qry.setReviewTimeEnd(LocalDateTimeUtil.handleEnd(dto.getReviewTimeEnd()));
        qry.setOverdueTimeStart(dto.getOverdueTimeStart());
        qry.setOverdueTimeEnd(LocalDateTimeUtil.handleEnd(dto.getOverdueTimeEnd()));
        qry.setAllocateTimeStart(dto.getAllocateTimeStart());
        qry.setAllocateTimeEnd(LocalDateTimeUtil.handleEnd(dto.getAllocateTimeEnd()));
        qry.setDefeatTimeStart(dto.getDefeatTimeStart());
        qry.setDefeatTimeEnd(LocalDateTimeUtil.handleEnd(dto.getDefeatTimeEnd()));
        qry.setOrderStatusList(dto.getOrderStatusList());
        qry.setChannelMCode(dto.getChannelMCode());
        qry.setChannelCodeList(dto.getChannelCodeList());
        qry.setHeat(dto.getHeat());
        qry.setReviewPersonIds(dto.getReviewPersonIds());
        qry.setSort(ClueSortEnum.getByCode(dto.getSort()));
        return qry;
    }

    /**
     * 添加子地区编码
     *
     * @param areaCodeList 地区编码列表
     * @return 转化后的列表
     */
    private static List<String> convertAreaList(List<String> areaCodeList) {
        if (CollectionUtil.isEmpty(areaCodeList)) {
            return areaCodeList;
        }

        List<String> resList = areaCodeList.stream()
                                           .distinct()
                                           .map(Area::selfAndChildrenCode)
                                           .filter(CollectionUtil::isNotEmpty)
                                           .flatMap(Collection::stream)
                                           .collect(Collectors.toList());
        if (resList.size() > AREA_LIST_MAX_LENGTH) {
            throw new IllegalArgumentException("所在地范围过大");
        }
        return resList;
    }
}
