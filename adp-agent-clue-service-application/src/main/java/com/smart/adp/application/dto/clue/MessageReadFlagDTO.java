package com.smart.adp.application.dto.clue;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.domain.bo.clue.SacClueMsgRecordBO;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.enums.ClueMsgReadEnum;
import com.smart.adp.domain.enums.MessageTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 消息标记已读入参
 * @Author: rik.ren
 * @Date: 2025/4/15 13:26
 **/
@Data
@Schema(description = "消息标记已读入参")
public class MessageReadFlagDTO {

    @Schema(description = "员工empId", required = false, hidden = true)
    private String empId;

    @Schema(description = "消息ID", required = false)
    private List<String> msgId;

    @Schema(description = "消息类型", required = false, hidden = true)
    private List<String> listMessageType;

    public String getEmpId() {
        if (StringUtils.isEmpty(this.empId)) {
            this.empId = UserInfoContext.get().getEmpID();
        }
        return empId;
    }

    public void setEmpId(String empId) {
        if (StringUtils.isEmpty(this.empId)) {
            UserBusiEntity userInfo = UserInfoContext.get();
            this.empId = userInfo.getEmpID();
        }
    }

    public SacClueMsgRecordBO buildSacClueMsgMarkReadBO() {
        SacClueMsgRecordBO bo = new SacClueMsgRecordBO();
        bo.setListMessageId(msgId);
        bo.setIsRead(ClueMsgReadEnum.READ.getCode());
        bo.setReceiveEmpId(getEmpId());
        bo.setLastUpdatedDate(LocalDateTime.now());
        bo.setListMessageType(getListMessageType());
        return bo;
    }

    public List<String> getListMessageType() {
        if (CollectionUtil.isEmpty(listMessageType)) {
            listMessageType = DEFAULT_MESSAGE_TYPE;
        }
        return listMessageType;
    }

    // 定义静态常量，JVM加载类时初始化
    private static final List<String> DEFAULT_MESSAGE_TYPE = MessageTypeEnum.getAllCodes();
}
