package com.smart.adp.application.dto.clue;

import com.smart.adp.domain.bo.clue.SacClueInfoDlrBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * @Description: 线索查询DTO
 * @Author: rik.ren
 * @Date: 2025/4/23 13:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "线索查询入参")
public class ClueDlrInfoQueryDTO {

    /**
     * 线索 ID
     */
    @Schema(description = "线索手机号", required = true)
    @NotEmpty(message = "线索手机号不能为空")
    private String phone;

    /**
     * 线索标签
     *
     * @see com.smart.adp.domain.enums.DefeatFlagEnum
     */
    @Schema(description = "战败标签，0非战败，1全部", required = false)
    private Integer defeatFlag = 1;

    @Schema(description = "获取线索的扩展数据")
    private Boolean needReamrk = Boolean.FALSE;

    public SacClueInfoDlrBO buildClueDlrEntity() {
        SacClueInfoDlrBO bo = new SacClueInfoDlrBO();
        bo.setDefeatFlag(defeatFlag);
        bo.setPhone(phone);
        return bo;
    }

}
