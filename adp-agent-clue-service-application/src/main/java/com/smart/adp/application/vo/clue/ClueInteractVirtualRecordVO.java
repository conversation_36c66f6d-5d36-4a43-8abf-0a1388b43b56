package com.smart.adp.application.vo.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.apache.poi.hpsf.Decimal;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 互动数据中的虚拟外呼
 * @Author: rik.ren
 * @Date: 2025/3/23 13:13
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ClueInteractVirtualRecordVO implements Serializable {
    /**
     * 开始时间，给跟进和虚拟外呼和试驾录音用
     */
    @Schema(description = "开始时间")
    private LocalDateTime beginDate;
    /**
     * 结束时间，给跟进和虚拟外呼和试驾录音用
     */
    @Schema(description = "结束时间")
    private LocalDateTime endDate;


    /**
     * 摘要，给虚拟外呼和试驾录音用
     */
    @Schema(description = "摘要")
    private String abstractContent;

    /**
     * 录音时长，给虚拟外呼和试驾录音用
     */
    @Schema(description = "录音时长")
    private String duration;
    /**
     * 录音得分，给虚拟外呼和试驾录音用
     */
    @Schema(description = "录音得分")
    private Decimal recordScore;

    /**
     * 跟进人姓名，给跟进和虚拟外呼和试驾录音用
     */
    @Schema(description = "跟进人姓名")
    private String resumePersonName;
    /**
     * 跟进人code，给跟进和虚拟外呼和试驾录音用
     */
    @Schema(description = "跟进人code")
    private String resumePersonCode;

    /**
     * 跟进人门店，给跟进和虚拟外呼和试驾录音用
     */
    @Schema(description = "跟进人门店")
    private String resumePersonDlrName;
    /**
     * 客户id，给跟进和虚拟外呼和试驾录音用
     */
    @Schema(description = "客户id")
    private String custId;

}
