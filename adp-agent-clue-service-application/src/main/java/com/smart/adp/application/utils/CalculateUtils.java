package com.smart.adp.application.utils;

import com.smart.adp.domain.valueObject.base.LookUpInfo;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/9 10:42
 * @description
 **/
@Slf4j
public class CalculateUtils {

    public static LocalDateTime calculateOverDueTime(LocalDateTime baseTime, LookUpInfo lookUpInfo) {
        // 首次逾期时间配置(LOOKUP_VALUE_CODE)
        Integer overTime = Integer.parseInt(lookUpInfo.getLookUpValueCode());

        // 1. 处理特殊情况：大于12小时使用原始值，未配置使用24小时
        if (overTime > 12 || overTime == 0) {
            int hours = overTime > 12 ? overTime : 24;
            log.info("逾期时间{}，按{}小时计算", overTime > 12 ? "大于12" : "未配置", hours);
            return baseTime.plusHours(hours);
        }

        // 获取基准时间对应的当前小时
        int currentHour = baseTime.getHour();

        // 2. 如果当前时间在非工作时间（21:00-09:00）
        if (currentHour >= 21 || currentHour < 9) {
            LocalDateTime nextWorkingDay = baseTime.withHour(9)
                    .withMinute(0)
                    .withSecond(0);
            return currentHour >= 21 ?
                    nextWorkingDay.plusDays(1).plusHours(overTime) :
                    nextWorkingDay.plusHours(overTime);
        }

        // 3. 计算基础逾期时间
        LocalDateTime overDueTime = baseTime.plusHours(overTime);

        // 4. 如果计算后的时间在非工作时间，使用配置的时间
        if (overDueTime.getHour() >= 21 || overDueTime.getHour() < 9) {
            //ATTRIBUTE2 as nextDayOverdueTime
            String[] timeParts = lookUpInfo.getAttribute2().split(":");
            return baseTime.plusDays(1)
                    .withHour(Integer.parseInt(timeParts[0]))
                    .withMinute(Integer.parseInt(timeParts[1]))
                    .withSecond(0)
                    .withNano(0);
        }

        return overDueTime;
    }
}
