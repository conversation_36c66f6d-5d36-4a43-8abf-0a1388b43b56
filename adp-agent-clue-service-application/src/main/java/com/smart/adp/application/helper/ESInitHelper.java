package com.smart.adp.application.helper;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.application.dto.clue.UserJourneysFixDTO;
import com.smart.adp.domain.common.constants.IndexNameConstant;
import com.smart.adp.domain.entity.clue.AgentClueESO;
import com.smart.adp.domain.entity.clue.CustResumeESO;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.gateway.clue.SacOnecustResumeGateway;
import com.smart.adp.domain.utils.IndexNameGenerator;
import com.smart.adp.domain.valueObject.clue.SacOnecustResumeVO;
import com.smart.adp.infrastructure.repository.clue.AllClueDlrMapper;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.smart.adp.domain.valueObject.clue.table.SacOnecustResumeVOTableDef.SAC_ONECUST_RESUME_VO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
@Slf4j
@Component
public class ESInitHelper {

    @Autowired
    private RestHighLevelClient esClient;

    @Autowired
    private SacOnecustResumeGateway resumeGateway;

    @Autowired
    private AllClueDlrMapper clueDlrMapper;

    public void esFix(List<SacClueInfoDlr> list) {
        try {
            if (CollectionUtil.isEmpty(list)) {
                return;
            }

            List<String> custIds = list.stream()
                                       .map(SacClueInfoDlr::getCustId)
                                       .collect(Collectors.toList());

            Map<String, List<SacOnecustResumeVO>> resumeMap = buildResumeMap(custIds);


            BulkRequest bulkRequest = new BulkRequest();
            list.forEach(clue -> {
                AgentClueESO eso = AgentClueESO.buildESO(clue);
                String custId = clue.getCustId();

                List<SacOnecustResumeVO> resumes = resumeMap.getOrDefault(custId, Collections.emptyList());
                eso.setCustResumeList(resumes.stream()
                                             .map(CustResumeESO::buildESO)
                                             .collect(Collectors.toList()));

                String index = IndexNameGenerator.getFinalIndex(IndexNameConstant.AGENT_CLUE_INDEX_NAME);
                UpdateRequest request = new UpdateRequest(index, custId).docAsUpsert(true)
                                                                        .doc(eso.toMap());
                bulkRequest.add(request);
            });

            esClient.bulk(bulkRequest, RequestOptions.DEFAULT);

            log.info("es fix finished {}", custIds);
        } catch (Exception e) {
            log.error("es fix exception", e);
        }
    }

    private Map<String, List<SacOnecustResumeVO>> buildResumeMap(List<String> custIds) {
        if (CollectionUtil.isEmpty(custIds)) {
            return Collections.emptyMap();
        }

        QueryColumn[] columns = new QueryColumn[]{SAC_ONECUST_RESUME_VO.CUST_ID, SAC_ONECUST_RESUME_VO.RESUME_ID,
            SAC_ONECUST_RESUME_VO.SENCE_CODE, SAC_ONECUST_RESUME_VO.RESUME_PERSON_NAME, SAC_ONECUST_RESUME_VO.COLUMN5,
            SAC_ONECUST_RESUME_VO.COLUMN1, SAC_ONECUST_RESUME_VO.RESUME_DESC, SAC_ONECUST_RESUME_VO.REMARK,
            SAC_ONECUST_RESUME_VO.CREATED_DATE};
        List<SacOnecustResumeVO> resumeList = resumeGateway.queryResumeListByCustId(custIds, columns);

        if (CollectionUtil.isEmpty(resumeList)) {
            return Collections.emptyMap();
        }

        // cust id -> resumes
        return resumeList.stream()
                         .collect(Collectors.groupingBy(SacOnecustResumeVO::getCustId));
    }

    public void initClue(UserJourneysFixDTO dto) {
        try {
            String dbIndex = null;
            List<SacClueInfoDlr> list;
            String index = IndexNameGenerator.getFinalIndex(IndexNameConstant.AGENT_CLUE_INDEX_NAME);

            for (int i = 1; ; i++) {
                QueryWrapper wrapper = dto.buildInitClueESWrapper(dbIndex);
                list = clueDlrMapper.selectListByQueryAs(wrapper, SacClueInfoDlr.class);

                if (list.isEmpty()) {
                    break;
                }

                dbIndex = list.get(list.size() - 1)
                              .getId();

                BulkRequest bulkRequest = new BulkRequest();
                list.forEach(clue -> {
                    AgentClueESO eso = AgentClueESO.buildESO(clue);
                    String custId = clue.getCustId();

                    UpdateRequest request = new UpdateRequest(index, custId).docAsUpsert(true)
                                                                            .doc(eso.toMap());
                    bulkRequest.add(request);
                });

                esClient.bulk(bulkRequest, RequestOptions.DEFAULT);

                log.info("initClueES finished {}", i);
            }
        } catch (Exception e) {
            log.error("initClueES exception", e);
        }
    }

    public void initResume(UserJourneysFixDTO dto) {
        try {
            String dbIndex = null;
            List<SacClueInfoDlr> list;
            String index = IndexNameGenerator.getFinalIndex(IndexNameConstant.AGENT_CLUE_INDEX_NAME);

            for (int i = 1; ; i++) {
                QueryWrapper wrapper = dto.buildInitResumeESWrapper(dbIndex);
                list = clueDlrMapper.selectListByQueryAs(wrapper, SacClueInfoDlr.class);

                if (list.isEmpty()) {
                    break;
                }

                dbIndex = list.get(list.size() - 1)
                              .getId();

                Map<String, List<SacOnecustResumeVO>> resumeMap = buildResumeMap(list.stream()
                                                                                     .map(SacClueInfoDlr::getCustId)
                                                                                     .collect(Collectors.toList()));


                BulkRequest bulkRequest = new BulkRequest();
                resumeMap.forEach((custId, resumes) -> {
                    AgentClueESO eso = new AgentClueESO();

                    eso.setCustResumeList(resumes.stream()
                                                 .map(CustResumeESO::buildESO)
                                                 .collect(Collectors.toList()));
                    UpdateRequest request = new UpdateRequest(index, custId).docAsUpsert(true)
                                                                            .doc(eso.toMap());
                    bulkRequest.add(request);
                });

                esClient.bulk(bulkRequest, RequestOptions.DEFAULT);

                log.info("initResumeES finished {}", i);
            }
        } catch (Exception e) {
            log.error("initResumeES exception", e);
        }
    }
}
