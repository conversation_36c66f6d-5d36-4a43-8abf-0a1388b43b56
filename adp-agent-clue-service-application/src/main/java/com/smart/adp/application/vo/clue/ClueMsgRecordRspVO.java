package com.smart.adp.application.vo.clue;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 店端线索消息提醒表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClueMsgRecordRspVO {
    /**
     * 消息ID
     */
    @Schema(description = "消息ID")
    private String messageId;

    /**
     * 0未读，1已读
     */
    @Schema(description = "0未读，1已读")
    private String isRead;

    /**
     * 经销商编码
     */
    @Schema(description = "经销商编码")
    private String dlrCode;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型")
    private String messageType;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型名称")
    private String messageTypeName;

    /**
     * 关键字ID
     */
    @Schema(description = "关键字ID")
    private String busiKeyvalue;

    /**
     * 消息接收人
     */
    @Schema(description = "消息接收人")
    private String receiveEmpId;

    /**
     * 消息内容
     */
    @Schema(description = "消息内容")
    private String messageContent;

    /**
     * 关联单据ID
     */
    @Schema(description = "关联单据ID")
    private String relationBillId;

    /**
     * 扩展信息
     */
    @Schema(description = "扩展信息")
    private Object extendJson;

    /**
     * 扩展字段1
     */
    @Schema(description = "扩展字段1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Schema(description = "扩展字段2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Schema(description = "扩展字段3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Schema(description = "扩展字段4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Schema(description = "扩展字段5")
    private String column5;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createdDate;

    /**
     * 最后更新日期
     */
    @Schema(description = "最后更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime lastUpdatedDate;

    /**
     * 时间范围
     */
    @Schema(description = "时间范围")
    private String timeFrame;
}
