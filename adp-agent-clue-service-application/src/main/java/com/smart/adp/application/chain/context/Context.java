package com.smart.adp.application.chain.context;

import com.alibaba.fastjson2.JSONObject;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/30
 */
public class Context {

    protected JSONObject params = new JSONObject();

    public void put(String key, Object value) {
        params.put(key, value);
    }

    public Object get(String key) {
        return params.get(key);
    }

    public JSONObject getParams() {
        return params;
    }
}
