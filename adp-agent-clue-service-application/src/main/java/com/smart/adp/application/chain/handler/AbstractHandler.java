package com.smart.adp.application.chain.handler;

import com.smart.adp.application.chain.context.Context;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/30
 */
public abstract class AbstractHandler<C extends Context> implements Handler<C> {

    protected Handler<C> next;

    @Override
    public void handle(C ctx) {
        doHandle(ctx);
        if (Objects.nonNull(next)) {
            next.handle(ctx);
        }
    }

    public AbstractHandler<C> setNext(AbstractHandler<C> next) {
        this.next = next;
        return next;
    }

    protected abstract void doHandle(C ctx);
}
