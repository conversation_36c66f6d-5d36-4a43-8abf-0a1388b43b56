package com.smart.adp.application.dto.clue;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.bo.clue.ClueDlrModifyBO;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.enums.DefeatFlagEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description: 更新意向车信息DTO
 * @Author: rik.ren
 * @Date: 2025/3/13 13:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "更新意向车信息DTO")
public class ModifyIntentionCarDTO {

    /**
     * 线索 ID
     */
    @Schema(description = "客户ID", required = true)
    private String custId;

    /**
     * 新的意向车
     */
    @Schema(description = "新的意向车", required = true)
    private String newIntentionCar;

    /**
     * 最近一次试驾时间
     */
    @Schema(description = "最近一次试驾时间", required = true)
    private LocalDateTime lastTestdriverTime;

    /**
     * 登录人信息
     */
    @Schema(description = "登录人信息，不显示只用来传递", hidden = true)
    private UserBusiEntity userBusiEntity;

    public ClueDlrModifyBO buildModifyBO(String newIntentionCar) {
        ClueDlrModifyBO modifyBO = new ClueDlrModifyBO();
        modifyBO.setCustId(custId);
        modifyBO.setValue(newIntentionCar);
        modifyBO.setDefeatFlag(DefeatFlagEnum.DEFEAT.getCode());
        return modifyBO;
    }

    public UserBusiEntity getUserBusiEntity() {
        if (ObjectUtil.isEmpty(userBusiEntity)) {
            userBusiEntity = UserInfoContext.get();
        }
        return userBusiEntity;
    }
}
