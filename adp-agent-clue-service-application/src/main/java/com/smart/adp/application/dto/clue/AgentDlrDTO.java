package com.smart.adp.application.dto.clue;

import com.mybatisflex.annotation.Column;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/3/9 14:22
 * @description 门店信息
 **/
@Data
@Schema(description = "门店信息")
public class AgentDlrDTO implements Serializable {

    /**
     * dlrCode
     */
    @Schema(description = "dlrCode")
    private String dlrCode;

    /**
     * dlrShortName
     */
    @Schema(description = "dlrShortName")
    private String dlrShortName;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "区编码")
    private String countyCode;

    @Schema(description = "区名称")
    private String countyName;

}
