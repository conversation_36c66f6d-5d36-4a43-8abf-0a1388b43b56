package com.smart.adp.application.chain.context;

import com.smart.adp.application.dto.clue.AgentClueDTO;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 线索创建上下文
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/1
 */
@Getter
@Setter
public class ClueCreateContext extends BizContext {

    /**
     * 线索入参DTO
     */
    private AgentClueDTO reqDto;

    /**
     * 线索保存DTO
     */
    private AgentClueSaveDTO saveDTO;

    /**
     * 用户信息
     */
    private UserBusiEntity userInfo;
}
