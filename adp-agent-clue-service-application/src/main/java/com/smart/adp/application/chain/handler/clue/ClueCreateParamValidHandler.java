package com.smart.adp.application.chain.handler.clue;

import com.smart.adp.application.chain.context.ClueCreateContext;
import com.smart.adp.application.chain.handler.StopWatchHandler;
import com.smart.adp.application.dto.clue.AgentClueDTO;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.application.utils.CheckParamUtils;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 线索创建参数校验处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/1
 */
@Component
public class ClueCreateParamValidHandler extends StopWatchHandler<ClueCreateContext> {

    @Override
    protected void doHandle(ClueCreateContext ctx) {
        AgentClueDTO agentClueDTO = ctx.getReqDto();
        CheckParamUtils.checkSaveClueParam(agentClueDTO);
        AgentClueSaveDTO param = AgentClueDTO.convert(agentClueDTO);
        ctx.setSaveDTO(param);
    }
}
