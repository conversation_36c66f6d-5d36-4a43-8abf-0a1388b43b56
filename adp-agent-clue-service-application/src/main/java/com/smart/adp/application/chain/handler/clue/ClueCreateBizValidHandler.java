package com.smart.adp.application.chain.handler.clue;

import cn.hutool.json.JSONUtil;
import com.smart.adp.application.chain.context.ClueCreateContext;
import com.smart.adp.application.chain.handler.StopWatchHandler;
import com.smart.adp.application.constant.CommonConstant;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.application.utils.CheckParamUtils;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.entity.base.AgentEmployee;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.entity.clue.ActivityInfo;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.enums.ClueMessageEnum;
import com.smart.adp.domain.enums.ClueStatusEnum;
import com.smart.adp.domain.enums.EmployeeStatusEnum;
import com.smart.adp.domain.gateway.clue.ActivityGateway;
import com.smart.adp.domain.gateway.clue.AgentEmployeeGateway;
import com.smart.adp.domain.gateway.clue.ClueDlrGateway;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.infrastructure.utils.RedisUtil;
import com.smart.tools.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *  线索创建业务校验处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/1
 */
@Component
@Slf4j
public class ClueCreateBizValidHandler extends StopWatchHandler<ClueCreateContext> {

    @Autowired
    private ClueDlrGateway clueDlrGateway;

    @Autowired
    private ActivityGateway activityGateway;

    @Autowired
    private AgentEmployeeGateway agentEmployeeGateway;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    protected void doHandle(ClueCreateContext ctx) {
        AgentClueSaveDTO saveDTO = ctx.getSaveDTO();

        // ADP自建线索特殊校验
        CheckParamUtils.checkADPCreateClue(saveDTO);

        // 校验线索是否存在
        handelClueExist(saveDTO);

        // 线索回访人员校验，回访人员ID对应员工信息表的userId
        handelReviewPerson(saveDTO, ctx.getUserInfo());

        // 二维码市场留资校验
        handelActivityQRCheck(saveDTO);
    }

    /**
     * 二维码市场留资校验
     * @param saveDTO
     */
    private void handelActivityQRCheck(AgentClueSaveDTO saveDTO) {
        if (!CommonConstant.SHARE_SWITCH_ON.equals(saveDTO.getFlag())) {
            return;
        }
        Date time = new Date();
        ActivityInfo activityInfo = activityGateway.findActivityData(saveDTO.getActivityId(), time, "DEVELOP", "1", "2", "1");
        if (Objects.isNull(activityInfo)) {
            String msg = String.format("当前活动已过期,二维码已失效");
            throw new BusinessException(RespCode.FAIL.getCode(), msg);
        }
    }

    /**
     * 线索回访人员校验，回访人员ID对应员工信息表的userId
     *
     * @param saveDTO
     * @param userInfo
     */
    private void handelReviewPerson(AgentClueSaveDTO saveDTO, UserBusiEntity userInfo) {
        //当前线索创建人如果是产品专家默认回访人员就是本人，店长角色需要指定回访人员
        if (UserUtil.isProductExpert(userInfo.getStationId())) {
            saveDTO.setReviewPersonId(userInfo.getUserID());
            saveDTO.setReviewPersonName(userInfo.getEmpName());
        }
        if (StringUtils.isEmpty(saveDTO.getReviewPersonId())) {
            return;
        }
        //获取员工信息
        String reviewPersonId = saveDTO.getReviewPersonId();
        AgentEmployee agentEmployee = getEmployeeWithCache(reviewPersonId, userInfo);
        //校验回访员工在职状态
        validateEmployeeStatus(agentEmployee, saveDTO.getPhone());
    }

    /**
     * 验证员工在职状态
     */
    private void validateEmployeeStatus(AgentEmployee employee, String phone) {
        if (!EmployeeStatusEnum.WORKING.getCode().equals(employee.getUserStatus()) && !EmployeeStatusEnum.WORKING.getDesc().equals(employee.getUserStatus())) {
            String msg = String.format("线索下发手机号：%s, 回访人员ID:%s 状态异常（当前状态：%s）", phone, employee.getUserId(),
                    employee.getUserStatus());
            log.warn(msg);
            throw new BusinessException(RespCode.FAIL.getCode(), msg);
        }
    }

    /**
     * 通过缓存获取员工信息（缓存24小时）
     */
    private AgentEmployee getEmployeeWithCache(String employeeId, UserBusiEntity userInfo) {
        String cacheKey = CommonConstant.CACHE_PREFIX + userInfo.getDlrCode() + ":" + employeeId;
        String cachedValue = (String) redisUtil.get(cacheKey);
        // 缓存命中
        if (StringUtils.isNotEmpty(cachedValue)) {
            return JSONUtil.toBean(cachedValue, AgentEmployee.class);
        }

        // 缓存未命中，查询数据库
        List<String> statusList = Collections.singletonList(EmployeeStatusEnum.WORKING.getCode());
        AgentEmployee employee = agentEmployeeGateway.findOne(employeeId, statusList);

        // 缓存空值防止穿透（设置较短过期时间）
        if (employee == null) {
            redisUtil.set(cacheKey, "", 60, TimeUnit.SECONDS);
            log.warn("Employee not found in DB, id: {}", employeeId);
            throw new BusinessException(RespCode.FAIL.getCode(), "回访人员不存在,请稍后重试！");
        }

        // 缓存有效数据
        redisUtil.set(cacheKey, JSONUtil.toJsonStr(employee), 24 * 60 * 60, TimeUnit.SECONDS);
        return employee;
    }

    /**
     * 校验线索是否存在
     *
     * @param saveDTO
     */
    private void handelClueExist(AgentClueSaveDTO saveDTO) {
        // 查询leads是否存在
        SacClueInfoDlr sacClueInfoDlr = clueDlrGateway.findClueByPhone(saveDTO.getPhone());
        if (Objects.nonNull(sacClueInfoDlr)) {
            // 线索数据不存在
            throw new BusinessException(RespCode.FAIL.getCode(), ClueMessageEnum.CLUE_BASE_DLR_05.getDesc());
        }

        // leads不存在获取csc数据
        SacAllClueInfoDlr allClueInfoDlr = clueDlrGateway.findAllClueByPhone(saveDTO.getPhone());
        if (Objects.isNull(allClueInfoDlr)) {
            // 线索数据不存在直接创建
            return;
        }

        // 存在则判断是否为战败状态，是战败设置战败激活
        saveDTO.setExistDefeatClue(ClueStatusEnum.isDefeatedClue(allClueInfoDlr.getStatusCode()));

        // todo 线索状态不为10则提示线索数据状态异常 or 直接战败激活？
    }
}
