package com.smart.adp.application.assembler.base;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.domain.valueObject.base.CarTypeInfoVO;
import com.smart.adp.domain.valueObject.base.SmallCarTypeInfo;

import java.util.ArrayList;
import java.util.List;

public class CarTypeVOAssembler implements Assembler<List<SmallCarTypeInfo>, List<CarTypeInfoVO>> {

    /**
     * 装配 请实现装配过程
     *
     * @param source 源
     * @param target 目标
     */
    @Override
    public List<CarTypeInfoVO> assemble(List<SmallCarTypeInfo> source, Class<List<CarTypeInfoVO>> target) {
        if (CollectionUtil.isEmpty(source)) {
            return null;
        }
        List<CarTypeInfoVO> resultList = new ArrayList<>();
        source.forEach(item -> {
            CarTypeInfoVO carTypeInfoVO = new CarTypeInfoVO();
            carTypeInfoVO.setCarTypeCode(item.getCarTypeCode());
            carTypeInfoVO.setCarTypeCN(item.getCarTypeCodeCN());
            carTypeInfoVO.setCarTypeDesc(item.getCarTypeCodeDesc());
            resultList.add(carTypeInfoVO);
        });
        return resultList;
    }
}