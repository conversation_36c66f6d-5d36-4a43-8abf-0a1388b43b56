package com.smart.adp.application.dto.clue;

import com.mybatisflex.core.paginate.Page;
import com.smart.adp.application.common.PageDTO;
import com.smart.adp.application.utils.LocalDateTimeUtil;
import com.smart.adp.domain.bo.clue.SacOnecustInfoEventBO;
import com.smart.adp.domain.bo.clue.SacOnecustResumeBO;
import com.smart.adp.domain.bo.clue.UscMdmVirtualRecordBO;
import com.smart.adp.domain.bo.drive.SacTestDriveSheetBO;
import com.smart.adp.domain.common.constants.BizConstants;
import com.smart.adp.domain.enums.EventTypeEnum;
import com.smart.adp.domain.enums.InteractDataSearchTypeEnum;
import com.smart.adp.domain.enums.InteractDataTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * @Description: 查询互动数据入参
 * @Author: rik.ren
 * @Date: 2025/3/13 13:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "查询互动数据入参")
public class InteractDataDTO extends PageDTO {

    /**
     * 线索 ID
     */
    @Schema(description = "客户ID", required = true)
    @NotEmpty(message = "客户 ID 不能为空")
    private String custId;
    /**
     * 手机号
     */
    @Schema(description = "手机号", required = true)
    @NotEmpty(message = "手机号不能为空")
    private String phone;

    /**
     * 创建开始时间
     */
    @Schema(description = "创建开始时间")
    private LocalDateTime beginTime;

    /**
     * 创建结束时间
     */
    @Schema(description = "创建结束时间")
    private LocalDateTime endTime;

    /**
     * 搜索关键字
     */
    @Schema(description = "搜索关键字")
    private String searchContent;


    /**
     * 数据类型
     *
     * @see InteractDataTypeEnum
     */
    @Schema(description = "数据类型，全部：all，跟进记录：review，用户行为：event")
    private String dataType;

    /**
     * 数据类型
     *
     * @see EventTypeEnum
     */
    @Schema(description = "客户行为类型，关键事件：crux，其他活跃事件：other")
    private String eventType;

    /**
     * 场景码，对应字典数据中的ADP_CLUE_001
     */
    @Schema(description = "场景码，对应字典数据中的ADP_CLUE_001，多选请用英文,分割，如果选择全部就传all")
    private String senceCode;

    public String getEventType() {
        return EventTypeEnum.ALL.getCode().equals(eventType) ? null : eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = EventTypeEnum.ALL.getCode().equals(eventType) ? null : eventType;
        ;
    }

    public String getSenceCode() {
        return "all".equals(senceCode) ? null : senceCode;
    }

    public void setSenceCode(String senceCode) {
        this.senceCode = "all".equals(senceCode) ? null : senceCode;
    }

    /**
     * 查询类型，0默认列表，1主动搜索
     * 默认搜索就是进入详情自带的搜索
     * 主动搜索就是用户选择条件搜索
     *
     * @see InteractDataSearchTypeEnum
     */
    @Schema(description = "查询类型，可以不传默认是0，0默认列表，1主动搜索")
    private Integer searchType = 0;

    public SacOnecustInfoEventBO buildSacOnecustInfoEventBO() {
        SacOnecustInfoEventBO eventBO = new SacOnecustInfoEventBO(new Page<>(getPageIndex(), getPageSize()));
        eventBO.setMobile(getPhone());
        if (StringUtils.isNotEmpty(eventType) && EventTypeEnum.CRUX.getCode().equals(eventType)) {
            eventBO.setListEventCode(BizConstants.OTHER_EVENT_LIST);
        } else if (StringUtils.isNotEmpty(eventType) && EventTypeEnum.OTHER.getCode().equals(eventType)) {
            eventBO.setListEventCode(BizConstants.CRUX_EVENT_LIST);
        }
        return eventBO;
    }

    public SacTestDriveSheetBO buildSacTestDriveSheetBO() {
        SacTestDriveSheetBO testDriveSheetBO = new SacTestDriveSheetBO(new Page<>(getPageIndex(),
                getPageSize()));
        testDriveSheetBO.setCustomerId(getCustId());
        return testDriveSheetBO;
    }

    public SacOnecustResumeBO buildSacOnecustResumeBO() {
        SacOnecustResumeBO resumtBoObj = new SacOnecustResumeBO(new Page<>(getPageIndex(), getPageSize()));
        resumtBoObj.setCustId(getCustId());
        resumtBoObj.setCreateDateBegin(getBeginTime());
        resumtBoObj.setCreateDateEnd(LocalDateTimeUtil.handleEnd(getEndTime()));
        resumtBoObj.setListSenceCode(StringUtils.isNotBlank(getSenceCode()) ? Arrays.asList(getSenceCode().split(",")) : null);
        resumtBoObj.setSearchContent(getSearchContent());
        return resumtBoObj;
    }

    public UscMdmVirtualRecordBO buildUscMdmVirtualRecordBO() {
        UscMdmVirtualRecordBO vrBO = new UscMdmVirtualRecordBO(new Page<>(getPageIndex(),
                getPageSize()));
        vrBO.setCustId(getCustId());
        vrBO.setConsumerMobile(getPhone());
        return vrBO;
    }
}
