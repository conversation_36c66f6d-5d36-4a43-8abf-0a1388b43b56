package com.smart.adp.application.event;

import cn.hutool.json.JSONUtil;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.domain.entity.message.CreateClueMessage;
import com.smart.adp.infrastructure.publisher.AgentDlrCreateCluePublisher;
import com.smart.tools.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * date 2025/3/10 22:41
 * @description 创建线索通知CDP异步事件
 **/
@Slf4j
@Component
public class AgentClueCreateEventListener implements ApplicationListener<AgentClueCreateEvent> {

    @Autowired
    private AgentDlrCreateCluePublisher agentDlrCreateCluePublisher;

    @Override
    @Async("clueExecutor")
    public void onApplicationEvent(AgentClueCreateEvent event) {
        if (event == null) {
            log.warn("创建线索通知CDP异步事件监听，event is null");
            return;
        }

        AgentClueSaveDTO dto = event.getAgentClueSaveDTO();
        if (dto == null) {
            log.warn("创建线索通知CDP异步事件监听，agentClueSaveDTO is null in event.");
            return;
        }

        try {
            CreateClueMessage createClueMessage = BeanUtils.copyProperties(event.getAgentClueSaveDTO(), CreateClueMessage.class);
            String jsonMessage = JSONUtil.toJsonStr(createClueMessage);
            agentDlrCreateCluePublisher.sendClueCreateMessage(jsonMessage);
            log.info("Clue message sent for phone: {}", createClueMessage.getPhone());
        } catch (Exception e) {
            log.error("Failed to process event for phone: {}", dto.getPhone(), e);
        }
    }
}
