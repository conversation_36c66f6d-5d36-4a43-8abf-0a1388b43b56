package com.smart.adp.application.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.smart.adp.application.dto.clue.ReviewDisTributeDTO;
import com.smart.adp.domain.entity.clue.ClueAllocatingRecord;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.gateway.base.ClueAllocatingRecordGateway;
import com.smart.adp.infrastructure.feign.OrcFeignService;
import com.smart.adp.infrastructure.feign.request.OrderPeInfoDTO;
import com.smart.adp.infrastructure.feign.request.UpdateOrderPeInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * date 2025/4/20 11:33
 * @description 店端线索分配日志
 **/
@Slf4j
@Component
public class ClueDisTributeLogEventListener implements ApplicationListener<ClueDisTributeLogEvent> {

    @Autowired
    private ClueAllocatingRecordGateway clueAllocatingRecordGateway;

    @Autowired
    private OrcFeignService orcFeignService;

    @Override
    @Async("clueExecutor")
    public void onApplicationEvent(ClueDisTributeLogEvent event) {
        if (event == null) {
            log.warn("线店端线索分配日志异步事件监听，event is null");
            return;
        }
        if (CollUtil.isEmpty(event.getDisTributeDTOList())) {
            log.warn("店端线索分配日志异步事件监听，disTributeDTOList is null in event.");
            return;
        }

        // 提前时间换空间，提升性能
        // 使用Stream初始化DTO映射
        Map<String, ReviewDisTributeDTO> reviewDisTributeDTOMap = event.getDisTributeDTOList().stream()
                .collect(Collectors.toMap(ReviewDisTributeDTO::getReviewId, Function.identity()));

        //查询活跃线索表
        List<ClueAllocatingRecord> clueAllocatingRecordList = new ArrayList<>();

        // 处理有效线索列表
        List<String> orderPeInfoDTOList = new ArrayList<>();
        processClueList(event.getQuerySacClueInfoDlrList(), reviewDisTributeDTOMap, clueAllocatingRecordList, orderPeInfoDTOList);

        // 处理战败线索列表
        processClueList(event.getQuerySacAllClueInfoDlrList(), reviewDisTributeDTOMap, clueAllocatingRecordList, orderPeInfoDTOList);

        // 批量保存记录
        if (CollUtil.isNotEmpty(clueAllocatingRecordList)) {
            clueAllocatingRecordGateway.batchSaveClueAllocatingRecord(clueAllocatingRecordList);
        }

        // 线索分配调用orc更新订单销售专员
        if (CollUtil.isNotEmpty(orderPeInfoDTOList)) {
            UpdateOrderPeInfoDTO updateOrderPeInfoDTO = new UpdateOrderPeInfoDTO();
            updateOrderPeInfoDTO.setCustIdList(orderPeInfoDTOList);
            updateOrderPeInfoDTO.setModifier(event.getDisTributeDTOList().get(0).getAssignPersonId());
            updateOrderPeInfoDTO.setModifyName(event.getDisTributeDTOList().get(0).getAssignPersonName());
            updateOrderPeInfoDTO.setPeId(event.getDisTributeDTOList().get(0).getReviewPersonId());
            try {
                log.info("线索分配调用orc更新订单销售专员信息,updateOrderPeInfoDTO={}", JSONUtil.toJsonStr(updateOrderPeInfoDTO));
                orcFeignService.updateOrderPeInfo(updateOrderPeInfoDTO);
            } catch (Exception e) {
                log.error("线索分配调用orc更新订单销售专员信息异常，updateOrderPeInfoDTO={}", JSONUtil.toJsonStr(updateOrderPeInfoDTO));
            }
        }
    }

    /**
     * 统一处理线索列表的方法
     */
    private void processClueList(
            List<?> clueList,
            Map<String, ReviewDisTributeDTO> dtoMap,
            List<ClueAllocatingRecord> resultList,
            List<String> orderPeInfoDTOList
    ) {
        if (CollUtil.isEmpty(clueList)) return;

        clueList.forEach(clue -> {
            if (clue instanceof SacClueInfoDlr) {
                resultList.add(buildRecord((SacClueInfoDlr) clue, dtoMap));
                orderPeInfoDTOList.add(buildOrderPeInfoDTO((SacClueInfoDlr) clue));
            } else if (clue instanceof SacAllClueInfoDlr) {
                resultList.add(buildRecord((SacAllClueInfoDlr) clue, dtoMap));
            }
        });
    }

    /**
     * 构建分配记录公共方法（SacClueInfoDlr版本）
     */
    private ClueAllocatingRecord buildRecord(SacClueInfoDlr clue, Map<String, ReviewDisTributeDTO> dtoMap) {
        return buildBaseRecord(
                clue.getServerOrder(),
                clue.getCustId(),
                clue.getCustName(),
                clue.getPhone(),
                clue.getDlrCode(),
                clue.getDlrShortName(),
                clue.getReviewPersonId(),
                clue.getReviewPersonName(),
                clue.getReviewId(),
                dtoMap
        );
    }

    private String buildOrderPeInfoDTO(SacClueInfoDlr clue) {
        return clue.getCustId();
    }

    /**
     * 构建分配记录公共方法（SacAllClueInfoDlr版本）
     */
    private ClueAllocatingRecord buildRecord(SacAllClueInfoDlr clue, Map<String, ReviewDisTributeDTO> dtoMap) {
        return buildBaseRecord(
                clue.getServerOrder(),
                clue.getCustId(),
                clue.getCustName(),
                clue.getPhone(),
                clue.getDlrCode(),
                clue.getDlrShortName(),
                clue.getReviewPersonId(),
                clue.getReviewPersonName(),
                clue.getReviewId(),
                dtoMap
        );
    }

    /**
     * 基础记录构建方法
     */
    private ClueAllocatingRecord buildBaseRecord(
            String serverOrder,
            String custId,
            String custName,
            String phone,
            String oldDlrCode,
            String oldDlrShortName,
            String oldSalespersonId,
            String oldSalespersonName,
            String reviewId,
            Map<String, ReviewDisTributeDTO> dtoMap
    ) {
        ClueAllocatingRecord record = new ClueAllocatingRecord();
        // 设置基础字段
        record.setRecordId(UUID.randomUUID().toString());
        record.setServerOrder(serverOrder);
        record.setCustId(custId);
        record.setCustName(custName);
        record.setCustPhone(phone);
        record.setOldDlrCode(oldDlrCode);
        record.setOldDlrShortName(oldDlrShortName);
        record.setOldSalespersonId(oldSalespersonId);
        record.setOldSalespersonName(oldSalespersonName);
        record.setColumn1("0");

        // 处理经销商分配信息
        Optional.ofNullable(dtoMap.get(reviewId)).ifPresent(dto -> {
            record.setNewDlrCode(dto.getDlrCode());
            record.setNewDlrShortName(dto.getDlrShortName());
            record.setNewSalespersonId(dto.getReviewPersonId());
            record.setNewSalespersonName(dto.getReviewPersonName());
            record.setCreator(dto.getAssignPersonId());
            record.setCreatedName(dto.getAssignPersonName());
            record.setModifier(dto.getAssignPersonId());
            record.setModifyName(dto.getAssignPersonName());
        });

        // 设置审计字段
        LocalDateTime now = LocalDateTime.now();
        record.setCreatedDate(now);
        record.setLastUpdatedDate(now);
        record.setUpdateControlId(UUID.randomUUID().toString());

        return record;
    }
}
