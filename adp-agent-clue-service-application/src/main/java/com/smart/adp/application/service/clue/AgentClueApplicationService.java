package com.smart.adp.application.service.clue;

import com.smart.adp.application.dto.clue.*;
import com.smart.adp.application.vo.clue.*;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.enums.ClueQueryTypeEnum;
import com.smart.adp.domain.valueObject.base.CompetitiveCarModelVO;
import com.smart.adp.domain.valueObject.clue.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2025/3/3 10:16
 * @description 店端线索application 服务接口
 **/
public interface AgentClueApplicationService {

    /**
     * 代理商线索保存application
     *
     * @param param
     * @return
     */
    ClueDlrSaveVO agentDlrClueSave(AgentClueDTO param);

    PageVO<ClueDlrListVO> page(ClueDlrListDTO dto);

    ClueDlrListVO get(String clueId, ClueDlrListDTO dto);

    PageVO<ClueDlrSearchVO> search(ClueDlrSearchDTO dto);

    List<ClueDlrStatisticsVO> statistics(Set<ClueQueryTypeEnum> types);

    /**
     * 查询线索详情
     *
     * @param dto
     * @return
     */
    ClueDlrDetailRspVO detail(ClueDlrDetailDTO dto);

    /**
     * 查询线索详情，多线程
     *
     * @param dto
     * @return
     */
    ClueDlrDetailRspVO detailExecutor(ClueDlrDetailDTO dto) throws Exception;

    /**
     * 更新线索内容，按模块更新
     *
     * @param dto
     * @return
     */
    Boolean modifyDlrInfo(ClueDlrModifyDTO dto);

    /**
     * 查询线索7天30天的活跃信息
     *
     * @param dto
     * @return
     */
    ClueActiveInfoRspVO queryActiveInfo(ClueActiveInfoDTO dto);

    List<CalenderCountVO> overdueCountCalender(LocalDateTime startTime, LocalDateTime endTime);

    List<CompetitiveCarModelVO> competitiveCarList();

    List<AgentClueSourceVO> agentClueSourceList();

    ClueDlrCheckVO agentDlrClueCheck(AgentClueCheckDTO param);

    /**
     * 保存虚拟外呼摘要内容
     *
     * @param param
     * @return
     */
    Boolean saveAbstractContent(AbstractContentDTO param);

    /**
     * 查询互动数据
     *
     * @param param
     * @return
     */
    DomainPage<ClueInteractRspVO> queryInteractDataMain(InteractDataDTO param) throws Exception;

    /**
     * 查询互动数据
     *
     * @param param
     * @return
     */
    DomainPage<ClueInteractRspVO> queryInteractDataDefault(InteractDataDTO param);

    /**
     * 查询互动数据，多线程
     *
     * @param param
     * @return
     */
    DomainPage<ClueInteractRspVO> queryInteractDataDefaultExecutor(InteractDataDTO param) throws Exception;

    /**
     * 详情页用户旅程
     *
     * @param param
     * @return
     */
    List<ClueEventFlowRspVO> queryUserEventFlow(ClueEventFlowDTO param);

    /**
     * 查询回访内容
     *
     * @param param
     * @return
     */
    SacReviewRspVO queryReviewInfo(ClueReviewDTO param);

    /**
     * 用户旅程 流水修复
     *
     * @param dto 入参
     * @return 修复线索数
     */
    int userJourneysFlowFix(UserJourneysFixDTO dto);

    /**
     * 用户旅程 阶段修复
     *
     * @param dto 入参
     * @return 修复线索数
     */
    int userJourneysStageFix(UserJourneysFixDTO dto);

    /**
     * ES fix
     *
     * @param dto 入参
     * @return 修复线索数
     */
    int esFix(UserJourneysFixDTO dto);

    /**
     * ES 线索初始化
     *
     * @param dto 入参
     */
    void initClueES(UserJourneysFixDTO dto);

    /**
     * ES 履历初始化
     *
     * @param dto 入参
     */
    void initResumeES(UserJourneysFixDTO dto);

    /**
     * 查询产品专家下的分组，和分组的custId
     *
     * @param param
     * @return
     */
    List<ClueUserGroupVO> queryUserGroupByCreate(QueryClueGroupDTO param);

    /**
     * 潜客信息前置校验
     *
     * @param param
     * @return
     */
    ClueDlrCheckVO agentDlrCustCheck(AgentClueCheckDTO param);

    /**
     * 店端线索店长分配
     *
     * @param param
     * @return
     */
    List<ClueDistributeVO> agentDlrDistribute(AgentClueDisTributeDTO param);

    /**
     * 根据手机号码精确查询指定线索
     *
     * @param param
     * @return
     */
    ClueDlrRspVO queryClueInfoDlr(ClueDlrInfoQueryDTO param);

    /**
     * 修改客户信息
     *
     * @param param
     * @return
     */
    Boolean modifyClueCustInfo(ModifyClueCustDTO param);

    /**
     * 获取 tips
     *
     * @param type 查询类型
     * @return com.smart.adp.domain.valueObject.clue.ClueTipsVO
     */
    ClueTipsVO getTips(ClueQueryTypeEnum type);
}
