package com.smart.adp.application.vo.clue;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.domain.bo.clue.CustEventFlowBO;
import com.smart.adp.domain.enums.CustEventFlowEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 线索用户旅程返回VO
 * @Author: rik.ren
 * @Date: 2025/3/15 18:11
 **/
@ToString
@EqualsAndHashCode
@Schema(description = "线索用户旅程返回VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClueEventFlowRspVO implements Serializable {
    /**
     * 触发时间
     */
    @Schema(description = "触发时间")
    private LocalDateTime triggerTime;
    /**
     * 事件名称
     */
    @Schema(description = "事件名称")
    private String eventName;
    /**
     * 事件类型
     */
    @Schema(description = "事件类型，1了解，2到店，3试驾，4下定，5交车，6战败")
    private Integer eventType;

    /**
     * 线索id
     */
    @Schema(description = "线索id")
    private String custId;

    public static ClueEventFlowRspVO conventBO(CustEventFlowBO boParam) {
        ClueEventFlowRspVO clueEventFlowRspVO = new ClueEventFlowRspVO();
        clueEventFlowRspVO.setTriggerTime(boParam.getEventTime());
        clueEventFlowRspVO.setEventName(CustEventFlowEnum.getByCode(boParam.getStage()).getHandlerName());
        clueEventFlowRspVO.setEventType(boParam.getStage());
        clueEventFlowRspVO.setCustId(boParam.getCustId());
        return clueEventFlowRspVO;

    }

    public static List<ClueEventFlowRspVO> conventBO(List<CustEventFlowBO> boList) {
        if (CollectionUtil.isEmpty(boList)) {
            return null;
        }
        List<ClueEventFlowRspVO> clueEventFlows = boList.stream()
                .map(ClueEventFlowRspVO::conventBO)
                .collect(Collectors.toList());
        Collections.sort(clueEventFlows, Comparator.comparing(ClueEventFlowRspVO::getTriggerTime));
        return clueEventFlows;
    }
}
