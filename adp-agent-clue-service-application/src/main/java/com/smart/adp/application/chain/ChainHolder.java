package com.smart.adp.application.chain;

import com.smart.adp.application.chain.context.ClueCreateContext;
import com.smart.adp.application.chain.handler.Handler;
import com.smart.adp.application.chain.handler.clue.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/1
 */
@Component
public class ChainHolder {

    @Autowired
    private ClueCreateParamValidHandler clueCreateParamValidHandler;

    @Autowired
    private ClueCreateBizValidHandler clueCreateBizValidHandler;

    @Autowired
    private ClueCreateAssignHandler clueCreateAssignHandler;

    @Autowired
    private ClueCreateWriteHandler clueCreateWriteHandler;

    @Autowired
    private ClueCreateNotifyHandler clueCreateNotifyHandler;

    @PostConstruct
    public void init() {
        clueCreateParamValidHandler.setNext(clueCreateBizValidHandler)
                .setNext(clueCreateAssignHandler)
                .setNext(clueCreateWriteHandler)
                .setNext(clueCreateNotifyHandler);
    }

    public Handler<ClueCreateContext> getClueCreateChain() {
        return clueCreateParamValidHandler;
    }

}
