package com.smart.adp.application.event;

import com.smart.adp.application.dto.clue.ReviewDisTributeDTO;
import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/16 20:47
 * @description 店端线索分配事件
 **/
@Getter
@ToString
public class AgentClueDistributeEvent extends ApplicationEvent {

    List<ReviewDisTributeDTO> reviewDisTributeDTOList;

    public AgentClueDistributeEvent(Object source, List<ReviewDisTributeDTO> reviewDisTributeDTOList) {
        super(source);
        this.reviewDisTributeDTOList = reviewDisTributeDTOList;
    }
}
