package com.smart.adp.application.dto.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/14 17:27
 * @description 修改线索客户信息DTO
 **/
@Data
@Schema(description = "修改线索客户信息DTO")
public class ModifyClueCustDTO implements Serializable {

    @Schema(description = "修改场景入口，1:线索详情页修改，2:线索跟进修改客户信息")
    @NotBlank(message = "修改场景入口不能为空")
    private String resource;

    @Schema(description = "是否为战败线索,默认非战败 false")
    private Boolean defeatFlag = Boolean.FALSE;

    @Schema(description = "客户姓名")
    @NotBlank(message = "客户姓名不能为空")
    private String custName;

    @Schema(description = "性别")
    private String genderName;

    @Schema(description = "性别编码")
    private String genderCode;

    @Schema(description = "客户手机号")
    @NotBlank(message = "客户手机号不能为空")
    private String phone;

    @Schema(description = "回访任务id")
    private String reviewId;
}
