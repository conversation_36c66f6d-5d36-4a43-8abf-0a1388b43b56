package com.smart.adp.application.vo.clue;

import com.smart.adp.application.assembler.AssemblerFactory;
import com.smart.adp.application.assembler.clue.ClueDlrVOAssembler;
import com.smart.adp.domain.bo.clue.SacClueInfoDlrBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 线索返回VO
 * @Author: rik.ren
 * @Date: 2025/4/23 16:37
 **/
@Data
public class ClueDlrRspVO {

    @Schema(description = "客户ID")
    private String custId;

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "联系号码")
    private String phone;

    @Schema(description = "意向车型名称")
    private String intenCarTypeName;

    @Schema(description = "最低一级的信息来源名称")
    private String channelName;

    @Schema(description = "意向车型编码")
    private String intenCarTypeCode;

    @Schema(description = "一级信息来源编码")
    private String infoChanMcode;

    @Schema(description = "一级信息来源名称")
    private String infoChanMname;

    @Schema(description = "二级信息来源编码")
    private String infoChanDcode;

    @Schema(description = "二级信息来源名称")
    private String infoChanDname;

    @Schema(description = "意向级别编码")
    private String intenLevelCode;

    @Schema(description = "性别编码")
    private String genderCode;

    @Schema(description = "扩展字段5 如：\"column5\": \"Hot\" 业务含义字段 businessHeatName")
    private String column5;

    @Schema(description = "扩展字段11，表示特别关注，1：特别关注 业务含义字段 isSpecial")
    private String column11;

    @Schema(description = "线索单号")
    private String serverOrder;

    @Schema(description = "扩展字段18 - 线索阶段")
    private String column18;

    @Schema(description = "线索所在门店")
    private String dlrCode;

    @Schema(description = "线索状态")
    private String statusCode;


    public static ClueDlrRspVO convent(SacClueInfoDlrBO clue) {
        return AssemblerFactory.getInstance().convert(ClueDlrVOAssembler.class, clue, ClueDlrRspVO.class);
    }
}
