package com.smart.adp.application.chain.handler.clue;

import com.smart.adp.application.chain.context.ClueCreateContext;
import com.smart.adp.application.chain.handler.StopWatchHandler;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 线索创建通知处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/3
 */
@Component
public class ClueCreateNotifyHandler extends StopWatchHandler<ClueCreateContext> {

    @Override
    protected void doHandle(ClueCreateContext ctx) {
        // 1. 发布CDP通知事件
        publishCdpEvent(ctx);

        // 2. 发布客户旅程事件
        publishCustomerJourneyEvent(ctx);

        // 3. 其他通知
        sendOtherNotifications(ctx);
    }

    private void publishCdpEvent(ClueCreateContext ctx) {

    }

    private void publishCustomerJourneyEvent(ClueCreateContext ctx) {

    }

    private void sendOtherNotifications(ClueCreateContext ctx) {
        // 实现其他通知逻辑
    }
}