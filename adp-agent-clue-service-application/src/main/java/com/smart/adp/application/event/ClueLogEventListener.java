package com.smart.adp.application.event;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.domain.entity.clue.SacClueInfoDlrLog;
import com.smart.adp.domain.gateway.clue.ClueDlrGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * date 2025/3/10 22:41
 * @description 线索创建日志异步事件监听
 **/
@Slf4j
@Component
public class ClueLogEventListener implements ApplicationListener<ClueLogEvent> {

    @Autowired
    private ClueDlrGateway clueDlrGateway;

    @Override
    @Async("clueExecutor")
    public void onApplicationEvent(ClueLogEvent event) {
        if (event == null) {
            log.warn("线索创建日志异步事件监听，event is null");
            return;
        }
        if (StrUtil.isBlank(event.getPhone())) {
            log.warn("线索创建日志异步事件监听，phone is null in event.");
            return;
        }

        SacClueInfoDlrLog sacClueInfoDlrLog = new SacClueInfoDlrLog();
        sacClueInfoDlrLog.setPhone(event.getPhone());
        sacClueInfoDlrLog.setSystemParam(JSONUtil.toJsonStr(event.getSaveDTO()));
        sacClueInfoDlrLog.setSystemRecord(event.getSystemRecord());
        sacClueInfoDlrLog.setSystemSource(event.getSystemSource());
        sacClueInfoDlrLog.setOemCode(event.getUserInfo().getOemCode());
        sacClueInfoDlrLog.setOemId(event.getUserInfo().getOemID());
        sacClueInfoDlrLog.setGroupId(event.getUserInfo().getGroupID());
        sacClueInfoDlrLog.setGroupCode(event.getUserInfo().getGroupCode());
        sacClueInfoDlrLog.setCreator(event.getUserInfo().getUserID());
        sacClueInfoDlrLog.setCreatedName(event.getUserInfo().getEmpName());
        sacClueInfoDlrLog.setModifier(event.getUserInfo().getUserID());
        sacClueInfoDlrLog.setModifierName(event.getUserInfo().getEmpName());
        sacClueInfoDlrLog.setLastUpdatedDate(LocalDateTime.now());
        sacClueInfoDlrLog.setCreatedDate(LocalDateTime.now());
        sacClueInfoDlrLog.setId(UUID.randomUUID().toString());
        sacClueInfoDlrLog.setIsEnable("1");
        sacClueInfoDlrLog.setSdpOrgId("1");
        sacClueInfoDlrLog.setSdpUserId("1");
        sacClueInfoDlrLog.setUpdateControlId(UUID.randomUUID().toString());

        clueDlrGateway.saveClueLog(sacClueInfoDlrLog);
    }
}
