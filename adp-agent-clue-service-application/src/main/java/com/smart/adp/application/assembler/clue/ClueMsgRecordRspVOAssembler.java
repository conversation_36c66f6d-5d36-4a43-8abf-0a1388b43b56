package com.smart.adp.application.assembler.clue;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.vo.clue.ClueMsgRecordRspVO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.enums.ClueMessageTypeEnum;
import com.smart.adp.domain.valueObject.clue.SacClueMsgRecordVO;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/15 11:40
 * @description
 **/
@Slf4j
public class ClueMsgRecordRspVOAssembler implements Assembler<DomainPage<SacClueMsgRecordVO>, DomainPage<ClueMsgRecordRspVO>> {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yy/MM/dd");

    /**
     * 装配 请实现装配过程
     *
     * @param source 源
     * @param target 目标
     */
    @Override
    public DomainPage<ClueMsgRecordRspVO> assemble(DomainPage<SacClueMsgRecordVO> source, Class<DomainPage<ClueMsgRecordRspVO>> target) {
        if (ObjectUtil.isEmpty(source)) {
            return null;
        }
        return convert(source);
    }

    private DomainPage<ClueMsgRecordRspVO> convert(DomainPage<SacClueMsgRecordVO> param) {
        if (ObjectUtil.isEmpty(param)) {
            return null;
        }
        DomainPage<ClueMsgRecordRspVO> res = new DomainPage<>();
        List<ClueMsgRecordRspVO> records = new ArrayList<>(param.getRecords().size());
        LocalDateTime now = LocalDateTime.now();
        param.getRecords().forEach(record -> {
            ClueMsgRecordRspVO clueMsgRecordRspVO = new ClueMsgRecordRspVO();
            clueMsgRecordRspVO.setMessageId(record.getMessageId());
            clueMsgRecordRspVO.setIsRead(record.getIsRead());
            clueMsgRecordRspVO.setDlrCode(record.getDlrCode());
            clueMsgRecordRspVO.setPhone(record.getPhone());
            clueMsgRecordRspVO.setMessageType(record.getMessageType());
            clueMsgRecordRspVO.setMessageTypeName(ClueMessageTypeEnum.getByCode(record.getMessageType()).getDesc());
            clueMsgRecordRspVO.setBusiKeyvalue(record.getBusiKeyvalue());
            clueMsgRecordRspVO.setReceiveEmpId(record.getReceiveEmpId());
            clueMsgRecordRspVO.setMessageContent(record.getMessageContent());
            clueMsgRecordRspVO.setRelationBillId(record.getRelationBillId());
            clueMsgRecordRspVO.setExtendJson(record.getExtendJson());
            clueMsgRecordRspVO.setColumn1(record.getColumn1());
            clueMsgRecordRspVO.setColumn2(record.getColumn2());
            clueMsgRecordRspVO.setColumn3(record.getColumn3());
            clueMsgRecordRspVO.setColumn4(record.getColumn4());
            clueMsgRecordRspVO.setColumn5(record.getColumn5());
            clueMsgRecordRspVO.setCreatedDate(record.getCreatedDate());
            clueMsgRecordRspVO.setLastUpdatedDate(record.getLastUpdatedDate());
            clueMsgRecordRspVO.setTimeFrame(formatTimeFrame(record.getCreatedDate(), now));
            records.add(clueMsgRecordRspVO);
        });
        res.setRecords(records);
        res.setPageNumber(param.getPageNumber());
        res.setPageSize(param.getPageSize());
        res.setTotalCount(param.getTotalCount());
        return res;
    }

    /**
     * 获取时间范围
     *
     * @param createdDate   创建时间
     * @param referenceTime 参照时间
     * @return
     */
    private String formatTimeFrame(LocalDateTime createdDate, LocalDateTime referenceTime) {
        if (ObjectUtil.isEmpty(createdDate)) {
            return "";
        }
        try {
            Duration duration = Duration.between(createdDate, referenceTime);
            long minutes = duration.toMinutes();
            long hours = duration.toHours();
            if (minutes == 0) {
                // 1分钟以内按一分钟内计算
                return "1分钟内";
            }
            if (minutes >= 1 && minutes < 60) {
                // 1小时以内按分钟计算
                return minutes + "分钟前";
            } else if (hours < 24) {
                // 1小时以上-24小时以内，按小时计算
                return hours + "小时前";
            } else if (hours < 48) {
                // 24小时以上-48小时以内，记为"昨天"
                return "昨天";
            } else {
                // 48小时以上显示日期
                return createdDate.format(DATE_FORMATTER);
            }
        } catch (Exception e) {
            log.error("格式化时间范围失败 formatTimeFrame", e);
            return "";
        }
    }
}
