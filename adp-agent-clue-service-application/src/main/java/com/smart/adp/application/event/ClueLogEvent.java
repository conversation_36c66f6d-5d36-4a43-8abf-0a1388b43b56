package com.smart.adp.application.event;

import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * date 2025/3/10 22:32
 * @description
 **/
@Getter
@ToString
public class ClueLogEvent extends ApplicationEvent {

    private String phone;

    private String systemRecord;

    private String systemSource;

    private UserBusiEntity userInfo;

    private AgentClueSaveDTO saveDTO;

    public ClueLogEvent(Object s, String phone, AgentClueSaveDTO systemParam, String systemRecord, String systemSource, UserBusiEntity userInfo) {
        super(s);
        this.phone = phone;
        this.saveDTO = systemParam;
        this.systemRecord = systemRecord;
        this.systemSource = systemSource;
        this.userInfo = userInfo;
    }
}
