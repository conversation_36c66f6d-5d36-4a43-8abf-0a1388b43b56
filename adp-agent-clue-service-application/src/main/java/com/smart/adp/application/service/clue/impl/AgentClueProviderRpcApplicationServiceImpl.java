package com.smart.adp.application.service.clue.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.application.dto.clue.*;
import com.smart.adp.application.service.clue.AgentClueProviderRpcApplicationService;
import com.smart.adp.application.vo.clue.*;
import com.smart.adp.domain.bo.clue.*;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;
import com.smart.adp.domain.enums.DefeatFlagEnum;
import com.smart.adp.domain.model.clue.ClueAggregate;
import com.smart.adp.domain.service.clue.*;
import com.smart.adp.domain.strategy.modifyClue.ClueModifyStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * <AUTHOR>
 * date 2025/5/16 10:17
 * @description 店端线索application 服务接口，提供给RPC服务使用
 **/
@Slf4j
@Service
public class AgentClueProviderRpcApplicationServiceImpl implements AgentClueProviderRpcApplicationService {

    //region 【注入】

    @Autowired
    private IClueDlrService clueDlrService;

    @Autowired
    private ICustEventFlowService custEventFlowService;

    @Autowired
    private ISacOneCustRemarkService sacOneCustRemarkService;

    @Autowired
    private ClueModifyStrategyFactory strategyFactory;

    @Autowired
    private ClueAggregate clueAggregate;
    //endregion

    /**
     * 详情页用户旅程
     *
     * @param param
     * @return
     */
    @Override
    public List<ClueEventFlowRspVO> queryListUserEventFlow(ClueEventFlowDTO param) {
        CustEventFlowBO boParam = new CustEventFlowBO();
        boParam.setCustId(param.getCustId());
        boParam.setListCustId(param.getListCustId());
        boParam.setNeedClueRemark(param.getNeedClueRemark());
        List<CustEventFlowBO> listCustEventFlow = custEventFlowService.queryEventByCustIdLast(boParam);
        return ClueEventFlowRspVO.conventBO(listCustEventFlow);
    }

    /**
     * 查询线索扩展信息
     *
     * @param dto
     * @return
     */
    @Override
    public List<SacOneCustRemarkRspVO> queryClueRemark(ClueRemarkDTO dto) {
        if (CollectionUtil.isEmpty(dto.getListCustId())) {
            return null;
        }
        SacOneCustRemarkBO bo = new SacOneCustRemarkBO();
        bo.setListCustId(dto.getListCustId());
        List<SacOneCustRemark> sacOneCustRemarks = sacOneCustRemarkService.queryRemark(bo);
        return BeanUtil.copyToList(sacOneCustRemarks, SacOneCustRemarkRspVO.class);
    }

    /**
     * 更新意向车信息
     *
     * @param param
     * @return
     */
    @Override
    public String modifyIntentionCarForStartDriving(ModifyIntentionCarDTO param) {
        SacClueInfoDlrBO queryClueBo = new SacClueInfoDlrBO();
        queryClueBo.setCustId(param.getCustId());
        queryClueBo.setNewIntentionCar(param.getNewIntentionCar());
        queryClueBo.setDefeatFlag(DefeatFlagEnum.DEFEAT.getCode());
        String modifyIntentionCarResult = clueAggregate.modifyIntentionCarForStartDriving(queryClueBo);
        return modifyIntentionCarResult;
    }
}
