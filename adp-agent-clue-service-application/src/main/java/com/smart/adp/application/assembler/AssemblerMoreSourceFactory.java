package com.smart.adp.application.assembler;

/**
 * @Description: 多数据源的数据装配
 * @Author: rik.ren
 * @Date: 2025/3/7 11:00
 **/
public class AssemblerMoreSourceFactory {
    private static final AssemblerMoreSourceFactory INSTANCE = new AssemblerMoreSourceFactory();

    public static AssemblerMoreSourceFactory getInstance() {
        return INSTANCE;
    }

    /**
     * 执行装配过程
     *
     * @param assembler  装配器实例
     * @param targetType 目标类型
     * @param sources    源对象数组
     */
    public <T> T assemble(AssemblerMoreSource<T> assembler, Class<T> targetType, Object... sources) {
        return assembler.assemble(targetType, sources);
    }

    /**
     * 通过反射获取装配器实例，并执行装配
     *
     * @param assemblerType 装配器类型
     * @param targetType    目标类型
     * @param sources       源对象数组
     */
    public <T> T convert(Class<? extends AssemblerMoreSource<T>> assemblerType, Class<T> targetType, Object... sources) {
        AssemblerMoreSource<T> assembler = getAssembler(assemblerType);
        if (assembler != null) {
            return assemble(assembler, targetType, sources);
        }
        return null;
    }

    /**
     * 反射创建装配器实例
     */
    private <T> AssemblerMoreSource<T> getAssembler(Class<? extends AssemblerMoreSource<T>> assemblerType) {
        try {
            return assemblerType.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
