package com.smart.adp.application.job;

import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.domain.common.constants.StringConstant;
import com.smart.adp.domain.context.TimeContext;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import com.smart.adp.domain.enums.EnableEnum;
import com.smart.adp.domain.valueObject.clue.SacOnecustInfoEventVO;
import com.smart.adp.infrastructure.repository.clue.CustRemarkMapper;
import com.smart.adp.infrastructure.repository.clue.OneCustInfoMapper;
import com.smart.adp.infrastructure.repository.clue.SacOnecustInfoEventMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.smart.adp.domain.entity.clue.table.SacOneCustRemarkTableDef.SAC_ONE_CUST_REMARK;
import static com.smart.adp.domain.entity.clue.table.SacOnecustInfoEntityTableDef.SAC_ONECUST_INFO_ENTITY;
import static com.smart.adp.domain.valueObject.clue.table.SacOnecustInfoEventVOTableDef.SAC_ONECUST_INFO_EVENT_VO;

/**
 * <p>
 * 线索活跃时间任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/18
 */
@Slf4j
@Component
public class ClueActivityTimeJob {

    @Autowired
    private CustRemarkMapper custRemarkMapper;

    @Autowired
    private OneCustInfoMapper custInfoMapper;

    @Autowired
    private SacOnecustInfoEventMapper cdpEventMapper;

    @XxlJob("clueActivityTimeJob")
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> run(String param) {
        log.info("clueActivityTimeJob run {}", param);
        ReturnT<String> res;
        try {
            LocalDateTime now = TimeContext.now();
            LocalDateTime start = now.minusMinutes(10);
            List<String> phones;
            if (StringConstant.FLAG.equals(param)) {
                // get event by flag
                phones = Collections.emptyList();
            } else {
                // get event by time
                QueryWrapper wrapper = QueryWrapper.create()
                                                   .select(SAC_ONECUST_INFO_EVENT_VO.MOBILE)
                                                   .and(SAC_ONECUST_INFO_EVENT_VO.CREATED_DATE.ge(start))
                                                   .and(SAC_ONECUST_INFO_EVENT_VO.CREATED_DATE.lt(now))
                                                   .and(SAC_ONECUST_INFO_EVENT_VO.IS_ENABLE.eq(EnableEnum.ENABLE.getCodeStr()));
                phones = cdpEventMapper.selectListByQuery(wrapper)
                                       .stream()
                                       .map(SacOnecustInfoEventVO::getMobile)
                                       .distinct()
                                       .collect(Collectors.toList());
            }
            if (!phones.isEmpty()) {
                log.info("clueActivityTimeJob phone size {}", phones.size());
                // update active time
                for (List<String> list : Lists.partition(phones, 1000)) {
                    updateActiveTime(list);
                }
                // update event flag
                SacOnecustInfoEventVO vo = new SacOnecustInfoEventVO();
                vo.setIsEnable(EnableEnum.DISABLE.getCodeStr());
                cdpEventMapper.updateByQuery(vo, QueryWrapper.create()
                                                             .and(SAC_ONECUST_INFO_EVENT_VO.CREATED_DATE.ge(start))
                                                             .and(SAC_ONECUST_INFO_EVENT_VO.CREATED_DATE.lt(now))
                                                             .and(SAC_ONECUST_INFO_EVENT_VO.IS_ENABLE.eq(EnableEnum.ENABLE.getCodeStr())));

            }
            res = ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("clueActivityTimeJob exception", e);
            res = new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } finally {
            log.info("clueActivityTimeJob finish");
            TimeContext.remove();
        }
        return res;
    }

    /**
     * 更新活跃时间
     *
     * @param phones phones
     */
    private void updateActiveTime(List<String> phones) {
        // get custIds
        List<String> custIds = custInfoMapper.selectListByQuery(QueryWrapper.create()
                                                                            .select(SAC_ONECUST_INFO_ENTITY.CUST_ID)
                                                                            .and(SAC_ONECUST_INFO_ENTITY.PHONE.in(phones)))
                                             .stream()
                                             .map(SacOnecustInfoEntity::getCustId)
                                             .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(custIds)) {
            return;
        }

        // update active time
        SacOneCustRemark custRemark = new SacOneCustRemark();
        custRemark.setLastActiveTime(TimeContext.now());
        custRemarkMapper.updateByQuery(custRemark, QueryWrapper.create()
                                                               .and(SAC_ONE_CUST_REMARK.CUST_ID.in(custIds)));
    }
}
