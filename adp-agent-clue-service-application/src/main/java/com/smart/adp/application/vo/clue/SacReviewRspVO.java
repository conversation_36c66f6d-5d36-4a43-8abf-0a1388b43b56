package com.smart.adp.application.vo.clue;

import com.smart.adp.application.assembler.AssemblerFactory;
import com.smart.adp.application.assembler.clue.SacReviewVOAssembler;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 线索跟进数据VO
 * @Author: rik.ren
 * @Date: 2025/3/17 18:11
 **/
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SacReviewRspVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "回访ID")
    private String reviewId;

    @Schema(description = "所属组织")
    private String orgCode;

    @Schema(description = "所属组织名称")
    private String orgName;

    @Schema(description = "回访单据类型")
    private String billType;

    @Schema(description = "回访单据类型名称")
    private String billTypeName;

    @Schema(description = "回访单据业务类型")
    private String businessType;

    @Schema(description = "回访单据业务类型名称")
    private String businessTypeName;

    @Schema(description = "一级信息来源编码")
    private String infoChanMCode;

    @Schema(description = "一级信息来源名称")
    private String infoChanMName;

    @Schema(description = "二级信息来源编码")
    private String infoChanDCode;

    @Schema(description = "二级信息来源名称")
    private String infoChanDName;

    @Schema(description = "三级信息来源编码")
    private String infoChanDdCode;

    @Schema(description = "三级信息来源名称")
    private String infoChanDdName;

    @Schema(description = "最低一级的信息来源编码")
    private String channelCode;

    @Schema(description = "最低一级的信息来源名称")
    private String channelName;

    @Schema(description = "关联业务单号")
    private String billCode;

    @Schema(description = "计划回访时间")
    private LocalDateTime planReviewTime;

    @Schema(description = "实际回访时间")
    private LocalDateTime reviewTime;

    @Schema(description = "上次回访时间")
    private LocalDateTime lastReviewTime;

    @Schema(description = "超期回访时间(到这个时间算超期)")
    private LocalDateTime overReviewTime;

    @Schema(description = "计划到店时间")
    private LocalDateTime planComeTime;

    @Schema(description = "实际到店时间")
    private LocalDateTime factComeTime;

    @Schema(description = "是否到店")
    private String isCome;

    @Schema(description = "分配状态")
    private String assignStatus;

    @Schema(description = "分配状态名称")
    private String assignStatusName;

    @Schema(description = "分配时间")
    private LocalDateTime assignTime;

    @Schema(description = "分配人用户ID")
    private String assignPersonId;

    @Schema(description = "分配人名称")
    private String assignPersonName;

    @Schema(description = "回访人员用户ID")
    private String reviewPersonId;

    @Schema(description = "回访人员名称")
    private String reviewPersonName;

    @Schema(description = "回访内容")
    private String reviewDesc;

    @Schema(description = "回访状态编码")
    private String reviewStatus;

    @Schema(description = "回访状态名称")
    private String reviewStatusName;

    @Schema(description = "客户ID")
    private String custId;

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "客户手机号")
    private String phone;

    @Schema(description = "客户性别")
    private String gender;

    @Schema(description = "客户性别名称")
    private String genderName;

    @Schema(description = "接触状态编码")
    private String touchStatus;

    @Schema(description = "接触状态名称")
    private String touchStatusName;

    @Schema(description = "异常(战败失控)原因编码")
    private String errorReasonCode;

    @Schema(description = "异常(战败失控)原因名称")
    private String errorReasonName;

    @Schema(description = "节点编码")
    private String nodeCode;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "下发经销商编码")
    private String sendDlrCode;

    @Schema(description = "下发经销商名称")
    private String sendDlrShortName;

    @Schema(description = "下发时间")
    private LocalDateTime sendTime;

    @Schema(description = "意向级别编码")
    private String intenLevelCode;

    @Schema(description = "意向级别名称")
    private String intenLevelName;

    @Schema(description = "意向品牌编码")
    private String intenBrandCode;

    @Schema(description = "意向品牌名称")
    private String intenBrandName;

    @Schema(description = "意向车系编码")
    private String intenSeriesCode;

    @Schema(description = "意向车系名称")
    private String intenSeriesName;

    @Schema(description = "意向车型编码")
    private String intenCarTypeCode;

    @Schema(description = "意向车型名称")
    private String intenCarTypeName;

    @Schema(description = "扩展字段1")
    private String column1;

    @Schema(description = "扩展字段2")
    private String column2;

    @Schema(description = "扩展字段3")
    private String column3;

    @Schema(description = "扩展字段4")
    private String column4;

    @Schema(description = "扩展字段5")
    private String column5;

    @Schema(description = "扩展字段6")
    private String column6;

    @Schema(description = "扩展字段7")
    private String column7;

    @Schema(description = "扩展字段8")
    private String column8;

    @Schema(description = "扩展字段9")
    private String column9;

    @Schema(description = "扩展字段10")
    private String column10;

    @Schema(description = "扩展字段11")
    private String column11;

    @Schema(description = "扩展字段12")
    private String column12;

    @Schema(description = "扩展字段13")
    private String column13;

    @Schema(description = "扩展字段14")
    private String column14;

    @Schema(description = "扩展字段15")
    private String column15;

    @Schema(description = "扩展字段16")
    private String column16;

    @Schema(description = "扩展字段17")
    private String column17;

    @Schema(description = "扩展字段18")
    private String column18;

    @Schema(description = "扩展字段19")
    private String column19;

    @Schema(description = "扩展字段20")
    private String column20;

    @Schema(description = "大字段1")
    private String bigColumn1;

    @Schema(description = "大字段2")
    private String bigColumn2;

    @Schema(description = "大字段3")
    private String bigColumn3;

    @Schema(description = "大字段4")
    private String bigColumn4;

    @Schema(description = "大字段5")
    private String bigColumn5;

    @Schema(description = "JSON扩展字段")
    private String extendsJson;

    @Schema(description = "厂商标识ID")
    private String oemId;

    @Schema(description = "集团标识ID")
    private String groupId;

    @Schema(description = "创建人ID")
    private String creator;

    @Schema(description = "创建人")
    private String createdName;

    @Schema(description = "创建日期")
    private LocalDateTime createdDate;

    @Schema(description = "修改人ID")
    private String modifier;

    @Schema(description = "修改人")
    private String modifyName;

    @Schema(description = "最后更新日期")
    private LocalDateTime lastUpdatedDate;

    @Schema(description = "是否可用")
    private String isEnable;

    @Schema(description = "并发控制ID")
    private String updateControlId;

    @Schema(description = "省份编码")
    private String provinceCode;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区县编码")
    private String countyCode;

    @Schema(description = "区县名称")
    private String countyName;

    @Schema(description = "城市公司编码")
    private String cityFirmCode;

    @Schema(description = "城市公司名称")
    private String cityFirmName;

    @Schema(description = "线索移交编码")
    private String manageLabelCode;

    @Schema(description = "线索移交名称")
    private String manageLabelName;

    @Schema(description = "战败一级原因编码")
    private String firstReasonCode;

    @Schema(description = "战败一级原因中文")
    private String firstReasonName;

    /**
     * 客户描述
     */
    @Schema(description = "客户描述")
    private String remark;

    /**
     * 用户阶段
     */
    @Schema(description = "用户阶段")
    private String userStage;

    /**
     * 上一个用户阶段
     */
    @Schema(description = "上一个用户阶段")
    private String previousUserStage;

    /**
     * 线索等级(H/A/B/C/D/E)
     */
    @Schema(description = "线索等级(H/A/B/C/D/E)")
    private String clueLevel;

    /**
     * 意向车型
     */
    @Schema(description = "意向车型")
    private String intentVehicleCode;

    /**
     * 竞品车型
     */
    @Schema(description = "竞品车型")
    private String competitiveVehicleCode;

    /**
     * 所在地
     */
    @Schema(description = "所在地")
    private String location;

    /**
     * 线索来源
     */
    @Schema(description = "线索来源")
    private String clueSource;

    /**
     * 用户阶段-了解的时间
     */
    @Schema(description = "用户阶段-了解的时间")
    private LocalDateTime knowDate;

    /**
     * 用户阶段-到店的时间
     */
    @Schema(description = "用户阶段-到店的时间")
    private LocalDateTime toStoreDate;

    /**
     * 用户阶段-试驾的时间
     */
    @Schema(description = "用户阶段-试驾的时间")
    private LocalDateTime testDriveDate;

    /**
     * 用户阶段-下定的时间
     */
    @Schema(description = "用户阶段-下定的时间")
    private LocalDateTime placeOrderDate;

    public static SacReviewRspVO convent(SacReviewBO source) {
        return AssemblerFactory.getInstance().convert(SacReviewVOAssembler.class, source, SacReviewRspVO.class);
    }
}