package com.smart.adp.application.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.smart.adp.application.dto.clue.ReviewDisTributeDTO;
import com.smart.adp.domain.entity.message.ReviewDisTributeMessage;
import com.smart.adp.infrastructure.publisher.AgentDlrCreateCluePublisher;
import com.smart.tools.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/4/16 20:58
 * @description 线索分配异步事件
 **/
@Slf4j
@Component
public class AgentClueDistributeEventListener implements ApplicationListener<AgentClueDistributeEvent> {

    @Autowired
    private AgentDlrCreateCluePublisher agentDlrCreateCluePublisher;

    @Override
    @Async("clueExecutor")
    public void onApplicationEvent(AgentClueDistributeEvent event) {
        if (event == null) {
            log.info("线索分配异步事件监听，event is null");
            return;
        }

        List<ReviewDisTributeDTO> reviewDisTributeDTOList = event.getReviewDisTributeDTOList();
        if (CollUtil.isEmpty(reviewDisTributeDTOList)) {
            log.info("线索分配异步事件监听，reviewDisTributeDTOList is null in event.");
            return;
        }


        for (ReviewDisTributeDTO reviewDisTributeDTO : reviewDisTributeDTOList) {
            ReviewDisTributeMessage reviewDisTributeMessage = BeanUtils.copyProperties(reviewDisTributeDTO, ReviewDisTributeMessage.class);
            String jsonMessage = JSONUtil.toJsonStr(reviewDisTributeMessage);
            agentDlrCreateCluePublisher.sendClueDisTributeMessage(jsonMessage);
        }
    }
}
