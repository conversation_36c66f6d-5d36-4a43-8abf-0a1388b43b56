package com.smart.adp.application.vo.clue;

import com.smart.adp.application.assembler.AssemblerFactory;
import com.smart.adp.application.assembler.clue.ClueActiveVOAssembler;
import com.smart.adp.domain.valueObject.clue.ClueActiveInfoVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
 * @Description: 线索活跃信息VO
 * @Author: rik.ren
 * @Date: 2025/3/11 14:12
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "线索活跃信息 VO")
public class ClueActiveInfoRspVO implements Serializable {

    @Schema(description = "线索 ID")
    private String custId;

    @Schema(description = "线索手机号")
    private String phone;

    @Schema(description = "线索7天活跃天数")
    private Integer sevenDays;

    @Schema(description = "线索30天活跃天数")
    private Integer thirthDays;

    @Schema(description = "活跃时间段")
    private String activeTimePeriod;

    public static ClueActiveInfoRspVO convent(ClueActiveInfoVO source) {
        return AssemblerFactory.getInstance().convert
                (ClueActiveVOAssembler.class, source, ClueActiveInfoRspVO.class);
    }
}
