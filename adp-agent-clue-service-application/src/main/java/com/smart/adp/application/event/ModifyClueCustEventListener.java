package com.smart.adp.application.event;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.gateway.clue.ClueDlrGateway;
import com.smart.adp.infrastructure.feign.XApiFeignService;
import com.smart.adp.infrastructure.utils.ProvinceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * date 2025/5/15 14:15
 * @description 修改客户信息异步事件监听
 **/
@Slf4j
@Component
public class ModifyClueCustEventListener implements ApplicationListener<ModifyClueCustEvent> {

    private static final String ADP_SYSTEM = "ADP";
    private static final String REMARK_CLUE_MODIFY = "客户信息修改";
    private static final String CDP_EVENT_CODE = "ADP_INSERT_CDP_LEADS";

    @Autowired
    private XApiFeignService xApiFeignService;

    @Autowired
    private ClueDlrGateway clueDlrGateway;

    @Autowired
    private ProvinceUtil provinceUtil;

    @Override
    public void onApplicationEvent(ModifyClueCustEvent event) {
        if (event == null) {
            log.warn("修改客户信息异步事件监听，event is null");
            return;
        }
        if (StrUtil.isBlank(event.getPhone())) {
            log.warn("修改客户信息异步事件监听，phone is null in event.");
            return;
        }

        notifyCdpSystem(event.getReviewId(), event.getIsDefeatedDis(), event.getPhone(), event.getCustName());

    }

    private void notifyCdpSystem(String reviewId, Boolean isDefeatedDis, String phone,String custName) {
        try {
            Object clueInfo = isDefeatedDis
                    ? clueDlrGateway.findDefeatedClueByReviewId(reviewId)
                    : clueDlrGateway.findActiveClueByReviewId(reviewId);

            if (clueInfo == null) {
                return;
            }

            Map<String, Object> noticeCdpMap = buildCdpMap(clueInfo,custName);
            sendCdpNotification(noticeCdpMap, phone);
        } catch (Exception e) {
            log.error("修改客户信息通知CDP异常 reviewId={}", reviewId, e);
        }
    }

    /**
     * 构建传递cdp入参模型
     *
     * @param clueInfo
     * @return
     */
    private Map<String, Object> buildCdpMap(Object clueInfo,String custName) {
        Map<String, Object> map = new HashMap<>(33);

        // 公共字段
        map.put("c_lastupdate_system", ADP_SYSTEM);
        map.put("remark", REMARK_CLUE_MODIFY);
        map.put("name", custName);

        // 动态字段处理
        if (clueInfo instanceof SacAllClueInfoDlr) {
            populateCdpMapFromSacAll((SacAllClueInfoDlr) clueInfo, map);
        } else if (clueInfo instanceof SacClueInfoDlr) {
            populateCdpMapFromSac((SacClueInfoDlr) clueInfo, map);
        }

        return MapUtil.removeNullValue(map);
    }

    /**
     * 构建战败线索通知对象
     *
     * @param info
     * @param map
     */
    private void populateCdpMapFromSacAll(SacAllClueInfoDlr info, Map<String, Object> map) {
        map.put("bk", info.getPhone());
        map.put("mobile", info.getPhone());
        map.put("c_smartid", info.getColumn10());
        // 2506 大区设置省份特殊处理
        map.put("c_province", provinceUtil.getMappingProvince(info.getProvinceName()));// 省份
        map.put("c_city", info.getCityName());// 城市
        map.put("c_county", info.getCountyName());// 地区
        map.put("c_gender", info.getGenderName());// 性别
        map.put("c_store", info.getDlrShortName());
        map.put("c_store_code", info.getDlrCode());
        map.put("c_store_name", info.getDlrShortName());
        // "用户注册/留资最早来源渠道（=一级来源）"
        map.put("c_register_channel", info.getChannelName());
        if (StrUtil.isNotBlank(info.getChannelCode())) {
            map.put("c_second_channel", info.getChannelCode());// 二级来源
        }
        if (StrUtil.isNotBlank(info.getInfoChanDCode())) {
            map.put("c_third_channel", info.getInfoChanDCode());// 三级来源
        }
        map.put("c_cus_source", info.getInfoChanDName());// 渠道描述
        map.put("c_heat_name", info.getColumn5());// 热度名称
        map.put("c_heat_code", info.getColumn6());// 热度编码
        map.put("c_interested_car_model", info.getIntenCarTypeName());// 意向车型
        map.put("c_interested_car_colour", info.getInnerColorName());// 意向车型颜色
        map.put("c_interested_car_model", info.getIntenCarTypeName());// 意向车型
        map.put("c_interested_car_colour", info.getInnerColorName());// 意向车型颜色
    }

    /**
     * 构建活跃线索分配对象
     *
     * @param info
     * @param map
     */
    private void populateCdpMapFromSac(SacClueInfoDlr info, Map<String, Object> map) {
        map.put("bk", info.getPhone());
        map.put("mobile", info.getPhone());
        map.put("c_smartid", info.getColumn10());
        // 2506 大区设置省份特殊处理
        map.put("c_province", provinceUtil.getMappingProvince(info.getProvinceName()));// 省份
        map.put("c_city", info.getCityName());// 城市
        map.put("c_county", info.getCountyName());// 地区
        map.put("c_gender", info.getGenderName());// 性别
        // "用户注册/留资最早来源渠道（=一级来源）"
        map.put("c_register_channel", info.getChannelName());
        if (StrUtil.isNotBlank(info.getChannelCode())) {
            map.put("c_second_channel", info.getChannelCode());// 二级来源
        }
        if (StrUtil.isNotBlank(info.getInfoChanDCode())) {
            map.put("c_third_channel", info.getInfoChanDCode());// 三级来源
        }
        map.put("c_cus_source", info.getInfoChanDName());// 渠道描述
        map.put("c_heat_name", info.getColumn5());// 热度名称
        map.put("c_heat_code", info.getColumn6());// 热度编码
        map.put("c_interested_car_model", info.getIntenCarTypeName());// 意向车型
        map.put("c_interested_car_colour", info.getInnerColorName());// 意向车型颜色
        map.put("c_interested_car_model", info.getIntenCarTypeName());// 意向车型
        map.put("c_interested_car_colour", info.getInnerColorName());// 意向车型颜色
    }

    private void sendCdpNotification(Map<String, Object> noticeCdpMap, String phone) {
        Map<String, Object> param = Collections.singletonMap("mapParam", noticeCdpMap);
        log.info("修改客户信息通知CDP开始,phone={}", phone);
        try {
            Map map = xApiFeignService.sendCancleData(CDP_EVENT_CODE, param);
            log.info("修改客户信息通知CDP结束,phone={},map={}", phone, JSONUtil.toJsonStr(map));
        } catch (Exception e) {
            log.error("修改客户信息通知CDP异常", e);
        }
    }
}
