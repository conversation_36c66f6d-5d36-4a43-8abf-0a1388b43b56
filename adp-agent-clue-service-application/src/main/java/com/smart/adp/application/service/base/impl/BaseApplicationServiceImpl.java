package com.smart.adp.application.service.base.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.smart.adp.application.assembler.AssemblerFactory;
import com.smart.adp.application.assembler.base.CarTypeVOAssembler;
import com.smart.adp.application.assembler.base.UserGroupVOAssembler;
import com.smart.adp.application.constant.CommonConstant;
import com.smart.adp.application.dto.base.DlrEmpDTO;
import com.smart.adp.application.dto.base.QueryLookUpInfoDTO;
import com.smart.adp.application.dto.base.QueryUserGroupDTO;
import com.smart.adp.application.dto.base.RemoveDTO;
import com.smart.adp.application.service.base.BaseApplicationService;
import com.smart.adp.application.vo.base.UserGroupVO;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.context.Channel;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.base.AgentEmployee;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.entity.clue.SacUserGroupEntity;
import com.smart.adp.domain.gateway.clue.AgentEmployeeGateway;
import com.smart.adp.domain.service.base.IBaseService;
import com.smart.adp.domain.service.base.ICareTypeService;
import com.smart.adp.domain.service.base.ILookUpService;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.domain.valueObject.base.*;
import com.smart.adp.infrastructure.utils.RedisUtil;
import com.smart.tools.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.smart.adp.domain.entity.base.table.AgentEmployeeTableDef.AGENT_EMPLOYEE;

/**
 * @Description: 基础application服务实现
 * @Author: rik.ren
 * @Date: 2025/3/9 17:33
 **/
@Slf4j
@Service
public class BaseApplicationServiceImpl implements BaseApplicationService {

    @Autowired
    private IBaseService baseService;

    @Autowired
    private ILookUpService lookUpService;

    @Autowired
    private ICareTypeService careTypeService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private AgentEmployeeGateway agentEmployeeGateway;

    /**
     * 获取指定创建人的用户分组
     *
     * @param param
     * @return
     */
    @Override
    public List<UserGroupVO> queryUserGroupByCreate(QueryUserGroupDTO param) {
        List<SacUserGroupEntity> result = baseService.queryUserGroup(param.buildEntity());
        return AssemblerFactory.getInstance().convert(UserGroupVOAssembler.class, result, List.class);
    }

    /**
     * 查询字典表
     *
     * @param dtoParam
     * @return
     */
    @Override
    public List<LookUpInfo> findLookUpInfo(QueryLookUpInfoDTO dtoParam) {
        LookUpInfo entity =
                LookUpInfo.builder().lookUpTypeCode(dtoParam.getLookUpTypeCode()).lookUpValueCode(dtoParam.getLookUpValueCode()).build();
        return lookUpService.findLookUpInfo(entity);
    }

    /**
     * 查询缓存中数据字典
     *
     * @param param
     * @return
     */
    @Override
    public List<LookUpInfoVO> findCacheLookUpInfo(QueryLookUpInfoDTO param) {
        if (Objects.isNull(param) || StrUtil.isBlank(param.getLookUpTypeCode())) {
            return Collections.emptyList();
        }
        // 参数校验
        if (isParamInvalid(param)) {
            return Collections.emptyList();
        }

        String lookUpTypeCode = param.getLookUpTypeCode();
        String cacheKey = CommonConstant.CACHE_PREFIX + lookUpTypeCode;

        // 尝试从缓存获取
        List<LookUpInfoVO> cachedResult = getFromCache(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        // 查询数据库并转换结果
        List<LookUpInfo> lookUpInfoList = queryFromDatabase(lookUpTypeCode);
        List<LookUpInfoVO> lookUpInfoVOS = convertToVOList(lookUpInfoList);

        // 设置缓存并返回结果
        cacheResult(cacheKey, lookUpInfoVOS, !lookUpInfoList.isEmpty());
        return lookUpInfoVOS;
    }

    /**
     * 参数校验
     *
     * @param param
     * @return
     */
    private boolean isParamInvalid(QueryLookUpInfoDTO param) {
        return param == null || StrUtil.isBlank(param.getLookUpTypeCode());
    }

    /**
     * 获取缓存信息
     *
     * @param cacheKey
     * @return
     */
    private List<LookUpInfoVO> getFromCache(String cacheKey) {
        String cachedValue = (String) redisUtil.get(cacheKey);
        if (StrUtil.isBlank(cachedValue)) {
            return null;
        }

        try {
            return JSONUtil.toList(cachedValue, LookUpInfoVO.class);
        } catch (JSONException e) {
            log.error("反序列化缓存数据失败，cacheKey: {}", cacheKey, e);
            redisUtil.del(cacheKey);
        }
        return null;
    }

    private List<LookUpInfo> queryFromDatabase(String lookUpTypeCode) {
        LookUpInfo queryEntity = LookUpInfo.builder().lookUpTypeCode(lookUpTypeCode).build();
        return lookUpService.findLookUpList(queryEntity);
    }

    private List<LookUpInfoVO> convertToVOList(List<LookUpInfo> lookUpInfoList) {
        return lookUpInfoList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    private LookUpInfoVO convertToVO(LookUpInfo lookUpInfo) {
        LookUpInfoVO vo = new LookUpInfoVO();
        vo.setLookupTypeCode(lookUpInfo.getLookUpTypeCode());
        vo.setLookupTypeName(lookUpInfo.getLookUpTypeName());
        vo.setLookupValueCode(lookUpInfo.getLookUpValueCode());
        vo.setLookupValueName(lookUpInfo.getLookUpValueName());
        vo.setDlrId(lookUpInfo.getDlrId());
        vo.setOrderNo(lookUpInfo.getOrderNo());
        vo.setCreatedDate(lookUpInfo.getCreatedDate());
        vo.setRemark(lookUpInfo.getRemark());
        vo.setIsEnable(lookUpInfo.getIsEnable());
        return vo;
    }

    private void cacheResult(String cacheKey, List<LookUpInfoVO> result, boolean hasData) {
        int expireTime = hasData ? CommonConstant.DLR_EMP_CACHE_EXPIRE_30
                : CommonConstant.DLR_EMP_CACHE_EXPIRE_HALF;
        String cacheValue = hasData ? JSONUtil.toJsonStr(result) : "[]";
        redisUtil.set(cacheKey, cacheValue, expireTime);
    }


    /**
     * 获取车型配置信息
     *
     * @return
     */
    @Override
    public List<CarTypeInfoVO> queryCarTypeList() {
        String cacheKey = CommonConstant.CAR_TYPE_CACHE_PREFIX + "CN";
        String cachedValue = (String) redisUtil.get(cacheKey);
        // 缓存命中
        List<SmallCarTypeInfo> smallCarTypeInfoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(cachedValue)) {
            smallCarTypeInfoList = JSONUtil.toList(cachedValue, SmallCarTypeInfo.class);
        } else {
            smallCarTypeInfoList = careTypeService.queryCarTypeList();
            if (CollUtil.isNotEmpty(smallCarTypeInfoList)) {
                redisUtil.set(cacheKey, JSONUtil.toJsonStr(smallCarTypeInfoList), 30 * 86400);
            }
        }

        return AssemblerFactory.getInstance().convert(CarTypeVOAssembler.class, smallCarTypeInfoList, List.class);
    }

    @Override
    public List<ChannelInfoVO> channelInfo() {
        return Channel.getChannelInfo();
    }


    @Override
    public List<AgentDlrEmpVO> queryDlrEmpList(DlrEmpDTO dlrEmpDTO) {
        log.info("获取产品专家列表场景来源，source={}", dlrEmpDTO.getSource());
        // 获取登录用户信息
        UserBusiEntity userInfo = UserInfoContext.get();
        if (Objects.isNull(userInfo)) {
            throw new BusinessException(RespCode.FAIL.getCode(), "用户信息获取失败，请重新登录！");
        }

        // 产品专家直接返回空列表
        if (UserUtil.isProductExpert(userInfo.getStationId())) {
            return Collections.emptyList();
        }

        final String cacheKey = CommonConstant.CACHE_PREFIX + CommonConstant.DLR_EMP_CACHE_PREFIX + userInfo.getDlrCode();
        // 尝试从缓存获取
        String cachedValue = (String) redisUtil.get(cacheKey);
        if (StringUtils.isNotEmpty(cachedValue)) {
            try {
                return JSONUtil.toList(cachedValue, AgentDlrEmpVO.class);
            } catch (JSONException e) {
                log.warn("缓存数据反序列化失败，缓存键: {}，将重新查询数据库", cacheKey, e);
                redisUtil.del(cacheKey);
            }
        }

        // 缓存未命中，查询数据库
        List<AgentDlrEmpVO> resultList = queryEmployeesFromDB(userInfo.getDlrCode(), dlrEmpDTO);

        // 更新缓存
        if (CollUtil.isNotEmpty(resultList)) {
            redisUtil.set(cacheKey, JSONUtil.toJsonStr(resultList), CommonConstant.DLR_EMP_CACHE_EXPIRE, TimeUnit.SECONDS);
        }

        return resultList;
    }

    private List<AgentDlrEmpVO> queryEmployeesFromDB(String dlrCode, DlrEmpDTO dlrEmpDTO) {
        // 构建查询条件,获取在职状态下产品专家信息
        AgentEmployee query = AgentEmployee.builder()
                .dlrCode(dlrCode)
                .userStatus(CommonConstant.USER_STATUS_ACTIVE)
                .build();

        // 查询数据库
        List<AgentEmployee> employees = agentEmployeeGateway.findEmployeeByDlrCode(
                query, AGENT_EMPLOYEE.EMP_NAME, AGENT_EMPLOYEE.USER_ID, AGENT_EMPLOYEE.DLR_CODE,
                AGENT_EMPLOYEE.EMP_CODE, AGENT_EMPLOYEE.STATION_ID, AGENT_EMPLOYEE.GENDER_CODE, AGENT_EMPLOYEE.MOBILE
        );

        if (CollUtil.isEmpty(employees)) {
            return Collections.emptyList();
        }

        // 转换为VO列表
        return employees.stream()
                .filter(e -> !e.getEmpCode().equals(e.getDlrCode()))
                .map(this::convertToAgentDlrEmpVO)
                .collect(Collectors.toList());
    }

    private AgentDlrEmpVO convertToAgentDlrEmpVO(AgentEmployee employee) {
        AgentDlrEmpVO vo = new AgentDlrEmpVO();
        vo.setUserId(employee.getUserId());
        vo.setEmpName(employee.getEmpName());
        vo.setEmpCode(employee.getEmpCode());
        vo.setDlrCode(employee.getDlrCode());
        vo.setGenderCode(employee.getGenderCode());
        vo.setMobile(employee.getMobile());
        if (UserUtil.isProductExpert(employee.getStationId())) {
            vo.setRoleName("专家");
        }
        if (UserUtil.isStoreManager(employee.getStationId())) {
            vo.setRoleName("店长");
        }
        return vo;
    }

    /**
     * 清理缓存信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean dealCacheInfo(RemoveDTO param) {
        // 获取登录用户信息
        UserBusiEntity userInfo = UserInfoContext.get();
        if (Objects.isNull(userInfo)) {
            throw new BusinessException(RespCode.FAIL.getCode(), "用户信息获取失败，请重新登录！");
        }
        if (!CommonConstant.SHARE_SWITCH_ON.equals(param.getCanRemove())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "不支持清理谢谢！");
        }
        try {
            redisUtil.del(param.getRemoveKey());
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("清理缓存信息异常", e);
            return Boolean.FALSE;
        }
    }
}
