package com.smart.adp.application.vo.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
 * @Description: 互动数据中的用户事件
 * @Author: rik.ren
 * @Date: 2025/3/23 13:13
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ClueInteractUserEventVO implements Serializable {

    /**
     * 事件名称
     */
    @Schema(description = "事件名称")
    private String eventName;
}
