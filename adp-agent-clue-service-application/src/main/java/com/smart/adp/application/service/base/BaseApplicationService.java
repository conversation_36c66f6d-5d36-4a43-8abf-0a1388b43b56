package com.smart.adp.application.service.base;

import com.smart.adp.application.dto.base.DlrEmpDTO;
import com.smart.adp.application.dto.base.QueryLookUpInfoDTO;
import com.smart.adp.application.dto.base.QueryUserGroupDTO;
import com.smart.adp.application.dto.base.RemoveDTO;
import com.smart.adp.application.vo.base.UserGroupVO;
import com.smart.adp.domain.valueObject.base.*;

import java.util.List;

/**
 * @Description: 用户service接口
 * @Author: rik.ren
 * @Date: 2025/3/9 15:58
 **/
public interface BaseApplicationService {

    /**
     * 获取指定创建人的用户分组
     *
     * @param param
     * @return
     */
    List<UserGroupVO> queryUserGroupByCreate(QueryUserGroupDTO param);

    /**
     * 查询字典表
     *
     * @param param
     * @return
     */
    List<LookUpInfo> findLookUpInfo(QueryLookUpInfoDTO param);

    /**
     * 获取车型信息配置信息如#1，#3，#5
     *
     * @return
     */
    List<CarTypeInfoVO> queryCarTypeList();

    /**
     * 二三级渠道信息
     *
     * @return
     */
    List<ChannelInfoVO> channelInfo();

    /**
     * 获取门店员工信息，产品专家信息
     *
     * @return
     */
    List<AgentDlrEmpVO> queryDlrEmpList(DlrEmpDTO dlrEmpDTO);

    /**
     * 查询缓存中数据字典
     *
     * @param param
     * @return
     */
    List<LookUpInfoVO> findCacheLookUpInfo(QueryLookUpInfoDTO param);

    Boolean dealCacheInfo(RemoveDTO param);
}
