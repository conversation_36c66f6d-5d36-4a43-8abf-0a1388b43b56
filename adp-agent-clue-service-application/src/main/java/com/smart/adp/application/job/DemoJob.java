package com.smart.adp.application.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/18
 */
@Slf4j
@Component
public class DemoJob {

    @XxlJob("demo")
    public ReturnT<String> demoJob() {
        log.info("job start");
        return ReturnT.SUCCESS;
    }
}
