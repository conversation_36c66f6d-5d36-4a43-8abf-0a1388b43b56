package com.smart.adp.application.dto.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/4/17 18:50
 * @description 获取产品专家信息DTO
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DlrEmpDTO implements Serializable {

    @Schema(description = "请求入口来源（1：分配获取员工信息；2：店长线索列表获取；3：首页小结详情）")
    private String source;
}
