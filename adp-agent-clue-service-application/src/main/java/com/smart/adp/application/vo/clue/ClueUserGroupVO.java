package com.smart.adp.application.vo.clue;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.application.vo.base.UserGroupVO;
import com.smart.adp.domain.bo.clue.SacUserGroupDetailBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 用户分组和线索所在分组返回的对象
 * @Author: rik.ren
 * @Date: 2025/3/9 17:21
 **/
@Data
@ToString
@EqualsAndHashCode
@Schema(description = "用户分组VO")
public class ClueUserGroupVO implements Serializable {
    /**
     * ID
     */
    @Schema(description = "用户分组ID")
    private String userGroupId;

    /**
     * 客户 ID
     */
    @Schema(description = "用户分组名称")
    private String userGroupName;

    /**
     * 分组下的用户
     */
    @Schema(description = "分组下的用户Id")
    private List<String> custId;

    public static List<ClueUserGroupVO> convent(List<UserGroupVO> listUserGroupVO, List<SacUserGroupDetailBO> listDetailBo) {
        if (CollectionUtil.isEmpty(listUserGroupVO)) {
            return null;
        }
        List<ClueUserGroupVO> result = new ArrayList<>();
        Map<String, List<String>> groupDetailBOMap = convertToCustIdMap(listDetailBo);
        listUserGroupVO.forEach(item -> {
            ClueUserGroupVO groupVO = new ClueUserGroupVO();
            groupVO.setUserGroupId(item.getUserGroupId());
            groupVO.setUserGroupName(item.getUserGroupName());
            if (CollectionUtil.isNotEmpty(groupDetailBOMap)) {
                groupVO.setCustId(groupDetailBOMap.get(item.getUserGroupId()));
            }
            result.add(groupVO);
        });
        return result;
    }

    /**
     * list转map list
     *
     * @param listDetailBo
     * @return
     */
    private static Map<String, List<String>> convertToCustIdMap(List<SacUserGroupDetailBO> listDetailBo) {
        if (CollectionUtil.isEmpty(listDetailBo)) {
            return null;
        }
        Map<String, List<String>> resultMap = new HashMap<>();

        for (SacUserGroupDetailBO bo : listDetailBo) {
            // 添加主 userGroupId 对应的 custId
            addCustIdToMap(resultMap, bo.getUserGroupId(), bo.getCustId());

            // 添加 listUserGroupId 中的所有 ID 对应的 custId
            if (bo.getListUserGroupId() != null) {
                for (String groupId : bo.getListUserGroupId()) {
                    addCustIdToMap(resultMap, groupId, bo.getCustId());
                }
            }
        }

        return resultMap;
    }

    private static void addCustIdToMap(Map<String, List<String>> map, String key, String custId) {
        if (custId != null) {  // 只添加非null的custId
            map.computeIfAbsent(key, k -> new ArrayList<>()).add(custId);
        }
    }

    /**
     * list转map实体
     *
     * @param listDetailBo
     * @return
     */
    public Map<String, List<SacUserGroupDetailBO>> convertToMap(List<SacUserGroupDetailBO> listDetailBo) {
        Map<String, List<SacUserGroupDetailBO>> resultMap = new HashMap<>();

        for (SacUserGroupDetailBO bo : listDetailBo) {
            // 添加主 userGroupId
            addToMap(resultMap, bo.getUserGroupId(), bo);

            // 添加 listUserGroupId 中的所有 ID
            if (bo.getListUserGroupId() != null) {
                for (String groupId : bo.getListUserGroupId()) {
                    addToMap(resultMap, groupId, bo);
                }
            }
        }

        return resultMap;
    }

    private void addToMap(Map<String, List<SacUserGroupDetailBO>> map, String key, SacUserGroupDetailBO value) {
        map.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
    }
}
