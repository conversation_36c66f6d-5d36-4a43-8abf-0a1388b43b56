package com.smart.adp.application.vo.clue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.bo.clue.SacOnecustInfoEventBO;
import com.smart.adp.domain.bo.clue.SacOnecustResumeBO;
import com.smart.adp.domain.bo.clue.UscMdmVirtualRecordBO;
import com.smart.adp.domain.bo.drive.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.enums.InteractDataBizType;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.utils.TimeUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @Description: 线索互动数据VO
 * @Author: rik.ren
 * @Date: 2025/3/15 18:11
 **/
@ToString
@EqualsAndHashCode
@Schema(description = "线索互动数据的返回VO")
@Data
public class ClueInteractRspVO implements Serializable {
    /**
     * 触发时间
     * 整个互动数据的排序，就用这个字段来最终排序
     */
    @Schema(description = "触发时间")
    private LocalDateTime triggerTime;

    /**
     * 互动数据业务类型
     *
     * @see InteractDataBizType
     */
    @Schema(description = "互动数据业务类型，0用户事件，1虚拟外呼录音，2跟进记录，3试驾")
    private Integer interactDataType;

    /**
     * 互动数据业务类型名称
     *
     * @see InteractDataBizType
     */
    @Schema(description = "互动数据业务类型名称，0用户事件，1虚拟外呼录音，2跟进记录，3试驾")
    private String interactDataTypeName;

    /**
     * 对应业务类型的业务数据
     */
    @Schema(description = "对应业务类型的业务数据")
    private Object businessInfo;

    public static DomainPage<ClueInteractRspVO> convent(DomainPage<SacOnecustInfoEventBO> pageCustEvent,
                                                        DomainPage<UscMdmVirtualRecordBO> pageVRRecords,
                                                        DomainPage<SacOnecustResumeBO> pageOnecustResume,
                                                        DomainPage<SacTestDriveSheetBO> pageTestDriveSheet) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<ClueInteractRspVO> listResult = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(pageCustEvent) && CollectionUtil.isNotEmpty(pageCustEvent.getRecords())) {
            pageCustEvent.getRecords().forEach(item -> {
                ClueInteractRspVO rspVO = new ClueInteractRspVO();
                rspVO.setTriggerTime(item.getEventTime());
                rspVO.setInteractDataType(InteractDataBizType.USER_EVENT.getCode());
                rspVO.setInteractDataTypeName(InteractDataBizType.USER_EVENT.getDesc());
                // 该模块的业务数据
                rspVO.setBusinessInfo(ClueInteractUserEventVO.builder().eventName(item.getEventName()).build());
                listResult.add(rspVO);
            });
        }
        if (ObjectUtil.isNotEmpty(pageVRRecords) && CollectionUtil.isNotEmpty(pageVRRecords.getRecords())) {
            pageVRRecords.getRecords().forEach(item -> {
                ClueInteractRspVO rspVO = new ClueInteractRspVO();
                rspVO.setTriggerTime(item.getCallStartTime());
                rspVO.setInteractDataType(InteractDataBizType.VIRTUAL_RECORD.getCode());
                rspVO.setInteractDataTypeName(InteractDataBizType.VIRTUAL_RECORD.getDesc());
                // 该模块的业务数据
                rspVO.setBusinessInfo(ClueInteractVirtualRecordVO.builder().beginDate(item.getCallStartTime())
                        .endDate(item.getCallEndTime())
                        .abstractContent(item.getAbstractContent())
                        .duration(TimeUtils.calculateDuration(item.getCallStartTime(), item.getCallEndTime()))
                        .resumePersonName(item.getEmpName())
                        .custId(item.getCustId())
                        .resumePersonCode(item.getEmpCode())
                        .resumePersonDlrName(item.getDlrShortName()).build());
                listResult.add(rspVO);
            });
        }
        if (ObjectUtil.isNotEmpty(pageOnecustResume) && CollectionUtil.isNotEmpty(pageOnecustResume.getRecords())) {
            pageOnecustResume.getRecords().forEach(item -> {
                ClueInteractRspVO rspVO = new ClueInteractRspVO();
                rspVO.setTriggerTime(item.getBussTime());
                rspVO.setInteractDataType(InteractDataBizType.CLUE_RESUME.getCode());
                rspVO.setInteractDataTypeName(InteractDataBizType.CLUE_RESUME.getDesc());
                // 该模块的业务数据
                rspVO.setBusinessInfo(ClueInteractResumeVO.builder().bussStartTime(item.getBussStartTime())
                        .bussEndTime(item.getBussEndTime())
                        .senceCode(item.getSenceCode())
                        .senceName(item.getSenceName())
                        .resumePersonName(item.getResumePersonName())
                        .dlrNameOwner(item.getDlrNameOwner())
                        .dlrCodeOwner(item.getDlrCodeOwner())
                        .resumeDesc(item.getResumeDesc())
                        .resumePersonCode(item.getResumePersonCode())
                        .clueLevel(item.getColumn1())
                        .relationBillId(item.getRelationBillId())
                        .resumeId(item.getResumeId())
                        .clueLevelCode(item.getColumn5())
                        .custId(item.getCustId())
                        .arrivalNum(StringUtils.isNotEmpty(item.getArrivalNum()) ?
                                (item.getArrivalNum().matches("-?\\d+") ? Integer.valueOf(item.getArrivalNum()) : null)
                                : null)
                        .arrivalTime(item.getArrivalTime())
                        .arrivalEndTime(item.getArrivalEndTime())
                        .arrivalMethod(item.getArrivalMethodName())
                        .filePathlist(item.getFilePathlist())
                        .remark(item.getRemark())
                        .build());
                listResult.add(rspVO);
            });
        }
        if (ObjectUtil.isNotEmpty(pageTestDriveSheet) && CollectionUtil.isNotEmpty(pageTestDriveSheet.getRecords())) {
            pageTestDriveSheet.getRecords().forEach(item -> {
                if (StringUtils.isEmpty(item.getStartTime()) || StringUtils.isEmpty(item.getEndTime())) {
                    return;
                }
                ClueInteractRspVO rspVO = new ClueInteractRspVO();
                rspVO.setTriggerTime(item.getCreatedDate());
                rspVO.setInteractDataType(InteractDataBizType.TEST_DRIVE.getCode());
                rspVO.setInteractDataTypeName(InteractDataBizType.TEST_DRIVE.getDesc());
                // 该模块的业务数据
                ClueInteractTestDriveSheetVO interactTestDriveSheetVO = new ClueInteractTestDriveSheetVO();
                interactTestDriveSheetVO.setBeginDate(ObjectUtil.isEmpty(item.getStartTime()) ? null :
                        LocalDateTime.parse(item.getStartTime(),
                                formatter));
                interactTestDriveSheetVO.setEndDate(ObjectUtil.isEmpty(item.getEndTime()) ? null :
                        LocalDateTime.parse(item.getEndTime(),
                                formatter));
                interactTestDriveSheetVO.setTestDriveType(ObjectUtil.isEmpty(item.getTestType()) ? null :
                        Integer.valueOf(item.getTestType()));
                interactTestDriveSheetVO.setTestDriveTypeName(ObjectUtil.isEmpty(interactTestDriveSheetVO.getTestDriveType()) ?
                        null :
                        TestDriveTypeEnum.getByCode(interactTestDriveSheetVO.getTestDriveType()).getDesc());
                interactTestDriveSheetVO.setTestDriveDesc(item.getColumn1());
                interactTestDriveSheetVO.setAbstractContent(item.getColumn10());
                interactTestDriveSheetVO.setDuration((ObjectUtil.isEmpty(item.getStartTime()) || ObjectUtil.isEmpty(item.getEndTime())) ?
                        null :
                        TimeUtils.calculateDuration(LocalDateTime.parse(item.getStartTime(), formatter),
                                LocalDateTime.parse(item.getEndTime(), formatter)));
                interactTestDriveSheetVO.setResumePersonName(item.getSalesConsultantName());
                interactTestDriveSheetVO.setResumePersonCode(item.getSalesConsultantId());
                interactTestDriveSheetVO.setResumePersonDlrName(item.getDlrName());
                interactTestDriveSheetVO.setRecordId(item.getRecordId());
                rspVO.setBusinessInfo(interactTestDriveSheetVO);
                listResult.add(rspVO);
            });
        }
        // 所有数据根据triggerTime进行倒序排序
        listResult.sort(Comparator.comparing(ClueInteractRspVO::getTriggerTime,
                Comparator.nullsLast(Comparator.reverseOrder())));

        DomainPage<ClueInteractRspVO> resultDomainPage = DomainPage.mergePages(listResult, pageCustEvent, pageVRRecords,
                pageOnecustResume, pageTestDriveSheet);
        return resultDomainPage;
    }

}
