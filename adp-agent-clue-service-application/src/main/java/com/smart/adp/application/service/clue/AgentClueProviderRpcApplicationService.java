package com.smart.adp.application.service.clue;

import com.smart.adp.application.dto.clue.*;
import com.smart.adp.application.vo.clue.*;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/3/3 10:16
 * @description 店端线索提供给外部服务application 服务接口
 **/
public interface AgentClueProviderRpcApplicationService {

    /**
     * 详情页用户旅程集合
     *
     * @param param
     * @return
     */
    List<ClueEventFlowRspVO> queryListUserEventFlow(ClueEventFlowDTO param);

    /**
     * 查询线索扩展信息
     *
     * @param param
     * @return
     */
    List<SacOneCustRemarkRspVO> queryClueRemark(ClueRemarkDTO param);

    /**
     * 更新意向车信息
     *
     * @param param
     * @return
     */
    String modifyIntentionCarForStartDriving(ModifyIntentionCarDTO param);
}
