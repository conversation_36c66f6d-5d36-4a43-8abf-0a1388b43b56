package com.smart.adp.application.service.base.impl;

import com.mybatisflex.core.util.CollectionUtil;
import com.smart.adp.application.service.base.WorkbenchApplicationService;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.resp.RespHelper;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.enums.StatisticsTypeEnum;
import com.smart.adp.domain.service.base.WorkbenchService;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.domain.valueObject.base.StatisticsVO;
import com.smart.adp.infrastructure.feign.DWSFeign;
import com.smart.adp.infrastructure.feign.request.SummaryDTO;
import com.smart.adp.infrastructure.feign.response.SummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/15
 */
@Slf4j
@Component
public class WorkbenchApplicationServiceImpl implements WorkbenchApplicationService {

    @Autowired
    public WorkbenchService workbenchService;

    @Autowired
    public DWSFeign dwsFeign;

    @Autowired
    @Qualifier("taskExecutor")
    private Executor taskExecutor;

    @Override
    public List<StatisticsVO> statistics(Set<StatisticsTypeEnum> types) {
        // 店端校验
        UserBusiEntity user = UserInfoContext.get();
        UserUtil.checkDlr(user);

        // 默认值处理
        if (CollectionUtil.isEmpty(types)) {
            if (UserUtil.isStoreManager(user.getStationId())) {
                types = StatisticsTypeEnum.STORE_MANAGER_STATISTICS;
            } else if (UserUtil.isProductExpert(user.getStationId())) {
                types = StatisticsTypeEnum.PRODUCT_EXPERT_STATISTICS;
            } else {
                types = Collections.emptySet();
            }
        }

        // do stat
        // TODO 考虑并行、异常处理
        return types.stream()
                    .map(type -> workbenchService.doStatistics(type, user))
                    .collect(Collectors.toList());
    }

    @Override
    public RespBody<SummaryVO> summary(SummaryDTO dto) {
        // check user
        UserBusiEntity user = UserInfoContext.get();
        UserUtil.checkDlr(user);

        boolean dlr = UserUtil.isStoreManager(user.getStationId());
        dto.setDlr(dlr);
        if (dlr) {
            dto.setDlrCode(user.getDlrCode());
        } else {
            dto.setUserIdList(CollectionUtil.newArrayList(user.getUserID()));
        }

        // feign
        return RespHelper.rateLimitFallback(() -> {
            RespBody<List<SummaryVO>> feignRes = dwsFeign.summary(dto);

            RespBody<SummaryVO> res;
            if (RespCode.OK.getCode().equals(feignRes.getCode())) {
                List<SummaryVO> body = feignRes.getBody();
                if (CollectionUtil.isNotEmpty(body)) {
                    res = RespBody.ok(body.get(0));
                } else {
                    // no res
                    res = RespBody.ok(new SummaryVO());
                }
            } else {
                res = RespHelper.rateLimitRes();
            }

            return res;
        });
    }

    @Override
    public RespBody<List<SummaryVO>> summaryList(SummaryDTO dto) {
        // check user
        UserBusiEntity user = UserInfoContext.get();
        UserUtil.checkDlr(user);
        if (!UserUtil.isStoreManager(user.getStationId())) {
            return RespBody.fail(RespCode.UNAUTHORIZED, "权限不足", null);
        }

        if (CollectionUtil.isEmpty(dto.getUserIdList())) {
            return RespBody.ok(Collections.emptyList());
        }

        dto.setDlr(Boolean.FALSE);

        // feign
        return RespHelper.rateLimitFallback(() -> dwsFeign.summary(dto));
    }

}
