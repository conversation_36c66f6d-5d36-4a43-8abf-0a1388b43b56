package com.smart.adp.application.vo.clue;

import com.mybatisflex.annotation.Column;
import com.smart.adp.application.assembler.AssemblerMoreSourceFactory;
import com.smart.adp.application.assembler.clue.ClueDlrDetailVOAssembler;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacOneCustRemark;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import com.smart.adp.domain.entity.clue.SacUserGroupDetailEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/4
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "线索详情 VO")
public class ClueDlrDetailRspVO implements Serializable {

    /**
     * ID
     */
    @Schema(description = "线索 ID")
    private String id;

    /**
     * 客户 ID
     */
    @Schema(description = "客户 ID")
    private String custId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String custName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 阶段
     */
    @Schema(description = "阶段")
    private Integer stage;


    /**
     * 状态编码
     */
    @Schema(description = "状态编码")
    private String statusCode;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 线索等级 - 营销助手维护
     */
    @Schema(description = "线索等级")
    private String level;

    /**
     * 用户等级
     */
    @Schema(description = "用户等级")
    private String userLevel;

    /**
     * 渠道来源
     */
    @Schema(description = "意向车型")
    private String intentionCarType;

    /**
     * 所在地
     */
    @Schema(description = "所在地")
    private String location;
    /**
     * 所在地
     */
    @Schema(description = "所在地Code")
    private String locationCode;

    /**
     * 线索来源
     */
    @Schema(description = "线索来源")
    private String channelSource;

    /**
     * 代理商线索来源
     */
    @Schema(description = "代理商线索来源")
    private String agentSource;

    /**
     * 竞品车型
     */
    @Schema(description = "竞品车型")
    private String competitorCarType;
    /**
     * 是否特别关注
     */
    @Schema(description = "是否特别关注，1是，0不是")
    private String isSpecial;
    /**
     * 性别
     */
    @Schema(description = "性别，0女1男")
    private String genderCode;
    /**
     * 商机热度编码
     */
    @Schema(description = "商机热度编码")
    private String businessOpportunityHeat;
    /**
     * 用户分组
     */
    @Schema(description = "用户分组")
    private String userGroupName;

    /**
     * 是否当日活跃
     */
    @Schema(description = "是否当日活跃")
    private Boolean activeOnTheDay;

    /**
     * 关联业务单号
     */
    @Schema(description = "关联业务单号")
    private String billCode;



    public static ClueDlrDetailRspVO convent(SacClueInfoDlr clue, SacReviewBO reviewBO, SacOnecustInfoEntity onecustInfo,
                                             SacUserGroupDetailEntity userGroupEntity, SacOneCustRemark sacOneCustRemarkEntity) {
        return AssemblerMoreSourceFactory.getInstance().convert(ClueDlrDetailVOAssembler.class, ClueDlrDetailRspVO.class, clue,
                reviewBO, onecustInfo, userGroupEntity, sacOneCustRemarkEntity);
    }
}
