package com.smart.adp.application.event;

import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * date 2025/3/11 15:49
 * @description 潜客异步事件
 **/
@Getter
@ToString
public class ClueCustInfoEvent extends ApplicationEvent {

    private AgentClueSaveDTO agentClueSaveDTO;

    public ClueCustInfoEvent(Object source, AgentClueSaveDTO param) {
        super(source);
        this.agentClueSaveDTO = param;
    }
}
