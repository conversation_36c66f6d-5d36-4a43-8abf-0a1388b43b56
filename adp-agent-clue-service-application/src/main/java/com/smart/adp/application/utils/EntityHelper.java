package com.smart.adp.application.utils;

import cn.hutool.core.util.StrUtil;
import com.smart.adp.application.dto.clue.ModifyClueCustDTO;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.entity.clue.SacAllClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacOnecustInfoEntity;
import com.smart.adp.domain.entity.clue.SacReview;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/5/15 13:18
 * @description 实体类辅助工具方法
 **/
@Slf4j
public class EntityHelper {

    /**
     * 处理性别星系
     *
     * @param param
     */
    public static void setGenderInfo(ModifyClueCustDTO param) {
        if (StrUtil.isNotBlank(param.getGenderName())) {
            if (StrUtil.isBlank(param.getGenderCode())) {
                // 默认值
                param.setGenderCode("0");
                if ("男".equals(param.getGenderName())) {
                    param.setGenderCode("1");
                }
            }
        }
    }

    /**
     * 抽象类信息转换
     *
     * @param entity
     * @param userInfo
     * @param updateTime
     * @param phone
     * @param <T>
     */
    public static <T> void setCommonFields(T entity, UserBusiEntity userInfo, LocalDateTime updateTime, String phone) {
        if (entity instanceof SacOnecustInfoEntity) {
            SacOnecustInfoEntity e = (SacOnecustInfoEntity) entity;
            e.setLastUpdatedDate(updateTime);
            e.setModifier(userInfo.getUserID());
            e.setModifyName(userInfo.getEmpName());
            e.setPhone(phone);
        } else if (entity instanceof SacClueInfoDlr) {
            SacClueInfoDlr e = (SacClueInfoDlr) entity;
            e.setLastUpdatedDate(updateTime);
            e.setModifier(userInfo.getUserID());
            e.setModifyName(userInfo.getEmpName());
            e.setPhone(phone);
        } else if (entity instanceof SacReview) {
            SacReview e = (SacReview) entity;
            e.setLastUpdatedDate(updateTime);
            e.setModifier(userInfo.getUserID());
            e.setModifyName(userInfo.getEmpName());
            e.setPhone(phone);
        } else if (entity instanceof SacAllClueInfoDlr) {
            SacAllClueInfoDlr e = (SacAllClueInfoDlr) entity;
            e.setLastUpdatedDate(updateTime);
            e.setModifier(userInfo.getUserID());
            e.setModifyName(userInfo.getEmpName());
            e.setPhone(phone);
        }
    }
}
