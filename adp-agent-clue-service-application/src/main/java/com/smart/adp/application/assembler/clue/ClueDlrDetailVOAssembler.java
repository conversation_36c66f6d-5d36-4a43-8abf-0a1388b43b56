package com.smart.adp.application.assembler.clue;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.assembler.AssemblerMoreSource;
import com.smart.adp.application.vo.clue.ClueDlrDetailRspVO;
import com.smart.adp.domain.bo.clue.SacReviewBO;
import com.smart.adp.domain.context.Area;
import com.smart.adp.domain.entity.clue.*;

import java.time.LocalDate;

public class ClueDlrDetailVOAssembler implements AssemblerMoreSource<ClueDlrDetailRspVO> {

    /**
     * 从多个源对象装配到目标对象
     *
     * @param targetType 目标类型
     * @param sources    可变长度的源对象
     * @return 装配后的目标对象
     */
    @Override
    public ClueDlrDetailRspVO assemble(Class<ClueDlrDetailRspVO> targetType, Object... sources) {
        if (ObjectUtil.isEmpty(sources)) {
            return null;
        }
        ClueDlrDetailRspVO detailVo = new ClueDlrDetailRspVO();
        // 遍历所有源对象，提取字段
        for (Object source : sources) {
            if (ObjectUtil.isNotEmpty(source) && source instanceof SacClueInfoDlr) {
                SacClueInfoDlr clue = (SacClueInfoDlr) source;
                detailVo.setCustId(clue.getCustId());//客户姓名
                detailVo.setCustName(clue.getCustName());//客户姓名
                detailVo.setPhone(clue.getPhone());//客户姓名
                detailVo.setLevel(clue.getIntenLevelCode());//意向等级
                detailVo.setIntentionCarType(clue.getIntenCarTypeCode());//意向车型
                detailVo.setChannelSource((ObjectUtil.isEmpty(clue.getInfoChanMName()) || ObjectUtil.isEmpty(clue.getInfoChanDName()))
                        ? null : clue.getInfoChanMName() + "|" + clue.getInfoChanDName());//线索来源
                detailVo.setIsSpecial(clue.getColumn11());//是否特别关注
                detailVo.setGenderCode(clue.getGenderCode());//性别
                detailVo.setBusinessOpportunityHeat(clue.getColumn5());//商机热度编码
                detailVo.setStatusCode(clue.getStatusCode());
            } else if (ObjectUtil.isNotEmpty(source) && source instanceof SacReviewBO) {
                SacReview review = (SacReview) source;
                detailVo.setBillCode(review.getBillCode());//关联业务单号
            } else if (ObjectUtil.isNotEmpty(source) && source instanceof SacOnecustInfoEntity) {
                SacOnecustInfoEntity oneCust = (SacOnecustInfoEntity) source;
                detailVo.setCompetitorCarType(oneCust.getCompetitorTypeCn());//竞品车型
            } else if (ObjectUtil.isNotEmpty(source) && source instanceof SacUserGroupDetailEntity) {
                SacUserGroupDetailEntity userGroupEntity = (SacUserGroupDetailEntity) source;
                detailVo.setUserGroupName(userGroupEntity.getUserGroupName());//用户分组名称
            } else if (ObjectUtil.isNotEmpty(source) && source instanceof SacOneCustRemark) {
                SacOneCustRemark sacOneCustRemarkEntity = (SacOneCustRemark) source;
                detailVo.setDescription(sacOneCustRemarkEntity.getRemark());//描述
                detailVo.setLocation(Area.fullPath(sacOneCustRemarkEntity.getLocation(), null));//位置
                detailVo.setLocationCode(sacOneCustRemarkEntity.getLocation());//位置
                detailVo.setUserLevel(sacOneCustRemarkEntity.getClueLevel());//线索等级HABCD
                detailVo.setAgentSource(sacOneCustRemarkEntity.getClueSource());//代理商线索来源
                detailVo.setCompetitorCarType(sacOneCustRemarkEntity.getCompetitiveVehicleCode());//竞品车型
                detailVo.setActiveOnTheDay(ObjectUtil.isEmpty(sacOneCustRemarkEntity.getLastActiveTime())
                        ? Boolean.FALSE
                        : ObjectUtil.equals(LocalDate.now(), sacOneCustRemarkEntity.getLastActiveTime().toLocalDate()));//当日活跃
            }
        }
        return detailVo;
    }
}