package com.smart.adp.application.service.clue.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.CollectionUtil;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.application.constant.CommonConstant;
import com.smart.adp.application.dto.base.QueryUserGroupDTO;
import com.smart.adp.application.dto.clue.*;
import com.smart.adp.application.event.*;
import com.smart.adp.application.helper.ESInitHelper;
import com.smart.adp.application.service.base.BaseApplicationService;
import com.smart.adp.application.service.clue.AgentClueApplicationService;
import com.smart.adp.application.utils.*;
import com.smart.adp.application.vo.base.UserGroupVO;
import com.smart.adp.application.vo.clue.*;
import com.smart.adp.domain.bo.clue.*;
import com.smart.adp.domain.bo.drive.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.common.annotation.SmartADPCache;
import com.smart.adp.domain.common.constants.StringConstant;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.context.TimeContext;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.base.AgentClueSource;
import com.smart.adp.domain.entity.base.AgentDlrInfo;
import com.smart.adp.domain.entity.base.AgentEmployee;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.entity.clue.*;
import com.smart.adp.domain.entity.message.CustEventMessage;
import com.smart.adp.domain.entity.message.MessageContent;
import com.smart.adp.domain.entity.message.SendWecomMessage;
import com.smart.adp.domain.enums.*;
import com.smart.adp.domain.gateway.base.*;
import com.smart.adp.domain.gateway.clue.*;
import com.smart.adp.domain.helper.TypeDomainPageConventHelp;
import com.smart.adp.domain.qry.ClueDlrQry;
import com.smart.adp.domain.qry.ClueSearchQry;
import com.smart.adp.domain.service.clue.*;
import com.smart.adp.domain.service.drive.ISacTestDriveSheetService;
import com.smart.adp.domain.strategy.modifyClue.ClueModifyStrategy;
import com.smart.adp.domain.strategy.modifyClue.ClueModifyStrategyFactory;
import com.smart.adp.domain.strategy.queryInteractData.AllDataTypeIneractStrategy;
import com.smart.adp.domain.strategy.queryInteractData.TypedDomainPage;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.domain.valueObject.base.*;
import com.smart.adp.domain.valueObject.clue.*;
import com.smart.adp.domain.valueObject.clue.AgentClueSourceVO;
import com.smart.adp.infrastructure.helper.UserJourneysHelper;
import com.smart.adp.infrastructure.publisher.AgentDlrCreateCluePublisher;
import com.smart.adp.infrastructure.publisher.CustEventPublisher;
import com.smart.adp.infrastructure.utils.RedisUtil;
import com.smart.tools.utils.BeanUtils;
import com.smart.tools.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.smart.adp.domain.common.constants.BizConstants.NEGATIVE_PREFIX;
import static com.smart.adp.domain.common.constants.BizConstants.clueTagRedisKey;
import static com.smart.adp.domain.entity.clue.table.SacAllClueInfoDlrTableDef.SAC_ALL_CLUE_INFO_DLR;
import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;
import static com.smart.adp.domain.entity.clue.table.SacOnecustInfoEntityTableDef.SAC_ONECUST_INFO_ENTITY;
import static com.smart.adp.domain.entity.clue.table.SacReviewTableDef.SAC_REVIEW;


/**
 * <AUTHOR>
 * date 2025/3/3 10:17
 * @description 店端线索application 服务接口
 **/
@Slf4j
@Service
public class AgentClueApplicationServiceImpl implements AgentClueApplicationService {

    //region 【注入】
    @Autowired
    private ClueDlrGateway clueDlrGateway;

    @Autowired
    private AgentEmployeeGateway agentEmployeeGateway;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IClueDlrService clueDlrService;

    @Autowired
    private IOneCustInfoService oneCustInfoService;

    @Autowired
    private ISacUserGroupDetailService userGroupDetailService;

    @Autowired
    private ISacReviewService reviewService;

    @Autowired
    private LookUpGateway lookUpGateway;

    @Autowired
    private ActivityGateway activityGateway;

    @Autowired
    private OneCustInfoGateway oneCustInfoGateway;

    @Autowired
    private AgentDlrGateway agentDlrGateway;

    @Autowired
    private SystemConfigGateway systemConfigGateway;

    @Autowired
    private RemoveRepeatConfigGateway removeRepeatConfigGateway;

    @Autowired
    private ProvinceGateway provinceGateway;

    @Autowired
    private CityGateway cityGateway;

    @Autowired
    private CountyGateway countyGateway;

    @Autowired
    private CityClueSwitchGateway cityClueSwitchGateway;

    @Autowired
    private ClueModifyStrategyFactory strategyFactory;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ReviewGateway reviewGateway;

    @Autowired
    private CompetitiveCarModelGateway competitiveCarModelGateway;

    @Autowired
    private ISacOneCustRemarkService oneCustRemarkService;

    @Autowired
    private IRecordAbstractService recordAbstractService;

    @Autowired
    private ISacOnecustResumeService onecustResumeService;

    @Autowired
    private ISacOnecustInfoEventService sacOnecustInfoEventService;

    @Autowired
    private IUscMdmVirtualRecordService virtualRecordService;

    @Autowired
    private ISacTestDriveSheetService testDriveSheetService;

    @Autowired
    private AgentClueSourceGateway agentClueSourceGateway;

    @Autowired
    private ICustEventFlowService custEventFlowService;

    @Autowired
    private CustEventPublisher custEventPublisher;

    @Autowired
    private UserJourneysHelper userJourneysHelper;

    @Autowired
    private ESInitHelper esInitHelper;

    @Autowired
    private BaseApplicationService baseApplicationService;

    @Autowired
    private AllDataTypeIneractStrategy allDataTypeStrategy;

    @Autowired
    private ReviewHisGateway reviewHisGateway;

    @Autowired
    private AgentDlrCreateCluePublisher agentDlrCreateCluePublisher;
    //endregion

    /**
     * 代理商线索保存application
     *
     * @param agentClueDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClueDlrSaveVO agentDlrClueSave(AgentClueDTO agentClueDTO) {
        log.info("代理商线索保存,phone={},clueLevel={},location={}", agentClueDTO.getPhone(), agentClueDTO.getClueLevel(),
                agentClueDTO.getLocation());
        //获取登录用户信息
        UserBusiEntity userInfo = UserInfoContext.get();
        try {
            //1、代理商线索保存入参校验
            CheckParamUtils.checkSaveClueParam(agentClueDTO);
            AgentClueSaveDTO param = AgentClueDTO.convert(agentClueDTO);
            //2、线索回访人员校验，回访人员ID对应员工信息表的userId
            handelReviewPerson(param, userInfo);

            //3、手机号查询是否存在战败线索，存在移动至历史表
            this.handelDefeatClue(param, userInfo);

            //4、smartId 查重（设计层面潜客信息表与线索表是一对一的关系，存在潜客信息线索也是存在的）
            this.handelSmartIDClue(param);

            //5、ADP自建线索特殊校验
            CheckParamUtils.checkADPCreateClue(param);

            //专营店信息处理
            this.handelDlrInfo(param);

            //处理经销商配置信息
            this.handelConfigInfo(param, userInfo);

            //处理移除校验配置
            RemoveRepeatConfig removeRepeatConfig = this.handelRemoveRepeatConfig(param);

            //Map<String, Object> clueExistParamMap = handelClueExistParam(removeRepeatConfig, param, userInfo);

            //校验线索是否存在
            SacClueInfoDlr sacClueInfoDlr = this.handelAgentClueExist(param, removeRepeatConfig);
            this.handelClueExistResult(param, sacClueInfoDlr, userInfo);
            this.handelHostClueCheck(param);

            // 二维码市场留资校验
            this.handelActivityQRCheck(param);

            //构建线索模型信息
            this.handelClueEntity(param, userInfo);

            //构建回访任务模型
            SacReview sacReviewEntity = this.handelSacReviewEntity(param);

            //线索保存
            SacClueInfoDlr sacClueInfo = AgentClueSaveDTO.convert(param);
            if (param.getUpdateClue()) {
                //更新线索
                clueDlrGateway.updateClueInfo(sacClueInfo);
            } else {
                sacClueInfo.setColumn1(param.getPlanBuyDateName());
                sacClueInfo.setColumn2(param.getPlanBuyDate());
                sacClueInfo.setColumn5(param.getBusinessHeatName());
                sacClueInfo.setColumn6(param.getBusinessHeatCode());
                sacClueInfo.setReviewId(sacReviewEntity.getReviewId());
                if (StringUtils.isNotEmpty(param.getStage())) {
                    sacClueInfo.setColumn18(param.getStage());
                }
                clueDlrGateway.saveClueInfo(sacClueInfo);
            }

            if (sacReviewEntity != null) {
                reviewGateway.saveReview(sacReviewEntity);
            }

            //潜客信息维护异步事件
            ClueCustInfoEvent clueCustInfoEvent = new ClueCustInfoEvent(this, param);
            applicationContext.publishEvent(clueCustInfoEvent);

            // 异步日志
            ClueLogEvent clueLogEvent = new ClueLogEvent(this, param.getPhone(), param, param.getSystemRecord(),
                    param.getSystemSource(), userInfo);
            applicationContext.publishEvent(clueLogEvent);

            //处理返参信息
            ClueDlrSaveVO clueDlrSaveVO = new ClueDlrSaveVO();
            clueDlrSaveVO.setId(param.getId());
            clueDlrSaveVO.setCustId(param.getCustId());
            clueDlrSaveVO.setCustName(param.getCustName());
            clueDlrSaveVO.setServerOrder(param.getServerOrder());
            clueDlrSaveVO.setReviewId(sacReviewEntity.getReviewId());
            clueDlrSaveVO.setPhone(param.getPhone());
            clueDlrSaveVO.setDlrShortName(param.getDlrShortName());
            clueDlrSaveVO.setChannelName(param.getChannelName());
            clueDlrSaveVO.setIntenLevelCode(param.getIntenLevelCode());

            //自建线索发送cdp ADP -> CDP 异步通知cdp 通过MQ形式通知CDP
            if (!param.getCityOpenFlag()) {
                AgentClueCreateEvent agentClueCreateEvent = new AgentClueCreateEvent(this, param);
                applicationContext.publishEvent(agentClueCreateEvent);
            }

            // 用户旅程 MQ
            JSONObject mqParam = new JSONObject();
            mqParam.put("channelCode", sacClueInfo.getInfoChanDCode());
            custEventPublisher.sendCustEventMessage(CustEventMessage.builder()
                    .custId(sacClueInfo.getCustId())
                    .type(CustEventEnum.CREATE.getCode())
                    .businessId(sacClueInfo.getServerOrder())
                    .eventTime(TimeContext.now())
                    .extendJson(mqParam)
                    .build());
            return clueDlrSaveVO;
        } catch (Exception e) {
            log.error("自建线索，线索创建异常,agentClueDTO={}", JSONUtil.toJsonStr(agentClueDTO), e);
            throw new BusinessException(RespCode.FAIL.getCode(), "线索创建失败，请稍后重试！");
        }
    }

    /**
     * 构建回访任务信息
     *
     * @param param
     * @return
     */
    private SacReview handelSacReviewEntity(AgentClueSaveDTO param) {
        //新建线索
        SacReview sacReview = null;
        if (param.getCount() < 1) {
            if (!param.getUpdateClue()) {
                //回访任务前置校验
                param.setBillCode(param.getServerOrder());
                CheckParamUtils.checkSaveReviewParam(param);
                //回访信息
                sacReview = AgentClueSaveDTO.convertReview(param);
                sacReview.setReviewStatus("0");
                sacReview.setReviewStatusName("待回访");
                sacReview.setNodeCode("new");
                sacReview.setNodeName("新建回访单");
                sacReview.setIsCome("0");
                if (StrUtil.isNotBlank(param.getFactComeTime())) {
                    sacReview.setIsCome("1");
                }
                if (StrUtil.isBlank(param.getReviewPersonId())) {
                    // 设为未分配
                    sacReview.setAssignStatus(ReviewAssignStatusEnum.unAssign.getResult());
                    sacReview.setAssignStatusName(ReviewAssignStatusEnum.unAssign.getMsg());
                } else {
                    sacReview.setAssignStatus(ReviewAssignStatusEnum.assignEd.getResult());
                    sacReview.setAssignStatusName(ReviewAssignStatusEnum.assignEd.getMsg());
                    sacReview.setAssignTime(param.getAssignTime());
                }
                //超期回访时间
                if (StrUtil.isNotBlank(param.getPlanReviewTime())) {
                    sacReview.setOverReviewTime(LocalDateTime.parse(param.getPlanReviewTime(), DateTimeFormatter.ofPattern(
                            "yyyy-MM-dd HH:mm:ss")));
                }
                sacReview.setReviewId(UUID.randomUUID().toString());
                sacReview.setColumn1(param.getOutColorCode());
                sacReview.setColumn2(param.getOutColorName());
                sacReview.setColumn3(param.getInnerColorCode());
                sacReview.setColumn4(param.getInnerColorName());
                sacReview.setColumn7(param.getBusinessHeatCode());
                sacReview.setColumn8(param.getBusinessHeatName());
                sacReview.setColumn5(param.getPlanBuyDate());
                sacReview.setColumn6(param.getPlanBuyDateName());
                sacReview.setUpdateControlId(UUID.randomUUID().toString());
            }
        }
        return sacReview;
    }

    private void handelHostClueCheck(AgentClueSaveDTO param) {
        if (ClueMessageEnum.CLUE_NOT_EXIST.getCode().equals(param.getClueExist()) && ClueMessageEnum.CLUE_NOT_EXIST.getDesc().equals(param.getClueExistDesc())) {
            if ("HOST".equals(param.getDlrId()) && "HOST".equals(param.getDlrCode()) && "HOST".equals(param.getDlrShortName())) {
                param.setSystemSource("CDP");
                if (StrUtil.isBlank(param.getProvinceCode())) {
                    throw new BusinessException(RespCode.FAIL.getCode(), "城市线索省份编码不能为空");
                }
                if (StrUtil.isBlank(param.getProvinceName())) {
                    throw new BusinessException(RespCode.FAIL.getCode(), "城市线索省份名称不能为空");
                }
                if (StrUtil.isBlank(param.getCityCode())) {
                    throw new BusinessException(RespCode.FAIL.getCode(), "城市线索城市编码不能为空");

                }
                if (StrUtil.isBlank(param.getCityName())) {
                    throw new BusinessException(RespCode.FAIL.getCode(), "城市线索城市名称不能为空");
                }
            }
        }
    }

    private void handelActivityQRCheck(AgentClueSaveDTO param) {
        if (CommonConstant.SHARE_SWITCH_ON.equals(param.getFlag())) {
            Date time = new Date();
            ActivityInfo activityInfo = activityGateway.findActivityData(param.getActivityId(), time, "DEVELOP", "1", "2", "1");
            if (Objects.isNull(activityInfo)) {
                String msg = String.format("当前活动已过期,二维码已失效");
                throw new BusinessException(RespCode.FAIL.getCode(), msg);
            }
        }
    }

    /**
     * 处理线索结果
     *
     * @param param
     */
    public void handelClueExistResult(AgentClueSaveDTO param, SacClueInfoDlr sacClueInfoDlr, UserBusiEntity userInfo) {
        if (Objects.isNull(param.getCount()) || param.getCount() == 0) {
            param.setClueExist(ClueMessageEnum.CLUE_NOT_EXIST.getCode());
            param.setClueExistDesc(ClueMessageEnum.CLUE_NOT_EXIST.getDesc());
        } else {
            throw new BusinessException(RespCode.FAIL.getCode(), ClueMessageEnum.CLUE_BASE_DLR_05.getDesc());
        }
    }

    private void handelClueEntity(AgentClueSaveDTO param, UserBusiEntity userInfo) {
        if (StringUtils.isEmpty(param.getId())) {
            param.setUpdateClue(Boolean.FALSE);
            param.setId(UUID.randomUUID().toString().replace("_", ""));
            param.setIsEnable("1");
            if (StringUtils.isEmpty(param.getDlrCode())) {
                param.setDlrCode(userInfo.getDlrCode());
                param.setDlrShortName(userInfo.getDlrShortName());
            }
            param.setStatusCode(ClueStatusEnum.AWAITING_ALLOCATION.getCode());
            param.setStatusName(ClueStatusEnum.AWAITING_ALLOCATION.getDesc());
            if (StrUtil.isNotBlank(param.getReviewPersonId())) {
                param.setStatusCode(ClueStatusEnum.AWAITING_FOLLOW_UP.getCode());
                param.setStatusName(ClueStatusEnum.AWAITING_FOLLOW_UP.getDesc());
                param.setAssignTime(LocalDateTime.now());
            }
            param.setChannelName("");
            if (StringUtils.isNotEmpty(param.getInfoChanMName())) {
                param.setChannelName(param.getInfoChanMName());
            }
            param.setChannelCode("");
            if (StringUtils.isNotEmpty(param.getInfoChanMCode())) {
                param.setChannelCode(param.getInfoChanMCode());
            }
            //planReviewTime  下次回访时间，前端没传，则默认给当前时间+1天
            param.setCreatedDate(LocalDateTime.now());
            if (StringUtils.isEmpty(param.getPlanReviewTime())) {
                if (StringUtils.isEmpty(param.getSystemSource()) || "CDP".equals(param.getSystemSource())) {
                    log.info("CDP线索来源,计划回访时间为空,时间判断开始,systemSource={}", param.getSystemSource());
                    param.setIsCdp(CommonConstant.SHARE_SWITCH_ON);
                    //查询VE1130时间配置信息
                    LookUpInfo lookUpInfo = lookUpGateway.findLookUpConfig("VE1130");
                    // 获取当前时间或指定时间
                    LocalDateTime now = Objects.isNull(param.getMorkPlanReviewTime())
                            ? LocalDateTime.now()
                            : LocalDateTimeUtil.parseStringToDateTime(param.getMorkPlanReviewTime());
                    // 计算出下次回访时间
                    LocalDateTime nextReviewTime = CalculateUtils.calculateOverDueTime(now, lookUpInfo);
                    if (Objects.nonNull(nextReviewTime)) {
                        param.setPlanReviewTime(LocalDateTimeUtil.getDateTimeAsString(nextReviewTime));
                    }
                } else {
                    //ADP 自建线索没有传递计划回访时间时，默认24小时
                    Calendar planReviewTimeCal = Calendar.getInstance();
                    planReviewTimeCal.add(Calendar.DATE, 1);
                    param.setPlanReviewTime(DateFormatUtils.format(planReviewTimeCal.getTime(), "yyyy-MM-dd HH:mm:ss"));
                }
            }
            param.setOemId(userInfo.getOemID());
            param.setOemCode(userInfo.getOemCode());
            param.setGroupId(userInfo.getGroupID());
            param.setGroupCode(userInfo.getGroupCode());
            param.setCreatedName(userInfo.getEmpName());
            param.setModifyName(userInfo.getEmpName());
            param.setCreator(userInfo.getUserID());
            if (param.getExistDefeatClue()) {
                param.setCreator(param.getCreatorT());
                param.setCreatedDate(param.getCreatedDateT());
                param.setCreatedName(param.getCreatedNameT());
            }
            param.setModifier(userInfo.getUserID());
            param.setUpdateControlId(UUID.randomUUID().toString().replace("_", ""));

            param.setLastUpdatedDate(LocalDateTime.now());
            param.setServerOrder(UUID.randomUUID().toString().replace("_", ""));

            //处理门店对应的省市区信息
            this.handelDlrLocation(param);

            //系统来源不存在，存在员工code时补全门店信息
            if (StrUtil.isBlank(param.getSystemSource()) && StrUtil.isNotBlank(param.getSellerCode())) {
                AgentEmployee agentEmployee = agentEmployeeGateway.findEmployee(param.getSellerCode());
                param.setReviewPersonId(agentEmployee.getUserId());
                param.setReviewPersonName(agentEmployee.getEmpName());
                if (Objects.nonNull(agentEmployee)) {
                    AgentDlrInfo agentDlrInfo = agentDlrGateway.findDlrInfo(agentEmployee.getDlrCode());
                    if (Objects.nonNull(agentDlrInfo)) {
                        param.setDlrId(agentDlrInfo.getDlrId());
                        param.setDlrCode(agentDlrInfo.getDlrCode());
                        param.setDlrShortName(agentDlrInfo.getDlrShortName());
                    }
                }
            }

            //系统来源不存在，不存在员工code时，门店code存在时，查询开关补全信息
            if (StrUtil.isBlank(param.getSystemSource()) && StrUtil.isBlank(param.getSellerCode()) && StrUtil.isNotBlank(param.getDlrCode())) {
                CityClueSwitch cityClueSwitch = cityClueSwitchGateway.findOne(param.getDlrCode());
                if (Objects.isNull(cityClueSwitch) || "0".equals(cityClueSwitch.getSwitchValue())) {
                    param.setDlrId("HOST");
                    param.setDlrCode("HOST");
                    param.setDlrShortName("HOST");
                    //fix-bug #710922619下发门店线索到已关闭获取线索的门店，该线索分配给其他门店
                    param.setReviewPersonId(null);
                    param.setReviewPersonName(null);
                    //此操作是为了判断A店城市线索关闭，但是同一城市下B店城市线索开启，根据该标准禁止去分配B店
                    param.setCityOpenFlag(Boolean.TRUE);
                } else {
                    if (Objects.nonNull(userInfo) && StrUtil.isNotBlank(userInfo.getDlrCode()) && !"HOST".equals(userInfo.getDlrCode())
                            && !"smart_bm_0005".equals(userInfo.getStationId()) && !"smart_bm_0016".equals(userInfo.getStationId())) {
                        // 非店长角色
                        param.setReviewPersonId(userInfo.getUserID());
                        param.setReviewPersonName(userInfo.getEmpName());
                    }
                }
            }
        } else {
            param.setLastUpdatedDate(LocalDateTime.now());
            param.setModifier(userInfo.getUserID());
            param.setModifyName(userInfo.getEmpName());
            param.setUpdateClue(Boolean.TRUE);
        }
    }

    /**
     * 处理门店对应的省市区信息
     *
     * @param param
     */
    private void handelDlrLocation(AgentClueSaveDTO param) {
        //处理门店对应的省市区
        AgentDlrDTO agentDlrDTO = null;
        if (StrUtil.isNotBlank(param.getDlrCode())) {
            String cacheKey = CommonConstant.CACHE_PREFIX + CommonConstant.CLUE_PROVINCE_CITY_COUNTY + ":" + param.getDlrCode();
            String cachedValue = (String) redisUtil.get(cacheKey);
            if (StringUtils.isNotEmpty(cachedValue)) {
                agentDlrDTO = JSONUtil.toBean(cachedValue, AgentDlrDTO.class);
            } else {
                agentDlrDTO = new AgentDlrDTO();
                AgentDlrInfo agentDlrInfo = this.getDlrInfoWithCache(param.getDlrCode());
                if (Objects.nonNull(agentDlrInfo)) {
                    agentDlrDTO.setDlrCode(agentDlrInfo.getDlrCode());
                    agentDlrInfo.setDlrShortName(agentDlrInfo.getDlrShortName());
                    ProvinceInfo provinceInfo = provinceGateway.findProvinceByProvinceId(agentDlrInfo.getProvinceId());
                    if (Objects.nonNull(provinceInfo)) {
                        agentDlrDTO.setProvinceCode(provinceInfo.getProvinceCode());
                        agentDlrDTO.setProvinceName(provinceInfo.getProvinceName());
                    }
                    CityInfo cityInfo = cityGateway.findCityByCityId(agentDlrInfo.getCityId());
                    if (Objects.nonNull(cityInfo)) {
                        agentDlrDTO.setCityCode(cityInfo.getCityCode());
                        agentDlrDTO.setCityName(cityInfo.getCityName());
                    }
                    CountyInfo countyInfo = countyGateway.findCountyByCountyId(agentDlrInfo.getCountyId());
                    if (Objects.nonNull(countyInfo)) {
                        agentDlrDTO.setCountyCode(countyInfo.getCountyCode());
                        agentDlrDTO.setCountyName(countyInfo.getCountyName());
                    }
                    redisUtil.set(cacheKey, JSONUtil.toJsonStr(agentDlrDTO), 30 * 86400, TimeUnit.SECONDS);
                }
            }
        }
        if (Objects.nonNull(agentDlrDTO)) {
            if (StrUtil.isBlank(param.getProvinceName()) && StrUtil.isBlank(param.getProvinceName())) {
                param.setProvinceCode(agentDlrDTO.getProvinceCode());
                param.setProvinceName(agentDlrDTO.getProvinceName());
            }
            if (StrUtil.isBlank(param.getCityCode()) && StrUtil.isBlank(param.getCityName())) {
                param.setCityCode(agentDlrDTO.getCityCode());
                param.setCityName(agentDlrDTO.getCityName());
            }
            if (StrUtil.isBlank(param.getCountyCode()) && StrUtil.isBlank(param.getCountyName())) {
                param.setCountyCode(agentDlrDTO.getCountyCode());
                param.setCountyName(agentDlrDTO.getCountyName());
            }
        }
    }

    /**
     * 处理线索查重参数
     *
     * @param removeRepeatConfig
     * @param param
     * @param userInfo
     * @return
     */
    public Map<String, Object> handelClueExistParam(RemoveRepeatConfig removeRepeatConfig, AgentClueSaveDTO param,
                                                    UserBusiEntity userInfo) {
        Map<String, Object> clueMap = new HashMap<>();
        clueMap.put("dlrCode", param.getDlrCode());
        clueMap.put("dlrShortName", param.getDlrShortName());
        clueMap.put("checkPhone", removeRepeatConfig.getCheckPhone());
        clueMap.put("checkTime", removeRepeatConfig.getCheckTime());
        clueMap.put("checkTimeHorizon", removeRepeatConfig.getCheckTimeHorizon());
        // 厂家设置【店端线索共享开关】启动时，去重查询时不区分专营店
        if (!"share".equals(removeRepeatConfig.getOrgCode())) {
            clueMap.put("dlrCode", userInfo.getDlrCode());
            clueMap.put("dlrShortName", userInfo.getDlrName());
        } else {
            clueMap.remove("dlrCode");
            clueMap.remove("dlrShortName");
        }

        return clueMap;
    }

    /**
     * 校验线索是否存在
     *
     * @param param
     */
    public SacClueInfoDlr handelAgentClueExist(AgentClueSaveDTO param, RemoveRepeatConfig removeRepeatConfig) {
        SacClueInfoDlr sacClueInfoDlr = null;
        if (!"false".equals(param.getGooFlag())) {
            // 提前校验配置是否有效，减少嵌套层级
            if (!isRemoveRepeatConfigValid(removeRepeatConfig)) {
                return new SacClueInfoDlr();
            }

            // 初始化 count，移除冗余的 param.setCount(0)
            int count = 0;
            // 查询线索（提取方法，简化主逻辑）
            sacClueInfoDlr = findClueByPhoneIfNeeded(param.getPhone());
            if (sacClueInfoDlr == null) {
                return new SacClueInfoDlr();
            }
            count = 1;
            // 时间范围检查（提取方法，避免主逻辑臃肿）
            boolean isWithinCheckTime = isWithinCheckTimeHorizon(sacClueInfoDlr.getCreatedDate(), removeRepeatConfig);
            if (!isWithinCheckTime) {
                count = 0;
            }
            param.setCount(count);
            // 设置最终结果
            if (count > 0) {
                updateParamForDuplicate(param, sacClueInfoDlr, count);
            }
        }
        return sacClueInfoDlr;
    }

    /**
     * 检查去重配置是否有效
     */
    private boolean isRemoveRepeatConfigValid(RemoveRepeatConfig config) {
        if (Objects.isNull(config)) {
            return Boolean.FALSE;
        }
        String checkPhone = config.getCheckPhone();
        String custom = config.getCustom();
        return CommonConstant.SHARE_SWITCH_ON.equals(checkPhone) && StringUtils.isNotEmpty(custom);
    }

    /**
     * 按需查询线索
     */
    private SacClueInfoDlr findClueByPhoneIfNeeded(String phone) {
        try {
            return clueDlrGateway.findClueByPhone(phone);
        } catch (Exception e) {
            log.error("查询线索异常, phone: {}", phone, e);
            return null;
        }
    }

    /**
     * 判断线索是否在配置的时间范围内
     */
    private boolean isWithinCheckTimeHorizon(LocalDateTime createdDate, RemoveRepeatConfig config) {
        if (!CommonConstant.SHARE_SWITCH_ON.equals(config.getCheckTime())) {
            // 未开启时间检查，默认认为有效
            return Boolean.TRUE;
        }

        String horizonStr = config.getCheckTimeHorizon();
        if (StrUtil.isBlank(horizonStr)) {
            log.warn("CheckTime 已开启，但 CheckTimeHorizon 为空");
            return Boolean.TRUE;
        }
        try {
            long days = Long.parseLong(horizonStr);
            LocalDateTime thresholdTime = LocalDateTime.now().minusDays(days);
            // 等价于 createdDate >= thresholdTime
            return !thresholdTime.isAfter(createdDate);
        } catch (NumberFormatException e) {
            log.error("CheckTimeHorizon 格式错误: {}", horizonStr);
            return Boolean.TRUE;
        }
    }

    /**
     * 更新参数为重复线索状态
     */
    private void updateParamForDuplicate(AgentClueSaveDTO param, SacClueInfoDlr clue, int count) {
        param.setId(clue.getId());
        param.setCount(count);
        param.setStatusCode(ClueStatusEnum.DUPLICATE_LEADS.getCode());
        param.setStatusName(ClueStatusEnum.DUPLICATE_LEADS.getDesc());
    }

    /**
     * 填充门店信息
     *
     * @param param
     */
    public void handelDlrInfo(AgentClueSaveDTO param) {
        if (!"false".equals(param.getGooFlag())) {
            if (StrUtil.isNotEmpty(param.getDlrCode()) && StrUtil.isEmpty(param.getDlrShortName())) {
                AgentDlrInfo agentDlrInfo = this.getDlrInfoWithCache(param.getDlrCode());
                if (Objects.nonNull(agentDlrInfo)) {
                    param.setDlrShortName(agentDlrInfo.getDlrShortName());
                }
            }
        }
    }

    /**
     * 通过缓存获取门店信息（缓存5分钟）
     *
     * @param dlrCode
     * @return
     */
    private AgentDlrInfo getDlrInfoWithCache(String dlrCode) {
        String cacheKey = CommonConstant.CACHE_PREFIX + "dlrCode:" + dlrCode;
        String cachedValue = (String) redisUtil.get(cacheKey);

        // 缓存命中
        if (StringUtils.isNotEmpty(cachedValue)) {
            return JSONUtil.toBean(cachedValue, AgentDlrInfo.class);
        }

        // 缓存未命中，查询数据库
        AgentDlrInfo agentDlrInfo = agentDlrGateway.findDlrInfo(dlrCode);
        // 缓存空值防止穿透（设置较短过期时间）
        if (agentDlrInfo == null) {
            redisUtil.set(cacheKey, "", 60, TimeUnit.SECONDS);
            log.warn("agentDlrInfo not found in DB, id: {}", dlrCode);
            return new AgentDlrInfo();
        }

        // 缓存有效数据
        redisUtil.set(cacheKey, JSONUtil.toJsonStr(agentDlrInfo), 86400, TimeUnit.SECONDS);
        return agentDlrInfo;
    }

    /**
     * smartId查重线索
     *
     * @param param
     */
    public void handelSmartIDClue(AgentClueSaveDTO param) {
        if (!CommonConstant.ADP_TRANSFER.equals(param.getSystemSource()) && StringUtils.isNotEmpty(param.getSmartId())) {
            List<SacOnecustInfoEntity> sacOnecustInfoEntities = oneCustInfoGateway.findListBySmartId(param.getSmartId());
            if (CollUtil.isNotEmpty(sacOnecustInfoEntities)) {
                // 不存在战败线索时候会维护当前结果
                if (!param.getExistDefeatClue()) {
                    param.setGooFlag(CommonConstant.G00_FLAG);
                    param.setSystemRecord(CommonConstant.G00_FLAG_DESC);
                }
            }
        }
    }

    /**
     * 处理战败线索
     *
     * @param param
     */
    public void handelDefeatClue(AgentClueSaveDTO param, UserBusiEntity userInfo) {
        List<SacAllClueInfoDlr> sacAllClueInfoDlrList = clueDlrGateway.findDefeatedClueByPhone(param.getPhone(),
                ClueStatusEnum.DEFEATED.getCode());
        SacAllClueInfoDlr sacAllClueInfoDlr = null;
        if (CollUtil.isNotEmpty(sacAllClueInfoDlrList)) {
            sacAllClueInfoDlr = sacAllClueInfoDlrList.get(0);
            //战败激活 需要继承如下字段
            param.setCreatorT(userInfo.getUserID());
            param.setCreatedNameT(userInfo.getEmpName());
            param.setCreatedDateT(sacAllClueInfoDlr.getCreatedDate());
            param.setFirstReviewTime(sacAllClueInfoDlr.getFirstReviewTime());
            param.setStage(sacAllClueInfoDlr.getColumn18());
            param.setExistDefeatClue(Boolean.TRUE);
            //线索战败时，潜客信息的custId
            param.setCustId(sacAllClueInfoDlr.getCustId());
            int num = clueDlrGateway.saveHistoryClueInfo(sacAllClueInfoDlr);
            if (num < 1) {
                throw new BusinessException(RespCode.FAIL.getCode(), "插入店端线索历史记录失败,请稍后重试！");
            }
            if (StrUtil.isNotBlank(param.getPhone())) {
                int delNum = clueDlrGateway.delDefeatedClueByPhone(param.getPhone());
                if (delNum < 1) {
                    throw new BusinessException(RespCode.FAIL.getCode(), "删除店端线索记录失败,请稍后重试！");
                }
            }
        }

        //是否注销线索 1 注销 特殊的战败激活场景 无注销场景 保留原有线索渠道信息；注销场景不保留历史渠道信息
        if (Objects.nonNull(sacAllClueInfoDlr) && (Objects.isNull(sacAllClueInfoDlr.getColumn20())
                || (Objects.nonNull(sacAllClueInfoDlr.getColumn20()) && !CommonConstant.SHARE_SWITCH_ON.equals(sacAllClueInfoDlr.getColumn20())))) {
            param.setInfoChanMCode(sacAllClueInfoDlr.getInfoChanMCode());
            param.setInfoChanMName(sacAllClueInfoDlr.getInfoChanMName());
            param.setInfoChanDCode(sacAllClueInfoDlr.getInfoChanDCode());
            param.setInfoChanDName(sacAllClueInfoDlr.getInfoChanDName());
            LookUpInfo lookUpInfo = getLookUpInfoWithCache("ADP_CLUE_049", sacAllClueInfoDlr.getInfoChanMCode());
            if (Objects.isNull(lookUpInfo) || !"1".equals(lookUpInfo.getIsEnable())) {
                throw new RuntimeException("二级渠道" + sacAllClueInfoDlr.getInfoChanMName() + "值列表不存在，不进行新建");
            }
            param.setFirstChannel(lookUpInfo.getAttribute1());
        } else {
            if (StrUtil.isNotBlank(param.getActivityId())) {
                ActivityInfo activityInfo = activityGateway.findActivityInfo(param.getActivityId(), "1");
                if (Objects.nonNull(activityInfo)) {
                    param.setCActivityVariety("用户运营活动");
                    if ("DEVELOP".equals(activityInfo.getCreateTypeCode())) {
                        param.setCActivityVariety("代理商活动");
                    }
                    param.setResumeDesc(activityInfo.getActivityName());
                    param.setInfoChanDName(activityInfo.getInfoChanDName());
                    if (StrUtil.isBlank(activityInfo.getInfoChanDCode())) {
                        param.setInfoChanDCode(CommonConstant.INFO_CHAN_D_CODE_ACTIVITY);
                    } else {
                        param.setInfoChanDCode(activityInfo.getInfoChanDCode());
                    }
                    if (StrUtil.isBlank(activityInfo.getInfoChanDDesc())) {
                        param.setInfoChanDDesc(activityInfo.getActivityName());
                    } else {
                        param.setInfoChanDDesc(activityInfo.getInfoChanDDesc());
                    }
                }
            }
            //一级信息来源名称 为活动报名时 设置code=ho_operationcampaign
            if (StrUtil.isNotBlank(param.getInfoChanMName()) && CommonConstant.INFO_CHAN_M_NAME_ACTIVITY.equals(param.getInfoChanMName())) {
                param.setInfoChanDCode(CommonConstant.INFO_CHAN_M_CODE_ACTIVITY);
            }
            if (StrUtil.isNotBlank(param.getFixedSecondChannel())) {
                param.setInfoChanMCode(param.getFixedSecondChannel());
            }
            if (StrUtil.isNotBlank(param.getFixedThirdChannel())) {
                param.setInfoChanDCode(param.getFixedThirdChannel());
            }
            if (StrUtil.isNotBlank(param.getInfoChanDCode())) {
                LookUpInfo lookUpInfo = getLookUpInfoWithCache("ADP_CLUE_072", param.getInfoChanDCode());
                if (Objects.nonNull(lookUpInfo)) {
                    param.setInfoChanDName(lookUpInfo.getLookUpValueName());
                    //根据三级来源编码获取二级来源编码
                    param.setInfoChanMCode(lookUpInfo.getAttribute1());
                }
            }
            if (StrUtil.isNotBlank(param.getInfoChanMCode())) {
                LookUpInfo lookUpInfo = getLookUpInfoWithCache("ADP_CLUE_049", param.getInfoChanMCode());
                if (Objects.isNull(lookUpInfo) || !"1".equals(lookUpInfo.getIsEnable())) {
                    throw new RuntimeException("二级渠道" + param.getInfoChanMCode() + "值列表不存在，不进行新建");
                }
                param.setInfoChanMName(lookUpInfo.getLookUpValueName());
                param.setFirstChannel(lookUpInfo.getAttribute1());
            }
        }
    }

    /**
     * 校验回访人员在职状态信息
     *
     * @param param
     */
    public void handelReviewPerson(AgentClueSaveDTO param, UserBusiEntity userInfo) {
        //当前线索创建人如果是产品专家默认回访人员就是本人，店长角色需要指定回访人员
        if (UserUtil.isProductExpert(userInfo.getStationId())) {
            param.setReviewPersonId(userInfo.getUserID());
            param.setReviewPersonName(userInfo.getEmpName());
        }
        if (StringUtils.isEmpty(param.getReviewPersonId())) {
            return;
        }
        //获取员工信息
        String reviewPersonId = param.getReviewPersonId();
        AgentEmployee agentEmployee = getEmployeeWithCache(reviewPersonId, userInfo);
        //校验回访员工在职状态
        validateEmployeeStatus(agentEmployee, param.getPhone());
    }

    /**
     * 通过缓存获取员工信息（缓存24小时）
     */
    private AgentEmployee getEmployeeWithCache(String employeeId, UserBusiEntity userInfo) {
        String cacheKey = CommonConstant.CACHE_PREFIX + userInfo.getDlrCode() + ":" + employeeId;
        String cachedValue = (String) redisUtil.get(cacheKey);
        // 缓存命中
        if (StringUtils.isNotEmpty(cachedValue)) {
            return JSONUtil.toBean(cachedValue, AgentEmployee.class);
        }

        // 缓存未命中，查询数据库
        List<String> statusList = Collections.singletonList(EmployeeStatusEnum.WORKING.getCode());
        AgentEmployee employee = agentEmployeeGateway.findOne(employeeId, statusList);

        // 缓存空值防止穿透（设置较短过期时间）
        if (employee == null) {
            redisUtil.set(cacheKey, "", 60, TimeUnit.SECONDS);
            log.warn("Employee not found in DB, id: {}", employeeId);
            throw new BusinessException(RespCode.FAIL.getCode(), "回访人员不存在,请稍后重试！");
        }

        // 缓存有效数据
        redisUtil.set(cacheKey, JSONUtil.toJsonStr(employee), 24 * 60 * 60, TimeUnit.SECONDS);
        return employee;
    }

    /**
     * 验证员工在职状态
     */
    private void validateEmployeeStatus(AgentEmployee employee, String phone) {
        if (!EmployeeStatusEnum.WORKING.getCode().equals(employee.getUserStatus()) && !EmployeeStatusEnum.WORKING.getDesc().equals(employee.getUserStatus())) {
            String msg = String.format("线索下发手机号：%s, 回访人员ID:%s 状态异常（当前状态：%s）", phone, employee.getUserId(),
                    employee.getUserStatus());
            log.warn(msg);
            throw new BusinessException(RespCode.FAIL.getCode(), msg);
        }
    }

    /**
     * 获取基础配置信息
     *
     * @param lookUpTypeCode
     * @param lookUpValueCode
     * @return
     */
    public LookUpInfo getLookUpInfoWithCache(String lookUpTypeCode, String lookUpValueCode) {
        String cacheKey = CommonConstant.CACHE_PREFIX_LOOK + lookUpTypeCode + ":" + lookUpValueCode;
        String cachedValue = (String) redisUtil.get(cacheKey);
        // 缓存命中
        if (StringUtils.isNotEmpty(cachedValue)) {
            return JSONUtil.toBean(cachedValue, LookUpInfo.class);
        }

        LookUpInfo lookUpInfo = lookUpGateway.findLookUpInfo(lookUpTypeCode, lookUpValueCode);
        if (Objects.isNull(lookUpInfo)) {
            return new LookUpInfo();
        }
        // 缓存有效数据
        redisUtil.set(cacheKey, JSONUtil.toJsonStr(lookUpInfo), 24 * 60 * 60, TimeUnit.SECONDS);
        return lookUpInfo;
    }

    /**
     * 处理移除校验配置
     *
     * @return
     */
    private RemoveRepeatConfig handelRemoveRepeatConfig(AgentClueSaveDTO param) {
        String cachedKey = CommonConstant.CACHE_PREFIX + CommonConstant.REMOVE_CONFIG + ":" + param.getOrgCode();
        String cachedValue = (String) redisUtil.get(cachedKey);
        RemoveRepeatConfig removeRepeatConfig;
        // 缓存命中
        if (StringUtils.isNotEmpty(cachedValue)) {
            removeRepeatConfig = JSONUtil.toBean(cachedValue, RemoveRepeatConfig.class);
        } else {
            //分析生产数据 仅存在一条数据配置，基本不变化，考虑缓存一天
            List<RemoveRepeatConfig> removeRepeatConfigs = removeRepeatConfigGateway.findRemoveRepeatConfig(param.getOrgCode());
            if (CollUtil.isEmpty(removeRepeatConfigs)) {
                log.warn("未找到配置项: {}", CommonConstant.REMOVE_CONFIG);
                return new RemoveRepeatConfig();
            } else {
                removeRepeatConfig = removeRepeatConfigs.get(0);
                redisUtil.set(cachedKey, JSONUtil.toJsonStr(removeRepeatConfig), 86400, TimeUnit.SECONDS);
            }
        }
        return removeRepeatConfig;
    }

    /**
     * 处理经销商配置信息
     *
     * @param param
     * @param userInfo
     */
    private void handelConfigInfo(AgentClueSaveDTO param, UserBusiEntity userInfo) {
        if (!CommonConstant.CLUE_TYPE_DLR.equals(param.getClueType())) {
            handleHeadquartersLogic(param, userInfo);
        } else {
            handleDealerLogic(param, userInfo);
        }
    }

    /**
     * 处理总部逻辑
     */
    private void handleHeadquartersLogic(AgentClueSaveDTO param, UserBusiEntity userInfo) {
        if (CommonConstant.DLR_CODE_PV.equals(userInfo.getDlrCode())) {
            param.setOrgCode(userInfo.getDlrCode());
            param.setOrgName(userInfo.getDlrName());
        }
    }

    /**
     * 处理经销商逻辑
     */
    private void handleDealerLogic(AgentClueSaveDTO param, UserBusiEntity userInfo) {
        //1. 获取或补全 orgCode
        String orgCode = null;
        if (StrUtil.isBlank(param.getOrgCode())) {
            orgCode = userInfo.getDlrCode();
        }

        SystemConfigVO systemConfigVO = this.delSystemConfig(CommonConstant.CONFIG_CLUE_SHARE_SWITCH, orgCode,
                CommonConstant.SHARE_SWITCH_ON);
        // 3. 根据配置设置机构信息
        if (Objects.nonNull(systemConfigVO) && CommonConstant.SHARE_SWITCH_ON.equals(systemConfigVO.getValueCode())) {
            param.setOrgCode(CommonConstant.SHARE_ORG_CODE);
            param.setOrgName(CommonConstant.SHARE_ORG_NAME);
        } else {
            applyDefaultDealerConfig(param, userInfo);
        }
    }

    private SystemConfigVO delSystemConfig(String configCode, String orgCode, String isEnable) {
        SystemConfigVO systemConfigVO;
        // 2. 查询配置（需实现 systemConfigService）
        String cacheKey = CommonConstant.CACHE_PREFIX + configCode + ":" + orgCode;
        String cachedValue = (String) redisUtil.get(cacheKey);
        // 缓存命中
        if (StringUtils.isNotEmpty(cachedValue)) {
            systemConfigVO = JSONUtil.toBean(cachedValue, SystemConfigVO.class);
        } else {
            //排查线上数据尽存在一条数据配置，而且不会进行变化
            List<SystemConfigVO> systemConfigVOList =
                    systemConfigGateway.findSystemConfigInfo(configCode, orgCode, isEnable);
            if (CollUtil.isEmpty(systemConfigVOList)) {
                log.warn("未找到配置项: {}", CommonConstant.CONFIG_CLUE_SHARE_SWITCH);
                return new SystemConfigVO();
            } else {
                systemConfigVO = systemConfigVOList.get(0);
                redisUtil.set(cacheKey, JSONUtil.toJsonStr(systemConfigVO), 86400, TimeUnit.SECONDS);
            }
        }
        return systemConfigVO;
    }

    /**
     * 应用默认经销商配置
     */
    private void applyDefaultDealerConfig(AgentClueSaveDTO param, UserBusiEntity userInfo) {
        param.setOrgCode(userInfo.getDlrCode());
        param.setOrgName(userInfo.getDlrName());
    }

    @Override
    public PageVO<ClueDlrListVO> page(ClueDlrListDTO dto) {
        log.info("{} page dto {}", StringConstant.CLUE_LOG_PREFIX, JSONObject.toJSONString(dto));
        UserBusiEntity userInfo = UserInfoContext.get();
        // check role
        if (StringUtil.noText(userInfo.getDlrCode())) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "当前用户无对应门店");
        }
        // dto -> qry
        ClueDlrQry qry = ClueDlrListDTO.buildQry(dto);
        // fill user info
        qry.setDlrCode(userInfo.getDlrCode());
        if (UserUtil.isProductExpert(userInfo.getStationId())) {
            // 产品专家只能看自己的线索
            qry.setReviewPersonId(userInfo.getUserID());
        }
        // do qry
        Page<ClueDlrListVO> page = clueDlrService.page(qry);
        return PageVO.of(page);
    }

    @Override
    public ClueDlrListVO get(String clueId, ClueDlrListDTO dto) {
        log.info("{} get dto {} {}", StringConstant.CLUE_LOG_PREFIX, clueId, JSONObject.toJSONString(dto));
        UserBusiEntity userInfo = UserInfoContext.get();
        // check role
        UserUtil.checkDlr(userInfo);
        // dto -> qry
        ClueDlrQry qry = ClueDlrListDTO.buildQry(dto);
        qry.setClueIds(ImmutableSet.of(clueId));
        // fill user info
        qry.setDlrCode(userInfo.getDlrCode());
        if (UserUtil.isProductExpert(userInfo.getStationId())) {
            // 产品专家只能看自己的线索
            qry.setReviewPersonId(userInfo.getUserID());
        }
        // do qry
        List<ClueDlrListVO> list = clueDlrService.list(qry);
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public PageVO<ClueDlrSearchVO> search(ClueDlrSearchDTO dto) {
        log.info("{} search dto {}", StringConstant.CLUE_LOG_PREFIX, JSONObject.toJSONString(dto));
        // check user
        UserBusiEntity user = UserInfoContext.get();
        UserUtil.checkDlr(user);
        // dto -> qry
        ClueSearchQry searchQry = dto.buildQry();
        searchQry.setDlrCode(user.getDlrCode());
        searchQry.setUserId(user.getUserID());
        // do search
        PageVO<ClueDlrSearchVO> pageVO = clueDlrService.search(searchQry);
        // db qry
        Set<String> idSet = pageVO.getRecords()
                .stream()
                .map(ClueDlrSearchVO::getClueId)
                .collect(Collectors.toSet());

        List<ClueDlrListVO> list = clueDlrService.list(idSet)
                .stream()
                .peek(ClueDlrListVO::calcRemainTime)
                .peek(ClueDlrListVO::handleDefeatStage)
                .peek(ClueDlrListVO::fill)
                .collect(Collectors.toList());
        // join
        ClueDlrSearchVO.join(pageVO.getRecords(), list);
        return pageVO;
    }

    @Override
    public List<ClueDlrStatisticsVO> statistics(Set<ClueQueryTypeEnum> types) {
        log.info("{} statistics types {}", StringConstant.CLUE_LOG_PREFIX, types);
        // user info
        UserBusiEntity userInfo = UserInfoContext.get();
        if (StringUtil.noText(userInfo.getDlrCode())) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "当前用户无对应门店");
        }
        // default value
        if (CollectionUtil.isEmpty(types)) {
            types = CollectionUtil.newHashSet(ClueQueryTypeEnum.NOT_DEFEATED, ClueQueryTypeEnum.WAIT_TO_CONVERT);
        }

        return types.stream()
                .map(type -> clueDlrGateway.statisticsByType(type, userInfo))
                .collect(Collectors.toList());
    }

    /**
     * 线索详情
     *
     * @param param
     * @return
     */
    @Deprecated
    @Override
    public ClueDlrDetailRspVO detail(ClueDlrDetailDTO param) {

        // 1. 先查询线索表，获取基本信息
        QueryColumn[] columnsDlr = {SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.CUST_NAME,
                SAC_CLUE_INFO_DLR.PHONE, SAC_CLUE_INFO_DLR.INTEN_CAR_TYPE_NAME, SAC_CLUE_INFO_DLR.CHANNEL_NAME,
                SAC_CLUE_INFO_DLR.INTEN_CAR_TYPE_NAME, SAC_CLUE_INFO_DLR.INTEN_CAR_TYPE_CODE, SAC_CLUE_INFO_DLR.INFO_CHAN_MCODE,
                SAC_CLUE_INFO_DLR.INFO_CHAN_MNAME, SAC_CLUE_INFO_DLR.INFO_CHAN_DCODE, SAC_CLUE_INFO_DLR.INFO_CHAN_DNAME,
                SAC_CLUE_INFO_DLR.INTEN_LEVEL_CODE, SAC_CLUE_INFO_DLR.GENDER_CODE, SAC_CLUE_INFO_DLR.COLUMN5,
                SAC_CLUE_INFO_DLR.COLUMN11, SAC_CLUE_INFO_DLR.SERVER_ORDER, SAC_CLUE_INFO_DLR.COLUMN18};
        SacClueInfoDlr clueEntity = clueDlrService.getEntity(param.buildClueDlrBO(), columnsDlr);

        // 2. 查询回访表，获取描述
        QueryColumn[] columnsReview = {SAC_REVIEW.REVIEW_ID, SAC_REVIEW.REVIEW_DESC};
        SacReviewBO reviewBO = reviewService.getReviewInfo(param.buildReviewEntity(), columnsReview);

        // 3. 查询客户表，获取竞品车型COMPETITOR_TYPE_CN和用户描述CUSTOMER_DESC
        QueryColumn[] columnsCustInfo = {SAC_ONECUST_INFO_ENTITY.COMPETITOR_TYPE_CN, SAC_ONECUST_INFO_ENTITY.CUSTOMER_DESC};
        SacOnecustInfoEntity oneCustEntity = oneCustInfoService.getEntity(param.buildOneCustInfoEntity(), columnsCustInfo);

        // 4. 查询用户分组表获取用户标签
        SacUserGroupDetailEntity userGroupEntity = userGroupDetailService.getEntity(param.buildSacUserGroupDetailEntity());

        // 5. 查询线索扩展表
        SacOneCustRemark sacOneCustRemarkEntity = oneCustRemarkService.queryRemark(param.buildSacClueRemarkEntity());

        // 6. 拼装数据
        return ClueDlrDetailRspVO.convent(clueEntity, reviewBO, oneCustEntity, userGroupEntity, sacOneCustRemarkEntity);
    }

    /**
     * 线索详情，多线程
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ClueDlrDetailRspVO detailExecutor(ClueDlrDetailDTO param) throws Exception {
        // 1. 准备参数
        // 准备线索参数
        QueryColumn[] columnsDlr = {SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.CUST_NAME,
                SAC_CLUE_INFO_DLR.PHONE, SAC_CLUE_INFO_DLR.CHANNEL_NAME, SAC_CLUE_INFO_DLR.STATUS_CODE,
                SAC_CLUE_INFO_DLR.INTEN_CAR_TYPE_NAME, SAC_CLUE_INFO_DLR.INTEN_CAR_TYPE_CODE, SAC_CLUE_INFO_DLR.INFO_CHAN_MCODE,
                SAC_CLUE_INFO_DLR.INFO_CHAN_MNAME, SAC_CLUE_INFO_DLR.INFO_CHAN_DCODE, SAC_CLUE_INFO_DLR.INFO_CHAN_DNAME,
                SAC_CLUE_INFO_DLR.INTEN_LEVEL_CODE, SAC_CLUE_INFO_DLR.GENDER_CODE, SAC_CLUE_INFO_DLR.COLUMN5,
                SAC_CLUE_INFO_DLR.COLUMN11, SAC_CLUE_INFO_DLR.SERVER_ORDER, SAC_CLUE_INFO_DLR.COLUMN18};
        SacClueInfoDlrBO clueBO = param.buildClueDlrBO();

        // 准备回访参数
        QueryColumn[] columnsReview = {SAC_REVIEW.REVIEW_ID, SAC_REVIEW.BILL_CODE};
        SacReviewBO vrBO = param.buildReviewEntity();

        // 准备潜客参数
        QueryColumn[] columnsCustInfo = {SAC_ONECUST_INFO_ENTITY.COMPETITOR_TYPE_CN, SAC_ONECUST_INFO_ENTITY.CUSTOMER_DESC};
        SacOnecustInfoEntity custBoObj = param.buildOneCustInfoEntity();

        // 准备用户分组参数
        SacUserGroupDetailEntity userGroupBO = param.buildSacUserGroupDetailEntity();

        // 准备扩展表参数
        SacOneCustRemark custRemark = param.buildSacClueRemarkEntity();

        // 2. 并行执行所有查询
        @SuppressWarnings("unchecked")
        ClueDlrDetailRspVO result = ParallelExecutorUtils.executeFunction(this::conventDetailRsp, null,
                () -> Optional.ofNullable(clueDlrService.getEntity(clueBO, columnsDlr)).orElse(new SacClueInfoDlr()),
                () -> Optional.ofNullable(reviewService.getReviewInfo(vrBO, columnsReview)).orElse(new SacReviewBO()),
                () -> Optional.ofNullable(oneCustInfoService.getEntity(custBoObj, columnsCustInfo)).orElse(new SacOnecustInfoEntity()),
                () -> Optional.ofNullable(userGroupDetailService.getEntity(userGroupBO)).orElse(new SacUserGroupDetailEntity()),
                () -> Optional.ofNullable(oneCustRemarkService.queryRemark(custRemark)).orElse(new SacOneCustRemark()));

        return result;
    }

    /**
     * 并行执行完毕，后会调用的方法
     *
     * @param results
     * @param param
     * @return
     */
    private ClueDlrDetailRspVO conventDetailRsp(List<Object> results, Object[] param) {
        @SuppressWarnings("unchecked")
        SacClueInfoDlr clueEntity = (SacClueInfoDlr) results.get(0);
        @SuppressWarnings("unchecked")
        SacReviewBO reviewBO = (SacReviewBO) results.get(1);
        @SuppressWarnings("unchecked")
        SacOnecustInfoEntity oneCustEntity = (SacOnecustInfoEntity) results.get(2);
        @SuppressWarnings("unchecked")
        SacUserGroupDetailEntity userGroupEntity = (SacUserGroupDetailEntity) results.get(3);
        @SuppressWarnings("unchecked")
        SacOneCustRemark sacOneCustRemarkEntity = (SacOneCustRemark) results.get(4);

        return ClueDlrDetailRspVO.convent(clueEntity, reviewBO, oneCustEntity, userGroupEntity, sacOneCustRemarkEntity);
    }

    @Override
    public List<CalenderCountVO> overdueCountCalender(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("{} overdueCountCalender {} {}", StringConstant.CLUE_LOG_PREFIX, startTime, endTime);
        UserBusiEntity userInfo = UserInfoContext.get();
        if (StringUtil.noText(userInfo.getDlrCode())) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "当前用户无对应门店");
        }

        Map<LocalDateTime, CalenderCountVO> countMap = clueDlrGateway.overdueCount(startTime, endTime, userInfo)
                .stream()
                .collect(Collectors.toMap(CalenderCountVO::getDate, Function.identity()));
        List<CalenderCountVO> res = new ArrayList<>();
        LocalDateTime dateTime = startTime;
        while (dateTime.isBefore(endTime) || dateTime.equals(endTime)) {
            CalenderCountVO vo = countMap.get(dateTime);
            if (Objects.isNull(vo)) {
                res.add(new CalenderCountVO(dateTime, 0));
            } else {
                res.add(vo);
            }
            dateTime = dateTime.plusDays(1);
        }
        return res;
    }

    /**
     * 更新线索内容，按模块更新
     * 潜客表更新用户描述、竞品车型
     * 用户分组表更新用户标签
     * 线索表更新用户等级、意向车型、用户所在地、代理商线索来源
     * 回访表更新意向车型
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modifyDlrInfo(ClueDlrModifyDTO param) {
        ClueModifyStrategy strategy = strategyFactory.getStrategy(param.getClueModifyType());
        return strategy.modify(param.buildModifyBO());
    }

    /**
     * 查询线索7天30天的活跃信息
     *
     * @param dto
     * @return
     */
    @Override
    @SmartADPCache(value = "ClueActiveInfo", key = "#dto.phone", expire = 10, timeUnit = TimeUnit.HOURS)
    public ClueActiveInfoRspVO queryActiveInfo(ClueActiveInfoDTO dto) {
        ClueActiveInfoVO result = clueDlrService.queryClueActive(dto.getPhone());
        if (ObjectUtil.isEmpty(result)) {
            return null;
        }
        return ClueActiveInfoRspVO.convent(result);
    }

    @Override
    public List<CompetitiveCarModelVO> competitiveCarList() {
        List<CompetitiveCarModels> competitiveCarModels = competitiveCarModelGateway.findCarModels();
        if (CollUtil.isEmpty(competitiveCarModels)) {
            return new ArrayList<>();
        }
        List<CompetitiveCarModelVO> competitiveCarModelVOS = BeanUtils.copyList(competitiveCarModels,
                CompetitiveCarModelVO.class);
        return competitiveCarModelVOS;
    }

    @Override
    public List<AgentClueSourceVO> agentClueSourceList() {
        //获取用户信息
        UserBusiEntity userInfo = UserInfoContext.get();
        if (Objects.nonNull(userInfo) && StrUtil.isNotBlank(userInfo.getAgentCode())) {
            List<AgentClueSource> agentClueSources = agentClueSourceGateway.findAgentClueSources(userInfo.getAgentCode());
            if (CollUtil.isNotEmpty(agentClueSources)) {
                return BeanUtils.copyList(agentClueSources, AgentClueSourceVO.class);
            }
        }
        return new ArrayList<>();
    }

    @Override
    public ClueDlrCheckVO agentDlrClueCheck(AgentClueCheckDTO agentClueCheckDTO) {
        log.info("代理商线索保存前置校验线索,param={}", JSONUtil.toJsonStr(agentClueCheckDTO));

        //获取登录用户信息
        UserBusiEntity userInfo = UserInfoContext.get();
        ClueDlrCheckVO clueDlrCheckVO = new ClueDlrCheckVO();
        try {
            CheckParamUtils.checkPhone(agentClueCheckDTO);
        } catch (Exception e) {
            clueDlrCheckVO.setCode(ClueMessageEnum.CLUE_BASE_DLR_07.getCode());
            clueDlrCheckVO.setMsg(e.getMessage());
            return clueDlrCheckVO;
        }

        SacClueInfoDlr sacClueInfoDlr = findClueByPhoneIfNeeded(agentClueCheckDTO.getPhone());
        if (Objects.isNull(sacClueInfoDlr)) {
            clueDlrCheckVO.setCode(ClueMessageEnum.CLUE_NOT_EXIST.getCode());
            clueDlrCheckVO.setMsg(ClueMessageEnum.CLUE_NOT_EXIST.getDesc());
        } else {
            //线索存在时
            clueDlrCheckVO.setCode(ClueMessageEnum.CLUE_BASE_DLR_07.getCode());
            clueDlrCheckVO.setMsg(ClueMessageEnum.CLUE_BASE_DLR_07.getDesc());
            clueDlrCheckVO.setCreatedDate(sacClueInfoDlr.getCreatedDate());
            clueDlrCheckVO.setSmartId(sacClueInfoDlr.getColumn10());
            clueDlrCheckVO.setCustId(sacClueInfoDlr.getCustId());
            clueDlrCheckVO.setIntenLevelCode(sacClueInfoDlr.getIntenLevelCode());
            if (Objects.nonNull(userInfo) && StrUtil.isNotBlank(userInfo.getDlrCode())) {
                if (userInfo.getDlrCode().equals(sacClueInfoDlr.getDlrCode())) {
                    if (StrUtil.isNotBlank(sacClueInfoDlr.getReviewPersonName())) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(ClueMessageEnum.CLUE_EXIST_REVIEW_PERSON.getDesc()).append(sacClueInfoDlr.getReviewPersonName()).append(CommonConstant.CLUE_COMMON_MSG);
                        clueDlrCheckVO.setMsg(sb.toString());
                    } else {
                        //clueDlrCheckVO.setMsg(ClueMessageEnum.CLUE_EXIST_NO_REVIEW_PERSON.getDesc());
                    }
                }
            }
        }
        return clueDlrCheckVO;
    }

    /**
     * 保存摘要内容
     * 是否需要先推送MQ，异步消费
     *
     * @param param
     * @return
     */
    @Override
    public Boolean saveAbstractContent(AbstractContentDTO param) {
        if (AbstractType.VIRTUALCALL.getCode().equals(param.getBusinessType())) {
            return virtualRecordService.modifyVirtualAbstractContent(param.buildVirtualRecordBO());
//            return recordAbstractService.saveVirtualAbstractContent(param.buildVirtualRecordEntity());
        } else if (AbstractType.TESTRECORD.getCode().equals(param.getBusinessType())) {
            return testDriveSheetService.modifyTestDriveSheet(param.buildTestDriveSheetBO());
//            return recordAbstractService.saveTestAbstractContent(param.buildTestRecordEntity());
        }
        return Boolean.FALSE;
    }

    @Override
    public DomainPage<ClueInteractRspVO> queryInteractDataMain(InteractDataDTO param) throws Exception {
        DomainPage<ClueInteractRspVO> result = null;
        if (InteractDataSearchTypeEnum.DEFAULT.getCode().equals(param.getSearchType())) {
            result = queryInteractDataDefaultExecutor(param);
        } else {
            result = queryInteractDataCondition(param);
        }
        return result;
    }

    /**
     * 查询互动数据，多线程
     *
     * @param param
     * @return
     */
    public DomainPage<ClueInteractRspVO> queryInteractDataDefaultExecutor(InteractDataDTO param) throws Exception {
        // 1. 准备参数
        // 准备用户事件参数
        SacOnecustInfoEventBO eventBO = param.buildSacOnecustInfoEventBO();
        // 准备虚拟外呼录音摘要参数
        UscMdmVirtualRecordBO vrBO = param.buildUscMdmVirtualRecordBO();
        // 准备ADP跟进记录参数
        SacOnecustResumeBO resumeBo = param.buildSacOnecustResumeBO();
        // 准备试驾单参数
        SacTestDriveSheetBO testDriveSheetBO = param.buildSacTestDriveSheetBO();

        // 2. 并行执行所有查询
        @SuppressWarnings("unchecked")
        DomainPage<ClueInteractRspVO> results = ParallelExecutorUtils.executeFunction(this::conventInteractDataRsp, null,
                () -> Optional.ofNullable(sacOnecustInfoEventService.querycustEvent(eventBO)).orElse(new DomainPage<SacOnecustInfoEventBO>().initEmpty()),
                () -> Optional.ofNullable(virtualRecordService.queryClueVirtualRecord(vrBO)).orElse(new DomainPage<UscMdmVirtualRecordBO>().initEmpty()),
                () -> Optional.ofNullable(onecustResumeService.queryOnecustResumeByCustId(resumeBo)).orElse(new DomainPage<SacOnecustResumeBO>().initEmpty()),
                () -> Optional.ofNullable(testDriveSheetService.queryTestDriveSheetPage(testDriveSheetBO)).orElse(new DomainPage<SacTestDriveSheetBO>().initEmpty())
        );
        return results;
    }

    /**
     * 互动数据多线程查询完毕后执行的方法
     *
     * @param results
     * @param param
     * @return
     */
    private DomainPage<ClueInteractRspVO> conventInteractDataRsp(List<Object> results, Object[] param) {
        @SuppressWarnings("unchecked")
        DomainPage<SacOnecustInfoEventBO> pageCustEvent = (DomainPage<SacOnecustInfoEventBO>) results.get(0);
        @SuppressWarnings("unchecked")
        DomainPage<UscMdmVirtualRecordBO> pageVRRecords = (DomainPage<UscMdmVirtualRecordBO>) results.get(1);
        @SuppressWarnings("unchecked")
        DomainPage<SacOnecustResumeBO> pageOnecustResume = (DomainPage<SacOnecustResumeBO>) results.get(2);
        @SuppressWarnings("unchecked")
        DomainPage<SacTestDriveSheetBO> pageTestDriveSheet = (DomainPage<SacTestDriveSheetBO>) results.get(3);

        return ClueInteractRspVO.convent(pageCustEvent, pageVRRecords, pageOnecustResume, pageTestDriveSheet);
    }

    /**
     * 查询互动数据
     *
     * @param param
     * @return
     */
    @Deprecated
    @Override
    public DomainPage<ClueInteractRspVO> queryInteractDataDefault(InteractDataDTO param) {
        // 1. 查询用户事件 csc.t_sac_onecust_info_event
        SacOnecustInfoEventBO eventBO = param.buildSacOnecustInfoEventBO();
        DomainPage<SacOnecustInfoEventBO> listCustEvent = sacOnecustInfoEventService.querycustEvent(eventBO);

        // 2. 查询虚拟外呼录音摘要 mp.t_sac_virtual_record_abstract
        UscMdmVirtualRecordBO vrBO = param.buildUscMdmVirtualRecordBO();
        DomainPage<UscMdmVirtualRecordBO> listVirtualRecordResult = virtualRecordService.queryClueVirtualRecord(vrBO);

        // 3. ADP跟进记录 csc.t_sac_onecust_resume
        SacOnecustResumeBO resumtBoObj = param.buildSacOnecustResumeBO();
        DomainPage<SacOnecustResumeBO> pageOnecustResume = onecustResumeService.queryOnecustResumeByCustId(resumtBoObj);

        // 4. 查询试驾单 csc.t_sac_test_drive_sheet
        SacTestDriveSheetBO testDriveSheetBO = param.buildSacTestDriveSheetBO();
        DomainPage<SacTestDriveSheetBO> sacTestDriveSheetBOPage = testDriveSheetService.queryTestDriveSheetPage(testDriveSheetBO);

        // 5. 拼装数据
        return ClueInteractRspVO.convent(listCustEvent, listVirtualRecordResult, pageOnecustResume, sacTestDriveSheetBOPage);
    }

    /**
     * 互动数据带条件查询
     *
     * @param param
     * @return
     * @throws Exception
     */
    public DomainPage<ClueInteractRspVO> queryInteractDataCondition(InteractDataDTO param) throws Exception {
        // 构建查询BO
        InteractDataBO interactDataBO = BeanUtil.copyProperties(param, InteractDataBO.class);
        interactDataBO.setPageNumber(param.getPageIndex());
        interactDataBO.setPageSize(param.getPageSize());
        // 构建查询条件
        List<Callable<DomainPage<?>>> tasks = allDataTypeStrategy.buildQueryTasks(interactDataBO);
        // 多线程查询，并构建结果
        return ParallelExecutorUtils.executeFunction(this::conventInteractDataRspCondition, null, tasks.toArray(new Callable[0]));
    }

    /**
     * 解析多线程查询结果，并拼装VO
     *
     * @param results
     * @param param
     * @return
     */
    private DomainPage<ClueInteractRspVO> conventInteractDataRspCondition(List<Object> results, Object[] param) {
        DomainPage<SacOnecustInfoEventBO> pageCustEvent = null;
        DomainPage<UscMdmVirtualRecordBO> pageVRRecords = null;
        DomainPage<SacOnecustResumeBO> pageOnecustResume = null;
        DomainPage<SacTestDriveSheetBO> pageTestDriveSheet = null;
        for (Object result : results) {
            if (result instanceof TypedDomainPage) {
                TypedDomainPage<?> typedPage = (TypedDomainPage<?>) result;
                switch (typedPage.getSourceType()) {
                    case CUSTOMER_EVENT:
                        pageCustEvent = TypeDomainPageConventHelp.<SacOnecustInfoEventBO>builder().build().convent(typedPage);
                        break;
                    case VIRTUAL_RECORD:
                        pageVRRecords = TypeDomainPageConventHelp.<UscMdmVirtualRecordBO>builder().build().convent(typedPage);
                        break;
                    case FOLLOW_RECORD:
                        pageOnecustResume = TypeDomainPageConventHelp.<SacOnecustResumeBO>builder().build().convent(typedPage);
                        break;
                    case TEST_DRIVE:
                        pageTestDriveSheet = TypeDomainPageConventHelp.<SacTestDriveSheetBO>builder().build().convent(typedPage);
                        break;
                }
            }
        }
        return ClueInteractRspVO.convent(pageCustEvent, pageVRRecords, pageOnecustResume, pageTestDriveSheet);
    }

    /**
     * 详情页用户旅程
     *
     * @param param
     * @return
     */
    @Override
    public List<ClueEventFlowRspVO> queryUserEventFlow(ClueEventFlowDTO param) {
        CustEventFlowBO boParam = new CustEventFlowBO();
        boParam.setCustId(param.getCustId());
        List<CustEventFlowBO> listCustEventFlow = custEventFlowService.queryEventByCondition(boParam);
        //兼容战败不显示问题
        String statusCode = param.getStatusCode();
        if (StringUtils.isEmpty(statusCode)) {
            //端上未传，获取状态code进行补偿
            QueryColumn[] columnsDlr = {SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.STATUS_CODE};
            SacClueInfoDlr clueBO = new SacClueInfoDlr();
            clueBO.setCustId(param.getCustId());
            SacClueInfoDlr sacClueInfoDlr = clueDlrService.getEntity(clueBO, columnsDlr);
            if (Objects.nonNull(sacClueInfoDlr)) {
                statusCode = sacClueInfoDlr.getStatusCode();
            }
        }
        List<CustEventFlowBO> listCustEventFlowBO = CustEventFlowBO.aggregateByStage(listCustEventFlow, statusCode);
        return ClueEventFlowRspVO.conventBO(listCustEventFlowBO);
    }

    /**
     * 查询回访内容
     *
     * @param param
     * @return
     */
    @Override
    public SacReviewRspVO queryReviewInfo(ClueReviewDTO param) {
        SacReviewBO reviewInfo = reviewService.getReviewInfo(param.buildQuery());
        return SacReviewRspVO.convent(reviewInfo);
    }

    @Override
    public int userJourneysFlowFix(UserJourneysFixDTO dto) {
        log.info("flow fix start {}", dto);
        QueryWrapper wrapper = dto.buildFlowsWrapper();
        List<SacClueInfoDlr> clues = clueDlrGateway.listAllByWrapper(wrapper);
        if (clues.size() > 200000) {
            throw new IllegalArgumentException("批次过大");
        }
        for (List<SacClueInfoDlr> list : Lists.partition(clues, 1000)) {
            userJourneysHelper.flowFix(list);
        }
        return clues.size();
    }

    @Override
    public int userJourneysStageFix(UserJourneysFixDTO dto) {
        log.info("stage fix start {}", dto);
        QueryWrapper wrapper = dto.buildStageWrapper();
        List<SacClueInfoDlr> clues = clueDlrGateway.listAllByWrapper(wrapper);
        if (clues.size() > 200000) {
            throw new IllegalArgumentException("批次过大");
        }
        for (List<SacClueInfoDlr> list : Lists.partition(clues, 1000)) {
            userJourneysHelper.stageFix(list);
        }
        return clues.size();
    }

    @Override
    public int esFix(UserJourneysFixDTO dto) {
        log.info("es fix start {}", dto);
        QueryWrapper wrapper = dto.buildESFixWrapper();
        List<SacClueInfoDlr> clues = clueDlrGateway.listAllByWrapper(wrapper);
        if (clues.size() > 200000) {
            throw new IllegalArgumentException("批次过大");
        }
        for (List<SacClueInfoDlr> list : Lists.partition(clues, 1000)) {
            esInitHelper.esFix(list);
        }
        return clues.size();
    }

    @Override
    public void initClueES(UserJourneysFixDTO dto) {
        log.info("init clue es start {}", dto);
        esInitHelper.initClue(dto);
    }

    @Override
    public void initResumeES(UserJourneysFixDTO dto) {
        log.info("init resume es start {}", dto);
        esInitHelper.initResume(dto);
    }

    /**
     * 获取指定创建人的用户分组
     *
     * @param param
     * @return
     */
    @Override
    public List<ClueUserGroupVO> queryUserGroupByCreate(QueryClueGroupDTO param) {
        // 1. 查询用户所有分组，调用基础聚合根去查询
        List<UserGroupVO> result =
                baseApplicationService.queryUserGroupByCreate(QueryUserGroupDTO.builder().creator(param.getCreator()).build());
        // 2. 如果传了custId，就查询这个custId所在这个专家的哪些分组里面，如果没有传custId，查的就是专家下面所有的线索所在的分组
        List<SacUserGroupDetailBO> listDetailBo = userGroupDetailService.queryGroupDetail(param.buildSacUserGroupDetailBo(result));
        // 3. 拼装数据
        return ClueUserGroupVO.convent(result, listDetailBo);
    }

    @Override
    public ClueDlrCheckVO agentDlrCustCheck(AgentClueCheckDTO param) {
        //校验用户是否登录
        UserInfoContext.get();

        CheckParamUtils.checkPhone(param);

        ClueDlrCheckVO clueDlrCheckVO = new ClueDlrCheckVO();
        SacOnecustInfoEntity sacOnecustInfoEntity = oneCustInfoGateway.findOneByPhone(param.getPhone());
        if (Objects.isNull(sacOnecustInfoEntity)) {
            return clueDlrCheckVO;
        }
        clueDlrCheckVO.setCustId(sacOnecustInfoEntity.getCustId());
        return clueDlrCheckVO;
    }

    /**
     * 店端线索店长分配
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ClueDistributeVO> agentDlrDistribute(AgentClueDisTributeDTO param) {
        // 获取用户登录信息
        final UserBusiEntity userInfo = UserInfoContext.get();
        // 校验店长角色
        if (!UserUtil.isStoreManager(userInfo.getStationId())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "请确认是否为店长角色！");
        }
        // 参数校验
        CheckParamUtils.checkDlrDistribute(param, userInfo);

        // 初始化分配数据
        List<SacClueInfoDlr> sacClueInfoDlrList = new ArrayList<>();
        List<SacReview> sacReviews = new ArrayList<>();
        List<SacAllClueInfoDlr> sacAllClueInfoDlrList = new ArrayList<>();
        List<SacReviewHis> sacReviewHisList = new ArrayList<>();
        List<ReviewDisTributeDTO> reviewDisTributeDTOList = new ArrayList<>();
        List<String> activityReviewIdList = new ArrayList<>();
        List<String> defeatedReviewIdList = new ArrayList<>();
        // 提取公共参数到循环外部
        LocalDateTime currentTime = LocalDateTime.now();
        Date nowDate = new Date();
        //回访任务遍历
        for (AgentReviewDisTributeDTO agentReviewDisTributeDTO : param.getReviewDisTributeDTOList()) {
            if (agentReviewDisTributeDTO.getIsDefeatedDis()) {
                // 战败场景处理
                sacAllClueInfoDlrList.add(createAllClueInfoDlr(agentReviewDisTributeDTO, userInfo, param, nowDate, currentTime));
                sacReviewHisList.add(createReviewHis(agentReviewDisTributeDTO, userInfo, param, currentTime));
                reviewDisTributeDTOList.add(createReviewDisTributeDTO(agentReviewDisTributeDTO, nowDate, param, userInfo, currentTime));
                defeatedReviewIdList.add(agentReviewDisTributeDTO.getReviewId());
            } else {
                // 非战败场景处理
                sacClueInfoDlrList.add(createClueInfoDlr(agentReviewDisTributeDTO, nowDate, param, userInfo, currentTime));
                sacReviews.add(createSacReview(agentReviewDisTributeDTO, userInfo, param, currentTime));
                reviewDisTributeDTOList.add(createReviewDisTributeDTO(agentReviewDisTributeDTO, nowDate, param, userInfo, currentTime));
                activityReviewIdList.add(agentReviewDisTributeDTO.getReviewId());
            }
        }

        //查询活跃线索表
        List<SacClueInfoDlr> querySacClueInfoDlrList = querySacClueInfoDlrList(activityReviewIdList);
        //查全部线索表，主要是获取战败线索
        List<SacAllClueInfoDlr> querySacAllClueInfoDlrList = querySacAllClueInfoDlrList(defeatedReviewIdList);

        Boolean sendWEMsg = Boolean.FALSE;
        Boolean updateBatch = Boolean.FALSE;
        // 批量活跃线索数据更新
        if (CollUtil.isNotEmpty(sacClueInfoDlrList) && CollUtil.isNotEmpty(querySacClueInfoDlrList)) {
            // 批量更新线索信息
            clueDlrGateway.updateClueInfoBatch(sacClueInfoDlrList);
        }

        // 批量活跃线索更新回访任务
        if (CollUtil.isNotEmpty(sacReviews) && CollUtil.isNotEmpty(querySacClueInfoDlrList)) {
            sendWEMsg = reviewGateway.updateReviewAssignBatch(sacReviews);
        }
        // 批量战败线索
        if (CollUtil.isNotEmpty(sacAllClueInfoDlrList) && CollUtil.isNotEmpty(querySacAllClueInfoDlrList)) {
            updateBatch = clueDlrGateway.updateClueInfoDlrBatch(sacAllClueInfoDlrList);
        }
        // 批量战败线索回访历史
        if (CollUtil.isNotEmpty(sacReviewHisList) && CollUtil.isNotEmpty(querySacAllClueInfoDlrList)) {
            reviewHisGateway.updateReviewAssignBatch(sacReviewHisList);
        }

        if (!sendWEMsg && !updateBatch) {
            log.error("线索分配不需要更新拦截,sendWEMsg={},updateBatch={}", sendWEMsg, updateBatch);
            throw new BusinessException(RespCode.FAIL.getCode(), "线索分配失败，请稍后重试！");
        }

        // 消息提醒数据处理（站内信消息）可异步处理 对应原拦截器 csc_message_notice_clue
        // 异步通知CDP 对应后置处理器 csc_clue_autofp_save_interface MQ消息实现
        AgentClueDistributeEvent agentClueDistributeEvent = new AgentClueDistributeEvent(this, reviewDisTributeDTOList);
        applicationContext.publishEvent(agentClueDistributeEvent);

        //记录分配日志更改销售顾问信息对应 csc_clue_autofp_update_advisor
        ClueDisTributeLogEvent clueDisTributeLogEvent = new ClueDisTributeLogEvent(this, reviewDisTributeDTOList, querySacClueInfoDlrList
                , querySacAllClueInfoDlrList);
        applicationContext.publishEvent(clueDisTributeLogEvent);

        //发送企微提醒消息
        if (shouldSendWecomMessage(sendWEMsg, sacReviews)) {
            // 使用预定义的 Predicate 过滤条件
            List<SacClueInfoDlr> isAwaitingAllocation = querySacClueInfoDlrList.stream()
                    .filter(Objects::nonNull)
                    .filter(dlr -> ClueStatusEnum.AWAITING_ALLOCATION.getCode().equals(dlr.getStatusCode()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(isAwaitingAllocation)) {
                log.info("店端线索分配发送企微提醒开始,reviewPersonName={},isAwaitingAllocation={}", param.getReviewPersonName(),
                        JSONUtil.toJsonStr(isAwaitingAllocation));
                sendWecomNotification(param.getReviewPersonId(),
                        String.format("您收到%d条店长分配的线索，请及时跟进", isAwaitingAllocation.size())
                );
            }
        }

        //构建返回结果
        List<ClueDistributeVO> clueDistributeVOList = new ArrayList<>(calculateInitialCapacity(sendWEMsg, updateBatch, sacReviews,
                sacAllClueInfoDlrList));
        if (sendWEMsg) {
            sacReviews.stream()
                    .map(this::convertSacReviewToVO)
                    .forEach(clueDistributeVOList::add);
        }
        if (updateBatch) {
            sacAllClueInfoDlrList.stream()
                    .map(this::convertSacAllClueInfoDlrToVO)
                    .forEach(clueDistributeVOList::add);
        }

        //维护战败再分配提示标签
        if (!querySacAllClueInfoDlrList.isEmpty()) {
            List<String> tagKeys = querySacAllClueInfoDlrList.stream()
                    .map(SacAllClueInfoDlr::getId)
                    .map(clueTagRedisKey)
                    .collect(Collectors.toList());
            for (String key : tagKeys) {
                String tagCode = ClueTagEnum.DEFEATED_ALLOCATE.getCode();
                stringRedisTemplate.opsForSet()
                        .add(key, tagCode);
                stringRedisTemplate.opsForSet()
                        .remove(key, NEGATIVE_PREFIX + tagCode);
                stringRedisTemplate.expire(key, Duration.ofMinutes(5));
            }
        }

        return clueDistributeVOList;
    }

    // 辅助方法
    private int calculateInitialCapacity(boolean sendWEMsg, boolean updateBatch,
                                         List<SacReview> sacReviews,
                                         List<SacAllClueInfoDlr> sacAllClueInfoDlrList) {
        int capacity = 0;
        if (sendWEMsg) capacity += sacReviews.size();
        if (updateBatch) capacity += sacAllClueInfoDlrList.size();
        return capacity;
    }

    private ClueDistributeVO convertSacReviewToVO(SacReview review) {
        ClueDistributeVO vo = new ClueDistributeVO();
        vo.setReviewId(review.getReviewId());
        vo.setReviewPersonId(review.getReviewPersonId());
        vo.setReviewPersonName(review.getReviewPersonName());
        vo.setAssignTime(review.getAssignTime());
        return vo;
    }

    private ClueDistributeVO convertSacAllClueInfoDlrToVO(SacAllClueInfoDlr dlr) {
        ClueDistributeVO vo = new ClueDistributeVO();
        vo.setReviewId(dlr.getReviewId());
        vo.setReviewPersonId(dlr.getReviewPersonId());
        vo.setReviewPersonName(dlr.getReviewPersonName());
        vo.setAssignTime(dlr.getAssignTime());
        return vo;
    }

    // 封装条件判断方法
    private boolean shouldSendWecomMessage(boolean sendFlag, Collection<?> reviews) {
        return sendFlag && CollUtil.isNotEmpty(reviews);
    }

    // 封装消息发送方法
    private void sendWecomNotification(String userId, String contentTemplate) {
        MessageContent messageContent = new MessageContent();
        messageContent.setContent(contentTemplate);
        SendWecomMessage message = new SendWecomMessage();
        message.setToUser(userId);
        message.setMsgType("text");
        message.setMsgContent(messageContent);
        try {
            agentDlrCreateCluePublisher.sendClueWECOMessage(JSONUtil.toJsonStr(message));
            log.info("企微提醒发送成功，接收人: {}", userId, JSONUtil.toJsonStr(message));
        } catch (Exception e) {
            log.error("企微提醒发送失败，接收人: {}", userId, e); // 异常处理
        }
    }

    private ReviewDisTributeDTO createReviewDisTributeDTO(AgentReviewDisTributeDTO agentReviewDisTributeDTO, Date date,
                                                          AgentClueDisTributeDTO param, UserBusiEntity user, LocalDateTime now) {
        ReviewDisTributeDTO reviewDisTributeDTO = new ReviewDisTributeDTO();
        reviewDisTributeDTO.setReviewId(agentReviewDisTributeDTO.getReviewId());
        reviewDisTributeDTO.setDlrCode(param.getDlrCode());
        reviewDisTributeDTO.setDlrShortName(user.getDlrShortName());
        reviewDisTributeDTO.setAllocateTime(date);
        reviewDisTributeDTO.setAssignTime(now);
        reviewDisTributeDTO.setReviewPersonId(param.getReviewPersonId());
        reviewDisTributeDTO.setReviewPersonName(param.getReviewPersonName());
        reviewDisTributeDTO.setReviewPersonPhone(param.getReviewPersonPhone());
        reviewDisTributeDTO.setScenario(param.getScenario());
        reviewDisTributeDTO.setMessageType(param.getMessageType());
        reviewDisTributeDTO.setAssignPersonId(user.getUserID());
        reviewDisTributeDTO.setAssignPersonName(user.getEmpName());
        reviewDisTributeDTO.setIsDefeatedDis(agentReviewDisTributeDTO.getIsDefeatedDis());
        reviewDisTributeDTO.setDlrId(user.getDlrID());
        return reviewDisTributeDTO;
    }

    // 创建 SacClueInfoDlr 对象（非战败）
    private SacClueInfoDlr createClueInfoDlr(AgentReviewDisTributeDTO agentReviewDisTributeDTO, Date date, AgentClueDisTributeDTO param,
                                             UserBusiEntity user, LocalDateTime now) {
        SacClueInfoDlr entity = new SacClueInfoDlr();
        entity.setReviewId(agentReviewDisTributeDTO.getReviewId());
        entity.setDlrCode(param.getDlrCode());
        entity.setDlrShortName(user.getDlrShortName());
        entity.setAllocateTime(date);
        entity.setAssignTime(now);
        entity.setStatusCode(ClueStatusEnum.AWAITING_FOLLOW_UP.getCode());
        entity.setStatusName(ClueStatusEnum.AWAITING_FOLLOW_UP.getDesc());
        entity.setReviewPersonId(param.getReviewPersonId());
        entity.setReviewPersonName(param.getReviewPersonName());
        entity.setModifier(user.getEmpID());
        entity.setModifyName(user.getEmpName());
        entity.setLastUpdatedDate(LocalDateTime.now());
        entity.setUpdateControlId(UUID.randomUUID().toString());
        if (this.changeLabel(agentReviewDisTributeDTO.getManageLabelCode())) {
            entity.setManageLabelCode(ManageLabelEnum.PROCESSED.getCode());
            entity.setManageLabelName(ManageLabelEnum.PROCESSED.getDesc());
        }
        return entity;
    }

    /**
     * 判断是否
     *
     * @param manageLabelCode
     * @return
     */
    private Boolean changeLabel(String manageLabelCode) {
        if (StrUtil.isNotBlank(manageLabelCode)
                && (ManageLabelEnum.CHANGE_STORE.getCode().equals(manageLabelCode)
                || ManageLabelEnum.CHANGE_EMP.getCode().equals(manageLabelCode))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    // 创建 SacReview 对象（非战败）
    private SacReview createSacReview(AgentReviewDisTributeDTO agentReviewDisTributeDTO, UserBusiEntity user,
                                      AgentClueDisTributeDTO param, LocalDateTime now) {
        SacReview entity = new SacReview();
        entity.setReviewId(agentReviewDisTributeDTO.getReviewId());
        entity.setAssignPersonId(user.getEmpID());
        entity.setAssignPersonName(user.getEmpName());
        entity.setReviewPersonId(param.getReviewPersonId());
        entity.setReviewPersonName(param.getReviewPersonName());
        entity.setAssignTime(now);
        entity.setAssignStatus("1");
        entity.setAssignStatusName("已分配");
        entity.setOrgCode(param.getDlrCode());
        entity.setOrgName(user.getDlrShortName());

        // 设置公共字段
        setCommonFields(entity, user);
        entity.setUpdateControlId(UUID.randomUUID().toString());

        if (changeLabel(agentReviewDisTributeDTO.getManageLabelCode())) {
            entity.setManageLabelCode(ManageLabelEnum.PROCESSED.getCode());
            entity.setManageLabelName(ManageLabelEnum.PROCESSED.getDesc());
            entity.setColumn13(ManageLabelEnum.getDescByCode(agentReviewDisTributeDTO.getManageLabelCode()));
        }

        return entity;
    }

    // 创建 SacAllClueInfoDlr 对象（战败）
    private SacAllClueInfoDlr createAllClueInfoDlr(AgentReviewDisTributeDTO agentReviewDisTributeDTO, UserBusiEntity user,
                                                   AgentClueDisTributeDTO param,
                                                   Date date, LocalDateTime now) {
        SacAllClueInfoDlr entity = new SacAllClueInfoDlr();
        entity.setReviewId(agentReviewDisTributeDTO.getReviewId());
        entity.setDlrCode(param.getDlrCode());
        entity.setDlrShortName(user.getDlrShortName());
        entity.setLastUpdatedDate(LocalDateTime.now());
        entity.setAllocateTime(date);
        entity.setReviewPersonId(param.getReviewPersonId());
        entity.setReviewPersonName(param.getReviewPersonName());
        entity.setModifier(user.getEmpID());
        entity.setModifyName(user.getEmpName());
        entity.setLastUpdatedDate(LocalDateTime.now());
        entity.setAssignTime(now);
        entity.setUpdateControlId(UUID.randomUUID().toString());
        if (changeLabel(agentReviewDisTributeDTO.getManageLabelCode())) {
            entity.setManageLabelCode(ManageLabelEnum.PROCESSED.getCode());
            entity.setManageLabelName(ManageLabelEnum.PROCESSED.getDesc());
        }
        if (agentReviewDisTributeDTO.getIsDefeatedDis()) {
            entity.setManageLabelCode(ManageLabelEnum.PROCESSED.getCode());
            entity.setManageLabelName(ManageLabelEnum.PROCESSED.getDesc());
        }
        return entity;
    }

    // 创建 SacReviewHis 对象（战败）
    private SacReviewHis createReviewHis(AgentReviewDisTributeDTO agentReviewDisTributeDTO, UserBusiEntity user,
                                         AgentClueDisTributeDTO param, LocalDateTime now) {
        SacReviewHis entity = new SacReviewHis();
        entity.setReviewId(agentReviewDisTributeDTO.getReviewId());
        entity.setOrgCode(param.getDlrCode());
        entity.setOrgName(user.getDlrShortName());
        entity.setLastUpdatedDate(now);
        entity.setAssignTime(now);
        entity.setUpdateControlId(UUID.randomUUID().toString());
        entity.setModifier(user.getEmpID());
        entity.setModifyName(user.getEmpName());
        if (agentReviewDisTributeDTO.getIsDefeatedDis()) {
            entity.setReviewPersonId(param.getReviewPersonId());
            entity.setReviewPersonName(param.getReviewPersonName());
        }
        return entity;
    }

    // 设置公共字段（修改人、时间等）
    private void setCommonFields(SacReview entity, UserBusiEntity user) {
        entity.setModifier(user.getEmpID());
        entity.setModifyName(user.getEmpName());
        entity.setLastUpdatedDate(LocalDateTime.now());
    }

    /**
     * 获取活跃线索信息
     *
     * @param activityReviewIdList
     * @return
     */
    private List<SacClueInfoDlr> querySacClueInfoDlrList(List<String> activityReviewIdList) {
        List<SacClueInfoDlr> querySacClueInfoDlrList = new ArrayList<>();
        if (CollUtil.isNotEmpty(activityReviewIdList)) {
            QueryWrapper wrapper = QueryWrapper.create()
                    .select(SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.SERVER_ORDER, SAC_CLUE_INFO_DLR.CUST_NAME, SAC_CLUE_INFO_DLR.PHONE,
                            SAC_CLUE_INFO_DLR.DLR_CODE, SAC_CLUE_INFO_DLR.REVIEW_PERSON_ID, SAC_CLUE_INFO_DLR.REVIEW_PERSON_NAME,
                            SAC_CLUE_INFO_DLR.DLR_SHORT_NAME
                            , SAC_CLUE_INFO_DLR.STATUS_CODE, SAC_CLUE_INFO_DLR.REVIEW_ID)
                    .and(SAC_CLUE_INFO_DLR.REVIEW_ID.in(activityReviewIdList, CollectionUtil::isNotEmpty));
            querySacClueInfoDlrList = clueDlrGateway.findActiveClueByReviewIds(wrapper);
        }
        return querySacClueInfoDlrList;
    }

    /**
     * 获取战败线索信息
     *
     * @param defeatedReviewIdList
     * @return
     */
    private List<SacAllClueInfoDlr> querySacAllClueInfoDlrList(List<String> defeatedReviewIdList) {
        List<SacAllClueInfoDlr> querySacAllClueInfoDlrList = new ArrayList<>();
        if (CollUtil.isNotEmpty(defeatedReviewIdList)) {
            QueryWrapper wrapper = QueryWrapper.create()
                    .select(SAC_ALL_CLUE_INFO_DLR.CUST_ID, SAC_ALL_CLUE_INFO_DLR.SERVER_ORDER, SAC_ALL_CLUE_INFO_DLR.CUST_NAME,
                            SAC_ALL_CLUE_INFO_DLR.PHONE,
                            SAC_ALL_CLUE_INFO_DLR.DLR_CODE, SAC_ALL_CLUE_INFO_DLR.REVIEW_PERSON_ID,
                            SAC_ALL_CLUE_INFO_DLR.REVIEW_PERSON_NAME, SAC_ALL_CLUE_INFO_DLR.DLR_SHORT_NAME,
                            SAC_ALL_CLUE_INFO_DLR.REVIEW_ID, SAC_ALL_CLUE_INFO_DLR.ID)
                    .and(SAC_CLUE_INFO_DLR.REVIEW_ID.in(defeatedReviewIdList, CollectionUtil::isNotEmpty));
            querySacAllClueInfoDlrList = clueDlrGateway.findDefeatedClueByReviewIds(wrapper);
        }
        return querySacAllClueInfoDlrList;
    }

    /**
     * 线索详情，多线程
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ClueDlrRspVO queryClueInfoDlr(ClueDlrInfoQueryDTO param) {
        // 1. 准备参数
        QueryColumn[] columnsDlr = {SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.CUST_NAME,
                SAC_CLUE_INFO_DLR.PHONE, SAC_CLUE_INFO_DLR.INTEN_CAR_TYPE_NAME, SAC_CLUE_INFO_DLR.CHANNEL_NAME,
                SAC_CLUE_INFO_DLR.INTEN_CAR_TYPE_CODE, SAC_CLUE_INFO_DLR.INFO_CHAN_MCODE,
                SAC_CLUE_INFO_DLR.INFO_CHAN_MNAME, SAC_CLUE_INFO_DLR.INFO_CHAN_DCODE, SAC_CLUE_INFO_DLR.INFO_CHAN_DNAME,
                SAC_CLUE_INFO_DLR.INTEN_LEVEL_CODE, SAC_CLUE_INFO_DLR.GENDER_CODE, SAC_CLUE_INFO_DLR.COLUMN5,
                SAC_CLUE_INFO_DLR.COLUMN11, SAC_CLUE_INFO_DLR.SERVER_ORDER, SAC_CLUE_INFO_DLR.COLUMN18,
                SAC_CLUE_INFO_DLR.DLR_CODE,SAC_CLUE_INFO_DLR.STATUS_CODE};

        // 2. 并行执行所有查询
        SacClueInfoDlr clueEntity = clueDlrService.getEntity(param.buildClueDlrEntity(), columnsDlr);
        SacClueInfoDlrBO sacClueInfoDlrBO = BeanUtil.copyProperties(clueEntity, SacClueInfoDlrBO.class);

        // 3. 拼装数据
        return ClueDlrRspVO.convent(sacClueInfoDlrBO);
    }

    /**
     * 线索客户信息修改
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modifyClueCustInfo(ModifyClueCustDTO param) {
        log.info("线索客户信息修改开始,param={}", JSONUtil.toJsonStr(param));
        // 获取用户登录信息
        final UserBusiEntity userInfo = UserInfoContext.get();

        LocalDateTime updateTime = LocalDateTime.now();

        // 处理性别信息
        EntityHelper.setGenderInfo(param);

        SacAllClueInfoDlr allClue = null;
        SacClueInfoDlr clue = null;
        SacReview review = null;
        if (param.getDefeatFlag()) {
            allClue = createAllClueInfo(param, userInfo, updateTime);
        } else {
            clue = createClueInfo(param, userInfo, updateTime);
            review = createReviewInfo(param, userInfo, updateTime);
        }
        // 潜客信息对象
        SacOnecustInfoEntity sacOnecustInfo = createOnecustInfo(param, userInfo, updateTime);
        if (Objects.nonNull(sacOnecustInfo)) {
            oneCustInfoGateway.modifyOnecustInfo(sacOnecustInfo);
        }
        if (Objects.nonNull(allClue)) {
            //战败线索更新
            clueDlrGateway.modifyAllClue(allClue);
        }
        if (Objects.nonNull(clue)) {
            //活跃线索更新
            clueDlrGateway.modifyClue(clue);
        }
        if (Objects.nonNull(review)) {
            //活跃线索任务更新
            reviewGateway.modifyReviewInfo(review);
        }

        ModifyClueCustEvent modifyClueCustEvent = new ModifyClueCustEvent(this, param.getPhone(), param.getCustName(),
                param.getDefeatFlag(), param.getReviewId());
        applicationContext.publishEvent(modifyClueCustEvent);

        return Boolean.TRUE;
    }

    /**
     * 构建潜客信息
     *
     * @param param
     * @param userInfo
     * @param updateTime
     * @return
     */
    private SacOnecustInfoEntity createOnecustInfo(ModifyClueCustDTO param, UserBusiEntity userInfo, LocalDateTime updateTime) {
        SacOnecustInfoEntity entity = new SacOnecustInfoEntity();
        entity.setCustName(param.getCustName());
        if (StrUtil.isNotBlank(param.getGenderName())) {
            entity.setGenderName(param.getGenderName());
        }
        if (StrUtil.isNotBlank(param.getGenderCode())) {
            entity.setGenderCode(param.getGenderCode());
        }
        EntityHelper.setCommonFields(entity, userInfo, updateTime, param.getPhone());
        return entity;
    }

    /**
     * 构建活跃线索信息
     *
     * @param param
     * @param userInfo
     * @param updateTime
     * @return
     */
    private SacClueInfoDlr createClueInfo(ModifyClueCustDTO param, UserBusiEntity userInfo, LocalDateTime updateTime) {
        SacClueInfoDlr entity = new SacClueInfoDlr();
        entity.setCustName(param.getCustName());
        if (StrUtil.isNotBlank(param.getGenderName())) {
            entity.setGenderName(param.getGenderName());
        }
        if (StrUtil.isNotBlank(param.getGenderCode())) {
            entity.setGenderCode(param.getGenderCode());
        }
        EntityHelper.setCommonFields(entity, userInfo, updateTime, param.getPhone());
        return entity;
    }

    /**
     * 构建活跃线索信息
     *
     * @param param
     * @param userInfo
     * @param updateTime
     * @return
     */
    private SacReview createReviewInfo(ModifyClueCustDTO param, UserBusiEntity userInfo, LocalDateTime updateTime) {
        SacReview entity = new SacReview();
        entity.setCustName(param.getCustName());
        if (StrUtil.isNotBlank(param.getGenderName())) {
            entity.setGenderName(param.getGenderName());
        }
        if (StrUtil.isNotBlank(param.getGenderCode())) {
            entity.setGender(param.getGenderCode());
        }
        EntityHelper.setCommonFields(entity, userInfo, updateTime, param.getPhone());
        return entity;
    }

    /**
     * 构建战败线索信息
     *
     * @param param
     * @param userInfo
     * @param updateTime
     * @return
     */
    private SacAllClueInfoDlr createAllClueInfo(ModifyClueCustDTO param, UserBusiEntity userInfo, LocalDateTime updateTime) {
        SacAllClueInfoDlr entity = new SacAllClueInfoDlr();
        entity.setCustName(param.getCustName());
        if (StrUtil.isNotBlank(param.getGenderName())) {
            entity.setGenderName(param.getGenderName());
        }
        if (StrUtil.isNotBlank(param.getGenderCode())) {
            entity.setGenderCode(param.getGenderCode());
        }
        EntityHelper.setCommonFields(entity, userInfo, updateTime, param.getPhone());
        return entity;
    }


    @Override
    public ClueTipsVO getTips(ClueQueryTypeEnum type) {
        // check user
        UserBusiEntity user = UserInfoContext.get();
        UserUtil.checkDlr(user);
        // check param
        if (Objects.isNull(type) || type.includeDefeat()) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "不支持的查询类型");
        }

        ClueTipsVO tipsVO = new ClueTipsVO();
        // build qry
        ClueDlrQry qry = new ClueDlrQry();
        qry.setQryType(type);
        qry.setDlrCode(user.getDlrCode());
        if (UserUtil.productExpertValid()) {
            qry.setReviewPersonId(user.getUserID());
        }
        // fill
        qry.setReviewStatus(ReviewStatusEnum.WAIT_FOR_REVIEW);
        tipsVO.setWaitForReview(clueDlrGateway.existByQry(qry));

        qry.setReviewStatus(ReviewStatusEnum.NO_REVIEW);
        tipsVO.setNoReview(clueDlrGateway.existByQry(qry));

        qry.setReviewStatus(ReviewStatusEnum.OVERDUE);
        tipsVO.setOverdue(clueDlrGateway.existByQry(qry));
        return tipsVO;
    }
}
