package com.smart.adp.application.vo.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Description: 用户分组返回的对象
 * @Author: rik.ren
 * @Date: 2025/3/9 17:21
 **/
@Data
@ToString
@EqualsAndHashCode
@Schema(description = "用户分组VO")
public class UserGroupVO implements Serializable {
    /**
     * ID
     */
    @Schema(description = "用户分组ID")
    private String userGroupId;

    /**
     * 客户 ID
     */
    @Schema(description = "用户分组名称")
    private String userGroupName;
}
