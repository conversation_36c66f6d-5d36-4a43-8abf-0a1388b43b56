package com.smart.adp.application.service.task.impl;

import com.mybatisflex.core.paginate.Page;
import com.smart.adp.application.dto.task.TaskPageDTO;
import com.smart.adp.application.service.task.TaskApplicationService;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.enums.TaskQryTypeEnum;
import com.smart.adp.domain.gateway.task.TaskGateway;
import com.smart.adp.domain.qry.TaskQry;
import com.smart.adp.domain.service.task.TaskService;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.domain.valueObject.task.TaskTipsVO;
import com.smart.adp.domain.valueObject.task.TaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/6
 */
@Component
public class TaskApplicationServiceImpl implements TaskApplicationService {

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskGateway taskGateway;

    @Override
    public PageVO<TaskVO> page(TaskPageDTO dto) {
        UserBusiEntity user = UserInfoContext.get();
        UserUtil.checkDlr(user);

        TaskQry qry = TaskPageDTO.buildQry(dto);

        qry.setDlrCode(user.getDlrCode());
        if (UserUtil.productExpertValid()) {
            qry.setPersonIds(Collections.singletonList(user.getUserID()));
        }

        Page<TaskVO> page = taskService.page(qry);
        return PageVO.of(page);
    }

    @Override
    public TaskTipsVO tips(List<String> userIds) {
        UserBusiEntity user = UserInfoContext.get();
        UserUtil.checkDlr(user);

        if (UserUtil.productExpertValid()) {
            userIds = Collections.singletonList(user.getUserID());
        }

        TaskTipsVO tipsVO = new TaskTipsVO();
        TaskQry qry = new TaskQry();
        qry.setDlrCode(user.getDlrCode());
        qry.setPersonIds(userIds);

        qry.setQryType(TaskQryTypeEnum.UNFINISHED);
        tipsVO.setUnfinished(taskGateway.existByQry(qry));

        qry.setQryType(TaskQryTypeEnum.OVERDUE);
        tipsVO.setOverdue(taskGateway.existByQry(qry));

        return tipsVO;
    }

    @Override
    public TaskVO detail(Long id) {
        TaskVO vo = taskService.detail(id);
        return vo;
    }
}
