package com.smart.adp.application.assembler.clue;

import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.dto.clue.AgentClueDTO;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;

/**
 * <AUTHOR>
 * date 2025/3/13 16:40
 * @description
 **/
public class AgentClueDTOAssembler implements Assembler<AgentClueDTO, AgentClueSaveDTO> {

    /**
     * 装配对象
     *
     * @param source 源
     * @param target 目标
     * @return
     */
    @Override
    public AgentClueSaveDTO assemble(AgentClueDTO source, Class<AgentClueSaveDTO> target) {
        return convert(source);
    }

    private AgentClueSaveDTO convert(AgentClueDTO par) {
        AgentClueSaveDTO agentClueSaveDTO = new AgentClueSaveDTO();
        agentClueSaveDTO.setCustName(par.getCustName());
        agentClueSaveDTO.setClueLevel(par.getClueLevel());
        agentClueSaveDTO.setCustId(par.getCustId());
        agentClueSaveDTO.setAgentResource(par.getAgentResource());
        agentClueSaveDTO.setLocation(par.getLocation());
        agentClueSaveDTO.setCompetitorModels(par.getCompetitorModels());
        agentClueSaveDTO.setPhone(par.getPhone());
        agentClueSaveDTO.setGenderCode(par.getGenderCode());
        agentClueSaveDTO.setGenderName(par.getGenderName());
        agentClueSaveDTO.setInfoChanMCode(par.getInfoChanMCode());
        agentClueSaveDTO.setInfoChanMName(par.getInfoChanMName());
        agentClueSaveDTO.setInfoChanDCode(par.getInfoChanDCode());
        agentClueSaveDTO.setInfoChanDName(par.getInfoChanDName());
        agentClueSaveDTO.setChannelCode(par.getChannelCode());
        agentClueSaveDTO.setChannelName(par.getChannelName());
        agentClueSaveDTO.setIntenCarTypeCode(par.getIntenCarTypeCode());
        agentClueSaveDTO.setIntenCarTypeName(par.getIntenCarTypeName());
        agentClueSaveDTO.setIntenLevelCode(par.getIntenLevelCode());
        agentClueSaveDTO.setIntenLevelName(par.getIntenLevelName());
        agentClueSaveDTO.setOutColorCode(par.getOutColorCode());
        agentClueSaveDTO.setOutColorName(par.getOutColorName());
        agentClueSaveDTO.setInnerColorCode(par.getInnerColorCode());
        agentClueSaveDTO.setInnerColorName(par.getInnerColorName());
        agentClueSaveDTO.setPlanBuyDate(par.getPlanBuyDate());
        agentClueSaveDTO.setPlanBuyDateName(par.getPlanBuyDateName());
        agentClueSaveDTO.setBusinessHeatName(par.getBusinessHeatName());
        agentClueSaveDTO.setBusinessHeatCode(par.getBusinessHeatCode());
        agentClueSaveDTO.setReviewPersonId(par.getReviewPersonId());
        agentClueSaveDTO.setReviewPersonName(par.getReviewPersonName());
        agentClueSaveDTO.setSystemSource(par.getSystemSource());
        agentClueSaveDTO.setSmartId(par.getSmartId());
        agentClueSaveDTO.setClueType(par.getClueType());
        agentClueSaveDTO.setDlrCode(par.getDlrCode());
        agentClueSaveDTO.setDlrShortName(par.getDlrShortName());
        agentClueSaveDTO.setFlag(par.getFlag());
        agentClueSaveDTO.setId(par.getId());
        agentClueSaveDTO.setPlanReviewTime(par.getPlanReviewTime());
        agentClueSaveDTO.setActivityId(par.getActivityId());
        return agentClueSaveDTO;
    }
}
