package com.smart.adp.application.dto.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/3/3 18:54
 * @description 店端线索校验DTO
 **/
@Data
@Schema(description = "店端线索校验入参对象")
public class AgentClueCheckDTO implements Serializable {

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    @NotBlank(message = "联系电话不能为空")
    private String phone;

}
