package com.smart.adp.application.vo.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 互动数据中的跟进
 * @Author: rik.ren
 * @Date: 2025/3/23 13:13
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ClueInteractResumeVO implements Serializable {
    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private LocalDateTime bussStartTime;
    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private LocalDateTime bussEndTime;

    /**
     * 场景类型code
     */
    @Schema(description = "场景类型code")
    private String senceCode;
    /**
     * 场景类型名称
     */
    @Schema(description = "场景类型名称")
    private String senceName;
    /**
     * 同行到店人数
     */
    @Schema(description = "到店同行人数")
    private Integer arrivalNum;

    /**
     * 到店时间
     */
    @Schema(description = "到店时间")
    private LocalDateTime arrivalTime;
    /**
     * 离店时间
     */
    @Schema(description = "离店时间")
    private LocalDateTime arrivalEndTime;
    /**
     * 到店方式
     */
    private String arrivalMethod;

    /**
     * 跟进人姓名
     */
    @Schema(description = "跟进人姓名")
    private String resumePersonName;


    /**
     * 跟进人姓名
     */
    @Schema(description = "跟进人code")
    private String resumePersonCode;

    /**
     * 跟进人门店
     */
    @Schema(description = "跟进门店名称")
    private String dlrNameOwner;

    /**
     * 跟进人门店
     */
    @Schema(description = "跟进门店code")
    private String dlrCodeOwner;

    /**
     * 跟进描述
     */
    @Schema(description = "跟进描述")
    private String resumeDesc;

    /**
     * 线索等级(H/A/B/C/D/E)
     */
    @Schema(description = "线索等级(H/A/B/C/D/E)")
    private String clueLevel;

    /**
     * 关联单号
     */
    @Schema(description = "关联单号")
    private String relationBillId;

    /**
     * 履历id
     */
    @Schema(description = "履历id")
    private String resumeId;

    /**
     * 热度
     */
    @Schema(description = "热度")
    private String clueLevelCode;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private String custId;

    /**
     * 客户履历备注
     */
    @Schema(description = "客户履历备注")
    private String remark;

    /**
     * 附件路径
     */
    @Schema(description = "附件路径")
    private List<String> filePathlist;
}
