package com.smart.adp.application.chain.handler.clue;

import com.smart.adp.application.chain.context.ClueCreateContext;
import com.smart.adp.application.chain.handler.StopWatchHandler;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/1
 */
@Component
public class ClueCreateAssignHandler extends StopWatchHandler<ClueCreateContext> {

    @Override
    protected void doHandle(ClueCreateContext ctx) {

    }
}
