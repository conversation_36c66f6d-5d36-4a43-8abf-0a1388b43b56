package com.smart.adp.application.dto.clue;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.CollectionUtil;
import com.mybatisflex.core.util.StringUtil;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.smart.adp.domain.entity.clue.table.SacClueInfoDlrTableDef.SAC_CLUE_INFO_DLR;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/26
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class UserJourneysFixDTO {

    /**
     * 门店编码
     */
    private String dlrCode;

    /**
     * 线索 id 列表
     */
    private List<String> clueIds;

    /**
     * 线索创建起始时间
     */
    private LocalDateTime createTimeStart;

    /**
     * 线索创建结束时间
     */
    private LocalDateTime createTimeEnd;

    public QueryWrapper buildFlowsWrapper() {
        return QueryWrapper.create()
                           .select(SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.SERVER_ORDER, SAC_CLUE_INFO_DLR.INFO_CHAN_DCODE, SAC_CLUE_INFO_DLR.CREATED_DATE)
                           .and(SAC_CLUE_INFO_DLR.DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
                           .and(SAC_CLUE_INFO_DLR.ID.in(getClueIds(), CollectionUtil::isNotEmpty))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.ge(getCreateTimeStart(), Objects::nonNull))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.lt(getCreateTimeEnd(), Objects::nonNull));
    }

    public QueryWrapper buildStageWrapper() {
        return QueryWrapper.create()
                           .select(SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.STATUS_CODE)
                           .and(SAC_CLUE_INFO_DLR.DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
                           .and(SAC_CLUE_INFO_DLR.ID.in(getClueIds(), CollectionUtil::isNotEmpty))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.ge(getCreateTimeStart(), Objects::nonNull))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.lt(getCreateTimeEnd(), Objects::nonNull));
    }

    public QueryWrapper buildESFixWrapper() {
        return QueryWrapper.create()
                           .select(SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.PHONE,
                               SAC_CLUE_INFO_DLR.DLR_CODE, SAC_CLUE_INFO_DLR.REVIEW_PERSON_ID,
                               SAC_CLUE_INFO_DLR.CUST_NAME, SAC_CLUE_INFO_DLR.STATUS_CODE,
                               SAC_CLUE_INFO_DLR.LAST_REVIEW_TIME, SAC_CLUE_INFO_DLR.CREATED_DATE)
                           .and(SAC_CLUE_INFO_DLR.DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
                           .and(SAC_CLUE_INFO_DLR.ID.in(getClueIds(), CollectionUtil::isNotEmpty))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.ge(getCreateTimeStart(), Objects::nonNull))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.lt(getCreateTimeEnd(), Objects::nonNull));
    }

    public QueryWrapper buildInitClueESWrapper(String index) {
        return QueryWrapper.create()
                           .select(SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID, SAC_CLUE_INFO_DLR.PHONE,
                               SAC_CLUE_INFO_DLR.DLR_CODE, SAC_CLUE_INFO_DLR.REVIEW_PERSON_ID,
                               SAC_CLUE_INFO_DLR.CUST_NAME, SAC_CLUE_INFO_DLR.STATUS_CODE,
                               SAC_CLUE_INFO_DLR.LAST_REVIEW_TIME, SAC_CLUE_INFO_DLR.CREATED_DATE)
                           .and(SAC_CLUE_INFO_DLR.ID.gt(index, Objects::nonNull))
                           .and(SAC_CLUE_INFO_DLR.DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
                           .and(SAC_CLUE_INFO_DLR.ID.in(getClueIds(), CollectionUtil::isNotEmpty))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.ge(getCreateTimeStart(), Objects::nonNull))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.lt(getCreateTimeEnd(), Objects::nonNull))
                           .orderBy(SAC_CLUE_INFO_DLR.ID, true)
                           .limit(1000);
    }

    public QueryWrapper buildInitResumeESWrapper(String index) {
        return QueryWrapper.create()
                           .select(SAC_CLUE_INFO_DLR.ID, SAC_CLUE_INFO_DLR.CUST_ID)
                           .and(SAC_CLUE_INFO_DLR.ID.gt(index, Objects::nonNull))
                           .and(SAC_CLUE_INFO_DLR.DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
                           .and(SAC_CLUE_INFO_DLR.ID.in(getClueIds(), CollectionUtil::isNotEmpty))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.ge(getCreateTimeStart(), Objects::nonNull))
                           .and(SAC_CLUE_INFO_DLR.CREATED_DATE.lt(getCreateTimeEnd(), Objects::nonNull))
                           .orderBy(SAC_CLUE_INFO_DLR.ID, true)
                           .limit(1000);
    }
}
