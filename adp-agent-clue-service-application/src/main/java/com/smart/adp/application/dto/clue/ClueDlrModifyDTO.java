package com.smart.adp.application.dto.clue;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.bo.clue.ClueDlrModifyBO;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.entity.base.UserBusiEntity;
import com.smart.adp.domain.enums.ClueModifyTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Description: 更新线索
 * @Author: rik.ren
 * @Date: 2025/3/7 13:26
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "更新线索入参")
public class ClueDlrModifyDTO {

    /**
     * 线索 ID
     */
    @Schema(description = "线索 ID", required = false)
//    @NotEmpty(message = "线索 ID 不能为空")
    private String id;
    /**
     * 客户ID
     */
    @Schema(description = "客户ID", required = true)
    @NotEmpty(message = "客户 ID 不能为空")
    private String custId;
    /**
     * 线索单号
     */
    @Schema(description = "线索单号", required = false)
//    @NotEmpty(message = "线索单号不能为空")
    private String serverOrder;
    /**
     * 更新的值对应的code
     */
    @Schema(description = "更新的值对应的code", required = false)
    private String valueCode;
    /**
     * 更新的值
     */
    @Schema(description = "更新的值", required = true)
    @NotEmpty(message = "更新的值不能为空")
    @Length(max = 500, message = "内容长度需小于500字符")
    private String value;
    /**
     * 更新的类型
     *
     * @see ClueModifyTypeEnum
     */
    @Schema(description = "更新类型", required = true)
    @NotNull(message = "更新类型不能为空")
    private Integer clueModifyType;

    /**
     * 战败标签
     */
    @Schema(description = "战败标签，0正常，1战败", required = true)
    private Integer defeatFlag = 0;

    /**
     * 登录人信息
     */
    @Schema(description = "登录人信息，不显示只用来传递", hidden = true)
    private UserBusiEntity userBusiEntity;

    public UserBusiEntity getUserBusiEntity() {
        if(ObjectUtil.isEmpty(userBusiEntity)) {
            userBusiEntity = UserInfoContext.get();
        }
        return userBusiEntity;
    }

    public ClueDlrModifyBO buildModifyBO() {
        return BeanUtil.copyProperties(this, ClueDlrModifyBO.class);
    }
}
