package com.smart.adp.application.chain.context;

import com.smart.adp.domain.common.resp.RespBody;
import org.springframework.util.StopWatch;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/30
 */
public class BizContext extends Context {

    protected Exception ex;

    protected StopWatch stopWatch;

    protected RespBody<?> result;

    public Exception getEx() {
        return ex;
    }

    public void setEx(Exception ex) {
        this.ex = ex;
    }

    public StopWatch getStopWatch() {
        if (Objects.isNull(stopWatch)) {
            stopWatch = new StopWatch();
        }

        return stopWatch;
    }

    public void setStopWatch(StopWatch stopWatch) {
        this.stopWatch = stopWatch;
    }

    public RespBody<?> getResult() {
        return result;
    }

    public void setResult(RespBody<?> result) {
        this.result = result;
    }
}
