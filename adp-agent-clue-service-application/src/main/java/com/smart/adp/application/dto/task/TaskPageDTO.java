package com.smart.adp.application.dto.task;

import com.smart.adp.application.common.PageDTO;
import com.smart.adp.application.utils.LocalDateTimeUtil;
import com.smart.adp.domain.enums.TaskQryTypeEnum;
import com.smart.adp.domain.qry.TaskQry;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/5
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
@Schema(description = "任务分页入参")
public class TaskPageDTO extends PageDTO {

    /**
     * 查询类型
     */
    @NotNull(message = "查询类型不能为空")
    @Schema(description = "查询类型")
    private TaskQryTypeEnum qryType;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    /**
     * 截止时间开始
     */
    @Schema(description = "截止时间开始")
    private LocalDateTime endTimeStart;

    /**
     * 截止时间结束
     */
    @Schema(description = "截止时间结束")
    private LocalDateTime endTimeEnd;

    /**
     * 专家 ID 列表
     */
    @Schema(description = "专家 ID 列表")
    private List<String> personIds;

    /**
     * dto -> qry
     *
     * @param dto 入参
     * @return qry
     */
    public static TaskQry buildQry(TaskPageDTO dto) {
        TaskQry qry = new TaskQry();
        qry.setQryType(dto.getQryType());
        qry.setCreateTimeStart(dto.getCreateTimeStart());
        qry.setCreateTimeEnd(LocalDateTimeUtil.handleEnd(dto.getCreateTimeEnd()));
        qry.setEndTimeStart(dto.getEndTimeStart());
        qry.setEndTimeEnd(LocalDateTimeUtil.handleEnd(dto.getEndTimeEnd()));
        qry.setPersonIds(dto.getPersonIds());
        return qry;
    }
}
