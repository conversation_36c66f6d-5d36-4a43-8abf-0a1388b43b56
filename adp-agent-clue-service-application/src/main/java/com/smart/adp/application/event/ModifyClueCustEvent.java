package com.smart.adp.application.event;

import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * date 2025/5/15 14:06
 * @description
 **/
@Getter
@ToString
public class ModifyClueCustEvent extends ApplicationEvent {

    /**
     * 手机号
     */
    private String phone;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 是否战败更改
     */
    private Boolean isDefeatedDis;

    /**
     * 回访任务id
     */
    private String reviewId;

    public ModifyClueCustEvent(Object source, String phone, String custName, Boolean isDefeatedDis, String reviewId) {
        super(source);
        this.phone = phone;
        this.custName = custName;
        this.isDefeatedDis = isDefeatedDis;
        this.reviewId = reviewId;
    }
}
