package com.smart.adp.application.assembler.clue;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.vo.clue.ClueDlrRspVO;
import com.smart.adp.domain.bo.clue.SacClueInfoDlrBO;

public class ClueDlrVOAssembler implements Assembler<SacClueInfoDlrBO, ClueDlrRspVO> {

    @Override
    public ClueDlrRspVO assemble(SacClueInfoDlrBO source, Class<ClueDlrRspVO> target) {
        if (ObjectUtil.isEmpty(source)) {
            return null;
        }
        return convert(source);
    }

    private ClueDlrRspVO convert(SacClueInfoDlrBO source) {
        ClueDlrRspVO clueDlrRspVO = new ClueDlrRspVO();
        clueDlrRspVO.setCustId(source.getCustId());
        clueDlrRspVO.setCustName(source.getCustName());
        clueDlrRspVO.setPhone(source.getPhone());
        clueDlrRspVO.setIntenCarTypeName(source.getIntenCarTypeName());
        clueDlrRspVO.setChannelName(source.getChannelName());
        clueDlrRspVO.setIntenCarTypeCode(source.getIntenCarTypeCode());
        clueDlrRspVO.setInfoChanMcode(source.getInfoChanMCode());
        clueDlrRspVO.setInfoChanMname(source.getInfoChanMName());
        clueDlrRspVO.setInfoChanDcode(source.getInfoChanDCode());
        clueDlrRspVO.setInfoChanDname(source.getInfoChanDName());
        clueDlrRspVO.setIntenLevelCode(source.getIntenLevelCode());
        clueDlrRspVO.setGenderCode(source.getGenderCode());
        clueDlrRspVO.setColumn5(source.getColumn5());
        clueDlrRspVO.setColumn11(source.getColumn11());
        clueDlrRspVO.setServerOrder(source.getServerOrder());
        clueDlrRspVO.setColumn18(source.getColumn18());
        clueDlrRspVO.setDlrCode(source.getDlrCode());
        clueDlrRspVO.setStatusCode(source.getStatusCode());
        return clueDlrRspVO;
    }
}