package com.smart.adp.application.validate;

import lombok.SneakyThrows;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @ClassName: ValueExistValidation
 * @Description: 自定义校验器，判断数据是否存在，还可以看一下DefaultGroupSequenceProvider接口
 * @Author: rik.ren
 * @Date: 2025/3/5 10:51
 **/
public class ValueExistValidation implements ConstraintValidator<ValidationAnnot, Object> {
    private String field;

    @Override
    public void initialize(ValidationAnnot constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
        field = constraintAnnotation.checkMarkName();
    }

    @SneakyThrows
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        ValidationEnum byField = ValidationEnum.getByField(field);
        assert byField != null;
        return byField.validationFunc(value);
    }
}
