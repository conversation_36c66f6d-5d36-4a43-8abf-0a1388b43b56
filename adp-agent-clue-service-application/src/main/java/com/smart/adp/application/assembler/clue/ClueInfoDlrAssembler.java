package com.smart.adp.application.assembler.clue;

import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;

/**
 * <AUTHOR>
 * date 2025/3/13 16:40
 * @description
 **/
public class ClueInfoDlrAssembler implements Assembler<AgentClueSaveDTO, SacClueInfoDlr> {

    /**
     * 装配对象
     *
     * @param source 源
     * @param target 目标
     * @return
     */
    @Override
    public SacClueInfoDlr assemble(AgentClueSaveDTO source, Class<SacClueInfoDlr> target) {
        return convert(source);
    }

    private SacClueInfoDlr convert(AgentClueSaveDTO par) {
        SacClueInfoDlr sacClueInfoDlr = new SacClueInfoDlr();
        sacClueInfoDlr.setId(par.getId());
        sacClueInfoDlr.setServerOrder(par.getServerOrder());
        sacClueInfoDlr.setCustId(par.getCustId());
        sacClueInfoDlr.setCustName(par.getCustName());
        sacClueInfoDlr.setPhone(par.getPhone());
        sacClueInfoDlr.setPhoneBackup(par.getPhoneBackUp());
        sacClueInfoDlr.setIntenLevelCode(par.getIntenLevelCode());
        sacClueInfoDlr.setIntenLevelName(par.getIntenLevelName());
        sacClueInfoDlr.setIntenCarTypeCode(par.getIntenCarTypeCode());
        sacClueInfoDlr.setIntenCarTypeName(par.getIntenCarTypeName());
        sacClueInfoDlr.setInnerColorCode(par.getInnerColorCode());
        sacClueInfoDlr.setInnerColorName(par.getInnerColorName());
        sacClueInfoDlr.setOutColorCode(par.getOutColorCode());
        sacClueInfoDlr.setOutColorName(par.getOutColorName());
        sacClueInfoDlr.setDlrCode(par.getDlrCode());
        sacClueInfoDlr.setDlrShortName(par.getDlrShortName());
        sacClueInfoDlr.setInfoChanMCode(par.getInfoChanMCode());
        sacClueInfoDlr.setInfoChanMName(par.getInfoChanMName());
        sacClueInfoDlr.setInfoChanDCode(par.getInfoChanDCode());
        sacClueInfoDlr.setInfoChanDName(par.getInfoChanDName());
        sacClueInfoDlr.setChannelCode(par.getChannelCode());
        sacClueInfoDlr.setChannelName(par.getChannelName());
        sacClueInfoDlr.setGenderCode(par.getGenderCode());
        sacClueInfoDlr.setGenderName(par.getGenderName());
        sacClueInfoDlr.setStatusCode(par.getStatusCode());
        sacClueInfoDlr.setStatusName(par.getStatusName());
        sacClueInfoDlr.setFirstReviewTime(par.getFirstReviewTime());
        sacClueInfoDlr.setAssignTime(par.getAssignTime());
        sacClueInfoDlr.setReviewPersonName(par.getReviewPersonName());
        sacClueInfoDlr.setReviewPersonId(par.getReviewPersonId());
        sacClueInfoDlr.setOemId(par.getOemId());
        sacClueInfoDlr.setGroupId(par.getGroupId());
        sacClueInfoDlr.setCreator(par.getCreator());
        sacClueInfoDlr.setCreatedName(par.getCreatedName());
        sacClueInfoDlr.setCreatedDate(par.getCreatedDate());
        sacClueInfoDlr.setModifier(par.getModifier());
        sacClueInfoDlr.setModifyName(par.getModifyName());
        sacClueInfoDlr.setLastUpdatedDate(par.getLastUpdatedDate());
        sacClueInfoDlr.setIsEnable(par.getIsEnable());
        sacClueInfoDlr.setUpdateControlId(par.getUpdateControlId());
        sacClueInfoDlr.setProvinceCode(par.getProvinceCode());
        sacClueInfoDlr.setProvinceName(par.getProvinceName());
        sacClueInfoDlr.setCityCode(par.getCityCode());
        sacClueInfoDlr.setCityName(par.getCityName());
        sacClueInfoDlr.setCountyCode(par.getCountyCode());
        sacClueInfoDlr.setCountyName(par.getCountyName());
        return sacClueInfoDlr;
    }
}
