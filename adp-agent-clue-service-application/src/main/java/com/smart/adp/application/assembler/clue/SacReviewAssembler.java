package com.smart.adp.application.assembler.clue;

import cn.hutool.core.util.StrUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.dto.clue.AgentClueSaveDTO;
import com.smart.adp.domain.entity.clue.SacClueInfoDlr;
import com.smart.adp.domain.entity.clue.SacReview;
import org.apache.commons.lang.time.DateFormatUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;

/**
 * <AUTHOR>
 * date 2025/3/13 16:40
 * @description
 **/
public class SacReviewAssembler implements Assembler<AgentClueSaveDTO, SacReview> {

    /**
     * 装配对象
     *
     * @param source 源
     * @param target 目标
     * @return
     */
    @Override
    public SacReview assemble(AgentClueSaveDTO source, Class<SacReview> target) {
        return convert(source);
    }

    private SacReview convert(AgentClueSaveDTO par) {
        SacReview sacReview = new SacReview();
        sacReview.setOrgCode(par.getDlrCode());
        sacReview.setOrgName(par.getDlrShortName());
        sacReview.setBillType(par.getBillType());
        sacReview.setBillTypeName(par.getBillTypeName());
        sacReview.setBusinessType(par.getBusinessType());
        sacReview.setBusinessTypeName(par.getBusinessTypeName());
        sacReview.setInfoChanMCode(par.getInfoChanMCode());
        sacReview.setInfoChanMName(par.getInfoChanMName());
        sacReview.setInfoChanDCode(par.getInfoChanDCode());
        sacReview.setInfoChanDName(par.getInfoChanDName());
        sacReview.setChannelCode(par.getChannelCode());
        sacReview.setChannelName(par.getChannelName());
        sacReview.setBillCode(par.getBillCode());
        if(StrUtil.isNotBlank(par.getPlanReviewTime())){
            sacReview.setPlanReviewTime(LocalDateTime.parse(par.getPlanReviewTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if(StrUtil.isNotBlank(par.getFactComeTime())){
            sacReview.setFactComeTime(LocalDateTime.parse(par.getFactComeTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        sacReview.setAssignTime(par.getAssignTime());
        sacReview.setReviewPersonId(par.getReviewPersonId());
        sacReview.setReviewPersonName(par.getReviewPersonName());
        sacReview.setCustId(par.getCustId());
        sacReview.setCustName(par.getCustName());
        sacReview.setPhone(par.getPhone());
        sacReview.setGender(par.getGenderCode());
        sacReview.setGenderName(par.getGenderName());
        sacReview.setIntenLevelCode(par.getIntenLevelCode());
        sacReview.setIntenLevelName(par.getIntenLevelName());
        sacReview.setIntenCarTypeCode(par.getIntenCarTypeCode());
        sacReview.setIntenCarTypeName(par.getIntenCarTypeName());
        sacReview.setOemId(par.getOemId());
        sacReview.setGroupId(par.getGroupId());
        sacReview.setCreator(par.getCreator());
        sacReview.setCreatedName(par.getCreatedName());
        sacReview.setCreatedDate(par.getCreatedDate());
        sacReview.setModifier(par.getModifier());
        sacReview.setModifyName(par.getModifyName());
        sacReview.setLastUpdatedDate(par.getLastUpdatedDate());
        sacReview.setIsEnable(par.getIsEnable());
        sacReview.setUpdateControlId(par.getUpdateControlId());
        sacReview.setProvinceCode(par.getProvinceCode());
        sacReview.setProvinceName(par.getProvinceName());
        sacReview.setCityCode(par.getCityCode());
        sacReview.setCityName(par.getCityName());
        sacReview.setCountyCode(par.getCountyCode());
        sacReview.setCountyName(par.getCountyName());
        return sacReview;
    }
}
