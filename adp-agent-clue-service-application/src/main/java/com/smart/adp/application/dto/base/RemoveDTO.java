package com.smart.adp.application.dto.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/4/21 15:33
 * @description
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoveDTO implements Serializable {

    @Schema(description = "清理key")
    @NotBlank(message = "清理key不能为空")
    private String removeKey;

    @Schema(description = "是否能清理")
    @NotBlank(message = "是否能清理标识不能为空")
    private String canRemove;
}
