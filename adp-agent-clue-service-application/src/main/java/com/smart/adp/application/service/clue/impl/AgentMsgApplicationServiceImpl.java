package com.smart.adp.application.service.clue.impl;

import com.smart.adp.application.assembler.AssemblerFactory;
import com.smart.adp.application.assembler.clue.ClueMsgRecordRspVOAssembler;
import com.smart.adp.application.dto.clue.MessageUnReadCountDTO;
import com.smart.adp.application.dto.clue.MessageQueryDTO;
import com.smart.adp.application.dto.clue.MessageReadFlagDTO;
import com.smart.adp.application.service.clue.AgentMsgApplicationService;
import com.smart.adp.application.vo.clue.ClueMsgRecordRspVO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.service.clue.ISacClueMsgRecordService;
import com.smart.adp.domain.valueObject.clue.SacClueMsgRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 消息服务的实现
 * @Author: rik.ren
 * @Date: 2025/4/15 16:07
 **/
@Service
public class AgentMsgApplicationServiceImpl implements AgentMsgApplicationService {
    @Autowired
    private ISacClueMsgRecordService sacClueMsgRecordService;

    /**
     * 查询符合条件的消息列表
     *
     * @param param
     * @return
     */
    @Override
    public DomainPage<ClueMsgRecordRspVO> queryMsg(MessageQueryDTO param) {
        // 查询msg
        DomainPage<SacClueMsgRecordVO> pageByCondition = sacClueMsgRecordService.findPageByCondition(param.buildSacClueMsgRecordBO());
        // 对象转化
        return AssemblerFactory.getInstance().convert(ClueMsgRecordRspVOAssembler.class, pageByCondition, DomainPage.class);
    }

    /**
     * 查询符合条件的消息个数
     *
     * @param param
     * @return
     */
    @Override
    public Long queryCountMsg(MessageUnReadCountDTO param) {
        // 查询msg
        return sacClueMsgRecordService.findCountByCondition(param.buildClueMsgUnReadRecordBO());
    }

    /**
     * 标记消息已读
     *
     * @param param
     * @return
     */
    @Override
    public Boolean markEmpMsgRead(MessageReadFlagDTO param) {
        return sacClueMsgRecordService.markEmpMsgRead(param.buildSacClueMsgMarkReadBO());
    }
}
